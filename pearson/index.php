<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <title>Avaliativa - Selecione o ano</title>

        <link href="_selecione_ano/bootstrap.min.css" rel="stylesheet" type="text/css">
        <script type="text/javascript" src="_selecione_ano/bootstrap.bundle.min.js"></script>
        <script type="text/javascript" src="_selecione_ano/jquery-3.6.0.min.js"></script>

        <style>
            body {
                background-image:url(_selecione_ano/fundo4.png);
                background-repeat: no-repeat;
                background-position: center;
                background-attachment: fixed;
                background-size: cover;
                overflow: hidden;
            }

            a{
                text-decoration: none;
            }

            .container-fluid, .row{
                --bs-gutter-x: 0 !important;
            }

            .row-logo{
                position: absolute;
                top: 0;
                width: 100%;
                height: 10%;
            }

            .col-logo{
                text-align: right !important;
                padding: 15px;
            }

            .logo{
                width:185px;
            }

            .row-titulo{
                position: absolute;
                top: 10%;
                width: 100%;
                height: 10%;
            }

            .col {
                
                text-align: center;
            }

            .col-titulo{
                padding: 15px;
            }

            h5 {
                font-size: 2.25rem;
                color: #616162;
            }

            .content-buttons{
                overflow-x: hidden; /* Hide horizontal scrollbar */
                overflow-y: scroll; /* Add vertical scrollbar */
                position: absolute;
                top: 135px;
                width: 100%;
                height: 70%;
                padding: 40px 0;
            }

            button{
                background-color: #07003B;
                border-radius: 8px 8px 8px 8px;
                font-size: 30px;
                border: 1px solid #07003B;
                box-shadow: 0 0 1.5em #ccc;
            }

            button:hover {
                background-color: #010322;
                border-color: #010322;
            }

            a button {
                color: white;
                width: 120px;
                height: 55px;
                margin-bottom: 15px;
            }

            .row-rodape{
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 10%;
            }

            .col-rodape{
                text-align: center;
            }

            .rodape{
                width:auto;
                height:auto;
            }

            #rp1{
                display: none;
            }

            @media only screen and (max-width: 420px) {
                body {
                    background-image:url(_selecione_ano/fundo4.png);
                    background-repeat: no-repeat;
                    background-position: center;
                    background-attachment: fixed;
                    background-size: cover;
                    overflow: hidden;
                    background-position-x: 17%;
                }

                a{
                    text-decoration: none;
                }

                .container-fluid, .row{
                    --bs-gutter-x: 0 !important;
                }

                .row-logo{
                    position: absolute;
                    top: 0;
                    width: 100%;
                    height: 7%;
                }

                .col-logo{
                    text-align: center;
                    background: white;
                    padding: 5px;
                }

                .logo{
                    width:230px;
                    height:50px;
                }

                .row-titulo{
                    position: absolute;
                    top: 60px;
                    width: 100%;
                    height: 8%;
                }

                .col {
                    flex: auto;
                    text-align: center;
                }

                .col-titulo-dt{
                    display: none;
                }

                .col-titulo{
                    padding: 15px;
                }

                h5 {
                    font-size: 2.25rem;
                    color: #616162;
                    text-align: center;
                    color: white !important;
                }

                .d-inline{
                    display: block !important;
                }

                .content-buttons{
                    overflow-x: hidden; /* Hide horizontal scrollbar */
                    overflow-y: scroll; /* Add vertical scrollbar */
                    position: absolute;
                    top: 135px;
                    width: 100%;
                    height: 77%;
                    padding: 40px 0;
                }

                button{
                    background-color: #07003B;
                    border-radius: 8px 8px 8px 8px;
                    font-size: 30px;
                    border: 1px solid #07003B;
                    box-shadow: 0 0 1.5em #ccc;
                }

                button:hover {
                    background-color: #010322;
                    border-color: #010322;
                }

                a button {
                    color: white;
                    width: 120px;
                    height: 55px;
                    margin-bottom: 15px;
                }

                .row-rodape{
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    height: 7%;
                }

                .col-rodape{
                    text-align: center;
                    padding: 15px;
                    background: white;
                }

                .rodape{
                    width:auto;
                    height:auto;
                }

                #rp1{
                    display: initial;
                }
            }
        </style>
    </head>
    <body>
        <div class="container-fluid" style="width: 100%; height: 100%;position: absolute;">
            <div class="row row-logo">
                <div class="col col-logo">
                    <img src="_selecione_ano/logo.png" class="logo">
                </div>
            </div>

            <div class="row row-titulo">
                <div class="col col-titulo-dt"></div>
                <div class="col col-titulo">
                    <h5>Selecione o ano:</h5>
                </div>
            </div>

            <div class="container-fluid content-buttons">

            <?php 
                ini_set('display_errors', '0');
                error_reporting('0');

                $ponteiro  = opendir(getcwd());
                while ($nome_itens = readdir($ponteiro)) {
                    if ($nome_itens!="." && $nome_itens!=".."){ 
                        if (is_dir($nome_itens) && is_numeric($nome_itens) && strlen($nome_itens)>=4){ 
                            $textos[] = $nome_itens;
                        }
                    }
                }
                rsort($textos);

                $textos = array_chunk($textos, 2);

                foreach($textos as $tk => $anosEm2){
                    echo'<div class="row">
                            <div class="col-6 d-inline">
                                <!-- espaço da esquerda  para ajustar os botoes na direita -->
                            </div>
                            <div class="col d-inline">';

                    foreach($anosEm2 as $aek => $aev){
                        $url = "https://".$_SERVER['SERVER_NAME'].str_replace("index.php", "", $_SERVER['REQUEST_URI']).$aev;
                        
                        echo'   <div class="col d-inline">
                                    <a href="'.$url.'">
                                        <button>'.$aev.'</button>
                                    </a>
                                </div>';
                    }

                    echo'    </div>
                        </div>';
                }   
            ?>

            </div>

            <div class="row row-rodape">
                <div class="col col-rodape">
                    <img src="_selecione_ano/logo_avaliativa.png" class="rodape" id='rp1'>
                </div>
            </div>
        </div>
    </body>
</html>