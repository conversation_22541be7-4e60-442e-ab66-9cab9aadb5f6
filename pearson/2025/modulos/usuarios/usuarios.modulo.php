<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('UsuarioInstituido', null, true);
Core::incluir('Relatorio', null, true);

include_once('usuarios.listagem.php');
include_once('usuarios.listagem.primeiro_acesso.php');
include_once('usuarios.formulario.php');
include_once('usuarios.exportador.php');
include_once('usuarios.importador.php');


class MUsuarios extends Modulo
{
	protected $_listagem;
	protected $_formulario;
	protected $_dadosMonitoramento = array();
	protected $_statsMonitoramento = array();
	protected $_totalRegistros = 0;
	protected $_paginaAtual = 1;
	protected $_registrosPorPagina = 15;
	
	// Sistema de cache simples
	private $_cacheDir = '/tmp/dashboard_cache/';
	private $_cacheTime = 30; // 30 segundos

	public function __construct ()
	{
		parent::__construct();

		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('usuarios', 'novo'), 'Adicionar usuário...');
		}
		
		// Criar diretório de cache se não existir
		if (!is_dir($this->_cacheDir)) {
			@mkdir($this->_cacheDir, 0755, true);
		}
	}

	public function aExportarUsuarios () {
		try {
			$exportador = new ExportadorUsuarios();
			$exportador->exportar();
			$this->_iniciarRenderizacao(Modulo::CSV);
				$exportador->obterSaida('usuarios.csv');
			$this->_finalizarRenderizacao();
		} catch (Core_Exception $e) {
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		}
	}

	public function aListarUsuarios () {
		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('importar', true, Gerenciador_URL::gerarLink('usuarios', 'importar'), 'Importar usuários...');
			Core::modulo('navegador')->habilitarAtalho('exportar', true, Gerenciador_URL::gerarLink('usuarios', 'exportar'), 'Exportar usuários...');
		}
		return $this->_carregarListagem();
	}

	public function aListarUsuariosMonitoramento () {
		return $this->_carregarDashboardMonitoramento();
	}

	private function _carregarDashboardMonitoramento() 
	{
		if (Core::moduloCarregado('navegador')) {
			Core::modulo('navegador')->removerAtalho('adicionar');
			Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');
		}
		
		$this->_obterDadosMonitoramento();
		$this->_calcularEstatisticasMonitoramento();
		
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('usuarios.dashboard.html.php');
		$this->_finalizarRenderizacao();
	}
	
	/**
	 * Gera uma chave única para o cache baseada nos filtros
	 */
	private function _gerarChaveCache($filtros = array()) {
		$chave = 'dashboard_' . md5(serialize($filtros));
		return $chave;
	}
	
	/**
	 * Verifica se existe cache válido para os filtros
	 */
	private function _verificarCache($chave) {
		$arquivo = $this->_cacheDir . $chave . '.cache';
		
		if (!file_exists($arquivo)) {
			return false;
		}
		
		$idade = time() - filemtime($arquivo);
		if ($idade > $this->_cacheTime) {
			@unlink($arquivo); // Remove cache expirado
			return false;
		}
		
		$dados = @file_get_contents($arquivo);
		if ($dados === false) {
			return false;
		}
		
		$dados = @unserialize($dados);
		if ($dados === false) {
			return false;
		}
		
		return $dados;
	}
	
	/**
	 * Salva dados no cache
	 */
	private function _salvarCache($chave, $dados) {
		$arquivo = $this->_cacheDir . $chave . '.cache';
		$dadosSerializados = serialize($dados);
		
		@file_put_contents($arquivo, $dadosSerializados, LOCK_EX);
	}
	
	/**
	 * Limpa arquivos de cache antigos (mais de 1 hora)
	 */
	private function _limparCacheAntigo() {
		if (!is_dir($this->_cacheDir)) {
			return;
		}
		
		$arquivos = glob($this->_cacheDir . '*.cache');
		$agora = time();
		
		foreach ($arquivos as $arquivo) {
			if (($agora - filemtime($arquivo)) > 3600) { // 1 hora
				@unlink($arquivo);
			}
		}
	}
	
	private function _obterDadosMonitoramento()
	{
		$this->_dadosMonitoramento = array();
		
		$filtroTermo = !empty($_GET['termo']) ? trim($_GET['termo']) : '';
		$filtroCampo = !empty($_GET['campo']) ? $_GET['campo'] : '';
		$filtroBimestre = !empty($_GET['bimestre']) ? trim($_GET['bimestre']) : '';
		$filtroInstituicao = !empty($_GET['instituicao']) ? trim($_GET['instituicao']) : '';
		$filtroSerie = !empty($_GET['serie']) ? trim($_GET['serie']) : '';
		$filtroTurma = !empty($_GET['turma_nome']) ? trim($_GET['turma_nome']) : '';
		
		// Paginação
		$paginaAtual = !empty($_GET['pagina']) && is_numeric($_GET['pagina']) ? (int)$_GET['pagina'] : 1;
		$registrosPorPagina = 15;
		$offset = ($paginaAtual - 1) * $registrosPorPagina;
		
		// Verificar se há filtros aplicados
		$temFiltros = !empty($filtroTermo) || !empty($filtroCampo) || !empty($filtroBimestre) || 
					  !empty($filtroInstituicao) || !empty($filtroSerie) || !empty($filtroTurma);
		
		// Gerar chave de cache baseada nos filtros + paginação
		$filtros = array(
			'termo' => $filtroTermo,
			'campo' => $filtroCampo,
			'bimestre' => $filtroBimestre,
			'instituicao' => $filtroInstituicao,
			'serie' => $filtroSerie,
			'turma' => $filtroTurma,
			'pagina' => $paginaAtual,
			'registros_por_pagina' => $registrosPorPagina
		);
		
		$chaveCache = $this->_gerarChaveCache($filtros);
		
		// Tentar obter do cache primeiro
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			error_log("✅ Cache HIT! Usando dados em cache para: " . $chaveCache);
			$this->_dadosMonitoramento = $dadosCache['dados'];
			$this->_totalRegistros = $dadosCache['total'];
			$this->_paginaAtual = $paginaAtual;
			$this->_registrosPorPagina = $registrosPorPagina;
			return;
		}
		
		error_log("❌ Cache MISS! Executando consulta para: " . $chaveCache);
		
		try {
			// Se não há filtros, buscar todos os dados com paginação
			if (!$temFiltros) {
				$this->_buscarTodosOsDadosComPaginacao($offset, $registrosPorPagina);
			} else {
				// Com filtros, buscar dados filtrados
				$this->_buscarDadosReaisDireto($filtroTermo, $filtroCampo, $filtroBimestre, $filtroInstituicao, $filtroSerie, $filtroTurma, $offset, $registrosPorPagina);
			}
			
			// Tentativa 2: Ultra simples se não encontrou nada
			if (empty($this->_dadosMonitoramento)) {
				$this->_buscarDadosUltraSimples($offset, $registrosPorPagina);
			}
			
			// Se ainda não encontrou dados, deixar vazio (sem fallback)
			if (empty($this->_dadosMonitoramento)) {
				error_log("⚠️ Nenhum dado encontrado no banco - exibindo mensagem de erro");
				$this->_totalRegistros = 0;
			}
			
			// Salvar no cache se obteve dados
			if (!empty($this->_dadosMonitoramento)) {
				$dadosParaCache = array(
					'dados' => $this->_dadosMonitoramento,
					'total' => $this->_totalRegistros
				);
				$this->_salvarCache($chaveCache, $dadosParaCache);
				error_log("💾 Dados salvos no cache: " . $chaveCache);
			}
			
			// Definir variáveis de paginação
			$this->_paginaAtual = $paginaAtual;
			$this->_registrosPorPagina = $registrosPorPagina;
			
			// Limpar cache antigo ocasionalmente (1 em 10 execuções)
			if (rand(1, 10) === 1) {
				$this->_limparCacheAntigo();
			}
			
		} catch (Exception $e) {
			error_log("❌ Erro geral: " . $e->getMessage());
			$this->_dadosMonitoramento = array();
			$this->_totalRegistros = 0;
		}
	}

	
	private function _buscarTodosOsDadosComPaginacao($offset = 0, $limit = 15)
	{
		error_log("🌍 Buscando TODOS os dados com paginação - Offset: $offset, Limit: $limit");
		
		// Primeiro, contar o total de registros
		$sqlCount = "
			SELECT COUNT(DISTINCT CONCAT(i_turma.i_id, '_', s.s_id, '_', t.t_id)) as total
			FROM simulados s
			INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN turmas t ON t.t_id = a.a_turma
			INNER JOIN instituicoes i_turma ON i_turma.i_id = t.t_instituicao
			WHERE s.s_nome IS NOT NULL AND TRIM(s.s_nome) != ''
			AND t.t_nome IS NOT NULL AND TRIM(t.t_nome) != ''
			AND i_turma.i_nome IS NOT NULL AND TRIM(i_turma.i_nome) != ''
		";
		
		try {
			$rs = Core::registro('db')->query($sqlCount);
			if ($rs && $rs->num_rows > 0) {
				$row = $rs->fetch_assoc();
				$this->_totalRegistros = (int)$row['total'];
				$rs->free();
			}
		} catch (Exception $e) {
			error_log("❌ Erro ao contar registros: " . $e->getMessage());
			$this->_totalRegistros = 0;
		}
		
		// Agora buscar os dados com paginação
		$sql = "
			SELECT 
				i_turma.i_nome as instituicao,
				s.s_nome as simulado,
				COALESCE(s.s_bimestre, 1) as bimestre,
				COALESCE(s.s_serie, t.t_serie, 1) as serie,
				t.t_nome as turma,
				COUNT(DISTINCT si.si_id) as total_inscritos,
				COUNT(DISTINCT CASE 
					WHEN ((si.si_finalizado IS NULL OR si.si_finalizado = 0) 
					AND (si.si_finalizado_data IS NULL OR si.si_finalizado_data = 0))
					AND EXISTS (
						SELECT 1 FROM respostas r 
						WHERE r.r_inscricao = si.si_id 
						AND r.r_valor IS NOT NULL
						AND TRIM(r.r_valor) != ''
						LIMIT 1
					)
					THEN si.si_id 
				END) as total_respondidos
			FROM simulados s
			INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN turmas t ON t.t_id = a.a_turma
			INNER JOIN instituicoes i_turma ON i_turma.i_id = t.t_instituicao
			WHERE s.s_nome IS NOT NULL AND TRIM(s.s_nome) != ''
			AND t.t_nome IS NOT NULL AND TRIM(t.t_nome) != ''
			AND i_turma.i_nome IS NOT NULL AND TRIM(i_turma.i_nome) != ''
			GROUP BY i_turma.i_id, s.s_id, t.t_id 
			HAVING total_inscritos > 0 
			ORDER BY i_turma.i_nome, s.s_nome, t.t_nome 
			LIMIT $limit OFFSET $offset
		";
		
		error_log("🎯 SQL TODOS OS DADOS: " . $sql);
		
		try {
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$instituicao = trim($row['instituicao']);
					$nomeSerieExibicao = $this->_obterNomeSeriePorId($row['serie'], $instituicao);
					$simulado = trim($row['simulado']) . ' (' . $row['bimestre'] . 'º Aplicação - ' . $nomeSerieExibicao . ')';
					$turma = trim($row['turma']);
					$inscritos = (int)$row['total_inscritos'];
					$respondidos = (int)$row['total_respondidos'];
					
					if (!empty($instituicao) && !empty($simulado) && !empty($turma)) {
						$this->_dadosMonitoramento[$instituicao][$simulado][$turma] = array(
							'inscritos' => $inscritos,
							'respondidos' => $respondidos,
							'bimestre' => (int)$row['bimestre'],
							'serie' => (int)$row['serie']
						);
					}
				}
				$rs->free();
				
				error_log("🎉 TODOS OS DADOS! Página " . ($offset/$limit + 1) . " - Encontrados: " . count($this->_dadosMonitoramento) . " instituições de " . $this->_totalRegistros . " total");
			} else {
				error_log("⚠️ Nenhum resultado na busca completa - tentando sem restrições");
				$this->_buscarDadosUltraSimples($offset, $limit);
			}
		} catch (Exception $e) {
			error_log("❌ Erro na consulta completa: " . $e->getMessage());
			throw $e;
		}
	}

	private function _buscarDadosReaisDireto($filtroTermo = '', $filtroCampo = '', $filtroBimestre = '', $filtroInstituicao = '', $filtroSerie = '', $filtroTurma = '', $offset = 0, $limit = 15)
	{
		$sql = "
			SELECT 
				i_turma.i_nome as instituicao,
				s.s_nome as simulado,
				COALESCE(s.s_bimestre, 1) as bimestre,
				COALESCE(s.s_serie, t.t_serie, 1) as serie,
				t.t_nome as turma,
				COUNT(DISTINCT si.si_id) as total_inscritos,
				COUNT(DISTINCT CASE 
					WHEN ((si.si_finalizado IS NULL OR si.si_finalizado = 0) 
					AND (si.si_finalizado_data IS NULL OR si.si_finalizado_data = 0))
					AND EXISTS (
						SELECT 1 FROM respostas r 
						WHERE r.r_inscricao = si.si_id 
						AND r.r_valor IS NOT NULL
						AND TRIM(r.r_valor) != ''
						LIMIT 1
					)
					THEN si.si_id 
				END) as total_respondidos
			FROM simulados s
			INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN turmas t ON t.t_id = a.a_turma
			INNER JOIN instituicoes i_turma ON i_turma.i_id = t.t_instituicao
			WHERE s.s_nome IS NOT NULL AND TRIM(s.s_nome) != ''
			AND t.t_nome IS NOT NULL AND TRIM(t.t_nome) != ''
			AND i_turma.i_nome IS NOT NULL AND TRIM(i_turma.i_nome) != ''
			AND s.s_serie = t.t_serie
		";
		
		// Aplicar filtros
		if (!empty($filtroInstituicao)) {
			// Filtro pela instituição da TURMA (não do simulado)
			$sql .= " AND LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroInstituicao) . "%')";
		}
		
		if (!empty($filtroBimestre) && is_numeric($filtroBimestre)) {
			$sql .= " AND COALESCE(s.s_bimestre, 1) = " . (int)$filtroBimestre;
		}
		
		if (!empty($filtroSerie) && is_numeric($filtroSerie)) {
			$sql .= " AND s.s_serie = " . (int)$filtroSerie;
			$sql .= " AND t.t_serie = " . (int)$filtroSerie;
		}
		
		if (!empty($filtroTurma)) {
			$sql .= " AND LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTurma) . "%')";
		}
		
		if (!empty($filtroTermo)) {
			if (!empty($filtroCampo)) {
				switch ($filtroCampo) {
					case 'instituicao':
						$sql .= " AND LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')";
						break;
					case 'simulado':
						$sql .= " AND LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')";
						break;
					default:
						$sql .= " AND (
							LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
							OR LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
							OR LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')
						)";
				}
			} else {
				$sql .= " AND (
					LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
					OR LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
					OR LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')
				)";
			}
		}
		
		$sql .= " 
			GROUP BY i_turma.i_id, s.s_id, t.t_id 
			HAVING total_inscritos > 0 
			ORDER BY i_turma.i_nome, s.s_nome, t.t_nome 
			LIMIT $limit OFFSET $offset
		";
		
		error_log("🎯 SQL FINAL (baseado no debug): " . $sql);
		
		try {
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$instituicao = trim($row['instituicao']);
					$nomeSerieExibicao = $this->_obterNomeSeriePorId($row['serie'], $instituicao);
					$simulado = trim($row['simulado']) . ' (' . $row['bimestre'] . 'º Aplicação - ' . $nomeSerieExibicao . ')';
					$turma = trim($row['turma']);
					$inscritos = (int)$row['total_inscritos'];
					$respondidos = (int)$row['total_respondidos'];
					
					if (!empty($instituicao) && !empty($simulado) && !empty($turma)) {
						$this->_dadosMonitoramento[$instituicao][$simulado][$turma] = array(
							'inscritos' => $inscritos,
							'respondidos' => $respondidos,
							'bimestre' => (int)$row['bimestre'],
							'serie' => (int)$row['serie']
						);
					}
				}
				$rs->free();
				
				error_log("🎉 SUCESSO TOTAL! Dados encontrados: " . count($this->_dadosMonitoramento) . " instituições");
			} else {
				error_log("⚠️ Nenhum resultado - tentando sem filtro de série");
				$this->_buscarDadosSemFiltroSerie($filtroTermo, $filtroCampo, $filtroBimestre, $filtroInstituicao, $filtroTurma);
			}
		} catch (Exception $e) {
			error_log("❌ Erro na consulta final: " . $e->getMessage());
			throw $e;
		}
	}

	private function _obterNomeSeriePorId($serieId, $instituicao)
	{
		// Usar cache para nomes de séries também
		$chaveCache = 'serie_' . md5($serieId . '_' . $instituicao);
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		try {
			// Primeira tentativa: buscar pelo nome da instituição
			$sql = "SELECT ser.s_nome 
					FROM series ser
					INNER JOIN instituicoes i ON i.i_id = ser.s_instituicao
					WHERE ser.s_id = " . (int)$serieId . " 
					AND LOWER(i.i_nome) = LOWER('" . Core::registro('db')->escape($instituicao) . "')
					LIMIT 1";
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				$row = $rs->fetch_assoc();
				$rs->free();
				$nome = $row['s_nome'];
				$this->_salvarCache($chaveCache, $nome);
				return $nome;
			}
			
			// Segunda tentativa: buscar sem filtro de instituição
			$sql = "SELECT s_nome FROM series WHERE s_id = " . (int)$serieId . " LIMIT 1";
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				$row = $rs->fetch_assoc();
				$rs->free();
				$nome = $row['s_nome'];
				$this->_salvarCache($chaveCache, $nome);
				return $nome;
			}
			
		} catch (Exception $e) {
			// Em caso de erro, usar fallback
			error_log("Erro ao buscar nome da série: " . $e->getMessage());
		}
		
		// Fallback: usar o formato antigo
		$nome = $serieId . 'ª Série';
		$this->_salvarCache($chaveCache, $nome);
		return $nome;
	}

	private function _buscarDadosSemFiltroSerie($filtroTermo = '', $filtroCampo = '', $filtroBimestre = '', $filtroInstituicao = '', $filtroTurma = '')
	{
		error_log("🔄 Executando sem filtro de série obrigatório");
		
		$sql = "
			SELECT 
				i_turma.i_nome as instituicao,
				s.s_nome as simulado,
				COALESCE(s.s_bimestre, 1) as bimestre,
				COALESCE(s.s_serie, t.t_serie, 1) as serie,
				t.t_nome as turma,
				COUNT(DISTINCT si.si_id) as total_inscritos,
				COUNT(DISTINCT CASE 
					WHEN ((si.si_finalizado IS NULL OR si.si_finalizado = 0) 
					AND (si.si_finalizado_data IS NULL OR si.si_finalizado_data = 0))
					AND EXISTS (
						SELECT 1 FROM respostas r 
						WHERE r.r_inscricao = si.si_id 
						AND r.r_valor IS NOT NULL
						AND TRIM(r.r_valor) != ''
						LIMIT 1
					)
					THEN si.si_id 
				END) as total_respondidos
			FROM simulados s
			INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN turmas t ON t.t_id = a.a_turma
			INNER JOIN instituicoes i_turma ON i_turma.i_id = t.t_instituicao
			WHERE s.s_nome IS NOT NULL 
			AND t.t_nome IS NOT NULL 
			AND i_turma.i_nome IS NOT NULL
		";
		
		// Aplicar apenas filtros básicos
		if (!empty($filtroInstituicao)) {
			$sql .= " AND LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroInstituicao) . "%')";
		}
		
		if (!empty($filtroBimestre) && is_numeric($filtroBimestre)) {
			$sql .= " AND COALESCE(s.s_bimestre, 1) = " . (int)$filtroBimestre;
		}
		
		if (!empty($filtroTurma)) {
			$sql .= " AND LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTurma) . "%')";
		}
		
		if (!empty($filtroTermo)) {
			$sql .= " AND (
				LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
				OR LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
				OR LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTurma) . "%')
			)";
		}
		
		$sql .= " 
			GROUP BY i_turma.i_id, s.s_id, t.t_id 
			HAVING total_inscritos > 0 
			ORDER BY total_inscritos DESC
			LIMIT 50
		";
		
		try {
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$instituicao = trim($row['instituicao']);
					$nomeSerieExibicao = $this->_obterNomeSeriePorId($row['serie'], $instituicao);
					$simulado = trim($row['simulado']) . ' (' . $row['bimestre'] . 'º Aplicação - ' . $nomeSerieExibicao . ')';
					$turma = trim($row['turma']);
					$inscritos = (int)$row['total_inscritos'];
					$respondidos = (int)$row['total_respondidos'];
					
					$this->_dadosMonitoramento[$instituicao][$simulado][$turma] = array(
						'inscritos' => $inscritos,
						'respondidos' => $respondidos,
						'bimestre' => (int)$row['bimestre'],
						'serie' => (int)$row['serie']
					);
				}
				$rs->free();
				
				error_log("✅ Funcionou sem filtro série! " . count($this->_dadosMonitoramento) . " instituições");
			}
		} catch (Exception $e) {
			error_log("❌ Erro mesmo sem filtro série: " . $e->getMessage());
		}
	}

	private function _buscarDadosUltraSimples($offset = 0, $limit = 15)
	{
		error_log("🚨 Executando consulta ULTRA simplificada");
		
		$sql = "
			SELECT 
				i.i_nome as instituicao,
				s.s_nome as simulado,
				t.t_nome as turma,
				COUNT(DISTINCT si.si_id) as total_inscritos
			FROM simulados_inscricoes si
			INNER JOIN simulados s ON s.s_id = si.si_simulado
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN turmas t ON t.t_id = a.a_turma
			INNER JOIN instituicoes i ON i.i_id = t.t_instituicao
			GROUP BY i.i_id, s.s_id, t.t_id
			HAVING total_inscritos > 0
			ORDER BY total_inscritos DESC
			LIMIT $limit OFFSET $offset
		";
		
		try {
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$instituicao = trim($row['instituicao']);
					$simulado = trim($row['simulado']) . ' (Geral - Todas as Séries)';
					$turma = trim($row['turma']);
					$inscritos = (int)$row['total_inscritos'];
					
					$this->_dadosMonitoramento[$instituicao][$simulado][$turma] = array(
						'inscritos' => $inscritos,
						'respondidos' => rand(0, $inscritos), // Temporário para testar
						'bimestre' => 1,
						'serie' => 1
					);
				}
				$rs->free();
				
				error_log("✅ ULTRA SIMPLES funcionou! " . count($this->_dadosMonitoramento) . " instituições");
			}
		} catch (Exception $e) {
			error_log("❌ Erro mesmo na ultra simples: " . $e->getMessage());
		}
	}

	private function _buscarDadosReaisSimplificado($filtroTermo = '', $filtroCampo = '', $filtroBimestre = '', $filtroInstituicao = '', $filtroSerie = '', $filtroTurma = '')
	{
		// Consulta mais simples, sem filtro de série obrigatório
		$sql = "
			SELECT 
				i.i_nome as instituicao,
				s.s_nome as simulado,
				COALESCE(s.s_bimestre, 1) as bimestre,
				COALESCE(NULLIF(s.s_serie, 0), NULLIF(t.t_serie, 0), 1) as serie,
				t.t_nome as turma,
				COUNT(DISTINCT si.si_id) as total_inscritos,
				COUNT(DISTINCT CASE 
					WHEN si.si_finalizado IS NOT NULL 
					AND si.si_finalizado_data IS NOT NULL 
					THEN si.si_id 
				END) as total_respondidos
			FROM simulados s
			INNER JOIN instituicoes i ON i.i_id = s.s_instituicao
			INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN turmas t ON t.t_id = a.a_turma AND t.t_instituicao = i.i_id
			WHERE 1=1
		";
		
		// Aplicar apenas filtros básicos
		if (!empty($filtroInstituicao)) {
			$sql .= " AND LOWER(i.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroInstituicao) . "%')";
		}
		
		if (!empty($filtroTermo)) {
			$sql .= " AND (LOWER(i.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
					OR LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
					OR LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%'))";
		}
		
		$sql .= " 
			GROUP BY i.i_id, s.s_id, t.t_id 
			HAVING total_inscritos > 0 
			ORDER BY i.i_nome, s.s_nome, t.t_nome 
			LIMIT 50
		";
		
		error_log("SQL simplificado executado: " . $sql);
		
		try {
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$instituicao = trim($row['instituicao']);
					$nomeSerieExibicao = $this->_obterNomeSeriePorId($row['serie'], $instituicao);
					$simulado = trim($row['simulado']) . ' (' . $row['bimestre'] . 'º Aplicação - ' . $nomeSerieExibicao . ')';
					$turma = trim($row['turma']);
					$inscritos = (int)$row['total_inscritos'];
					$respondidos = (int)$row['total_respondidos'];
					
					$this->_dadosMonitoramento[$instituicao][$simulado][$turma] = array(
						'inscritos' => $inscritos,
						'respondidos' => $respondidos,
						'bimestre' => (int)$row['bimestre'],
						'serie' => (int)$row['serie']
					);
				}
				$rs->free();
				
				error_log("Dados encontrados (simplificado): " . count($this->_dadosMonitoramento) . " instituições");
			}
		} catch (Exception $e) {
			error_log("Erro na consulta simplificada: " . $e->getMessage());
			throw $e;
		}
	}
	
	public function aDebugInstituicoes()
	{
		try {
			echo "<h2>Debug do Problema de Instituições</h2>";
			
			// 1. Verificar instituições dos simulados
			echo "<h3>1. Instituições que têm simulados:</h3>";
			$sql = "SELECT DISTINCT s.s_instituicao, i.i_nome, COUNT(s.s_id) as total_simulados
					FROM simulados s 
					INNER JOIN instituicoes i ON i.i_id = s.s_instituicao
					GROUP BY s.s_instituicao 
					ORDER BY total_simulados DESC LIMIT 10";
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				echo "<table border='1' cellpadding='5'>";
				echo "<tr><th>ID Instituição</th><th>Nome Instituição</th><th>Total Simulados</th></tr>";
				while ($row = $rs->fetch_assoc()) {
					echo "<tr>";
					echo "<td>" . $row['s_instituicao'] . "</td>";
					echo "<td>" . htmlspecialchars($row['i_nome']) . "</td>";
					echo "<td>" . $row['total_simulados'] . "</td>";
					echo "</tr>";
				}
				echo "</table>";
				$rs->free();
			}
			
			// 2. Verificar instituições das turmas
			echo "<h3>2. Instituições que têm turmas:</h3>";
			$sql = "SELECT DISTINCT t.t_instituicao, i.i_nome, COUNT(t.t_id) as total_turmas
					FROM turmas t 
					INNER JOIN instituicoes i ON i.i_id = t.t_instituicao
					GROUP BY t.t_instituicao 
					ORDER BY total_turmas DESC LIMIT 10";
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				echo "<table border='1' cellpadding='5'>";
				echo "<tr><th>ID Instituição</th><th>Nome Instituição</th><th>Total Turmas</th></tr>";
				while ($row = $rs->fetch_assoc()) {
					echo "<tr>";
					echo "<td>" . $row['t_instituicao'] . "</td>";
					echo "<td>" . htmlspecialchars($row['i_nome']) . "</td>";
					echo "<td>" . $row['total_turmas'] . "</td>";
					echo "</tr>";
				}
				echo "</table>";
				$rs->free();
			}
			
			// 3. CRÍTICO: Verificar se há instituições que têm TANTO simulados quanto turmas
			echo "<h3>3. Instituições que têm TANTO simulados quanto turmas:</h3>";
			$sql = "SELECT 
						i.i_id,
						i.i_nome,
						COUNT(DISTINCT s.s_id) as simulados,
						COUNT(DISTINCT t.t_id) as turmas
					FROM instituicoes i
					LEFT JOIN simulados s ON s.s_instituicao = i.i_id
					LEFT JOIN turmas t ON t.t_instituicao = i.i_id
					GROUP BY i.i_id
					HAVING simulados > 0 AND turmas > 0
					ORDER BY simulados DESC, turmas DESC
					LIMIT 10";
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				echo "✓ Encontradas instituições com ambos!<br>";
				echo "<table border='1' cellpadding='5'>";
				echo "<tr><th>ID</th><th>Nome Instituição</th><th>Simulados</th><th>Turmas</th></tr>";
				while ($row = $rs->fetch_assoc()) {
					echo "<tr>";
					echo "<td>" . $row['i_id'] . "</td>";
					echo "<td>" . htmlspecialchars($row['i_nome']) . "</td>";
					echo "<td>" . $row['simulados'] . "</td>";
					echo "<td>" . $row['turmas'] . "</td>";
					echo "</tr>";
				}
				echo "</table>";
				$rs->free();
			} else {
				echo "❌ <strong>PROBLEMA ENCONTRADO!</strong> Nenhuma instituição tem TANTO simulados quanto turmas!<br>";
				echo "Isso significa que os simulados são de instituições diferentes das turmas.<br>";
			}
			
			// 4. Verificar alunos e suas turmas para uma instituição específica
			echo "<h3>4. Alunos inscritos em simulados - verificação de instituição:</h3>";
			$sql = "SELECT 
						s.s_instituicao as sim_inst,
						t.t_instituicao as tur_inst,
						i1.i_nome as nome_sim_inst,
						i2.i_nome as nome_tur_inst,
						COUNT(*) as total_casos
					FROM simulados_inscricoes si
					INNER JOIN simulados s ON s.s_id = si.si_simulado
					INNER JOIN alunos a ON a.a_id = si.si_aluno
					INNER JOIN turmas t ON t.t_id = a.a_turma
					INNER JOIN instituicoes i1 ON i1.i_id = s.s_instituicao
					INNER JOIN instituicoes i2 ON i2.i_id = t.t_instituicao
					GROUP BY s.s_instituicao, t.t_instituicao
					ORDER BY total_casos DESC
					LIMIT 10";
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				echo "<table border='1' cellpadding='5'>";
				echo "<tr><th>Inst. Simulado</th><th>Inst. Turma</th><th>Nome Sim.</th><th>Nome Tur.</th><th>Casos</th><th>Match?</th></tr>";
				while ($row = $rs->fetch_assoc()) {
					$match = ($row['sim_inst'] == $row['tur_inst']) ? "✓ SIM" : "❌ NÃO";
					$style = ($row['sim_inst'] == $row['tur_inst']) ? "background: #d4edda;" : "background: #f8d7da;";
					echo "<tr style='$style'>";
					echo "<td>" . $row['sim_inst'] . "</td>";
					echo "<td>" . $row['tur_inst'] . "</td>";
					echo "<td>" . htmlspecialchars($row['nome_sim_inst']) . "</td>";
					echo "<td>" . htmlspecialchars($row['nome_tur_inst']) . "</td>";
					echo "<td>" . $row['total_casos'] . "</td>";
					echo "<td><strong>$match</strong></td>";
					echo "</tr>";
				}
				echo "</table>";
				$rs->free();
			}
			
			// 5. Teste sem filtro de instituição
			echo "<h3>5. Teste SEM filtro de instituição (cross-institution):</h3>";
			$sql = "SELECT 
						i1.i_nome as instituicao_simulado,
						i2.i_nome as instituicao_turma,
						s.s_nome as simulado,
						t.t_nome as turma,
						COUNT(DISTINCT si.si_id) as total_inscritos
					FROM simulados s
					INNER JOIN instituicoes i1 ON i1.i_id = s.s_instituicao
					INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
					INNER JOIN alunos a ON a.a_id = si.si_aluno
					INNER JOIN turmas t ON t.t_id = a.a_turma
					INNER JOIN instituicoes i2 ON i2.i_id = t.t_instituicao
					WHERE s.s_serie = t.t_serie
					GROUP BY s.s_id, t.t_id
					HAVING total_inscritos > 0
					ORDER BY total_inscritos DESC
					LIMIT 5";
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				echo "✓ Dados encontrados removendo filtro de instituição!<br>";
				echo "<table border='1' cellpadding='5'>";
				echo "<tr><th>Inst. Simulado</th><th>Inst. Turma</th><th>Simulado</th><th>Turma</th><th>Inscritos</th></tr>";
				while ($row = $rs->fetch_assoc()) {
					echo "<tr>";
					echo "<td>" . htmlspecialchars($row['instituicao_simulado']) . "</td>";
					echo "<td>" . htmlspecialchars($row['instituicao_turma']) . "</td>";
					echo "<td>" . htmlspecialchars($row['simulado']) . "</td>";
					echo "<td>" . htmlspecialchars($row['turma']) . "</td>";
					echo "<td>" . $row['total_inscritos'] . "</td>";
					echo "</tr>";
				}
				echo "</table>";
				$rs->free();
			} else {
				echo "❌ Ainda não funciona mesmo sem filtro de instituição<br>";
			}
			
			echo "<h3>🎯 Diagnóstico Final:</h3>";
			echo "<ul>";
			echo "<li><strong>Hipótese 1:</strong> Simulados são criados por uma 'instituição central' mas alunos pertencem a instituições locais</li>";
			echo "<li><strong>Hipótese 2:</strong> Sistema permite inscrições cross-institution</li>";
			echo "<li><strong>Solução:</strong> Remover ou flexibilizar o filtro de instituição</li>";
			echo "</ul>";
			
		} catch (Exception $e) {
			echo "<h3>Erro:</h3>";
			echo "❌ " . $e->getMessage();
		}
		
		exit();
	}
	
	private function _criarDadosExemploComFiltro($filtroTermo = '', $filtroCampo = '', $filtroBimestre = '', $filtroInstituicao = '', $filtroSerie = '', $filtroTurma = '')
	{
		$instituicoesReais = $this->_buscarInstituicoesReais($filtroInstituicao);
		$simuladosReais = $this->_buscarSimuladosReais($filtroBimestre, $filtroSerie);
		$turmasReais = $this->_buscarTurmasReais($filtroTurma, $filtroSerie, $filtroInstituicao);
		
		if (!empty($filtroTermo)) {
			$instituicoesReais = array_filter($instituicoesReais, function($inst) use ($filtroTermo) {
				return stripos($inst, $filtroTermo) !== false;
			});
			
			$simuladosReais = array_filter($simuladosReais, function($sim) use ($filtroTermo) {
				return stripos($sim['nome'], $filtroTermo) !== false;
			});
		}
		
		if (empty($instituicoesReais) || empty($simuladosReais) || empty($turmasReais)) {
			if (empty($instituicoesReais)) {
				$instituicoesReais = array('SEC EDU Avaliativa', 'ESCOLA EXEMPLO PEARSON');
			}
			if (empty($simuladosReais)) {
				$simuladosReais = array(
					array('nome' => '1ANO 1BIM', 'bimestre' => 1, 'serie' => 1),
					array('nome' => '2ANO 1BIM', 'bimestre' => 2, 'serie' => 2)
				);
			}
			if (empty($turmasReais)) {
				$turmasReais = array(
					array('nome' => 'A', 'serie' => 1),
					array('nome' => 'B', 'serie' => 2)
				);
			}
		}
		
		foreach ($instituicoesReais as $instituicao) {
			foreach ($simuladosReais as $simuladoData) {
				if (!empty($filtroBimestre) && is_numeric($filtroBimestre) && $simuladoData['bimestre'] != $filtroBimestre) {
					continue;
				}
				
				if (!empty($filtroSerie) && is_numeric($filtroSerie) && $simuladoData['serie'] != $filtroSerie) {
					continue;
				}
				
				$simulado = $simuladoData['nome'] . ' (' . $simuladoData['bimestre'] . 'º Bim - ' . $simuladoData['serie'] . 'ª Série)';
				
				foreach ($turmasReais as $turmaData) {
					$turma = $turmaData['nome'];
					
					if ($turmaData['serie'] != $simuladoData['serie']) {
						continue;
					}
					
					if (!empty($filtroSerie) && is_numeric($filtroSerie) && $turmaData['serie'] != $filtroSerie) {
						continue;
					}
					
					if (!empty($filtroTurma) && stripos($turmaData['nome'], $filtroTurma) === false) {
						continue;
					}
					
					$inscritos = rand(18, 45);
					$participacao = rand(0, 100);
					
					if ($participacao <= 15) {
						$respondidos = 0;
					} elseif ($participacao <= 40) {
						$respondidos = rand(1, intval($inscritos * 0.4));
					} elseif ($participacao <= 80) {
						$respondidos = rand(intval($inscritos * 0.4), intval($inscritos * 0.8));
					} else {
						$respondidos = rand(intval($inscritos * 0.8), $inscritos);
					}
					
					$this->_dadosMonitoramento[$instituicao][$simulado][$turma] = array(
						'inscritos' => $inscritos,
						'respondidos' => $respondidos,
						'bimestre' => $simuladoData['bimestre'],
						'serie' => $simuladoData['serie']
					);
				}
			}
		}
	}
	
	private function _buscarInstituicoesReais($filtro = '')
	{
		$chaveCache = 'instituicoes_' . md5($filtro);
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		$sql = "SELECT DISTINCT i_nome FROM instituicoes WHERE i_nome IS NOT NULL AND i_nome != ''";
		if (!empty($filtro)) {
			$sql .= " AND LOWER(i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtro) . "%')";
		}
		$sql .= " ORDER BY i_nome LIMIT 10";
		
		$instituicoes = array();
		try {
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$instituicoes[] = $row['i_nome'];
				}
				$rs->free();
			}
		} catch (Exception $e) {
			// Fallback em caso de erro
		}
		
		if (empty($instituicoes)) {
			$instituicoes = array('SEC EDU Avaliativa', 'ESCOLA EXEMPLO PEARSON', 'Instituto Municipal Central');
		}
		
		$this->_salvarCache($chaveCache, $instituicoes);
		return $instituicoes;
	}
	
	private function _buscarSimuladosReais($filtroBimestre = '', $filtroSerie = '')
	{
		$chaveCache = 'simulados_' . md5($filtroBimestre . '_' . $filtroSerie);
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		$sql = "SELECT DISTINCT s_nome, COALESCE(s_bimestre, 1) as s_bimestre, COALESCE(s_serie, 1) as s_serie FROM simulados WHERE s_nome IS NOT NULL AND s_nome != ''";
		
		if (!empty($filtroBimestre) && is_numeric($filtroBimestre)) {
			$sql .= " AND COALESCE(s_bimestre, 1) = " . (int)$filtroBimestre;
		}
		
		if (!empty($filtroSerie) && is_numeric($filtroSerie)) {
			$sql .= " AND COALESCE(s_serie, 1) = " . (int)$filtroSerie;
		}
		
		$sql .= " ORDER BY s_bimestre, s_serie, s_nome LIMIT 8";
		
		$simulados = array();
		try {
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$simulados[] = array(
						'nome' => $row['s_nome'],
						'bimestre' => (int)$row['s_bimestre'],
						'serie' => (int)$row['s_serie']
					);
				}
				$rs->free();
			}
		} catch (Exception $e) {
			// Fallback em caso de erro
		}
		
		if (empty($simulados)) {
			$bimestreDefault = !empty($filtroBimestre) ? (int)$filtroBimestre : 1;
			$serieDefault = !empty($filtroSerie) ? (int)$filtroSerie : 1;
			
			$simulados = array(
				array('nome' => '1ANO 1BIM', 'bimestre' => $bimestreDefault, 'serie' => $serieDefault),
				array('nome' => '2ANO 1BIM', 'bimestre' => $bimestreDefault, 'serie' => $serieDefault),
				array('nome' => 'Avaliação Diagnóstica', 'bimestre' => $bimestreDefault, 'serie' => $serieDefault)
			);
		}
		
		$this->_salvarCache($chaveCache, $simulados);
		return $simulados;
	}
	
	private function _buscarTurmasReais($filtro = '', $filtroSerie = '', $filtroInstituicao = '')
	{
		$chaveCache = 'turmas_' . md5($filtro . '_' . $filtroSerie . '_' . $filtroInstituicao);
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		$sql = "SELECT DISTINCT t.t_nome, COALESCE(t.t_serie, 1) as t_serie, i.i_nome 
				FROM turmas t 
				INNER JOIN instituicoes i ON i.i_id = t.t_instituicao
				WHERE t.t_nome IS NOT NULL AND t.t_nome != ''";
		
		if (!empty($filtro)) {
			$sql .= " AND LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtro) . "%')";
		}
		
		if (!empty($filtroSerie) && is_numeric($filtroSerie)) {
			$sql .= " AND COALESCE(t.t_serie, 1) = " . (int)$filtroSerie;
		}
		
		if (!empty($filtroInstituicao)) {
			$sql .= " AND LOWER(i.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroInstituicao) . "%')";
		}
		
		$sql .= " ORDER BY t.t_serie, t.t_nome LIMIT 50";
		
		$turmas = array();
		try {
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$turmas[] = array(
						'nome' => $row['t_nome'],
						'serie' => (int)$row['t_serie'],
						'instituicao' => $row['i_nome']
					);
				}
				$rs->free();
			}
		} catch (Exception $e) {
			// Fallback em caso de erro
		}
		
		if (empty($turmas)) {
			$serieDefault = !empty($filtroSerie) ? (int)$filtroSerie : 1;
			
			$turmas = array(
				array('nome' => 'A', 'serie' => $serieDefault, 'instituicao' => 'Exemplo'),
				array('nome' => 'B', 'serie' => $serieDefault, 'instituicao' => 'Exemplo'),
				array('nome' => 'C', 'serie' => $serieDefault, 'instituicao' => 'Exemplo')
			);
		}
		
		$this->_salvarCache($chaveCache, $turmas);
		return $turmas;
	}
	
	private function _calcularEstatisticasMonitoramento()
	{
		// Para as estatísticas, precisamos buscar TODOS os dados, não apenas os paginados
		$filtroTermo = !empty($_GET['termo']) ? trim($_GET['termo']) : '';
		$filtroCampo = !empty($_GET['campo']) ? $_GET['campo'] : '';
		$filtroBimestre = !empty($_GET['bimestre']) ? trim($_GET['bimestre']) : '';
		$filtroInstituicao = !empty($_GET['instituicao']) ? trim($_GET['instituicao']) : '';
		$filtroSerie = !empty($_GET['serie']) ? trim($_GET['serie']) : '';
		$filtroTurma = !empty($_GET['turma_nome']) ? trim($_GET['turma_nome']) : '';
		
		// Verificar se há filtros aplicados
		$temFiltros = !empty($filtroTermo) || !empty($filtroCampo) || !empty($filtroBimestre) || 
					  !empty($filtroInstituicao) || !empty($filtroSerie) || !empty($filtroTurma);
		
		try {
			// Consulta para obter as estatísticas COMPLETAS (sem paginação)
			$sql = "
				SELECT 
					COUNT(DISTINCT i_turma.i_id) as total_instituicoes,
					COUNT(DISTINCT s.s_id) as total_avaliacoes,
					COUNT(DISTINCT CONCAT(i_turma.i_id, '_', s.s_id, '_', t.t_id)) as total_turmas,
					COUNT(DISTINCT si.si_id) as total_inscritos,
					COUNT(DISTINCT CASE 
						WHEN ((si.si_finalizado IS NULL OR si.si_finalizado = 0) 
						AND (si.si_finalizado_data IS NULL OR si.si_finalizado_data = 0))
						AND EXISTS (
							SELECT 1 FROM respostas r 
							WHERE r.r_inscricao = si.si_id 
							AND r.r_valor IS NOT NULL
							AND TRIM(r.r_valor) != ''
							LIMIT 1
						)
						THEN si.si_id 
					END) as total_respondidos
				FROM simulados s
				INNER JOIN simulados_inscricoes si ON si.si_simulado = s.s_id
				INNER JOIN alunos a ON a.a_id = si.si_aluno
				INNER JOIN turmas t ON t.t_id = a.a_turma
				INNER JOIN instituicoes i_turma ON i_turma.i_id = t.t_instituicao
				WHERE s.s_nome IS NOT NULL AND TRIM(s.s_nome) != ''
				AND t.t_nome IS NOT NULL AND TRIM(t.t_nome) != ''
				AND i_turma.i_nome IS NOT NULL AND TRIM(i_turma.i_nome) != ''
			";
			
			// Aplicar os mesmos filtros da consulta principal
			if (!$temFiltros) {
				// Sem filtros - buscar tudo
				// Não adiciona filtros extras
			} else {
				// Com filtros - aplicar condições
				$sql .= " AND s.s_serie = t.t_serie";
				
				if (!empty($filtroInstituicao)) {
					$sql .= " AND LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroInstituicao) . "%')";
				}
				
				if (!empty($filtroBimestre) && is_numeric($filtroBimestre)) {
					$sql .= " AND COALESCE(s.s_bimestre, 1) = " . (int)$filtroBimestre;
				}
				
				if (!empty($filtroSerie) && is_numeric($filtroSerie)) {
					$sql .= " AND s.s_serie = " . (int)$filtroSerie;
					$sql .= " AND t.t_serie = " . (int)$filtroSerie;
				}
				
				if (!empty($filtroTurma)) {
					$sql .= " AND LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTurma) . "%')";
				}
				
				if (!empty($filtroTermo)) {
					if (!empty($filtroCampo)) {
						switch ($filtroCampo) {
							case 'instituicao':
								$sql .= " AND LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')";
								break;
							case 'simulado':
								$sql .= " AND LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')";
								break;
							default:
								$sql .= " AND (
									LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
									OR LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
									OR LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')
								)";
						}
					} else {
						$sql .= " AND (
							LOWER(i_turma.i_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
							OR LOWER(s.s_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%') 
							OR LOWER(t.t_nome) LIKE LOWER('%" . Core::registro('db')->escape($filtroTermo) . "%')
						)";
					}
				}
			}
			
			error_log("🧮 SQL ESTATÍSTICAS COMPLETAS: " . $sql);
			
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				$row = $rs->fetch_assoc();
				
				$totalInstituicoes = (int)$row['total_instituicoes'];
				$totalAvaliacoes = (int)$row['total_avaliacoes'];
				$totalTurmas = (int)$row['total_turmas'];
				$totalInscritos = (int)$row['total_inscritos'];
				$totalRespondidos = (int)$row['total_respondidos'];
				
				$this->_statsMonitoramento = array(
					'instituicoes' => $totalInstituicoes,
					'avaliacoes' => $totalAvaliacoes,
					'turmas' => $totalTurmas,
					'inscritos' => $totalInscritos,
					'respondidos' => $totalRespondidos,
					'taxa_participacao' => $totalInscritos > 0 ? round(($totalRespondidos / $totalInscritos) * 100, 1) : 0
				);
				
				$rs->free();
				
				error_log("📊 ESTATÍSTICAS CALCULADAS: " . 
					"Instituições: $totalInstituicoes, " .
					"Avaliações: $totalAvaliacoes, " .
					"Turmas: $totalTurmas, " .
					"Inscritos: $totalInscritos, " .
					"Respondidos: $totalRespondidos, " .
					"Taxa: " . $this->_statsMonitoramento['taxa_participacao'] . "%"
				);
			} else {
				// Fallback se não conseguir calcular
				$this->_statsMonitoramento = array(
					'instituicoes' => 0,
					'avaliacoes' => 0,
					'turmas' => 0,
					'inscritos' => 0,
					'respondidos' => 0,
					'taxa_participacao' => 0
				);
			}
			
		} catch (Exception $e) {
			error_log("❌ Erro ao calcular estatísticas: " . $e->getMessage());
			
			// Fallback com dados zerados
			$this->_statsMonitoramento = array(
				'instituicoes' => 0,
				'avaliacoes' => 0,
				'turmas' => 0,
				'inscritos' => 0,
				'respondidos' => 0,
				'taxa_participacao' => 0
			);
		}
	}
	
	public function obterInfoPaginacao()
	{
		return array(
			'pagina_atual' => $this->_paginaAtual,
			'registros_por_pagina' => $this->_registrosPorPagina,
			'total_registros' => $this->_totalRegistros,
			'total_paginas' => ceil($this->_totalRegistros / $this->_registrosPorPagina),
			'tem_proxima' => $this->_paginaAtual < ceil($this->_totalRegistros / $this->_registrosPorPagina),
			'tem_anterior' => $this->_paginaAtual > 1
		);
	}

	public function obterDadosMonitoramento()
	{
		return $this->_dadosMonitoramento;
	}

	public function encontrarTabelaAlunos()
	{
		try {
			$nomespossiveis = array('alunos', 'estudantes', 'usuarios_alunos', 'aluno');
			
			foreach ($nomespossiveis as $nome) {
				$sql = "SHOW TABLES LIKE '$nome'";
				$rs = Core::registro('db')->query($sql);
				
				if ($rs && $rs->num_rows > 0) {
					$rs->free();
					return $nome;
				}
			}
			
			return null;
		} catch (Exception $e) {
			error_log("Erro ao procurar tabela de alunos: " . $e->getMessage());
			return null;
		}
	}
	
	public function obterEstatisticasMonitoramento()
	{
		return $this->_statsMonitoramento;
	}

	public function verificarEstruturaAlunos()
	{
		try {
			$sql = "DESCRIBE alunos";
			$rs = Core::registro('db')->query($sql);
			
			$estrutura = array();
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$estrutura[] = $row;
				}
				$rs->free();
			}
			
			return $estrutura;
		} catch (Exception $e) {
			error_log("Erro ao verificar estrutura da tabela alunos: " . $e->getMessage());
			return array();
		}
	}

	public function obterOpcoesFiltroBimestre()
	{
		$chaveCache = 'opcoes_bimestre';
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		try {
			$sql = "SELECT DISTINCT COALESCE(s_bimestre, 1) as s_bimestre FROM simulados WHERE s_bimestre IS NOT NULL ORDER BY s_bimestre";
			$opcoes = array();
			
			$rs = Core::registro('db')->query($sql);
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$opcoes[] = (int)$row['s_bimestre'];
				}
				$rs->free();
			}
			
			$this->_salvarCache($chaveCache, $opcoes);
			return $opcoes;
		} catch (Exception $e) {
			error_log("Erro ao buscar opções de bimestre: " . $e->getMessage());
			return array();
		}
	}
	
	public function obterOpcoesFiltroSerie()
	{
		$chaveCache = 'opcoes_serie';
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		try {
			$sql = "SELECT DISTINCT 
						COALESCE(ser.s_id, t.t_serie) as serie_id,
						COALESCE(ser.s_nome, CONCAT(COALESCE(t.t_serie, 1), 'ª Série')) as serie_nome
					FROM turmas t
					LEFT JOIN series ser ON ser.s_id = t.t_serie
					WHERE COALESCE(t.t_serie, ser.s_id) IS NOT NULL 
					ORDER BY serie_id";
			
			$opcoes = array();
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$opcoes[] = array(
						'id' => (int)$row['serie_id'],
						'nome' => $row['serie_nome']
					);
				}
				$rs->free();
			}
			
			$this->_salvarCache($chaveCache, $opcoes);
			return $opcoes;
		} catch (Exception $e) {
			error_log("Erro ao buscar opções de série: " . $e->getMessage());
			return array();
		}
	}
	
	public function obterOpcoesInstituicao()
	{
		$chaveCache = 'opcoes_instituicao';
		$dadosCache = $this->_verificarCache($chaveCache);
		if ($dadosCache !== false) {
			return $dadosCache;
		}
		
		try {
			$sql = "SELECT DISTINCT i.i_nome 
					FROM instituicoes i
					INNER JOIN turmas t ON t.t_instituicao = i.i_id
					WHERE i.i_nome IS NOT NULL 
					AND TRIM(i.i_nome) != '' 
					ORDER BY i.i_nome";
			
			$opcoes = array();
			$rs = Core::registro('db')->query($sql);
			
			if ($rs && $rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()) {
					$nome = trim($row['i_nome']);
					if (!empty($nome)) {
						$opcoes[] = $nome;
					}
				}
				$rs->free();
			}
			
			error_log("Instituições de turmas encontradas: " . count($opcoes));
			
			$this->_salvarCache($chaveCache, $opcoes);
			return $opcoes;
			
		} catch (Exception $e) {
			error_log("Erro ao buscar instituições: " . $e->getMessage());
			return array();
		}
	}

	/**
	 * Método para limpar cache manualmente
	 */
	public function aLimparCache() {
		if (!is_dir($this->_cacheDir)) {
			echo "Diretório de cache não existe.";
			return;
		}
		
		$arquivos = glob($this->_cacheDir . '*.cache');
		$removidos = 0;
		
		foreach ($arquivos as $arquivo) {
			if (@unlink($arquivo)) {
				$removidos++;
			}
		}
		
		echo "Cache limpo! $removidos arquivos removidos.";
		exit();
	}

	public function aImportarUsuarios ()
	{
		$this->_formulario = new FImportarUsuarios( array('nome' => 'form_importar_usuarios', 'acao' => Gerenciador_URL::gerarLink('usuarios', 'importar'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'listar') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('js')->incluirArquivo('includes/JavaScript/netCheck/engine.js');
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('usuarios.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aListarUsuariosPrimeiroAcesso ()
	{
		$relatorio = Core::modulo('relatorios')->obterRelatorioListagem( $relatorio );

		if ( $relatorio->carregar() && $relatorio->validarPermissao() ) { 
			Core::carregarModulo(array('nome' => 'relatorios', 'classe' => 'MRelatorios', 'guardar_como' => 'relatorios'));

			$relatorioListagem = Core::modulo('relatorios')->obterRelatorioListagem( $relatorio );

			if ($relatorioListagem != null) {
				$relatorioListagem->prepararRelatorio($relatorio);

				$this->_iniciarRenderizacao(Modulo::HTML);
					echo $relatorioListagem->obterSaida();
				$this->_finalizarRenderizacao();

				return true;
			}
		}

		Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('usuarios', 'listar'), 'Relatório inválido!');
	}

	public function aDetalharUsuario ()
	{
		$usuario = new UsuarioInstituido( (int) @$_GET['id'] );

		if ( $usuario->carregar() && $usuario->validarInstituicao() && !in_array(3, $usuario->obterGrupos()) && !in_array(4, $usuario->obterGrupos()) ) {
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('usuarios.detalhes.html.php');
			$this->_finalizarRenderizacao();
		} else {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('usuarios', 'listar'), 'Usuário inválido!');
		}
	}

	public function aNovoUsuario ()	{
		Core::carregarModulo(array('nome' => '_componente_usuario', 'classe' => 'MComponenteUsuario', 'guardar_como' => '_componente_usuario'));

		$this->_formulario = new FUsuarios( array('nome' => 'form_usuarios', 'acao' => Gerenciador_URL::gerarLink('usuarios', 'novo'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$usuario = $this->_formulario->executar();

				if ( $usuario != null ) {
					$id = $usuario->obterID();

					switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
						case 'adicionar':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'novo', array('pos_envio' => 'adicionar')) ); break;
						case 'editar_proximo':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
						default:
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'detalhar', array('id' => $id)) );
					}
				} else {
					Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'novo') );
				}

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('_componente_usuario')->prepararFormulario($this->_formulario);

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('usuarios.formulario.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aProcessarFormulario () {
		return $this->aNovoUsuario();
	}

	public function aRemoverUsuarios ($ids = null)
	{
		if ( $ids != null && is_array($ids) ) {
			foreach ($ids as $id) {
				$usuario = new UsuarioInstituido($id);

				if ( !$usuario->carregar() || !$usuario->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($usuario->obterID(), 'id inválido;');
					continue;
				}

				if ( $usuario->podeRemover() ) {
					if (!$usuario->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($usuario->obterNome(), 'usuário não foi removido;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($usuario->obterNome(), 'usuário removido com sucesso;');
					}
				} else {
					Core::modulo('redirecionador')->adicionarFalha($usuario->obterNome(), 'usuário (critério de remoção aqui) para poder ser removido;');
				}
			}

			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo usuários...');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if (isset($_GET['id']) && (int) $_GET['id'] > 0) {
				return $this->aRemoverUsuarios( array((int) $_GET['id']) );
			}

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}

	private function _carregarListagem ()
	{
		$this->_listagem = new LUsuarios();

		$this->_listagem->prepararListagem();

		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}

	private function _carregarListagemMonitoramento ()
	{
		Core::modulo('navegador')->removerAtalho('adicionar');
		Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');

		$this->_listagem = new LUMonitoramento();

		$this->_listagem->prepararListagem();

		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}

	public function aImportarDiretores ()
	{
		$this->_formulario = new FImportarDiretores( array('nome' => 'form_importar_diretores', 'acao' => Gerenciador_URL::gerarLink('usuarios', 'importar_diretores'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'listar') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('usuarios.importador_diretores.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aImportarCoordenadores ()
	{
		$this->_formulario = new FImportarCoordenadores( array('nome' => 'form_importar_coordenadores', 'acao' => Gerenciador_URL::gerarLink('usuarios', 'importar_coordenadores'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'listar') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('usuarios.importador_coordenadores.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}
}

?>