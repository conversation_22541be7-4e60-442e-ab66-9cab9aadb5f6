<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('ord_usuarios')->obterSaida(); ?>

<?= Core::modulo('procurar_usuarios')->obterSaida(); ?>

<?
if ( $this->_paginacao->paginador->precisaDePaginacao() ) {
?>
	<div class="vd_BlocoBotoes" align="right"><?= $this->_paginacao->obterSaida(); ?></div>
<?
}
?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>  
<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
	  <td width="1%" class="lp_ColHeader"><?= $this->_formulario->obterHTML('selecionar_todos', Formulario::HTML_CAMPO, true); ?></td>
	  <td width="25%" class="lp_ColHeader"><?= $this->obterBotaoAlterador('u_nome'); ?></td>
	  <td width="25%" class="lp_ColHeader"><?= $this->obterBotaoAlterador('emails.e_endereco'); ?></td>
	  <td width="15%" class="lp_ColHeader"><?= $this->obterBotaoAlterador('u_grupos'); ?></td>
	  <td class="lp_ColHeader"><?= $this->obterBotaoAlterador('instituicoes.i_nome'); ?></td>
      <td class="lp_ColHeader">Última entrada</td>
      <td class="lp_ColHeader">Qtd de Entradas</td>
	</tr>
<?
if ( count($this->_dados) ) {
	$camposIDs = $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true);
	$i = 0;
	foreach ( $this->_dados as &$d ) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
      <td align="center" ><?= $camposIDs[$i++][0]; ?></td>
	  <td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('usuarios', 'detalhar', array('id' => $d->obterID())); ?>"><?= $d->obterNome(); ?></a></td>
	  <td><a title="Enviar e-mail" href="mailto: <?= $d->obterEmail()->obterEndereco(); ?>"><?= $d->obterEmail()->obterEndereco(); ?></a></td>
	  <td><?= $d->obterNomeGrupo(); ?></td>
	  <td><a title="Visualizar detalhes da instituição" href="<?= Gerenciador_URL::gerarLink('instituicoes', 'detalhar', array('id' => $d->obterInstituicao()->obterID())); ?>"><?= $d->obterInstituicao()->obterNome(); ?></a></td>
        <td><?= $d->obterLogUltimaEntradaSaida(); ?></td>
        <td><?= $d->obterLogTotalEntrada(); ?></td>
    </tr>
<?
	}
} else {
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('Nenhum usuário encontrado!');
?>
	<tr>
		<td height="200" colspan="5" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
<?
}
?>
</table>
  
  <table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_BlocoBotoes">
    <tr>
      <td><?= $this->_formulario->obterHTML('remover', Formulario::HTML_CAMPO, true); ?> 
	  <?= $this->_formulario->obterHTML('editar_em_ordem', Formulario::HTML_CAMPO, true); ?> 
	  <?= $this->_formulario->obterHTML('primeiro_acesso', Formulario::HTML_CAMPO, true); ?></td>
<?
	if ( $this->_paginacao->paginador->precisaDePaginacao() ) {
?>
      <td align="right"><?= $this->_paginacao->obterSaida(); ?></td>
<?
	}
?>
    </tr>
</table>
<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>