<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio"><strong><?= $usuario->obterNome(); ?></strong></div>

<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('usuarios', 'detalhar', array('id' => $usuario->obterID())); ?>">Informações do usu&aacute;rio </a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('usuarios', 'detalhar', array('id' => $usuario->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_MAIS_INFO)); ?>">Mais informa&ccedil;&otilde;es</a></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<?
if ($this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO)) {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
	Core::carregarModulo(array('nome' => '_componente_usuario', 'classe' => 'MComponenteUsuario', 'guardar_como' => '_componente_usuario'));
	Core::modulo('_componente_usuario')->mostrarDetalhes($usuario, Gerenciador_URL::gerarLink('usuarios', 'detalhar', array('id' => $usuario->obterID())), self::VISAO_MAIS_INFO);
	
	echo Core::modulo('_componente_usuario')->obterSaida();
?>

	</td>
  </tr>
</table>
<?
} else {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

	<div class="vd_BlocoBotoes">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
	  <tr>
		<td><input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('usuarios', 'editar', array('id' => $usuario->obterID())); ?>'" value="Editar" class="botao editar"></td>
		<td align="right"><input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('usuarios', 'remover', array('id' => $usuario->obterID())); ?>'; }" value="Remover" class="botao remover"></td>
	  </tr>
	</table>
	</div>

<?
Core::carregarModulo(array('nome' => '_componente_usuario', 'classe' => 'MComponenteUsuario', 'guardar_como' => '_componente_usuario'));
Core::modulo('_componente_usuario')->mostrarDetalhes($usuario, Gerenciador_URL::gerarLink('usuarios', 'editar', array('id' => $usuario->obterID())));

echo Core::modulo('_componente_usuario')->obterSaida();
?>
	
	</td>
  </tr>
</table>
<?
}
?>