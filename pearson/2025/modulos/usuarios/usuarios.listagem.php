<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');

class LUsuarios extends Listagem
{
	
	protected $_procuraSQL = null;
	
	public function prepararListagem ()
	{
		$this->_prepararBusca();
	
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormularioAcoes();


		$this->_iniciarRenderizacao(Modulo::HTML);
			include('usuarios.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_usuarios')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_usuarios')->termoProcurado != null && Core::modulo('procurar_usuarios')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_usuarios')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_usuarios')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}
		
			if ( Core::modulo('procurar_usuarios')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(u_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_usuarios')->letraSelecionada);
			}
		}
		
		// configura pra mostrar somente da instituição selecionada
		if ( Core::registro('instituicao')->obterID() != null ) {
			$this->_procuraSQL[] = 'u_instituicao = '. Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID());
		}
		
		if ( is_array($this->_procuraSQL) ) {
			$this->_procuraSQL = ' AND ' . implode(' AND ', $this->_procuraSQL);
		}
	}
	
	// @todo: tirar a exclusão de grupos hardcoded
	protected function _preObterDados ()
	{	
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT COUNT(0) AS total FROM usuarios 
			  LEFT JOIN emails ON emails.e_id = usuarios.u_email 
			  WHERE ((u_grupos != \'3\' AND u_grupos != \'4\') OR u_grupos IS NULL) %s', $this->_procuraSQL) );
			  
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}
	
	protected function _obterDados ()
	{
		$rs = Core::registro('db')->query( sprintf(
			'SELECT * FROM usuarios 
			LEFT JOIN emails ON emails.e_id = usuarios.u_email 
			LEFT JOIN instituicoes ON usuarios.u_instituicao = instituicoes.i_id 
			WHERE ((u_grupos != \'3\' AND u_grupos != \'4\') OR u_grupos IS NULL) %s 
			ORDER BY %s %s 
			LIMIT %s'
			, $this->_procuraSQL, $this->_ordenacao->ordenarPor, $this->_ordenacao->tipoOrdem, $this->_paginacao->paginador->obterLimitesSQL() ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$usuario = new UsuarioInstituido($row['u_id']);
				$usuario->fixarLogin($row['u_login']);
				$usuario->fixarNome($row['u_nome']);
				$usuario->fixarGrupos( explode(';', $row['u_grupos']) );

				$usuario->fixarEmail( new Email($row['e_id']) );				
				$usuario->obterEmail()->fixarEndereco($row['e_endereco']);
				$usuario->obterEmail()->fixarEstadoDaConfirmacao( (bool) $row['e_confirmado'] );
				$usuario->obterEmail()->fixarChaveConfirmacao( ( !empty($row['e_chave_confirmacao']) ? $row['e_chave_confirmacao'] : null ) );
				$usuario->obterEmail()->fixarDataUltimoEnvio($row['e_data_ultimo_envio']);
				
				$usuario->fixarEndereco( new Endereco($row['u_endereco']) );

				$inst = new Instituicao($row['u_instituicao']);
				if ( !empty($row['u_instituicao']) ) {
					$inst->fixarNome($row['i_nome']);
				}
				$usuario->fixarInstituicao( $inst );

				$this->_dados[] = $usuario;
			}
		}
		$rs->free();
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_usuarios'));

		Core::modulo('procurar_usuarios')->prepararProcura('form_procurar_usuarios', 'caixa_procurar_usuarios');
		Core::modulo('procurar_usuarios')->configurarTermo();
		Core::modulo('procurar_usuarios')->configurarOndeProcurar( array(   'u_nome' => 'Nome do usuário',
																			'emails.e_endereco' => 'Login (E-mail) do usuário' ) );
		Core::modulo('procurar_usuarios')->configurarAlfabeto();
		
		Core::modulo('procurar_usuarios')->carregarProcura();
		
		Core::modulo('procurar_usuarios')->prepararAtalho();
	}
	
	protected function _prepararOrdenacao ()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_usuarios'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_usuarios');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('u_nome' => 'Nome', 'emails.e_endereco' => 'Login (E-mail)', 'u_grupos' => 'Grupo', 'instituicoes.i_nome' => 'Instituição'), 'u_nome' );
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();
		
		Core::modulo('ord_usuarios')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_usuarios')->carregarOrdenacao();
		Core::modulo('ord_usuarios')->prepararAtalho(null, 'caixa_ord_usuarios');
		
		if ( Core::modulo('procurar_usuarios')->procuraSolicitada() ) {
			Core::modulo('ord_usuarios')->anexarHTML( Core::modulo('procurar_usuarios')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao ()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_usuarios'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormularioAcoes ()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_usuarios','acao' => Gerenciador_URL::gerarLink('usuarios', 'listar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$v) {
			$ids[] = $v->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Remover', 'Tem certeza que deseja REMOVER todos os itens selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		$this->_formulario->adicionarBotao('primeiro_acesso', 'Obter lista de primeiro acesso', null, '');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();

				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {
					if (is_array($ids)) {
						Core::modulo('solicitado')->aRemoverUsuarios($ids);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('usuarios', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'primeiro_acesso' ) {					
					if (is_array($ids)) {
						$dados = array('ids_primeiro_acesso' => chunk_split(base64_encode(serialize( $ids ))));
			
						Gerenciador_URL::habilitarAtualiacaoReferencia(true);
						Gerenciador_URL::autoFixarIndexCompleto(true);
						Redirecionador::finalizarAoRedirecionar(true);
						Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'listarprimeiroacesso'), $dados );
					}
				}
			}
			catch (Formulario_Exception $e)	{ }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('usuários', $totais);
	}
		
}

?>