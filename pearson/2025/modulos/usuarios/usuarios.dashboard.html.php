<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard de Monitoramento</title>

<style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* CSS mais específico para garantir que seja aplicado */
        div.dashboard-container {
            max-width: 1400px !important;
            margin: 20px auto !important;
            border-radius: 20px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
            overflow: hidden !important;
        }

        .dashboard-container .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            padding: 35px !important;
            text-align: center !important;
        }

        .dashboard-container .dashboard-title {
            font-size: 2.8rem !important;
            font-weight: 700 !important;
            margin-bottom: 10px !important;
        }

        .dashboard-container .dashboard-subtitle {
            opacity: 0.9 !important;
            font-size: 1.1rem !important;
            font-weight: 300 !important;
        }

        .dashboard-container .filters-section {
            background: #f8f9fa !important;
            padding: 30px !important;
            border-bottom: 1px solid #e9ecef !important;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            background: white;
            padding: 22px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18), 0 2px 8px rgba(0, 0, 0, 0.12);
            transition: transform 0.2s ease;
        }

        .filter-group:hover {
            transform: translateY(-2px);
        }

        .filter-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: block;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-input, .filter-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .filter-input:focus, .filter-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .active-filters {
            margin-top: 15px;
            padding: 15px;
            background:rgb(197, 230, 253);
            border-radius: 8px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-tag {
            background: #1976d2;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .stats-overview {
            padding: 35px;
            background: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 28px;
            border-radius: 15px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18), 0 2px 8px rgba(44, 62, 80, 0.12);
            text-align: center;
            transition: transform 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .data-table-container {
            background: white;
            margin: 0 35px 35px;
            border-radius: 15px;
            box-shadow: 0 16px 50px rgba(0, 0, 0, 0.18), 0 4px 16px rgba(44, 62, 80, 0.18);
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #ccc;
            vertical-align: top;
        }

        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #ccc;
            vertical-align: top;
        }

        .data-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-indicators {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 14px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
        }

        .status-item:hover {
            transform: translateX(3px);
        }

        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            flex-shrink: 0;
        }

        .status-success {
            background: #28a745;
        }

        .status-warning {
            background: #ffc107;
            color: #000;
        }

        .status-danger {
            background: #dc3545;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .progress-warning {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }

        .progress-danger {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-data-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 10px;
        }

        .pagination-btn {
            padding: 10px 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .pagination-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .pagination-btn.active {
            background: #667eea;
            color: white;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-info {
            margin: 0 15px;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            div.dashboard-container {
                margin: 10px !important;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .data-table th,
            .data-table td {
                padding: 12px 8px;
                font-size: 0.9rem;
            }
            
            .dashboard-container .dashboard-title {
                font-size: 2rem !important;
            }

            .filter-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                text-align: center;
            }
            
            .stats-overview,
            .filters-section {
                padding: 20px;
            }
            
            .data-table-container {
                margin: 0 20px 20px;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }

            .pagination-btn {
                width: 100%;
                text-align: center;
            }
        }
</style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Dashboard de Monitoramento</h1>
            <p class="dashboard-subtitle">Acompanhamento de lançamento on-line</p>
        </div>

        <div class="filters-section">
            <form method="GET" id="filterForm" action="<?= Gerenciador_URL::gerarLink('usuarios', 'monitorar'); ?>">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label" for="termo">Termo de Busca</label>
                        <input type="text" 
                               id="termo" 
                               name="termo" 
                               class="filter-input" 
                               placeholder="Digite para buscar..." 
                               value="<?= htmlspecialchars($_GET['termo'] ?? ''); ?>">
                    </div>
                    
                    <div class="filter-group" hidden="true">
                        <label class="filter-label" for="campo">Buscar em</label>
                        <select id="campo" name="campo" class="filter-select">
                            <option value="">Todos os campos</option>
                            <option value="instituicao" <?= (($_GET['campo'] ?? '') == 'instituicao') ? 'selected' : ''; ?>>Instituição</option>
                            <option value="simulado" <?= (($_GET['campo'] ?? '') == 'simulado') ? 'selected' : ''; ?>>Avaliação</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="instituicao">Instituição</label>
                        <select id="instituicao" name="instituicao" class="filter-select">
                            <option value="">Todas as instituições</option>
                            <?php
                            $instituicoes = $this->obterOpcoesInstituicao();
                            foreach ($instituicoes as $inst) {
                                $selected = (($_GET['instituicao'] ?? '') == $inst) ? 'selected' : '';
                                echo '<option value="' . htmlspecialchars($inst) . '" ' . $selected . '>' . htmlspecialchars($inst) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="bimestre">Aplicação</label>
                        <select id="bimestre" name="bimestre" class="filter-select">
                            <option value="">Todos as aplicações</option>
                            <?php
                            $bimestres = $this->obterOpcoesFiltroBimestre();
                            foreach ($bimestres as $bim) {
                                $selected = (($_GET['bimestre'] ?? '') == $bim) ? 'selected' : '';
                                echo '<option value="' . $bim . '" ' . $selected . '>' . $bim . 'º Aplicação</option>';
                            }
                            ?>
                        </select>
                    </div>
                    

                    <div class="filter-group">
                        <label class="filter-label" for="serie">Série</label>
                        <select id="serie" name="serie" class="filter-select">
                            <option value="">Todas as séries</option>
                            <?php
                            $series = $this->obterOpcoesFiltroSerie();
                            foreach ($series as $serieData) {
                                if (is_array($serieData)) {
                                    $serieId = $serieData['id'];
                                    $serieNome = $serieData['nome'];
                                } else {
                                    $serieId = $serieData;
                                    $serieNome = $serieData . 'ª Série';
                                }
                                
                                $selected = (($_GET['serie'] ?? '') == $serieId) ? 'selected' : '';
                                echo '<option value="' . $serieId . '" ' . $selected . '>' . htmlspecialchars($serieNome) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label" for="turma_nome">Nome da Turma</label>
                        <input type="text" 
                               id="turma_nome" 
                               name="turma_nome" 
                               class="filter-input" 
                               placeholder="Ex: A, B, Turma 1..." 
                               value="<?= htmlspecialchars($_GET['turma_nome'] ?? ''); ?>">
                    </div>
                </div>
                
                <div class="filter-buttons">
                    <button type="button" id="clearFilters" class="btn btn-secondary">🗑️ Limpar Filtros</button>
                    <button type="submit" class="btn btn-primary">🔍 Aplicar Filtros</button>
                </div>
                
                <!-- Indicador de filtros ativos -->
                <?php
                    $filtrosAtivos = array();
                    if (!empty($_GET['termo'])) $filtrosAtivos[] = 'Termo: ' . $_GET['termo'];
                    if (!empty($_GET['instituicao'])) $filtrosAtivos[] = 'Instituição: ' . $_GET['instituicao'];
                    if (!empty($_GET['bimestre'])) $filtrosAtivos[] = 'Aplicação: ' . $_GET['bimestre'] . 'º';
                    
                    if (!empty($_GET['serie'])) {
                        $serieNomeExibicao = $_GET['serie'] . 'ª'; // Fallback padrão
                        
                        // Buscar o nome real da série
                        $series = $this->obterOpcoesFiltroSerie();
                        foreach ($series as $serieData) {
                            if (is_array($serieData)) {
                                if ($serieData['id'] == $_GET['serie']) {
                                    $serieNomeExibicao = $serieData['nome'];
                                    break;
                                }
                            } else {
                                if ($serieData == $_GET['serie']) {
                                    $serieNomeExibicao = $serieData . 'ª Série';
                                    break;
                                }
                            }
                        }
                        
                        $filtrosAtivos[] = 'Série: ' . $serieNomeExibicao;
                    }

                    if (!empty($_GET['turma_nome'])) $filtrosAtivos[] = 'Turma: ' . $_GET['turma_nome'];
                    
                    if (!empty($filtrosAtivos)) {
                        echo '<div class="active-filters">';
                        echo '<strong>🔍 Filtros ativos:</strong>';
                        foreach ($filtrosAtivos as $filtro) {
                            echo '<span class="filter-tag">' . htmlspecialchars($filtro) . '</span>';
                        }
                        echo '</div>';
                    }
                ?>
            </form>
        </div>

        <div class="loading" id="loadingIndicator">
            <p>⏳ Carregando dados...</p>
        </div>

        <div class="stats-overview">
            <?php $stats = $this->obterEstatisticasMonitoramento(); ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['instituicoes']); ?></div>
                    <div class="stat-label">Instituições</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['avaliacoes']); ?></div>
                    <div class="stat-label">Avaliações</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['turmas']); ?></div>
                    <div class="stat-label">Turmas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['inscritos']); ?></div>
                    <div class="stat-label">Alunos Inscritos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['respondidos']); ?></div>
                    <div class="stat-label">Respostas Recebidas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['taxa_participacao'], 1); ?>%</div>
                    <div class="stat-label">Taxa de Participação</div>
                </div>
            </div>
        </div>

        <div class="data-table-container">
            <div class="table-header">
                📊 Detalhamento por Instituição e Avaliação
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Instituição</th>
                        <th>Avaliação</th>
                        <th>Turmas - Status de Participação</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $dados = $this->obterDadosMonitoramento();
                    
                    if (!empty($dados)) {
                        foreach ($dados as $escola => $avaliacoes) {
                            foreach ($avaliacoes as $avaliacao => $turmas) {
                                ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($escola); ?></strong></td>
                                    <td><strong><?= htmlspecialchars($avaliacao); ?></strong></td>
                                    <td>
                                        <div class="status-indicators">
                                            <?php
                                            foreach ($turmas as $turma => $dadosTurma) {
                                                $inscritos = (int)$dadosTurma['inscritos'];
                                                $respondidos = (int)$dadosTurma['respondidos'];
                                                $porcentagem = $inscritos > 0 ? round(($respondidos/$inscritos)*100, 1) : 0;
                                                
                                                // Determinar status
                                                $statusClass = 'status-danger';
                                                $statusIcon = '✗';
                                                $progressClass = 'progress-danger';
                                                
                                                if ($respondidos > 0) {
                                                    if ($porcentagem > 70) {
                                                        $statusClass = 'status-success';
                                                        $statusIcon = '✓';
                                                        $progressClass = '';
                                                    } else if ($porcentagem > 30) {
                                                        $statusClass = 'status-warning';
                                                        $statusIcon = '!';
                                                        $progressClass = 'progress-warning';
                                                    }
                                                }
                                                ?>
                                                
                                                <div class="status-item">
                                                    <div class="status-icon <?= $statusClass; ?>"><?= $statusIcon; ?></div>
                                                    <div style="flex: 1;">
                                                        <div><strong><?= htmlspecialchars($turma); ?></strong></div>
                                                        <div style="font-size: 0.8rem; color: #666;">
                                                            <?= $inscritos; ?> inscritos / <?= $respondidos; ?> lançados
                                                        </div>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill <?= $progressClass; ?>" 
                                                                 style="width: <?= $porcentagem; ?>%"></div>
                                                        </div>
                                                        <div style="font-size: 0.8rem; margin-top: 2px;">
                                                            <?= $porcentagem; ?>% de participação
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <?php
                                            }
                                            ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                            }                
                        }
                    } else {
                        ?>
                        <tr>
                            <td colspan="3" class="no-data">
                                <div class="no-data-icon">🔍</div>
                                <h3>Nenhum resultado encontrado</h3>
                                <p>
                                    <?php 
                                    $temFiltros = !empty($_GET['termo']) || !empty($_GET['instituicao']) || !empty($_GET['bimestre']) || !empty($_GET['serie']) || !empty($_GET['turma_nome']);
                                    
                                    if ($temFiltros) {
                                        echo "Os filtros aplicados não retornaram resultados.<br>";
                                        echo "Sugestões:<br>";
                                        echo "• Remover alguns filtros<br>";
                                        echo "• Usar termos de busca mais amplos<br>";
                                        echo "• Verificar se os filtros estão corretos";
                                    } else {
                                        echo "Não há dados disponíveis no sistema.<br>";
                                        echo "Verifique se existem:<br>";
                                        echo "• Instituições cadastradas<br>";
                                        echo "• Simulados criados<br>";
                                        echo "• Turmas e alunos inscritos";
                                    }
                                    ?>
                                </p>
                                <div style="margin-top: 15px;">
                                    <button type="button" id="clearAllFilters" class="btn btn-primary">🗑️ Limpar Todos os Filtros</button>
                                </div>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <?php
        // Exibir paginação
        $paginacao = $this->obterInfoPaginacao();
        if ($paginacao['total_paginas'] > 1) {
            $temFiltros = !empty($_GET['termo']) || !empty($_GET['instituicao']) || !empty($_GET['bimestre']) || !empty($_GET['serie']) || !empty($_GET['turma_nome']);
            
            // Preservar parâmetros de filtro na paginação
            $parametrosFiltro = array();
            if (!empty($_GET['termo'])) $parametrosFiltro[] = 'termo=' . urlencode($_GET['termo']);
            if (!empty($_GET['campo'])) $parametrosFiltro[] = 'campo=' . urlencode($_GET['campo']);
            if (!empty($_GET['instituicao'])) $parametrosFiltro[] = 'instituicao=' . urlencode($_GET['instituicao']);
            if (!empty($_GET['bimestre'])) $parametrosFiltro[] = 'bimestre=' . urlencode($_GET['bimestre']);
            if (!empty($_GET['serie'])) $parametrosFiltro[] = 'serie=' . urlencode($_GET['serie']);
            if (!empty($_GET['turma_nome'])) $parametrosFiltro[] = 'turma_nome=' . urlencode($_GET['turma_nome']);
            
            $baseUrl = Gerenciador_URL::gerarLink('usuarios', 'monitorar');
            $separador = '&';
            $parametrosString = !empty($parametrosFiltro) ? '&' . implode('&', $parametrosFiltro) : '';
        ?>
        
        <div class="pagination">
            <?php if ($paginacao['tem_anterior']): ?>
                <a href="<?= $baseUrl . $separador . 'pagina=' . ($paginacao['pagina_atual'] - 1) . $parametrosString; ?>" 
                   class="pagination-btn">← Anterior</a>
            <?php endif; ?>
            
            <div class="pagination-info">
                Página <?= $paginacao['pagina_atual']; ?> de <?= $paginacao['total_paginas']; ?>
                (<?= $paginacao['total_registros']; ?> <?= $temFiltros ? 'resultados' : 'registros'; ?> no total)
            </div>
            
            <?php if ($paginacao['tem_proxima']): ?>
                <a href="<?= $baseUrl . $separador . 'pagina=' . ($paginacao['pagina_atual'] + 1) . $parametrosString; ?>" 
                   class="pagination-btn">Próxima →</a>
            <?php endif; ?>
        </div>
        
        <?php 
        } 
        
        // Mostrar dica quando não há filtros
        $temFiltros = !empty($_GET['termo']) || !empty($_GET['instituicao']) || !empty($_GET['bimestre']) || !empty($_GET['serie']) || !empty($_GET['turma_nome']);
        if (!$temFiltros): 
        ?>
            <div style="text-align: center; margin: 20px 0; color: #6c757d; font-size: 0.9rem;">
                💡 <strong>Dica:</strong> Mostrando 15 registros por página. Use os filtros acima para buscar dados específicos.
            </div>
        <?php endif; ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('filterForm');
            const clearButton = document.getElementById('clearFilters');
            const clearAllButton = document.getElementById('clearAllFilters');
            const loadingIndicator = document.getElementById('loadingIndicator');

            // Função para limpar filtros
            function clearFilters() {
                // Redireciona para a URL limpa do sistema
                window.location.href = '<?= Gerenciador_URL::gerarLink("usuarios", "monitorar"); ?>';
            }

            // Event listeners para limpar filtros
            if (clearButton) {
                clearButton.addEventListener('click', clearFilters);
            }
            
            if (clearAllButton) {
                clearAllButton.addEventListener('click', clearFilters);
            }

            // Mostrar loading ao enviar formulário
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // Previne o submit padrão
                
                loadingIndicator.style.display = 'block';
                
                // Constrói URL correta do sistema
                const baseUrl = '<?= Gerenciador_URL::gerarLink("usuarios", "monitorar"); ?>';
                const params = new URLSearchParams();
                
                // Adiciona apenas filtros preenchidos
                if (document.getElementById('termo').value.trim()) {
                    params.append('termo', document.getElementById('termo').value.trim());
                }
                if (document.getElementById('campo').value) {
                    params.append('campo', document.getElementById('campo').value);
                }
                if (document.getElementById('instituicao').value) {
                    params.append('instituicao', document.getElementById('instituicao').value);
                }
                if (document.getElementById('bimestre').value) {
                    params.append('bimestre', document.getElementById('bimestre').value);
                }
                if (document.getElementById('serie').value) {
                    params.append('serie', document.getElementById('serie').value);
                }
                if (document.getElementById('turma_nome').value.trim()) {
                    params.append('turma_nome', document.getElementById('turma_nome').value.trim());
                }
                
                // Redireciona para URL com parâmetros (sempre volta para página 1 quando aplica filtros)
                const finalUrl = params.toString() ? baseUrl + '&' + params.toString() : baseUrl;
                window.location.href = finalUrl;
            });

            // Enter para buscar
            document.getElementById('termo').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    
                    // Constrói URL correta
                    const baseUrl = '<?= Gerenciador_URL::gerarLink("usuarios", "monitorar"); ?>';
                    const params = new URLSearchParams();
                    
                    if (this.value.trim()) {
                        params.append('termo', this.value.trim());
                        if (document.getElementById('campo').value) {
                            params.append('campo', document.getElementById('campo').value);
                        }
                        
                        const finalUrl = baseUrl + '&' + params.toString();
                        window.location.href = finalUrl;
                    }
                }
            });

            // Auto-submit para selects importantes
            document.getElementById('instituicao').addEventListener('change', function() {
                if (this.value) {
                    // Constrói URL com parâmetros do sistema
                    const baseUrl = '<?= Gerenciador_URL::gerarLink("usuarios", "monitorar"); ?>';
                    const params = new URLSearchParams();
                    
                    // Adiciona apenas filtros preenchidos
                    if (document.getElementById('termo').value.trim()) {
                        params.append('termo', document.getElementById('termo').value.trim());
                    }
                    if (document.getElementById('campo').value) {
                        params.append('campo', document.getElementById('campo').value);
                    }
                    if (this.value) {
                        params.append('instituicao', this.value);
                    }
                    if (document.getElementById('bimestre').value) {
                        params.append('bimestre', document.getElementById('bimestre').value);
                    }
                    if (document.getElementById('serie').value) {
                        params.append('serie', document.getElementById('serie').value);
                    }
                    if (document.getElementById('turma_nome').value.trim()) {
                        params.append('turma_nome', document.getElementById('turma_nome').value.trim());
                    }
                    
                    const finalUrl = params.toString() ? baseUrl + '&' + params.toString() : baseUrl;
                    window.location.href = finalUrl;
                }
            });

            // Efeitos visuais nos cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Animação das barras de progresso
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach((bar, index) => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 200 + (index * 50));
            });
            
            // Log para debug
            console.log('Dashboard carregado com sucesso!');
            console.log('URL atual:', window.location.href);
            console.log('URL base do sistema:', '<?= Gerenciador_URL::gerarLink("usuarios", "monitorar"); ?>');
            
            <?php 
            $paginacao = $this->obterInfoPaginacao();
            ?>
            console.log('Paginação:', {
                pagina: <?= $paginacao['pagina_atual']; ?>,
                total_paginas: <?= $paginacao['total_paginas']; ?>,
                total_registros: <?= $paginacao['total_registros']; ?>
            });
        });
    </script>
</body>
</html>