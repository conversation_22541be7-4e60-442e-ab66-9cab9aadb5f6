<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('ExportadorCVS', 'Exportador/');

class ExportadorUsuarios extends ExportadorCVS
{
	
	public function exportar ()
	{
		$this->_dados = array( array('Nome', 'E-mail', 'Senha') );
		
		$procuraInstituicao = null;
		if ( Core::registro('instituicao')->obterID() != null ) {
			$procuraInstituicao = ' AND u_instituicao = '. Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID());
		}
	
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT * FROM usuarios 
			  LEFT JOIN emails ON emails.e_id = usuarios.u_email 
			  WHERE (u_grupos != \'3\' AND u_grupos != \'4\') OR u_grupos IS NULL %s ORDER BY u_nome', 
			  $procuraInstituicao) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$this->_dados[] = array($row['u_nome'], $row['e_endereco'], '');
			}
		}
		$rs->free();
				
		if ( count($this->_dados) < 2 ) {
			Core::modulo('redirecionador')->fixarMensagem('Nenhum usuário encontrado!', 'Exportando usuários...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			throw new Core_Exception('Sem dados!');
		}
	}
		
}

?>