<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('ImportadorCVS', 'Importador/');
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);

class FImportarUsuarios_Exception extends Formulario_Exception { }

class FImportarUsuarios extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;
	
	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_dadosImportados = array();

    public function __construct ($info = array())
    {
        parent::__construct($info);

        $this->_dados = array();
        $this->_correspondencias = array(
            'NULA' => '',
            'nome' => 'Nome do usuário',
            'email' => 'E-mail',
            'senha' => 'Senha de acesso',
            'municipio' => 'Município',
            'grupo' => 'Grupo (ID)' 
        );
        
        $this->_correspondenciasObrigatorias = array('nome');
    }

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;


		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );
								  
			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => (!$this->foiEnviado() && !isset($_POST['post_anterior']) ? 1 : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ',',
													'html_ordem' => $ordem++
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );
			
			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}
		
		if ( $this->obterEstado() == self::PASSO_2) {
			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}
		}
		
		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';
			
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {		
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}
			
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');
			
				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarUsuarios_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					$this->_correspondenciasSel[$i] = $this->_campos['correspondencia_'. $i]->obter('valor');
				}

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarUsuarios_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarUsuarios_Exception('Correspondência obrigatória não selecionada!');
					}
				}
				
				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}
				
				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarUsuarios_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FImportarUsuarios_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$importados = $this->_executarPasso_3();
			Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' usuários importados com sucesso!', 'Importando usuários...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}
	
	public function &obterDadosImportados () {
		return $this->_dadosImportados;
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));
			
			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;
				
			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();
				
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}
	
				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}
		
		if ( $passo == null )
			$passo = self::PASSO_1;
			
		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;
				
				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}
		
		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'importar'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarUsuarios_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'importar'), $dados );
	}

	protected function _executarPasso_3()
	{
		$importados = 0;
		
		// Array para armazenar IDs e dados extras dos usuários importados
		$usuarios_importados = [];
		
		foreach ($this->_dadosImportados as $obj) {    
			$primeiroAcesso = false;
			$senhaViaEmail = false;
				
			// email - precisamos garantir que o objeto Email seja criado corretamente
			$email = new Email(null);
			
			if ($obj->obterEmail()->obterEndereco() != null) {
				// Verifica se já existe um email com este endereço
				$emailID = Email::obterIDPeloEndereco($obj->obterEmail()->obterEndereco());
				
				if ($emailID !== false) {
					// O email já existe, carregá-lo
					$email = new Email($emailID);
					$email->carregar();
					
					// Mesmo que exista, vamos confirmar o email
					if (!$email->foiConfirmado()) {
						$email->fixarEstadoDaConfirmacao(true); // Confirmar o email
						$email->fixarChaveConfirmacao(null);    // Remover chave de confirmação
						$email->fixarDataUltimoEnvio(null);     // Remover data de último envio
						$email->salvar();                       // Salvar as alterações
					}
					
					if ($obj->obterSenha() == null)
						$senhaViaEmail = true;
				} else {
					// Criar um novo email
					$email = Email::obterNovoEmail($obj->obterEmail()->obterEndereco());
					
					// Definir como CONFIRMADO (importante para este caso)
					$email->fixarEstadoDaConfirmacao(true);
					
					// Como o email já está confirmado, não precisamos de chave ou data
					$email->fixarChaveConfirmacao(null);
					$email->fixarDataUltimoEnvio(null);
					
					// Salvar o email com as atualizações
					$email->salvar();
				}
			}
			
			// senha
			if ($obj->obterSenha() == null)
				$obj->fixarSenha(randomico(true, 16));
			
			// Atualizar o objeto com o email correto
			$obj->fixarEmail($email);
			
			// Criar o usuário com o email e senha definidos
			// Passamos o grupo como terceiro parâmetro se estiver definido
			$grupos = isset($obj->grupo_id) ? array($obj->grupo_id) : array(1); // Grupo padrão é 1 se não informado
			
			$usuario = UsuarioInstituido::obterNovoUsuarioInstituido($email, $obj->obterSenha(), $grupos);
			
			if ($usuario->obterID() == null) continue;
			
			// Definir nome e outros dados
			$usuario->fixarNome($obj->obterNome());
			$usuario->fixarInstituicao(Core::registro('instituicao'));
			$usuario->fixarEndereco(new Endereco(null));
			
			if (!$usuario->salvar()) continue;
			
			// Armazenar o ID do usuário e os dados extras
			$usuarios_importados[] = [
				'id' => $usuario->obterID(),
				'municipio' => isset($obj->municipio) ? $obj->municipio : null
			];
			
			// Não precisamos do "primeiro acesso" se o email já está confirmado
			// if ($primeiroAcesso && !PrimeiroAcesso::usuarioTemPrimeiroAcesso($usuario))
			//     PrimeiroAcesso::obterNovoPrimeiroAcesso($usuario);
				
			// if (!$primeiroAcesso) {
			//     if ($senhaViaEmail)
			//         @RecuperadorSenhasUsuario::obterNovoRecuperadorSenhasUsuario($usuario);
					
			//     @DespachadorEmailsUsuario::enviarEmailValidacaoEmail($usuario->obterEmail(), $senhaViaEmail);
			// }
			
			// Se o usuário precisa da senha via email
			if ($senhaViaEmail)
				@RecuperadorSenhasUsuario::obterNovoRecuperadorSenhasUsuario($usuario);
			
			$importados++;
		}
		
		// Atualizar o município para todos os usuários importados
		$db = Core::registro('db');
		
		// Verificar se a coluna u_municipio existe
		$rs = $db->query("SHOW COLUMNS FROM usuarios LIKE 'u_municipio'");
		if ($rs->num_rows == 0) {
			// Criar a coluna se ela não existir
			$db->query("ALTER TABLE usuarios ADD COLUMN u_municipio VARCHAR(255) NULL");
		}
		$rs->free();
		
		foreach ($usuarios_importados as $usuario) {
			if ($usuario['municipio'] !== null) {
				$db->query(sprintf(
					"UPDATE usuarios SET u_municipio = %s WHERE u_id = %s",
					$db->formatarValor($usuario['municipio']),
					$db->formatarValor($usuario['id'])
				));
			}
		}
		
		// Garantir que todos os emails estejam confirmados (verificação adicional)
		foreach ($usuarios_importados as $usuario) {
			$db->query(sprintf(
				"UPDATE emails e JOIN usuarios u ON e.e_id = u.u_email 
				 SET e.e_confirmado = 1, e.e_chave_confirmacao = NULL, e.e_data_ultimo_envio = NULL 
				 WHERE u.u_id = %s",
				$db->formatarValor($usuario['id'])
			));
		}
		
		// Opcional: Output de depuração para verificar os municípios
		// echo "<pre>Municípios importados:\n";
		// foreach ($usuarios_importados as $usuario) {
		//     if ($usuario['municipio'] !== null) {
		//         echo "Usuário ID {$usuario['id']}: Município = {$usuario['municipio']}\n";
		//     }
		// }
		// echo "</pre>";
		
		return $importados;
	}

	protected function _gerarDadosImportados()
	{
		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;
		$emailsImportados = array();
	
		for ($i; $i < count($this->_dados); $i++) {
			$obj = new UsuarioInstituido(null);
			$obj->fixarEndereco(new Endereco(null));
			$obj->fixarInstituicao(Core::registro('instituicao'));
			$obj->fixarEmail(new Email(null));
			
			// Propriedade adicional para armazenar o município
			$obj->municipio = null;
	
			foreach ($this->_correspondenciasSel as $j => $cID) {
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;
				switch ($cID) {
					case 'nome':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if (strlen($valor) >= 2 && strlen($valor) <= 255)
							$obj->fixarNome($valor);
						break;
					case 'email':
						if (strlen($valor) > 0 && !in_array($valor, $emailsImportados)) {
							$obj->obterEmail()->fixarEndereco($valor);
							$emailsImportados[] = $valor;
						}
						break;
					case 'senha':
						if (strlen($valor) >= 4 && strlen($valor) <= 255)
							$obj->fixarSenha($valor);
						break;
					case 'municipio':
						if (strlen($valor) > 0)
							$obj->municipio = $valor;
						break;
					case 'grupo':
						if (is_numeric($valor) && $valor > 0)
							$obj->grupo_id = (int)$valor;
						break;
					default:
						break;
				}
			}
	
			if ($obj->obterNome() != null)
				$this->_dadosImportados[] = $obj;
		}
	}
}

/* IMPORTAR DIRETORES EM MASSA PARA VARIAS ESCOLAS */

class FImportarDiretores_Exception extends Formulario_Exception { }

class FImportarDiretores extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;
	const PASSO_4 = 3;
	
	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_dadosImportados = array();
	protected $feedback = array();

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_dados = array();
		$this->_correspondencias = array(   'NULA' => '',
											'nome' => 'Nome do Diretor',
											'email' => 'E-mail',
											'senha' => 'Senha de acesso',
											'escola' => 'Escola'
									    );
							
		$this->_correspondenciasObrigatorias = array( 'nome','email','escola' );
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;


		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );
								  
			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => (!$this->foiEnviado() && !isset($_POST['post_anterior']) ? 1 : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ',',
													'html_ordem' => $ordem++
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );
			
			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}
		
		if ( $this->obterEstado() == self::PASSO_2) {
			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}
		}
		
		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';
			
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {		
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}
			
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');
			
				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarDiretores_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					$this->_correspondenciasSel[$i] = $this->_campos['correspondencia_'. $i]->obter('valor');
				}

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarDiretores_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarDiretores_Exception('Correspondência obrigatória não selecionada!');
					}
				}
				
				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}
				
				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarDiretores_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FImportarDiretores_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$importados = $this->_executarPasso_3();
			//Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' professores importados com sucesso!', 'Importando professores...');
			//Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}
	
	public function &obterDadosImportados () {
		return $this->_dadosImportados;
	}
	
	public function &obterFeedback () {
		return $this->feedback;
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));
			
			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;
				
			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();
				
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}
	
				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}
		
		if ( $passo == null )
			$passo = self::PASSO_1;
			
		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;
				
				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}
		
		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'importar_diretores'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarDiretores_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'importar_diretores'), $dados );
	}

	protected function _executarPasso_3 ()
	{
		$return = array();
		$feedback = array();

		// ESCOLAS
		$escolasBuffer = array();
		$escolasPossiveis = Instituicao::obterArrayInstituicoesParaFormulario();

		if(array_key_exists('escola', $this->_dadosImportados[0])){
			foreach ($this->_dadosImportados as $k => $obj) {
				$instID = array_search($obj['escola'], $escolasPossiveis);

				if ($instID === false) {
					$endereco = new Endereco(NULL);

					$escola = Instituicao::obterNovaInstituicao($obj['escola'], $endereco);

					if ($escola->obterID()) {
						$escola->fixarNome($obj['escola']);
						$escola->salvar();

						$instID = $escola->obterID();
						$escolasPossiveis[$instID] = $obj['escola'];
					}				
				}

				if ($instID > 0) {
					if (!isset($escolasBuffer[$instID])) {
						$escolasBuffer[$instID] = new Instituicao($instID);
						$escolasBuffer[$instID]->carregar();
					}
				} else {
					unset($this->_dadosImportados[$k]);
				}
			}
		}

		$importados = 0;	
		foreach ($this->_dadosImportados as $dados)	{	
			$primeiroAcesso = false;
			$senhaViaEmail = false;

			//checa nome
			$nomeExiste = 0;
			$uExiste = UsuarioInstituido::obterUsuarioPeloNome($dados['nome']);
			if($uExiste !== null){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." já está cadastrado.";// Registro pulado.";
				$nomeExiste = 1;
				//continue;
			}

			// email e senha
			$email = new Email(null);
			$senha = randomico(true, 16);
			if(array_key_exists('email', $dados)){
				$id = Email::obterIDPeloEndereco( $dados['email'] );	
				if ( $id !== false ) {
					if($nomeExiste){	
						$feedback[] = "Registro da linha de nome ".$dados['nome']." já tem este email ".$dados['email']." e nome cadastrado. Registro pulado.";
						continue;
					}

					$feedback[] = "Registro da linha de nome ".$dados['nome']." já tem o email ".$dados['email']." cadastrado. Ativando recurso de Primeiro Acesso.";
					$primeiroAcesso = true;	
				}
				else{
					$email = Email::obterNovoEmail($dados['email']);

					if(array_key_exists('senha', $dados)){
						$senha = $dados['senha'];
						
						$email->fixarEstadoDaConfirmacao(1);
						$email->fixarChaveConfirmacao(null);
						$email->fixarDataUltimoEnvio(null);
						$email->salvar();
					}
					else{	
						$senhaViaEmail = true;
					}
				}
			}

			// usuario
			$usuario = UsuarioInstituido::obterNovoUsuarioInstituido($email, $senha, array(2));	
			$usuario->fixarEndereco( new Endereco(null) );	
			$usuario->fixarNome( $dados['nome'] );
			$usuario->fixarEmail( $email );
			$usuario->fixarSenha( $senha );

			if(array_key_exists('escola', $dados)){
				$instID = array_search($dados['escola'], $escolasPossiveis);
				$instTmp = new Instituicao($instID);
				$usuario->fixarInstituicao( $instTmp );
			}

			if ($usuario->obterID() == null){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." falha ao registrar usuario.";
				continue;
			}			

			if ( !$usuario->salvar() ) {
				$feedback[] = "Registro da linha de nome ".$dados['nome']." falha ao salvar registro de usuario.";
				continue;
			}
			else{
				$feedback[] = "Registro da linha de nome ".$dados['nome']." cadastrado com sucesso.";
				$importados++;
			}

			if ( $primeiroAcesso && !PrimeiroAcesso::usuarioTemPrimeiroAcesso( $usuario ) ){
				PrimeiroAcesso::obterNovoPrimeiroAcesso( $usuario );
				$feedback[] = "Registro da linha de nome ".$dados['nome']." está com recurso de Primeiro Acesso.";
			}

			if ( !$primeiroAcesso ) {
				if ($senhaViaEmail){
					@RecuperadorSenhasUsuario::obterNovoRecuperadorSenhasUsuario( $usuario );
					$feedback[] = "Registro da linha de nome ".$dados['nome']." a senha será enviada por email.";
				}
					
				//@DespachadorEmailsUsuario::enviarEmailValidacaoEmail( $usuario->obterEmail(), $senhaViaEmail );
			}
		}

		$return['importados'] = $importados;
		$return['feedback'] = $feedback;
		$this->feedback = $return;

		echo"<pre>";print_r($return);echo"</pre>";exit;

		//return $return;
	}

	protected function _gerarDadosImportados ()
	{
		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;	
		$obj = array();

		for ( $i; $i < count($this->_dados); $i++ )	{		
			foreach ($this->_correspondenciasSel as $j => $cID) {				
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;

				switch ($cID) {
					case 'nome':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) >= 2 && strlen($valor) <= 255 )
							$obj[$cID] = $valor;
						break;
					case 'email':
						if ( strlen($valor) > 0 && !in_array($valor, $emailsImportados) )
							$obj[$cID] = $valor;
						break;
					case 'senha':
						if ( strlen($valor) >= 4 && strlen($valor) <= 16 )
							$obj[$cID] = $valor;
						break;
					case 'escola':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) >= 2 && strlen($valor) <= 255 )
							$obj[$cID] = $valor;
						break;
					default:
						break;
				}
			}

			$this->_dadosImportados[] = $obj;
		}
	}
}

/* IMPORTAR COORDENADORES EM MASSA PARA VARIAS ESCOLAS */

class FImportarCoordenadores_Exception extends Formulario_Exception { }

class FImportarCoordenadores extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;
	const PASSO_4 = 3;
	
	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_dadosImportados = array();
	protected $feedback = array();

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_dados = array();
		$this->_correspondencias = array(   'NULA' => '',
											'nome' => 'Nome do Coordenador',
											'email' => 'E-mail',
											'senha' => 'Senha de acesso',
											'escola' => 'Escola'
									    );
							
		$this->_correspondenciasObrigatorias = array( 'nome','email','escola' );
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;


		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );
								  
			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => (!$this->foiEnviado() && !isset($_POST['post_anterior']) ? 1 : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ',',
													'html_ordem' => $ordem++
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );
			
			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}
		
		if ( $this->obterEstado() == self::PASSO_2) {
			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}
		}
		
		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';
			
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {		
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}
			
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');
			
				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarCoordenadores_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					$this->_correspondenciasSel[$i] = $this->_campos['correspondencia_'. $i]->obter('valor');
				}

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarCoordenadores_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarCoordenadores_Exception('Correspondência obrigatória não selecionada!');
					}
				}
				
				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}
				
				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarCoordenadores_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FImportarCoordenadores_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$importados = $this->_executarPasso_3();
			//Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' professores importados com sucesso!', 'Importando professores...');
			//Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}
	
	public function &obterDadosImportados () {
		return $this->_dadosImportados;
	}
	
	public function &obterFeedback () {
		return $this->feedback;
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));
			
			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;
				
			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();
				
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}
	
				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}
		
		if ( $passo == null )
			$passo = self::PASSO_1;
			
		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;
				
				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}
		
		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'importar_coordenadores'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarCoordenadores_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('usuarios', 'importar_coordenadores'), $dados );
	}

	protected function _executarPasso_3 ()
	{
		$return = array();
		$feedback = array();

		// ESCOLAS
		$escolasBuffer = array();
		$escolasPossiveis = Instituicao::obterArrayInstituicoesParaFormulario();

		if(array_key_exists('escola', $this->_dadosImportados[0])){
			foreach ($this->_dadosImportados as $k => $obj) {
				$instID = array_search($obj['escola'], $escolasPossiveis);

				if ($instID === false) {
					$endereco = new Endereco(NULL);

					$escola = Instituicao::obterNovaInstituicao($obj['escola'], $endereco);

					if ($escola->obterID()) {
						$escola->fixarNome($obj['escola']);
						$escola->salvar();

						$instID = $escola->obterID();
						$escolasPossiveis[$instID] = $obj['escola'];
					}				
				}

				if ($instID > 0) {
					if (!isset($escolasBuffer[$instID])) {
						$escolasBuffer[$instID] = new Instituicao($instID);
						$escolasBuffer[$instID]->carregar();
					}
				} else {
					unset($this->_dadosImportados[$k]);
				}
			}
		}

		$importados = 0;	
		foreach ($this->_dadosImportados as $dados)	{	
			$primeiroAcesso = false;
			$senhaViaEmail = false;

			//checa nome
			$nomeExiste = 0;
			$uExiste = UsuarioInstituido::obterUsuarioPeloNome($dados['nome']);
			if($uExiste !== null){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." já está cadastrado.";// Registro pulado.";
				$nomeExiste = 1;
				//continue;
			}

			// email e senha
			$email = new Email(null);
			$senha = randomico(true, 16);
			if(array_key_exists('email', $dados)){
				$id = Email::obterIDPeloEndereco( $dados['email'] );	
				if ( $id !== false ) {
					if($nomeExiste){	
						$feedback[] = "Registro da linha de nome ".$dados['nome']." já tem este email ".$dados['email']." e nome cadastrado. Registro pulado.";
						continue;
					}

					$feedback[] = "Registro da linha de nome ".$dados['nome']." já tem o email ".$dados['email']." cadastrado. Ativando recurso de Primeiro Acesso.";
					$primeiroAcesso = true;	
				}
				else{
					$email = Email::obterNovoEmail($dados['email']);

					if(array_key_exists('senha', $dados)){
						$senha = $dados['senha'];
						
						$email->fixarEstadoDaConfirmacao(1);
						$email->fixarChaveConfirmacao(null);
						$email->fixarDataUltimoEnvio(null);
						$email->salvar();
					}
					else{	
						$senhaViaEmail = true;
					}
				}
			}

			// usuario
			$usuario = UsuarioInstituido::obterNovoUsuarioInstituido($email, $senha, array(7));	
			$usuario->fixarEndereco( new Endereco(null) );	
			$usuario->fixarNome( $dados['nome'] );
			$usuario->fixarEmail( $email );
			$usuario->fixarSenha( $senha );

			if(array_key_exists('escola', $dados)){
				$instID = array_search($dados['escola'], $escolasPossiveis);
				$instTmp = new Instituicao($instID);
				$usuario->fixarInstituicao( $instTmp );
			}

			if ($usuario->obterID() == null){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." falha ao registrar usuario.";
				continue;
			}			

			if ( !$usuario->salvar() ) {
				$feedback[] = "Registro da linha de nome ".$dados['nome']." falha ao salvar registro de usuario.";
				continue;
			}
			else{
				$feedback[] = "Registro da linha de nome ".$dados['nome']." cadastrado com sucesso.";
				$importados++;
			}

			if ( $primeiroAcesso && !PrimeiroAcesso::usuarioTemPrimeiroAcesso( $usuario ) ){
				PrimeiroAcesso::obterNovoPrimeiroAcesso( $usuario );
				$feedback[] = "Registro da linha de nome ".$dados['nome']." está com recurso de Primeiro Acesso.";
			}

			if ( !$primeiroAcesso ) {
				if ($senhaViaEmail){
					@RecuperadorSenhasUsuario::obterNovoRecuperadorSenhasUsuario( $usuario );
					$feedback[] = "Registro da linha de nome ".$dados['nome']." a senha será enviada por email.";
				}
					
				//@DespachadorEmailsUsuario::enviarEmailValidacaoEmail( $usuario->obterEmail(), $senhaViaEmail );
			}
		}

		$return['importados'] = $importados;
		$return['feedback'] = $feedback;
		$this->feedback = $return;

		echo"<pre>";print_r($return);echo"</pre>";exit;

		//return $return;
	}

	protected function _gerarDadosImportados ()
	{
		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;	
		$obj = array();

		for ( $i; $i < count($this->_dados); $i++ )	{		
			foreach ($this->_correspondenciasSel as $j => $cID) {				
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;

				switch ($cID) {
					case 'nome':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) >= 2 && strlen($valor) <= 255 )
							$obj[$cID] = $valor;
						break;
					case 'email':
						if ( strlen($valor) > 0 && Filtrador::email($valor) && !in_array($valor, $emailsImportados) )
							$obj[$cID] = $valor;
						break;
					case 'senha':
						if ( strlen($valor) >= 4 && strlen($valor) <= 16 )
							$obj[$cID] = $valor;
						break;
					case 'escola':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) >= 2 && strlen($valor) <= 255 )
							$obj[$cID] = $valor;
						break;
					default:
						break;
				}
			}

			$this->_dadosImportados[] = $obj;
		}
	}
}

?>