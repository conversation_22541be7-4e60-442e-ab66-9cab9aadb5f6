<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('JDesabilitadorDeCampos', 'JavaScript/JDesabilitadorDeCampos/', true);

include_once('usuarios.formulario.exception.php');

class FUsuarios extends Formulario
{
	protected $_formularioUsuario;
	
	public function __construct ($info = array())
	{
		parent::__construct($info);
		
		$this->_formularioUsuario = Core::modulo('_componente_usuario')->obterFormulario();
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar'
							  )) );

		$this->_formularioUsuario->fixarEstado($this->obterEstado());
		$this->_formularioUsuario->prepararEntrada($this->_entrada);
		$ordem = $this->_formularioUsuario->carregarFormulario($this->_dados, array('grupos' => array('TODOS' => true, 3 => false, 4 => false, 6 => false, 8 => true, 9 => true)));
		
		foreach ($this->_formularioUsuario->obterCampos() as $campo) {
			$this->adicionarCampo($campo);
		}
		
		$this->_campos['enviar']->fixar('html_ordem', $ordem);
		
		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outro usuário');		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próximo usuário');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			$this->_formularioUsuario->checarFormulario();
		}
		catch (FComponenteUsuario_Exception $e)
		{
			$this->_erros = $this->_formularioUsuario->obterErros();
			throw new FUsuarios_Exception($e->getMessage());
		}
		catch (Formulario_Exception $e)
		{
			throw new FUsuarios_Exception($e->getMessage());
		}
	}
	
	public function &executar ()
	{
		$this->_dados = $this->_formularioUsuario->executar();
	
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_dados != false && $this->_dados->obterID() != null ) {
				Core::modulo('redirecionador')->fixarMensagem('Usuário adicionado com sucesso!', 'Adicionando usuário...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o usuário!', 'Adicionando usuário...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_dados != false ) {
				Core::modulo('redirecionador')->fixarMensagem('Usuário editado com sucesso!', 'Editando usuário...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar o usuário!', 'Editando usuário...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		return $this->_dados;
	}
	
	public function foiEnviado ()
	{
		$retorno = parent::foiEnviado();
		
		$this->_formularioUsuario->fixarFoiEnviado($retorno);
		
		return $retorno;
	}
	
	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}
		
		$this->_dados = new UsuarioInstituido($id);
		
		// @todo: tirar exclusao por grupo, hardcoded
		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->validarInstituicao() && !in_array(3, $this->_dados->obterGrupos()) && !in_array(4, $this->_dados->obterGrupos()) && !in_array(6, $this->_dados->obterGrupos()) ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('usuarios', 'listar'), 'Usuário inválido!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$this->_dados->fixarInstituicao( new Instituicao(null) );
			$this->_dados->fixarEndereco( new Endereco(null) );
			$this->_dados->fixarEmail( new Email(null) );
		}
	}
	
}

?>