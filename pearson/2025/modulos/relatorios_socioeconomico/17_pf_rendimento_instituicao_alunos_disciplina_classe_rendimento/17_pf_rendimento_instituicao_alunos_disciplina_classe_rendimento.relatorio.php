<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoInstituicaoAlunosPorDisciplinaPorClasseRendimento extends RelListPesquisaSEc {
	protected $_nomesTurmas = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false,
			'_turma' => false,
			'_turma_id' => null,
			'_portador' => false
		);

		$this->_config = array(
			'diretor' => null
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			if (count($this->_dados)) {
				foreach ( $this->_nomesTurmas as $tID => $tNome ) {
					include '17_pf_rendimento_instituicao_alunos_disciplina_classe_rendimento.relatorio.tabela.html.php';
				}
			}

			include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		if (count($this->_dados)) {
			foreach ( $this->_nomesTurmas as $tID => $tNome ) {
				include '17_pf_rendimento_instituicao_alunos_disciplina_classe_rendimento.relatorio.tabela.html.php';
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();


		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR &&
				Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$this->_config['diretor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( Core::registro('usuario') == null)
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_socioeconomico', 'listar'), 'Diretor inválido!');
			else
				$this->_config['diretor'] = Core::registro('usuario');
		}

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		if($this->_seletorSimulados->simulado->obterSecaoRels() !== 'relatorios')
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritos(false);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorTurma();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorDisciplina();

		$this->_analizadorSimulado2->carregarInscritos(false);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado3->carregarInscritos(false);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);

		foreach ( $this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			if (!isset($this->_nomesTurmas[$tID]))
				$this->_nomesTurmas[$tID] = ProvaFloripa::obterTurmaComNome($tNome);
		}

		// ALUNOS
		foreach ($this->_analizadorSimulado1->inscritos as $iID => $inscricao) {
			$dado = $this->_modelo;

			$dado['nome'] = $inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['_turma_id'] = $inscricao->obterAluno()->obterTurma()->obterID();

			if ($inscricao->obterAluno()->obterPortadorNecessidade())
				$dado['_portador'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoGlobal[$iID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoGlobal[$iID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset($this->_analizadorSimulado1->rendimentoPorDisciplina[$iID]) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorDisciplina[$iID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['a'.$iID][1] = $dado;
		}

		// ALUNOS
		foreach ($this->_analizadorSimulado2->inscritos as $iID => $inscricao) {
			$this->_dados['a'.$iID][2] = true;
		}

		// ALUNOS
		foreach ($this->_analizadorSimulado3->inscritos as $iID2 => $inscricao2) {
			$this->_dados['a'.$iID2][3] = true;
		}

		// TURMAS
		foreach($this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;
			$dado['_turma_id'] = $tID;

			if (isset($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['t'.$tID][1] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['s'.$sID][1] = $dado;
		}

		asort($this->_nomesTurmas, SORT_LOCALE_STRING);
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>