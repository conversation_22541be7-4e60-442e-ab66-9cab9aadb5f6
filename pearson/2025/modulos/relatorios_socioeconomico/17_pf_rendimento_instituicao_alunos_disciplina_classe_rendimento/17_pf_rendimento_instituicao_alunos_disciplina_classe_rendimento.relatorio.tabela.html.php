<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();

$tabela['titulo'] = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br/>
	%s',
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome()
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="50%"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
}
?>
		<th align="center" nowrap="nowrap"> Grupo 2 - Racial </th>
		<th align="center" nowrap="nowrap"> Grupo 2 - SE </th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	//$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'_'. $tID .'_'. $dID  .'.png" border="0" />';

	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		//if((!array_key_exists(2, $d) || $d[2] === false) && (!array_key_exists(3, $d) || $d[3] === false)){
		//	continue;
		//}

		//if (!in_array($k, $dados_mostrar)) continue;
		if (!$d[1]['_rede'] && $d[1]['_turma_id'] != $tID) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr <?= $d[1]['_rede'] || $d[1]['_turma'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td <?= $d[1]['_rede'] || $d[1]['_turma'] ? 'bgcolor=""' : ''; ?> nowrap="nowrap"><?= @$d[1]['nome']; ?> <?= $d[1]['_portador'] ? '*' : ''; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td align="center" nowrap="nowrap"><?= @$d[1]['rendimento']; ?>% </td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if ( isset($d[1]['rendimento_por_disciplina'][$dID]) && $d[1]['rendimento_por_disciplina'][$dID] !== null ) {
					$cor = '#ffffff';
					if ( @$d[1]['rendimento_por_disciplina'][$dID] <= ProporcaoDeDesempenho4::PROPORCAO_V_1 )
						$cor = ProporcaoDeDesempenho4::PROPORCAO_COR_1;
					else if ( @$d[1]['rendimento_por_disciplina'][$dID] <= ProporcaoDeDesempenho4::PROPORCAO_V_2 )
						$cor = ProporcaoDeDesempenho4::PROPORCAO_COR_2;
					else if ( @$d[1]['rendimento_por_disciplina'][$dID] <= ProporcaoDeDesempenho4::PROPORCAO_V_3 )
						$cor = ProporcaoDeDesempenho4::PROPORCAO_COR_3;
					else if ( @$d[1]['rendimento_por_disciplina'][$dID] <= ProporcaoDeDesempenho4::PROPORCAO_V_4 )
						$cor = ProporcaoDeDesempenho4::PROPORCAO_COR_4;

					echo '<td bgcolor="#fff" title="'. $d[1]['rendimento_por_disciplina'][$dID] .'%">';
					echo '<div style="margin: 0 auto; width: 15px; height: 15px; border-radius: 50%; background-color:'.$cor.';">&nbsp;</div>';
					echo '</td>';
				} else {
					echo '<td style="text-align:center;">Ausente</td>';
				}
			}
		}
?>
		<td align="center" nowrap="nowrap">
			<?php 
				if(array_key_exists(2, $d) && $d[2] === true){ 
					echo'<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sucesso-blue.png" style="width: 15%;" />'; 
				} 
			?>
		</td>
		<td align="center" nowrap="nowrap">
			<?php 
				if(array_key_exists(3, $d) && $d[3] === true){ 
					echo'<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sucesso-blue.png" style="width: 15%;" />';
				} 
			?>
		</td>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<!--<div align="center" style="margin-top: 5px;">* aluno com necessidade especial</div>-->

	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table align="center" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_1; ?>; text-align: center; color: #fff;font-weight: bold;"> 0-<?= ProporcaoDeDesempenho4::PROPORCAO_V_1; ?>% </td>
			<td width="20px"> </td>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_2; ?>; text-align: center; color: #fff;font-weight: bold;"> <?= ProporcaoDeDesempenho4::PROPORCAO_V_1+1; ?>-<?= ProporcaoDeDesempenho4::PROPORCAO_V_2; ?>% </td>
			<td width="20px"> </td>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_3; ?>; text-align: center; color: #fff;font-weight: bold;"> <?= ProporcaoDeDesempenho4::PROPORCAO_V_2+1; ?>-<?= ProporcaoDeDesempenho4::PROPORCAO_V_3; ?>% </td>
			<td width="20px"> </td>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_4; ?>; text-align: center; color: #fff;font-weight: bold;"> <?= ProporcaoDeDesempenho4::PROPORCAO_V_3+1; ?>-<?= ProporcaoDeDesempenho4::PROPORCAO_V_4; ?>% </td>
		</tr>
	</table>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>