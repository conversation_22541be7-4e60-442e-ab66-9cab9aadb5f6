<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false
);

$selecionado = $this->_analizadorSimulado1->simulado->obterID();
$simuCheck = new Simulado($selecionado);
$simuCheck->carregar();
$simuDisc = $simuCheck->obterDisciplinaPB();

$nome = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();

if($simuDisc == 'MAT'){
	$nome = str_replace("habilidades", "competï¿½ncias", $nome);
	$HAT = ProvinhaBrasil::COM_ADQUIRIDA_TEXTO;
	$HEAT = ProvinhaBrasil::COM_EM_AQUISICAO_TEXTO;
	$HNAT = ProvinhaBrasil::COM_NAO_ADQUIRIDA_TEXTO;
}
else{
	$HAT = ProvinhaBrasil::HAB_ADQUIRIDA_TEXTO;
	$HEAT = ProvinhaBrasil::HAB_EM_AQUISICAO_TEXTO;
	$HNAT = ProvinhaBrasil::HAB_NAO_ADQUIRIDA_TEXTO;
}

$tID =  $this->_turmas_mostrar[0]->obterID();
$nome_turma =  $this->_turmas_mostrar[0]->obterNome();

$tabela['titulo'] = str_replace('impressï¿½o','',$nome) .' - '.  'Turma ' . $nome_turma ;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
	<strong>%s</strong><br />
	<strong>Turma:</strong> %s',
	
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome(),
	$nome_turma);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th style="padding-left: 120px;padding-right: 120px;"><?= $this->obterBotaoAlterador('nome', false, 'Aluno') ;?></th><? } ?>
		<th>Grupo 2 - Racial</th>
		<th>Grupo 2 - SE</th>
		<th valign="middle" width="5%" style="text-align: center; padding-left: 7px; padding-right: 7px;"></th>
<?
	if ( count($this->_dados) ) {
		foreach ($this->_analizadorSimulado1->nomesHabilidades as $hID => $hNome ){
		//	if (isset($d['rendimento_por_habilidade'][$hID])) {
				echo '<th style="text-align: center;" width="10px" nowrap="nowrap">'. $hNome .' </th>';
		//	}
		}
	}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'.$this->_tituloCache.'_'.$tID.'.png" border="0" />';

	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
		?>

		<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<? if ($this->_colunas['nome']) { ?><td><?= @$d[1]['nome']; ?> <?= $d[1]['_inscricao']->obterAluno()->obterPortadorNecessidade() ? '*' : ''; ?></td><? } ?>
			

			<td align="center" nowrap="nowrap">
			<?php 
				if(@$d[2]['qrc'] === true){ 
					echo'<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sucesso-blue.png" style="width: 20px;" />'; 
				} 
			?>
			</td>
			<td align="center" nowrap="nowrap">
			<?php 
				if(@$d[3]['qse'] === true){ 
					echo'<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sucesso-blue.png" style="width: 20px;" />';
				} 
			?>
			</td>
			
			<?
			//$cor = ProvinhaBrasil::$NIVEL_5_COR;
			$cor = '#F6F6F6';
			$texte_cor = $this->obterModoVisualizacao() == self::PDF ? '#303030' : '#737373';
			$nivel = ProvinhaBrasil::$NIVEL_5_NOME;

			if ($d[1]['rendimento'] < ProvinhaBrasil::$NIVEL_1) {
				//$cor = ProvinhaBrasil::$NIVEL_1_COR;
				$nivel = ProvinhaBrasil::$NIVEL_1_NOME;
			} elseif ($d[1]['rendimento'] < ProvinhaBrasil::$NIVEL_2) {
				//$cor = ProvinhaBrasil::$NIVEL_2_COR;
				$nivel = ProvinhaBrasil::$NIVEL_2_NOME;
			} elseif ($d[1]['rendimento'] < ProvinhaBrasil::$NIVEL_3) {
				//$cor = ProvinhaBrasil::$NIVEL_3_COR;
				$nivel = ProvinhaBrasil::$NIVEL_3_NOME;
			} elseif ($d[1]['rendimento'] < ProvinhaBrasil::$NIVEL_4) {
				//$cor = ProvinhaBrasil::$NIVEL_4_COR;
				$nivel = ProvinhaBrasil::$NIVEL_4_NOME;
			} else {
				//$cor = ProvinhaBrasil::$NIVEL_5_COR;
				$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
			}

			if ($d[1]['rendimento'] != NULL)
				//echo '<td nowrap="nowrap" style="text-align: center; color: '. ProvinhaBrasil::COR_TEXTO .'; background-color: '. $cor .'"> '. $nivel .' </td>';
				echo '<td nowrap="nowrap" style="text-align: center; color: '. $texte_cor/*ProvinhaBrasil::COR_TEXTO*/ .'; background-color: '. $cor .'">'. $nivel .' </td>';
			else
				echo '<td>&nbsp;</td>';
			?>
			<?
			foreach ($this->_analizadorSimulado1->nomesHabilidades as $hID => $hNome ) {
				if (isset($d[1]['rendimento_por_habilidade'][$hID])) {
					$img = 'rend_ruim';
					$cor = '#f381b9';#'#B32B02';

					if ($d[1]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM){
						$img = 'rend_bom';
						$cor = '#61E3A9';#'#539E48';
					}
					elseif ($d[1]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM){
						$img = 'rend_medio';
						$cor = '#fcb955';#'#F4D826';
					}

					$img .= '.gif';

					//echo '<td align="center"><img title="'. @sprintf('%d%%', $d[1]['rendimento_por_habilidade'][$hID]['rendimento']) .'" style="padding-top: 2px;" width="12" height="12" src="'. Core::diretiva('ESTILO:DIRETORIO:media').$img .'" border="0" /> </td>';

					echo '<td>';

					echo '<div style="margin: 0 auto; width: 13px; height: 13px; border-radius: 50%; background-color:'.$cor.';color-adjust: exact;-webkit-print-color-adjust: exact;">&nbsp;&nbsp;&nbsp;&nbsp;</div>';
					#echo '<span style="padding: 1px 6px; margin-right: 10px;width: 13px; height: 13px; border-radius: 50%; background-color:'.$cor.';">&nbsp;</span>';

					echo '</td>';
				}
				else{
					echo '<td align="center">Ausente</td>';
				}
			}
			?>
		</tr>
		<?
		$i++;
	}
} else {
	echo $tabela['th'];
	?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div align="center" style="margin-top: 5px;">
	<?php
	if(Core::diretiva('RELATORIO:Exibir_Legenda_Alunos_Especiais') == '1'){
		echo"* alunos com necessidades especiais";
	}
	?>
	</div>

	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;" class="">
		<table align="center" width="60%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra" style="margin-bottom: 5px;">
			<tr>
				<td><div style="margin: 0 auto; width: 13px; height: 13px; border-radius: 50%; background-color:#61E3A9;color-adjust: exact;-webkit-print-color-adjust: exact;">&nbsp;</div></td>
				<td nowrap="nowrap"><?= $HAT; ?> &nbsp;&nbsp;&nbsp;&nbsp; </td>

				<td><div style="margin: 0 auto; width: 13px; height: 13px; border-radius: 50%; background-color:#fcb955;color-adjust: exact;-webkit-print-color-adjust: exact;">&nbsp;</div></td>
				<td nowrap="nowrap"><?= $HEAT; ?> &nbsp;&nbsp;&nbsp;&nbsp; </td>

				<td><div style="margin: 0 auto; width: 13px; height: 13px; border-radius: 50%; background-color:#f381b9;color-adjust: exact;-webkit-print-color-adjust: exact;">&nbsp;</div></td>
				<td nowrap="nowrap"><?= $HNAT; ?>  </td>
			</tr>
		</table>
	</div>
	<div style="margin-top: 10px; border: 0px #cccccc solid; padding: 5px; word-wrap: break-word;width: 100%; overflow-wrap: break-word;" class="">
		<?
		foreach ($this->_analizadorSimulado1->nomesHabilidades as $hID => $hNome ) {
			echo '<div style="word-break: keep-all;"><strong>'.$hNome.'</strong> - '. $this->_analizadorSimulado1->descricoesHabilidades[$hID].'</div>&nbsp; &nbsp; ';
		}
?>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>