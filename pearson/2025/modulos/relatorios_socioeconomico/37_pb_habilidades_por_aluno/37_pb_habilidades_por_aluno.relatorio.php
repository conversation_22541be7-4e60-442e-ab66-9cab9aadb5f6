<?php
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);
Core::incluir('ProvinhaBrasil', null, true);

class RLPBHabilidadesPorAluno extends RelListPesquisaSEc {
	const GRAFICO_COR_TEXTO = '#ffffff';

	public $_por_pagina = 28;

	protected $_turmas_mostrar = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_habilidade' => true
		);

		$this->_modelo = array(
			'turma' => null,
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_habilidade' => array(),
			'_inscricao' => null,
			'_turma' => false
		);

		$this->_config = array(
			'professor' => null
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvinhaBrasil();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvinhaBrasil();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvinhaBrasil();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

        $this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		if ($this->obterModoVisualizacao() != self::PDF)
			$this->_por_pagina = 20;

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();
		
		include '37_pb_habilidades_por_aluno.relatorio.tabela.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);
		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		if ($this->obterModoVisualizacao() != self::PDF)
			$this->_por_pagina = 20;

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '37_pb_habilidades_por_aluno.relatorio.tabela.html.php';
		
		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo,ExportadorPDF::RETRATO, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR &&
			Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			$this->_config['professor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( CarregadorUsuarioEspecifico::obterProfessor() == null )
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Professor inválido!');
			else
				$this->_config['professor'] = CarregadorUsuarioEspecifico::obterProfessor();
		}

		$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmasComAulas();

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
		$this->_sugestao['nome'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$prof = Core::modulo('_perfil_falso')->obterPerfilSelecionado();

		$turmas = array();
		$turma = Core::modulo('_perfil_falso')->_perfil_turma;
		$turma->carregar();
		$turmas[0] = $turma;
		$this->_turmas_mostrar = $turmas;

		$this->_obterDadosInscricoes();

		$this->gerarGrafico();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		if ( $this->_config['professor'] !== null )
			$this->_tituloCache .= '_pro'.$this->_config['professor']->obterID();

        $idTurmas = array();
		$idTurmas[0] = $this->_turmas_mostrar[0]->obterID();

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}

			if($idTurmas[0] != $aIDsV->obterAluno()->obterTurma()->obterID()){
				continue;
			}

			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritosPorInstituicao($this->_turmas_mostrar[0]->obterInstituicao()->obterID());
		$this->_analizadorSimulado1->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorHabilidadeDosInscritos();

		$this->_analizadorSimulado2->carregarInscritosPorInstituicao($this->_turmas_mostrar[0]->obterInstituicao()->obterID());
		$this->_analizadorSimulado2->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorHabilidadeDosInscritos();

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}

			if($idTurmas[0] != $aIDsV->obterAluno()->obterTurma()->obterID()){
				continue;
			}

			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}		

		$this->_analizadorSimulado3->carregarInscritos(false);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorHabilidadeDosInscritos();

		foreach ( $this->_analizadorSimulado1->inscritos as $iID => $inscricao ) {
			$inscricao->carregar();

			if ($inscricao->obterAluno()->obterPortadorNecessidade())
				continue;

			if ($inscricao->obterNaoAlfabetico())
				continue;

			//$inscricao->verificarAusentePorFases();
			//$oapf = $inscricao->obterAusentePorFases();

			$dado = $this->_modelo;

			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
			$dado['_inscricao'] = $inscricao;

			if ( count($this->_analizadorSimulado1->respostas[$iID]) ) {
				$dado['rendimento'] = $this->_analizadorSimulado1->rendimentoGlobal[$iID]['total_pontos'];
				$dado['rendimento_por_habilidade'] = $this->_analizadorSimulado1->rendimentoPorHabilidade[$iID];
			}			

			$this->_dados['i'.$iID][1] = $dado;
		}

		// ALUNOS
		foreach ($this->_analizadorSimulado2->inscritos as $iID => $inscricao) {
			$inscricao->carregar();

			if ($inscricao->obterAluno()->obterPortadorNecessidade())
				continue;

			if ($inscricao->obterNaoAlfabetico())
				continue;

			//$inscricao->verificarAusentePorFases();
			//$oapf = $inscricao->obterAusentePorFases();

			$dado = $this->_modelo;

			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
			$dado['_inscricao'] = $inscricao;

			if ( count($this->_analizadorSimulado2->respostas[$iID]) ) {
				$dado['rendimento'] = $this->_analizadorSimulado2->rendimentoGlobal[$iID]['total_pontos'];
				$dado['rendimento_por_habilidade'] = $this->_analizadorSimulado2->rendimentoPorHabilidade[$iID];
			}			

			$this->_dados['i'.$iID][2] = $dado;
			$this->_dados['i'.$iID][2]['qrc'] = true;
		}

		// ALUNOS
		foreach ($this->_analizadorSimulado3->inscritos as $iID => $inscricao) {
			$inscricao->carregar();

			if ($inscricao->obterAluno()->obterPortadorNecessidade())
				continue;

			if ($inscricao->obterNaoAlfabetico())
				continue;

			//$inscricao->verificarAusentePorFases();
			//$oapf = $inscricao->obterAusentePorFases();

			$dado = $this->_modelo;

			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
			$dado['_inscricao'] = $inscricao;

			if ( count($this->_analizadorSimulado3->respostas[$iID]) ) {
				$dado['rendimento'] = $this->_analizadorSimulado3->rendimentoGlobal[$iID]['total_pontos'];
				$dado['rendimento_por_habilidade'] = $this->_analizadorSimulado3->rendimentoPorHabilidade[$iID];
			}			

			$this->_dados['i'.$iID][3] = $dado;
			$this->_dados['i'.$iID][3]['qse'] = true;
		}
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		$proporcaoPorHabilidadePorTurma = array();
		foreach($this->_dados as $k => &$d) {
			if (!isset($proporcaoPorHabilidadePorTurma[$d[1]['turma']][1]))
				$proporcaoPorHabilidadePorTurma[$d[1]['turma']][1] = array();

			if (!isset($proporcaoPorHabilidadePorTurma[$d[2]['turma']][2]))
				$proporcaoPorHabilidadePorTurma[$d[2]['turma']][2] = array();
			
			if (!isset($proporcaoPorHabilidadePorTurma[$d[3]['turma']][3]))
				$proporcaoPorHabilidadePorTurma[$d[3]['turma']][3] = array();

			foreach ($this->_analizadorSimulado1->nomesHabilidades as $hID => $hNome ) {
				if (!isset($proporcaoPorHabilidadePorTurma[$d[1]['turma']][1][$hID])){
					$proporcaoPorHabilidadePorTurma[$d[1]['turma']][1][$hID] = array(
						ProvinhaBrasil::HAB_ADQUIRIDA => 0,
						ProvinhaBrasil::HAB_EM_AQUISICAO => 0,
						ProvinhaBrasil::HAB_NAO_ADQUIRIDA => 0,
					);
				}

				if (!isset($proporcaoPorHabilidadePorTurma[$d[2]['turma']][2][$hID])){
					$proporcaoPorHabilidadePorTurma[$d[2]['turma']][2][$hID] = array(
						ProvinhaBrasil::HAB_ADQUIRIDA => 0,
						ProvinhaBrasil::HAB_EM_AQUISICAO => 0,
						ProvinhaBrasil::HAB_NAO_ADQUIRIDA => 0,
					);
				}
				
				if (!isset($proporcaoPorHabilidadePorTurma[$d[3]['turma']][3][$hID])){
					$proporcaoPorHabilidadePorTurma[$d[3]['turma']][3][$hID] = array(
						ProvinhaBrasil::HAB_ADQUIRIDA => 0,
						ProvinhaBrasil::HAB_EM_AQUISICAO => 0,
						ProvinhaBrasil::HAB_NAO_ADQUIRIDA => 0,
					);
				}
			}

			foreach ($this->_analizadorSimulado1->nomesHabilidades as $hID => $hNome ) {
				if (isset($d[1]['rendimento_por_habilidade'][$hID])) {
					if ($d[1]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d[1]['turma']][1][$hID][ProvinhaBrasil::HAB_ADQUIRIDA]++;
					elseif ($d[1]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d[1]['turma']][1][$hID][ProvinhaBrasil::HAB_EM_AQUISICAO]++;
					else
						$proporcaoPorHabilidadePorTurma[$d[1]['turma']][1][$hID][ProvinhaBrasil::HAB_NAO_ADQUIRIDA]++;
				}

				if (isset($d[2]['rendimento_por_habilidade'][$hID])) {
					if ($d[2]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d[2]['turma']][2][$hID][ProvinhaBrasil::HAB_ADQUIRIDA]++;
					elseif ($d[2]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d[2]['turma']][2][$hID][ProvinhaBrasil::HAB_EM_AQUISICAO]++;
					else
						$proporcaoPorHabilidadePorTurma[$d[2]['turma']][2][$hID][ProvinhaBrasil::HAB_NAO_ADQUIRIDA]++;
				}
				
				if (isset($d[3]['rendimento_por_habilidade'][$hID])) {
					if ($d[3]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d[3]['turma']][3][$hID][ProvinhaBrasil::HAB_ADQUIRIDA]++;
					elseif ($d[3]['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d[3]['turma']][3][$hID][ProvinhaBrasil::HAB_EM_AQUISICAO]++;
					else
						$proporcaoPorHabilidadePorTurma[$d[3]['turma']][3][$hID][ProvinhaBrasil::HAB_NAO_ADQUIRIDA]++;
				}
			}
		}

		$proporcaoPorHabilidadePorTurmaAbs = array();
		foreach($proporcaoPorHabilidadePorTurma as $turma => &$proporcoesPorTurma) {
			foreach($proporcoesPorTurma as $gi => &$proporcoesPorGrupo) {
				foreach($proporcoesPorGrupo as $hID => &$proporcoes) {
					$proporcaoPorHabilidadePorTurmaAbs[$turma][$gi][$hID] = $proporcoes;
					$proporcoes = self::calcularPorcentagemPerfeita($proporcoes);
				}
			}
		}

		$tID = $this->_turmas_mostrar[0]->obterID();
		$tNome = $this->_turmas_mostrar[0]->obterNome();

		$grafico = new Graph( 1000, 2000, $this->_tituloCache.'_'.$tID.'.png', (int) CACHE_RELATORIOS / 60, false);
		$grafico->SetMargin(40, 0, 10, 30);
		$grafico->setFrame(false);
		$grafico->SetScale('textint', 0, 100, 0, 0);
		$grafico->yaxis->scale->ticks->Set(20);
		$grafico->yaxis->SetFont(FF_ARIAL,FS_BOLD, 11);
		$grafico->yaxis->SetLabelFormat('%d%%');
		$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
		$grafico->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);
		$grafico->Set90AndMargin(250, 5, 5, 5);

		$labelsX = $barras = array();

		$dadosProporcoes = array(
			ProvinhaBrasil::HAB_NAO_ADQUIRIDA => array(),
			ProvinhaBrasil::HAB_EM_AQUISICAO => array(),
			ProvinhaBrasil::HAB_ADQUIRIDA => array()
		);

		foreach ($this->_analizadorSimulado1->nomesHabilidades as $hID => $hNome ) {
			$labelsX[] = 'Global: '.$this->_analizadorSimulado1->nomesHabilidades[$hID];
			$labelsX[] = 'Grupo 2 - Racial: '.$this->_analizadorSimulado1->nomesHabilidades[$hID];
			$labelsX[] = 'Grupo 2 - SE: '.$this->_analizadorSimulado1->nomesHabilidades[$hID];
		}

		foreach($proporcaoPorHabilidadePorTurma[$tNome] as $gi => $grupos) {
			//if($gi == 1){continue;}
			foreach ($grupos as $hID => $proporcoes) {
				$dadosProporcoes[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ][] = $proporcoes[ProvinhaBrasil::HAB_NAO_ADQUIRIDA];
				$dadosProporcoes[ ProvinhaBrasil::HAB_EM_AQUISICAO ][] = $proporcoes[ProvinhaBrasil::HAB_EM_AQUISICAO];
				$dadosProporcoes[ ProvinhaBrasil::HAB_ADQUIRIDA ][] = $proporcoes[ProvinhaBrasil::HAB_ADQUIRIDA];
			}
		}

		//if (!count($labelsX))
		//	continue;

		$grafico->xaxis->SetTickLabels($labelsX);

		$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] );
		$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]->SetFillColor(ProvinhaBrasil::HAB_NAO_ADQUIRIDA_COR);
		$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_EM_AQUISICAO ] );
		$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ]->SetFillColor(ProvinhaBrasil::HAB_EM_AQUISICAO_COR);
		$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_ADQUIRIDA ] );
		$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ]->SetFillColor(ProvinhaBrasil::HAB_ADQUIRIDA_COR);

		// new GroupBarPlot
		$gruposBarras1 = new AccBarPlot( array(
			$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ],
			$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ],
			$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ],
		) );

		$grafico->Add($gruposBarras1);

		$gruposBarras1->SetWidth(0.64);

		foreach ($barras as $bk => $barra) {
			$barra->value->Show();
			$barra->value->SetFormat('%d%%');
			$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
			$barra->SetValuePos('center');
			$barra->value->SetFont(FF_ARIAL,FS_BOLD, 11);
		}

		$grafico->Stroke();
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento',
												 'rendimento_questoes' ),
							 'linhas' => array(),
							 'config' => array( 'professor' => true, 'ordenar_questoes' => false ) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>