<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoTurmaSeriePorDisciplina extends RelListPesquisaSEc {
	protected $_nomesTurmas = array();
	protected $_turmasMostrar = array();

	public $orientacao_PDF = ExportadorPDF::PAISAGEM;

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false,
			'_instituicao' => false,
			'_turma' => false,
			'_turma_id' => null,
			'cor' => false,
			'_portador' => false,
			'_nao_alfabetico' => false
		);

		$this->_config = array(
			'professor' => null
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		if (count($this->_dados)) {
			foreach ( $this->_nomesTurmas as $tID => $tNome ) {
				include '26_pf_rendimento_turma_serie_por_disciplina.relatorio.tabela.html.php';
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF ($id_turma, Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		if (count($this->_dados)) {
			$this->_nomesTurmas = array_unique($this->_nomesTurmas);

			foreach ( $this->_nomesTurmas as $tID => $tNome ){
				$nomeTurma[$tID] = $tNome;
				include '26_pf_rendimento_turma_serie_por_disciplina.relatorio.tabela.html.php';
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR &&
				Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$this->_config['professor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( CarregadorUsuarioEspecifico::obterProfessor() == null )
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_socioeconomico', 'listar'), 'Professor inválido!');
			else
				$this->_config['professor'] = CarregadorUsuarioEspecifico::obterProfessor();
		}

		$this->_turmasMostrar = $this->_config['professor']->obterArrayTurmasComAulas();

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$turmas = array();
		$turma = Core::modulo('_perfil_falso')->_perfil_turma;
		$turma->carregar();
		$turmas[0] = $turma;
		$this->_turmas_mostrar = $turmas;

		$this->_obterDadosInscricoes();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		if ( $this->_config['professor'] !== null )
			$this->_tituloCache .= '_pro'.$this->_config['professor']->obterID();

		$idTurmas = array();
		$idTurmas[0] = $this->_turmas_mostrar[0]->obterID();

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}

			if($idTurmas[0] != $aIDsV->obterAluno()->obterTurma()->obterID()){
				continue;
			}

			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritos(false);
		$this->_analizadorSimulado1->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorTurma();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorDisciplina();

		$this->_analizadorSimulado2->carregarInscritos(false);
		$this->_analizadorSimulado2->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorTurma();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorDisciplina();

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}

			if($idTurmas[0] != $aIDsV->obterAluno()->obterTurma()->obterID()){
				continue;
			}

			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}
		
		$this->_analizadorSimulado3->carregarInscritos(false);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorTurma();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado3->calcularRendimentoPorSerie();
		$this->_analizadorSimulado3->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorDisciplina();

		foreach ( $this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			if (!isset($this->_nomesTurmas[$tID]))
				$this->_nomesTurmas[$tID] = ProvaFloripa::obterTurmaComNome($tNome);
		}

		asort($this->_nomesTurmas, SORT_LOCALE_STRING);

		$this->_analizadorSimulado1->limitarInscritosPorTurmas( array_keys($this->_turmasMostrar) );

		// ALUNOS
		foreach ($this->_analizadorSimulado1->inscritos as $iID => $inscricao) {
			$dado = $this->_modelo;

			$dado['nome'] = $inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['_turma_id'] = $inscricao->obterAluno()->obterTurma()->obterID();
			$dado['cor'] = $inscricao->obterAluno()->obterCor();

			if ($inscricao->obterAluno()->obterPortadorNecessidade()){
				$dado['_portador'] = true;
			}

			if ($inscricao->obterNaoAlfabetico()){
				$dado['_nao_alfabetico'] = true;
			}

			if (isset($this->_analizadorSimulado1->rendimentoGlobal[$iID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoGlobal[$iID]['rendimento']);
			}

			//RODAR DISCIPLINAS E ZERAR COM Ausente -> caso todos sejam ausentes ai tem q marcar em $dado['rendimento']
			foreach ($this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome) {
				$dado['rendimento_por_disciplina'][$dID] = 'Ausente';
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset($this->_analizadorSimulado1->rendimentoPorDisciplina[$iID]) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorDisciplina[$iID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
				}
			}

			$dAusente = 0;
			foreach ($dado['rendimento_por_disciplina'] as $dID => $dValor) {
				if($dValor === 'Ausente'){
					$dAusente++;
				}
			}

			if($dAusente == count($dado['rendimento_por_disciplina'])){
				$dado['rendimento'] = 'Ausente';
			}

			$this->_dados['ii'.$iID][1] = $dado;
		}

		// TURMAS
		foreach($this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;
			$dado['_turma_id'] = $tID;

			if (isset($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['t'.$tID][1] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado1->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modelo;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['i'.$instID][1] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['s'.$sID][1] = $dado;
		}






		$this->_analizadorSimulado2->limitarInscritosPorTurmas( array_keys($this->_turmasMostrar) );

		// ALUNOS
		foreach ($this->_analizadorSimulado2->inscritos as $iID => $inscricao) {
			$dado = $this->_modelo;

			$dado['nome'] = $inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['_turma_id'] = $inscricao->obterAluno()->obterTurma()->obterID();

			if ($inscricao->obterAluno()->obterPortadorNecessidade()){
				$dado['_portador'] = true;
			}

			if ($inscricao->obterNaoAlfabetico()){
				$dado['_nao_alfabetico'] = true;
			}

			if (isset($this->_analizadorSimulado2->rendimentoGlobal[$iID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoGlobal[$iID]['rendimento']);
			}

			//RODAR DISCIPLINAS E ZERAR COM Ausente -> caso todos sejam ausentes ai tem q marcar em $dado['rendimento']
			foreach ($this->_analizadorSimulado2->nomesDisciplinas as $dID => $dNome) {
				$dado['rendimento_por_disciplina'][$dID] = 'Ausente';
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset($this->_analizadorSimulado2->rendimentoPorDisciplina[$iID]) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorDisciplina[$iID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
				}
			}

			$dAusente = 0;
			foreach ($dado['rendimento_por_disciplina'] as $dID => $dValor) {
				if($dValor === 'Ausente'){
					$dAusente++;
				}
			}

			if($dAusente == count($dado['rendimento_por_disciplina'])){
				$dado['rendimento'] = 'Ausente';
			}

			$this->_dados['ii'.$iID][2] = $dado;
		}

		// TURMAS
		foreach($this->_analizadorSimulado2->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;
			$dado['_turma_id'] = $tID;

			if (isset($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['t'.$tID][2] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado2->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modelo;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['i'.$instID][2] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['s'.$sID][2] = $dado;
		}







		$this->_analizadorSimulado3->limitarInscritosPorTurmas( array_keys($this->_turmasMostrar) );

		// ALUNOS
		foreach ($this->_analizadorSimulado3->inscritos as $iID => $inscricao) {
			$dado = $this->_modelo;

			$dado['nome'] = $inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['_turma_id'] = $inscricao->obterAluno()->obterTurma()->obterID();

			if ($inscricao->obterAluno()->obterPortadorNecessidade()){
				$dado['_portador'] = true;
			}

			if ($inscricao->obterNaoAlfabetico()){
				$dado['_nao_alfabetico'] = true;
			}

			if (isset($this->_analizadorSimulado3->rendimentoGlobal[$iID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoGlobal[$iID]['rendimento']);
			}

			//RODAR DISCIPLINAS E ZERAR COM Ausente -> caso todos sejam ausentes ai tem q marcar em $dado['rendimento']
			foreach ($this->_analizadorSimulado3->nomesDisciplinas as $dID => $dNome) {
				$dado['rendimento_por_disciplina'][$dID] = 'Ausente';
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset($this->_analizadorSimulado3->rendimentoPorDisciplina[$iID]) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorDisciplina[$iID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
				}
			}

			$dAusente = 0;
			foreach ($dado['rendimento_por_disciplina'] as $dID => $dValor) {
				if($dValor === 'Ausente'){
					$dAusente++;
				}
			}

			if($dAusente == count($dado['rendimento_por_disciplina'])){
				$dado['rendimento'] = 'Ausente';
			}

			$this->_dados['ii'.$iID][3] = $dado;
		}


	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>