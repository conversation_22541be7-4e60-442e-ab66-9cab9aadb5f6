<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();

if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo = str_replace('nas disciplinas', 'na produção de texto', utf8_encode($this->_relatorio->obterNome()));
}

$tabela['titulo'] = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
	%s<br />
	%s - %s',
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome(),
	ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ),
	ProvaFloripa::obterTurmaComNome( @$this->_analizadorSimulado1->nomesTurmas[array_pop(array_keys($this->_turmasMostrar))] )
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="50%"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
}
?>
		<th align="center" nowrap="nowrap"> Grupo 2 - Racial </th>
		<th align="center" nowrap="nowrap"> Grupo 2 - SE </th>
	</tr>
<?

$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
		?>
		<tr <?= $d[1]['_rede'] || $d[1]['_instituicao'] || $d[1]['_turma'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<? if ($this->_colunas['nome']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor=""' : ($d[1]['_instituicao'] ? 'bgcolor=""' : ''); ?> nowrap="nowrap"><?= @$d[1]['nome']; ?> <?= $d[1]['_nao_alfabetico'] ? '*' : ''; ?></td><? } ?>
			<? if ($this->_colunas['rendimento']) { ?><td align="center"><?php if(@$d[1]['rendimento'] !== 'Ausente'){ echo $d[1]['rendimento'].'%'; }else{ echo'Ausente'; } ?></td><? } ?>
			<?
			if ( $this->_colunas['rendimento_por_disciplina'] ) {
				foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
					if (isset( $d[1]['rendimento_por_disciplina'][$dID] )){
						$valor = $d[1]['rendimento_por_disciplina'][$dID];
						if($valor !== 'Ausente'){ $valor .= '%'; }
						echo '<td align="center">';
						echo'<span style="padding: 1px 6px; margin-right: 10px;width: 13px; height: 13px; border-radius: 50%; background-color:'. ( ($d[1]['rendimento_por_disciplina'][$dID] < 60 && $valor !== 'Ausente') ? '#F4D826' : ($d[1]['_rede'] ? '' : ($d[1]['_instituicao'] ? '' : ''))) .';">';
						echo $valor;
						echo '</span>';
						echo '</td>';
					}
					else{
						echo '<td '. ($d[1]['_rede'] ? 'bgcolor=""' : ($d[1]['_instituicao'] ? 'bgcolor=""' : '')) .'>&nbsp;</td>';
					}
				}
			}

			$aluno_racial = array('4','5','6');

			$pega_racial = '';
			foreach($d as $tu =>$eu){
				if(in_array($eu['cor'], $aluno_racial)){
					$pega_racial = true;
				} 
			}

			if($pega_racial == true){ ?>
				<td align="center" nowrap="nowrap">
					<?php 
						echo'<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sucesso-blue.png" style="width: 15%;" />'; 
					?>
				</td>
			<? } else {
				echo '<td align="center" nowrap="nowrap" style="background-color: #FFFFFF"></td>';
			}
			?>
				<td align="center" nowrap="nowrap">
					<?php 
						if(array_key_exists(3, $d) && $d[3] == true){ 
							echo'<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sucesso-blue.png" style="width: 15%;" />';
						} 
					?>
				</td>
		</tr>
		<?
		$i++;
	}
	
} else {
	echo $tabela['th'];
	?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
	<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
		<table align="center" cellpadding="1" cellspacing="0" class="rlt_tabela_extra" style="width: 100%;">
			<tr style="width: 100%;">
				<td nowrap="nowrap" style='text-align:center;'>
					<span style="padding: 1px 6px; margin-right: 10px;width: 13px; height: 13px; border-radius: 50%; background-color:#F4D826;">&nbsp;</span>
					Rendimento abaixo de 60%
				</td>
			</tr>
		</table>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>