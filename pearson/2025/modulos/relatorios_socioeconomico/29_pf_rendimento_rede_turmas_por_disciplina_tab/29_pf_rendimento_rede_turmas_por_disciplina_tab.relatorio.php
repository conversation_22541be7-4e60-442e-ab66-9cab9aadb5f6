<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoRedeTurmasPorDisciplinaTab extends RelListPesquisaSEc {
	const POR_PAGINA = 99;

	protected $_disciplinasOrdencao;

	public function __construct () {
		parent::__construct();

		$this->orientacao_PDF = ExportadorPDF::PAISAGEM;
		
		$this->filtros = array();

		$this->encondarParametros();

		$this->_disciplinasOrdencao = Disciplina::obterArrayDisciplinasParaFormulario();

		$this->_colunas = array(
			'nome' => true,
			'instituicao' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		foreach ($this->_disciplinasOrdencao as $dID => $dNome)
			$this->_colunas[ $dID ] = true;

		$this->_modelo = array(
			'nome' => null,
			'instituicao' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false,
			'_instituicao' => false
		);

		$this->_config = array();

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_prepararPainel();

		$this->obterFiltrosPesquisa();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			
		$relHTML = array();

		include '29_pf_rendimento_rede_turmas_por_disciplina_tab.relatorio.tabela.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::PAISAGEM);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->obterFiltrosPesquisa();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		include '29_pf_rendimento_rede_turmas_por_disciplina_tab.relatorio.tabela.html.php';
		
		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function obterFiltrosPesquisa(){		
		if(array_key_exists('atualizar',$_POST)){
			if(array_key_exists('f_cor',$_POST)){
				foreach ($_POST['f_cor'] as $f_cork => $f_corv) {
					$ci = explode('_',$f_cork);
					$this->filtros['f_cor'][$ci[0]][] = $ci[1];
				}
			}
		}
	}
	
	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'instituicao' => 'Escola',
				'nome' => 'Turma',
				'rendimento' => 'Rendimento'
			);

			foreach ($this->_disciplinasOrdencao as $dID => $dNome)
				$itensOrdenacao[ $dID ] = $dNome;

			$itensOrdenacaoPadroes = array( array('instituicao'), array('nome') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritos(true);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorTurma();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorDisciplina();

		$instituicaoPorTurma = Turma::obterArrayInstituicaoPorTurma();

		$this->_analizadorSimulado2->carregarInscritos(true);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));//$this->filtros['f_cor'][0]);
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorTurma();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorDisciplina();

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado3->carregarInscritos(true);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorTurma();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado3->calcularRendimentoPorSerie();
		$this->_analizadorSimulado3->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorDisciplina();

		//--------------------------------------->>>

		// TURMAS
		foreach($this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['instituicao'] = @$this->_analizadorSimulado1->nomesInstituicoes[ $instituicaoPorTurma[$tID] ];

			if (isset($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']);
			}
			else{
				$dado['rendimento'] = 0;
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
					else{
						$dado['rendimento_por_disciplina'][$dID] = 0;
					}
				}
			}

			$this->_dados[$tID][1] = $dado;
		}	

		//--------------------------------------->>>

		// TURMAS
		foreach($this->_analizadorSimulado2->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['instituicao'] = @$this->_analizadorSimulado2->nomesInstituicoes[ $instituicaoPorTurma[$tID] ];

			if (isset($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']);
			}
			else{
				$dado['rendimento'] = 0;
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
					else{
						$dado['rendimento_por_disciplina'][$dID] = 0;
					}
				}
			}

			$this->_dados[$tID][2] = $dado;
		}

		//--------------------------------------->>>

		// TURMAS
		foreach($this->_analizadorSimulado3->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['instituicao'] = @$this->_analizadorSimulado3->nomesInstituicoes[ $instituicaoPorTurma[$tID] ];

			if (isset($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']);
			}
			else{
				$dado['rendimento'] = 0;
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado3->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
					else{
						$dado['rendimento_por_disciplina'][$dID] = 0;
					}
				}
			}

			$this->_dados[$tID][3] = $dado;
		}	

		ksort($this->_dados);		

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);
			}
			else{
				$dado['rendimento'] = 0;
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
					else{
						$dado['rendimento_por_disciplina'][$dID] = 0;
					}
				}
			}

			$this->_dados['s'.$sID][1] = $dado;
		}	

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);
			}
			else{
				$dado['rendimento'] = 0;
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
					else{
						$dado['rendimento_por_disciplina'][$dID] = 0;
					}
				}
			}

			$this->_dados['s'.$sID][2] = $dado;
		}	

		// SERIE
		foreach($this->_analizadorSimulado3->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento'])){
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']);
			}
			else{
				$dado['rendimento'] = 0;
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] )){
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
					else{
						$dado['rendimento_por_disciplina'][$dID] = 0;
					}
				}
			}

			$this->_dados['s'.$sID][3] = $dado;
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>