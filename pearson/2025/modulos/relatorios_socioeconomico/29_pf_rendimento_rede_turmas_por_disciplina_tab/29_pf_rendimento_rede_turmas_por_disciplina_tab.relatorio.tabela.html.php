<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento das turmas na prova(produção textual) e seus aspectos'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );;

}
$tabela['titulo']  = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong>'
);

ob_start();
?>
	<tr>
		<th></th>
		<th></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Global</th>
		<th></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Grupo 2 - Racial</th>
		<th></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Grupo 2 - SE</th>
	</tr>
	<tr>
		<? if ($this->_colunas['instituicao']) { ?><th width="30%">Escola</th><? } ?>
		<? if ($this->_colunas['nome']) { ?><th align="center">Turma</th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center">Global</th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		$dNome = wordwrap($dNome, 12, "<br>", true);
?>
		<th align="center" nowrap="nowrap"> <?= $dNome;?> </th>
<?
	}
}
?>
<td></td>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center">Global</th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		$dNome = wordwrap($dNome, 12, "<br>", true);
?>
		<th align="center" nowrap="nowrap"> <?= $dNome;?> </th>
<?
	}
}
?>
<td></td>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center">Global</th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		$dNome = wordwrap($dNome, 12, "<br>", true);
?>
		<th align="center" nowrap="nowrap"> <?= $dNome;?> </th>
<?
	}
}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr <?= $d['_rede'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['instituicao']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> nowrap="nowrap"><?= @$d[1]['instituicao']; ?></td><? } ?>
		<? if ($this->_colunas['nome']) { ?><td align="center" <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> nowrap="nowrap"><?= @$d[1]['nome']; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d[1]['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[1]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[1]['_rede'] ? 'bgcolor="#dddddd"' : '') .' align="center">'. $d[1]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[1]['_rede'] ? 'bgcolor="#dddddd"' : '') .'>&nbsp;</td>';
			}
		}
?>
<td></td>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[2]['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d[2]['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[2]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[2]['_rede'] ? 'bgcolor="#dddddd"' : '') .' align="center">'. $d[2]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[2]['_rede'] ? 'bgcolor="#dddddd"' : '') .'>&nbsp;</td>';
			}
		}
?>
<td></td>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[3]['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d[3]['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[3]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[3]['_rede'] ? 'bgcolor="#dddddd"' : '') .' align="center">'. $d[3]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[3]['_rede'] ? 'bgcolor="#dddddd"' : '') .'>&nbsp;</td>';
			}
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>