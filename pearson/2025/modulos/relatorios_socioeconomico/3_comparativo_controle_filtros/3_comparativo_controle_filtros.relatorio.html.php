<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();
$tabela['titulo']  = $titulo;

$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCA&Ccedil;&Atilde;O DE '.MUNICIPIO.'</strong>'
);
ob_start();

if ( count($this->_dados1) && count($this->_dados2) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'.png" border="0" />';
}

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>