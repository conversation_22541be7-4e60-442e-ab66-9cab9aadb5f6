<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

class Rel_65ComparativoControleFiltros extends RelListPesquisaSEc {
	const GRAFICO_COR_TEXTO = '#ffffff';
	const GRAFICO_CONTROLE_COR = '#3B867F';
	const GRAFICO_FILTRO_COR = '#22b4e5';
	const GRAFICO_PESQUISA_COR = '#033c4f';

	public $orientacao_PDF = ExportadorPDF::PAISAGEM;

	public function __construct () {
		parent::__construct();

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '65_comparativo_controle_filtros.relatorio.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);
		
		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '65_comparativo_controle_filtros.relatorio.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarTemFiltro( false );
		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		$idREDE = null;
		foreach ($this->_dados1 as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados1[$idREDE])) {
			$rede = $this->_dados1[$idREDE];
			unset($this->_dados1[$idREDE]);
			array_push($this->_dados1, $rede);
		}

		$idREDE = null;
		foreach ($this->_dados2 as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados2[$idREDE])) {
			$rede = $this->_dados2[$idREDE];
			unset($this->_dados2[$idREDE]);
			array_push($this->_dados2, $rede);
		}

		$idREDE = null;
		foreach ($this->_dados3 as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados3[$idREDE])) {
			$rede = $this->_dados3[$idREDE];
			unset($this->_dados3[$idREDE]);
			array_push($this->_dados3, $rede);
		}

		$this->gerarGrafico();
	}

	protected function _obterDadosInscricoes () {	
		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sim'.$this->_seletorSimulados->simulado->obterID().'_p'.$this->_seletorPesquisas->socioeconomico->obterID();

		$this->_analizadorSimulado1->carregarInscritos(true);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados1[] = $dado;
		}

		//--------------------------------------->>>

		$this->_analizadorSimulado2->carregarInscritos(true);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados2[] = $dado;
		}

		//--------------------------------------->>>
		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
		}

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado3->carregarInscritos(true);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado3->calcularRendimentoPorSerie();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado3->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados3[] = $dado;
		}
	}

	protected function gerarGrafico() 
	{		
		require_once ('jpgraph/jpgraph.php');
		require_once ('jpgraph/jpgraph_bar.php');
		 
		$graph = new Graph(1000, 400, $this->_tituloCache.'.png', (int) CACHE_RELATORIOS / 60, false);
		$graph->SetMargin(34, 0, 10, 75);
		$graph->setFrame(false);
		$graph->SetScale('textint', 0, 100, 0, 0);
		$graph->yaxis->scale->ticks->Set(10);
		$graph->yaxis->SetLabelFormat('%d%%');
		$graph->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
		$graph->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
		$graph->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);

		$graph->legend->Pos(0.2,0.996,'left','bottom');
		$graph->legend->SetLayout(LEGEND_HOR);
		$graph->legend->SetFillColor('white');
		$graph->legend->SetFrameWeight(0);
		$graph->legend->SetShadow(false);
		$graph->legend->SetReverse(false);
		$graph->legend->SetFont(FF_ARIAL,FS_NORMAL, 9);

		$labelsX = array();
		$dados1 = array();
		foreach($this->_dados1 as $k => &$d) {
			$labelsX[] = 'Global';
			$dados1[] = $d['rendimento'];

			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset($d['rendimento_por_disciplina'][$dID])) {
					$labelsX[] = $dNome;
					$dados1[] = $d['rendimento_por_disciplina'][$dID];
				}
			}
		}

		$dados2 = array();
		foreach($this->_dados2 as $k => &$d) {
			$dados2[] = $d['rendimento'];

			foreach ( $this->_analizadorSimulado2->nomesDisciplinas as $dID => $dNome ) {
				if (isset($d['rendimento_por_disciplina'][$dID])) {
					$dados2[] = $d['rendimento_por_disciplina'][$dID];
				}
			}
		}

		$dados3 = array();
		foreach($this->_dados3 as $k => &$d) {
			$dados3[] = $d['rendimento'];

			foreach ( $this->_analizadorSimulado3->nomesDisciplinas as $dID => $dNome ) {
				if (isset($d['rendimento_por_disciplina'][$dID])) {
					$dados3[] = $d['rendimento_por_disciplina'][$dID];
				}
			}
		}

		$graph->xaxis->SetTickLabels($labelsX);

		$b1plot = new BarPlot($dados1);
		$b2plot = new BarPlot($dados2);
		$b3plot = new BarPlot($dados3);

		$gbplot = new GroupBarPlot(array($b1plot,$b2plot,$b3plot));
		$graph->Add($gbplot);

		$b1plot->SetColor('gray');
		$b2plot->SetColor('gray');
		$b3plot->SetColor('gray');
		$gbplot->SetColor('gray');
		$gbplot->SetWeight(1);

		$b1plot->SetFillColor(self::GRAFICO_CONTROLE_COR);
		$b1plot->SetLegend('Global: '.count($this->_analizadorSimulado1->inscritos).' alunos');
		$b1plot->value->SetColor(self::GRAFICO_COR_TEXTO);
		$b1plot->value->Show();
		$b1plot->value->SetFormat('%d%%');
		$b1plot->SetValuePos('center');
		$b1plot->value->SetFont(FF_ARIAL, FS_BOLD, 12);
		$b1plot->SetWidth(0.5);

		$b2plot->SetFillColor(self::GRAFICO_FILTRO_COR);
		$b2plot->SetLegend('Grupo 2 - Racial: '.count($this->_analizadorSimulado2->inscritos).' alunos');
		$b2plot->value->SetColor(self::GRAFICO_COR_TEXTO);
		$b2plot->value->Show();
		$b2plot->value->SetFormat('%d%%');
		$b2plot->SetValuePos('center');
		$b2plot->value->SetFont(FF_ARIAL, FS_BOLD, 12);
		$b2plot->SetWidth(0.5);

		$b3plot->SetFillColor(self::GRAFICO_PESQUISA_COR);
		$b3plot->SetLegend('Grupo 2 - SE: '.count($this->_analizadorSimulado3->inscritos).' alunos');
		$b3plot->value->SetColor(self::GRAFICO_COR_TEXTO);
		$b3plot->value->Show();
		$b3plot->value->SetFormat('%d%%');
		$b3plot->SetValuePos('center');
		$b3plot->value->SetFont(FF_ARIAL, FS_BOLD, 12);
		$b3plot->SetWidth(0.5);
		
		$graph->Stroke();
	}
}

?>