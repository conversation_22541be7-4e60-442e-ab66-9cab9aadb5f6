<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('CarregadorUsuarioEspecifico', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('Inscricao', null, true);
Core::incluir('Aula', null, true);
Core::incluir('Turma', null, true);

class MSeletorSimulados
{
	public $simulado;

	protected $_simulados;
	protected $_selecionado = null;
	protected $_ordenacao;

	public function __construct (Ordenacao &$ordenacao, $auto_preparar = true)	{
		$this->_ordenacao = $ordenacao;

		$this->simulado = new Simulado(null);

		if ($auto_preparar)
			$this->_prepararListaSimulados();
	}

	public function configurarCampoSeletorSimulados ($rel_secao = '') {
		$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios');//$rel_secao);

		if(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR){
			if(in_array('3', Core::registro('usuario')->obterGrupos()) === true){
				$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();		
				$turmaProf = $professor->obterArrayTurmasComAulas();
				$turmaProf = key($turmaProf);
				$sids = Aula::obterSimuladosPorTurmaID($turmaProf);

				foreach ($simus as $sk => $sv) {
					if(!in_array($sk, $sids)){
						unset($simus[$sk]);
					}
				}
			}
			else{
				$turma = Core::modulo('_perfil_falso')->_perfil_turma;
				$turma->carregar();

				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if($turma->obterSerie()->obterID() !== $simulado->obterSerieAvaliacao()->obterID()){
						unset($simus[$sk]);
					}
				}
			}
		}
		elseif(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO){
			$perfil_falso_user = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			$simus = Inscricao::obterSimuladosPeloAlunoPorSecao($perfil_falso_user,'relatorios');
		}

		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			if(Core::registro('usuario')->obterGrupo() != '1'){
				if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
					unset($simus[$sk]);
				}

				if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
					unset($simus[$sk]);
				}
			}
		}

		$this->fixarArraySimulados($simus);

		$segundo = null;
		$simuladosFormulario = array(Campo::NULO => '');
		foreach ($this->_simulados as $id => $nome) {
			$simuladosFormulario[$id] = $nome;

			if ($segundo == null)
				$segundo = $id;
		}

		$selecionado = $segundo;
		if ( Core::diretiva('_seletor_simulados.selecionado.relatorios_socioeconomico') !== false && isset($this->_simulados[Core::diretiva('_seletor_simulados.selecionado.relatorios_socioeconomico')]) )
			$selecionado = Core::diretiva('_seletor_simulados.selecionado.relatorios_socioeconomico');

		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_simulado',
												'etiqueta' => '<strong>Selecione uma avalia&ccedil;&atilde;o</strong>',
												'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $simuladosFormulario,
												'valor_pos_erro' => $selecionado,
												'autocomplete_off' => true,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function fixarArraySimulados ($simulados)
	{
		$this->_simulados = $simulados;
	}

	public function ajustarSimuladoSelecionado ()
	{
		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = @$this->_ordenacao->campo('seletor_simulado')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_simulados[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$perfil_falso = Core::modulo('_perfil_falso');
			$perfil_falso_tipo = $perfil_falso->obterTipoPerfilSelecionado();
			$perfil_falso_user = $perfil_falso->obterPerfilSelecionado();
			$simuladoDoUsuario = Core::diretiva('_seletor_simulados.selecionado.relatorios_socioeconomico');

			if($perfil_falso_tipo == MPerfilFalso::ALUNO){
				$this->_simulados = Inscricao::obterSimuladosPeloAlunoPorSecao($perfil_falso_user,'relatorios');
				if(array_key_exists($simuladoDoUsuario, $this->_simulados)){
					$this->_selecionado = $simuladoDoUsuario;								
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);												
				}
			}
			elseif($perfil_falso_tipo == MPerfilFalso::PROFESSOR){
				if(array_key_exists($simuladoDoUsuario, $this->_simulados)){
					$this->_selecionado = $simuladoDoUsuario;								
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);												
				}
			}
			elseif($perfil_falso_tipo == MPerfilFalso::DIRETOR){
				$escola = $perfil_falso->obterInstituicaoDoPerfil();
				$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios');
				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if(Core::registro('usuario')->obterGrupo() != '1'){
						if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
							unset($simus[$sk]);
							continue;
						}

						if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
							unset($simus[$sk]);
							continue;
						}

						$turmas = Turma::obterArrayTurmasParaFormularioPorSimuladoIntituicao($escola->obterID(),$sk);				
						if(count($turmas)<=0){
							unset($simus[$sk]);
						}
					}
				}
				
				if(count($simus)>0){
					if(array_key_exists($simuladoDoUsuario, $simus)){
						$this->_selecionado = $simuladoDoUsuario;
					}
					else{
						$this->_selecionado = reset($simus);					
					}
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);
				}
			}
			else{
				$this->_selecionado = $simuladoDoUsuario;
				if($simuladoDoUsuario == null){
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);			
				}
			}
		}

		if($this->_selecionado != null){
			$this->_ordenacao->campo('seletor_simulado')->fixar('valor', $this->_selecionado);
			Core::fixarDiretiva('_seletor_simulados.selecionado.relatorios_socioeconomico', $this->_selecionado);
			$this->simulado = new Simulado($this->_selecionado);
			$this->simulado->carregar();
		}
		else{
			Core::fixarDiretiva('_seletor_simulados.selecionado.relatorios_socioeconomico', null);
			$this->simulado = new Simulado(null);
		}
	}

	protected function _prepararListaSimulados () {
		$this->_simulados = array();

		$professor = null;

		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		//else
			//$professor = CarregadorUsuarioEspecifico::obterProfessor();

		if ( $professor != null ) {
			$turmasPossiveis = array();
			//$disciplinasPossiveis = array();
			foreach ( $professor->obterAulas() as $aula ) {
				$turmasPossiveis[] = @$aula->obterTurma()->obterID();
				//$disciplinasPossiveis[] = Core::registro('db')->formatarValor( @$aula->obterDisciplina()->obterID() );
			}

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT simulados.*, series.s_nome AS series_s_nome FROM simulados
				  INNER JOIN turmas ON turmas.t_id = %s
				  INNER JOIN series ON series.s_id = turmas.t_serie
				  WHERE simulados.s_instituicao = %s
				  ORDER BY s_ordem',
				  Core::registro('db')->formatarValor( array_pop($turmasPossiveis) ),
				  //Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(Core::registro('usuario')->obterGrupo() != '1'){
						if(time() >= $row['s_data_inicio_lancamento'] AND time() <= $row['s_data_fim_lancamento']){
							continue;
						}

						if(time() >= $row['s_data_inicio_inscricao'] AND time() <= $row['s_data_fim_inscricao']){
							continue;
						}
					}

					if ( time() < $row['s_data'] )
						continue;

					if ( strstr($row['s_nome'], $row['series_s_nome']) === false ) continue;

					if ( !isset($this->_simulados[$row['s_id']]) )
						$this->_simulados[$row['s_id']] = strftime('%d/%m/%Y', $row['s_data']) . ' | ' .$row['s_nome'];
				}
			}
			$rs->free();
		} else {
			$aluno = null;
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$aluno = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			else
				$aluno = CarregadorUsuarioEspecifico::obterAluno();

			$sqlSimulado = null;
			if ( $aluno != null )
				$sqlSimulado = ' INNER JOIN simulados_inscricoes ON simulados_inscricoes.si_aluno = '. Core::registro('db')->formatarValor( $aluno->obterID() ) .' AND simulados_inscricoes.si_simulado = s_id ';

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT simulados.*, questoes.q_id FROM simulados
				  %s
				  INNER JOIN questoes ON questoes.q_simulado = s_id
				  WHERE s_instituicao = %s AND q_id IS NOT NULL
				  ORDER BY s_data DESC, s_nome ASC',
				  $sqlSimulado,
				  //Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(Core::registro('usuario')->obterGrupo() != '1'){
						if(time() >= $row['s_data_inicio_lancamento'] AND time() <= $row['s_data_fim_lancamento']){
							continue;
						}

						if(time() >= $row['s_data_inicio_inscricao'] AND time() <= $row['s_data_fim_inscricao']){
							continue;
						}
					}

					if ( time() < $row['s_data'] )
						continue;

					if ( !isset($this->_simulados[$row['s_id']]) )
						$this->_simulados[$row['s_id']] = strftime('%d/%m/%Y', $row['s_data']) . ' | ' .$row['s_nome'];
				}
			}
			$rs->free();
		}
	}
}

#=====================================================================================================================>

class MSeletorSimuladosPeriodos
{
	public $periodo;

	protected $_periodos;
	protected $_selecionado = null;
	protected $_ordenacao;

	public function __construct (Ordenacao &$ordenacao)	{
		$this->_ordenacao = $ordenacao;

		$this->periodo = null;
	}

	public function configurarCampoSeletorPeriodos ($rel_secao = '') {
		$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios');

		if(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR){
			if(in_array('3', Core::registro('usuario')->obterGrupos()) === true){
				$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();		
				$turmaProf = $professor->obterArrayTurmasComAulas();
				$turmaProf = key($turmaProf);
				$sids = Aula::obterSimuladosPorTurmaID($turmaProf);

				foreach ($simus as $sk => $sv) {
					if(!in_array($sk, $sids)){
						unset($simus[$sk]);
					}
				}
			}
			else{
				$turma = Core::modulo('_perfil_falso')->_perfil_turma;
				$turma->carregar();

				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if($turma->obterSerie()->obterID() !== $simulado->obterSerieAvaliacao()->obterID()){
						unset($simus[$sk]);
					}
				}
			}
		}

		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			if(Core::registro('usuario')->obterGrupo() != '1'){
				if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
					unset($simus[$sk]);
				}

				if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
					unset($simus[$sk]);
				}
			}
		}

		$periodos = array();
		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			$aplicacao = $simulado->obterBimestre();
			$lancamentoi = $simulado->obterInicioLancamento(true,'%d/%m/%Y');//'%d/%m/%Y %H:%M'
			$lancamentof = $simulado->obterFimLancamento(true,'%d/%m/%Y');//'%d/%m/%Y %H:%M'

			$periodos[$aplicacao] = $lancamentoi.' - '.$lancamentof;
		}

		$this->fixarArrayPeriodos($periodos);

		$segundo = null;
		$periodosFormulario = array(Campo::NULO => '');
		foreach ($this->_periodos as $id => $nome) {
			$periodosFormulario[$id] = $nome;

			if ($segundo == null)
				$segundo = $id;
		}

		$selecionado = $segundo;
		if ( Core::diretiva('_seletor_periodos.selecionado.relatorios_socioeconomico') !== false && isset($this->_periodos[Core::diretiva('_seletor_periodos.selecionado.relatorios_socioeconomico')]) )
			$selecionado = Core::diretiva('_seletor_periodos.selecionado.relatorios_socioeconomico');

		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_periodos',
												'etiqueta' => '<strong>Selecione um per&iacute;odo</strong>',
												'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($periodosFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $periodosFormulario,
												'valor_pos_erro' => $selecionado,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function fixarArrayPeriodos ($periodos)
	{
		$this->_periodos = $periodos;
	}

	public function ajustarPeriodoSelecionado ()
	{
		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = @$this->_ordenacao->campo('seletor_periodos')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_periodos[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$this->_selecionado = Core::diretiva('_seletor_periodos.selecionado.relatorios_socioeconomico');
		}

		if($this->_selecionado != null){
			$this->_ordenacao->campo('seletor_periodos')->fixar('valor', $this->_selecionado);
			Core::fixarDiretiva('_seletor_periodos.selecionado.relatorios_socioeconomico', $this->_selecionado);
			$this->periodo = $this->_selecionado;
		}
		else{
			Core::fixarDiretiva('_seletor_periodos.selecionado.relatorios_socioeconomico', null);
			$this->periodo = reset(array_keys($this->_periodos));
		}
	}
}

?>