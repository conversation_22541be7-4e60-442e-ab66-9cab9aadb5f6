<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();

if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento das turmas por aspecto da produção textual'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );;

}
$tabela['titulo']  = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
	%s',
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome()
);

ob_start();
?>
	<tr>
		<th width="25%"></th>
		<th width="25%"></th>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_questao'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		if (!count($this->_questoesPorDisciplina[$dID])) continue;
?>
		<th colspan="<?= count($this->_questoesPorDisciplina[$dID]); ?>" align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
}
?>
	</tr>
	<tr>
		<th></th>
		<? if ($this->_colunas['nome']) { ?><th><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_questao'] ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		if (!count($this->_questoesPorDisciplina[$dID])) continue;

		foreach($this->_questoesPorDisciplina[$dID] as $qID) {
?>
			<th align="center" nowrap="nowrap"> <?= $this->_analizadorSimulado1->nomesQuestoes[$qID] ;?> </th>
<?
		}
	}
}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => $d ) {
		foreach ($d as $dk => $d) {
			if($dk == 1){
				$tipo = 'Global';
			}
			elseif($dk == 2){
				$tipo = 'Grupo 2 - Racial';
			}
			elseif($dk == 3){
				$tipo = 'Grupo 2 - SE';
			}

			if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
				echo $tabela['th'];
	?>
		<tr <?= $d['_rede'] || $d['_instituicao'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= $tipo; ?></td>
			<? if ($this->_colunas['nome']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= @$d['nome']; ?></td><? } ?>
	<?
			if ( $this->_colunas['rendimento_por_questao'] ) {
				foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
					if (!count($this->_questoesPorDisciplina[$dID])) continue;

					foreach($this->_questoesPorDisciplina[$dID] as $qID) {
						if (isset( $d['rendimento_por_questao'][$qID] ))
							echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d['rendimento_por_questao'][$qID] .'</td>';
						else
							echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
					}
				}
			}
			$i++;
		}
	?>
		</tr>
	<?
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div style="margin-top: 15px; border: 1px #cccccc solid; padding: 10px;">
<?
foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
	if (!count($this->_questoesPorDisciplina[$dID])) continue;

	echo ''. $dNome . ': ';
	$questoesTemp = array();
	foreach ( $this->_questoesPorDisciplina[$dID] as $qID)
		$questoesTemp[] = $this->_analizadorSimulado1->nomesQuestoes[$qID];

	echo implode(', ', $questoesTemp) . '<br />';
}
?>
	</div>
<?
$tabela['extra'] = ob_get_clean(); $tabela['extra'] = null;

ob_start();
?>
	<tr>
		<th></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Global</th>
		<th></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Grupo 2 - Racial</th>
		<th></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Grupo 2 - SE</th>
	</tr>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="25%"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $disciplinaID => $dNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
?>
<td></td>
<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
<?
	foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $disciplinaID => $dNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
?>
<td></td>
<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
<?	
	foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $disciplinaID => $dNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
}
?>
	</tr>
<?
	$i = 0;
	foreach ( $this->_dados as $k => $d ) {
?>
	<tr <?= $d[1]['_rede'] || $d[1]['_instituicao'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= @$d[1]['nome']; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d[1]['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[1]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d[1]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
			}
		}
?>
<td></td>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[2]['_rede'] ? 'bgcolor="#dddddd"' : ($d[2]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d[2]['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[2]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[2]['_rede'] ? 'bgcolor="#dddddd"' : ($d[2]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d[2]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[2]['_rede'] ? 'bgcolor="#dddddd"' : ($d[2]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
			}
		}
?>
<td></td>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[3]['_rede'] ? 'bgcolor="#dddddd"' : ($d[3]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d[3]['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[3]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[3]['_rede'] ? 'bgcolor="#dddddd"' : ($d[3]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d[2]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[3]['_rede'] ? 'bgcolor="#dddddd"' : ($d[3]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
			}
		}
?>
	</tr>
<?
		$i++;
	}

$tabela['td_extra'] = ob_get_clean();

$relHTML[] = $tabela;
?>