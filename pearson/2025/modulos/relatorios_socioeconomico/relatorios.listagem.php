<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');

class LRelatorios extends Listagem
{
	protected $_procuraSQL = null;
	protected $_sugestoesRelatorios = array();
	protected $_numerSugestoesRelatorios = 0;

	public function prepararListagem ()
	{
		$this->_obterDados();
		
		$this->_carregarSugestoesRelatorios();

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('relatorios.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _obterDados ()
	{
		$queryOculto = '';
		if(Core::registro('usuario')->obterGrupo() != 1){
			$queryOculto = "r_oculto != '1' AND";
		}

		$rs = Core::registro('db')->query(
			  'SELECT * FROM relatorios 
			  INNER JOIN relatorios_grupos ON relatorios_grupos.rg_id = relatorios.r_grupo 
			  LEFT JOIN permissoes ON permissoes.p_id = r_permissao 
			  WHERE '.$queryOculto.' r_secao = "relatorios_socioeconomico"
			  ORDER BY rg_ordem ASC, r_ordem ASC, r_nome ASC' );
			  
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$relatorio = new Relatorio( $row['r_id'] );
				
				$relatorio->fixarNome( $row['r_nome'] );
				$relatorio->fixarDescricao( $row['r_descricao'] );
				$relatorio->fixarDescricaoLonga( $row['r_descricao_longa'] );
				$relatorio->fixarGrupo( $row['r_grupo'] );
				$relatorio->fixarPermissao( $row['r_permissao'], $row['p_nome'] );
				$relatorio->fixarOrdem( $row['r_ordem'] );
				$relatorio->fixarOculto( $row['r_oculto'] == 1 );
				$relatorio->fixarArquivo( $row['r_arquivo'] );
				$relatorio->fixarClasse( $row['r_classe'] );
				
				if ( Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ) {
					 if(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado() != null) {
						if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()))
							continue;
					}
				} else if ( !$relatorio->validarPermissao() )
					continue;
				
				if ( !isset( $this->_dados[ $row['rg_id'] ] ) )
					$this->_dados[ $row['rg_id'] ] = array('nome' => $row['rg_nome'], 'relatorios' => array());
					
				$this->_dados[ $row['rg_id'] ]['relatorios'][] = $relatorio;
				$this->_sugestoesRelatorios[$row['r_id']] = array();
			}
		}
		$rs->free();
	}
	
	protected function _carregarSugestoesRelatorios ()
	{
		$rs = Core::registro('db')->query( 'SELECT * FROM relatorios_sugestoes ORDER BY rs_ordem ASC, rs_nome ASC' );
			  
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if (isset( $this->_sugestoesRelatorios[$row['rs_relatorio']] )) {
					$this->_sugestoesRelatorios[$row['rs_relatorio']][$row['rs_id']] = $row['rs_nome'];
					$this->_numerSugestoesRelatorios++;	
				}
			}
		}
		$rs->free();
	}
}

?>