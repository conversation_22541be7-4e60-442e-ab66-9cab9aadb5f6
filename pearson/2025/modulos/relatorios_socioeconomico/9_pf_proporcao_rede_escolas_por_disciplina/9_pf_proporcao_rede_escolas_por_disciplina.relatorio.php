<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFProporcaoRedeEscolasPorDisciplina extends RelListPesquisaSEc {
	const GRAFICO_PROPORCAO_1_PROVA_COR = '#f1501f';
	const GRAFICO_PROPORCAO_2_PROVA_COR = '#f4d826';
	const GRAFICO_PROPORCAO_3_PROVA_COR = '#8be37d';
	const GRAFICO_PROPORCAO_4_PROVA_COR = '#539e48';

	const GRAFICO_PROPORCAO_1_DISCIPLINA_COR = '#f1501f';
	const GRAFICO_PROPORCAO_2_DISCIPLINA_COR = '#f4d826';
	const GRAFICO_PROPORCAO_3_DISCIPLINA_COR = '#8be37d';
	const GRAFICO_PROPORCAO_4_DISCIPLINA_COR = '#539e48';

	const GRAFICO_COR_TEXTO = '#ffffff';

	public function __construct () {
		parent::__construct();

		$this->filtros = array();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_proporcao' => true,
			'rendimento_por_proporcao_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_proporcao' => array(),
			'rendimento_por_proporcao_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_config = array(

		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_prepararPainel();

		$this->obterFiltrosPesquisa();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		
		$relHTML = array();

		if (count($this->_dados)) {
			$_dados = array_chunk($this->_dados, 30);
			foreach ($_dados as $dk => $dv) {
				$dID = -1; $dNome = 'Global';
				include '9_pf_proporcao_rede_escolas_por_disciplina.relatorio.tabela.html.php';
			}

			foreach ($_dados as $dk => $dv) {
				foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ){
					include '9_pf_proporcao_rede_escolas_por_disciplina.relatorio.tabela.html.php';
				}
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null,ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->obterFiltrosPesquisa();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		if (count($this->_dados)) {
			$_dados = array_chunk($this->_dados, 30);
			foreach ($_dados as $dk => $dv) {
				$dID = -1; $dNome = 'Global';
				include '9_pf_proporcao_rede_escolas_por_disciplina.relatorio.tabela.html.php';
			}
			
			foreach ($_dados as $dk => $dv) {
				foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ){
					include '9_pf_proporcao_rede_escolas_por_disciplina.relatorio.tabela.html.php';
				}
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo,ExportadorPDF::RETRATO, $pasta);
	}

	protected function obterFiltrosPesquisa(){		
		if(array_key_exists('atualizar',$_POST)){
			if(array_key_exists('f_cor',$_POST)){
				foreach ($_POST['f_cor'] as $f_cork => $f_corv) {
					$ci = explode('_',$f_cork);
					$this->filtros['f_cor'][$ci[0]][] = $ci[1];
				}
			}
		}
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();

		$this->gerarGrafico();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritos(true);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorProporcao4();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorProporcao4();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorProporcao4PorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorProporcao4PorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'Global';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado1->rendimentoPorSeriePorProporcao[$sID]))
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado1->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado1->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados[] = $dado;
		}

		//--------------------------------------->>>

		$this->_analizadorSimulado2->carregarInscritos(true);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorProporcao4();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorProporcao4();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorProporcao4PorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorProporcao4PorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'Grupo 2 - Racial';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado2->rendimentoPorSeriePorProporcao[$sID]))
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado2->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado2->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados[] = $dado;
		}

		//--------------------------------------->>>

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado3->carregarInscritos(true);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado3->calcularRendimentoPorSerie();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorProporcao4();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorProporcao4();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorProporcao4PorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorProporcao4PorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado3->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'Grupo 2 - SE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado3->rendimentoPorSeriePorProporcao[$sID]))
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado3->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado3->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados[] = $dado;
		}
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		$_dados = array_chunk($this->_dados, 30);

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		$nomesDisciplinas = $this->_analizadorSimulado1->nomesDisciplinas;
		$nomesDisciplinas[-1] = 'Global';
		foreach ( $nomesDisciplinas as $dID => $dNome ) {
			foreach ($_dados as $dk => $dv) {
			    $grafico = new Graph( 700 , count($dv) * 25 + 25,  $this->_tituloCache.'_'.$dk.'_'.$dID.'.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetMargin(34, 0, 10, 30);
				$grafico->setFrame(false);
				$grafico->SetScale('textint', 0, 100, 0, 0);
				$grafico->yaxis->scale->ticks->Set(100);
				$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
				$grafico->yaxis->HideLabels();
				$grafico->xaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
			    $grafico->Set90AndMargin(386, 5, 5, 5);

				$labelsX = $barras = array();
				$dadosProporcoes = array(
					ProporcaoDeDesempenho4::PROPORCAO_1 => array(),
					ProporcaoDeDesempenho4::PROPORCAO_2 => array(),
					ProporcaoDeDesempenho4::PROPORCAO_3 => array(),
					ProporcaoDeDesempenho4::PROPORCAO_4 => array()
				);

				foreach($dv as $k => &$d) {
					if ($dID == -1) {
						$labelsX[] = $d['nome'] . ' ('. $d['rendimento'] .'%)';
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_1];
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_2];
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_3];
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_4];
					} elseif (isset( $d['rendimento_por_proporcao_por_disciplina'][$dID] )) {
						$labelsX[] = $d['nome'] . ' ('. $d['rendimento'] .'%)';
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_1];
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_2];
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_3];
						$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_4];
					}
				}

				$grafico->xaxis->SetTickLabels($labelsX);

				$barras[ ProporcaoDeDesempenho4::PROPORCAO_1 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ] );
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_1 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_1_PROVA_COR : self::GRAFICO_PROPORCAO_1_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho4::PROPORCAO_2 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ] );
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_2 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_2_PROVA_COR : self::GRAFICO_PROPORCAO_2_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho4::PROPORCAO_3 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ] );
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_3 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_3_PROVA_COR : self::GRAFICO_PROPORCAO_3_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho4::PROPORCAO_4 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ] );
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_4 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_4_PROVA_COR : self::GRAFICO_PROPORCAO_4_DISCIPLINA_COR);

				// new GroupBarPlot
				$gruposBarras = new AccBarPlot( array(
					$barras[ ProporcaoDeDesempenho4::PROPORCAO_1 ],
					$barras[ ProporcaoDeDesempenho4::PROPORCAO_2 ],
					$barras[ ProporcaoDeDesempenho4::PROPORCAO_3 ],
					$barras[ ProporcaoDeDesempenho4::PROPORCAO_4 ],
				) );

				$grafico->Add($gruposBarras);

				$gruposBarras->SetWidth(0.64);

				foreach ($barras as &$barra) {
					$barra->value->Show();
					$barra->value->SetFormat('%d%%');
					$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
					$barra->SetValuePos('center');
					$barra->value->SetFont(FF_ARIAL,FS_BOLD, 10);
				}

				$grafico->Stroke();
			}
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>