<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);
$titulo = $this->_relatorio->obterNome().' - '.$this->_seletorPesquisas->socioeconomico->obterNome();


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Proporção de alunos por faixa de rendimento - prova(produção textual) e seus aspectos'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .' - ' . $dNome;

}
$tabela['titulo']  = $titulo.'<br><br>'.ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome()). ' - '. $dNome;

$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong>'
);

ob_start();

if ( count($this->_dados) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'.$this->_tituloCache.'_'.$dk.'_'.$dID.'.png" border="0" />';
} 

?>
	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table align="center" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_1; ?>; text-align: center; color: #fff;font-weight: bold;"> 0-<?= ProporcaoDeDesempenho4::PROPORCAO_V_1; ?>% </td>
			<td width="20px"> </td>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_2; ?>; text-align: center; color: #fff;font-weight: bold;"> <?= ProporcaoDeDesempenho4::PROPORCAO_V_1+1; ?>-<?= ProporcaoDeDesempenho4::PROPORCAO_V_2; ?>% </td>
			<td width="20px"> </td>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_3; ?>; text-align: center; color: #fff;font-weight: bold;"> <?= ProporcaoDeDesempenho4::PROPORCAO_V_2+1; ?>-<?= ProporcaoDeDesempenho4::PROPORCAO_V_3; ?>% </td>
			<td width="20px"> </td>
			<td width="60px" style="border-radius: 50px; background-color: <?= ProporcaoDeDesempenho4::PROPORCAO_COR_4; ?>; text-align: center; color: #fff;font-weight: bold;"> <?= ProporcaoDeDesempenho4::PROPORCAO_V_3+1; ?>-<?= ProporcaoDeDesempenho4::PROPORCAO_V_4; ?>% </td>
		</tr>
	</table>
	</div>
<?
$tabela['grafico'] .= ob_get_clean();

//$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>