<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('Disciplina', null, true);

class RLPFProporcaoInstituicaoSerieTurmasPorDisciplina extends RelListPesquisaSEc {
	const GRAFICO_COR_TEXTO = '#ffffff';

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_proporcao' => true,
			'rendimento_por_proporcao_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_proporcao' => array(),
			'rendimento_por_proporcao_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_config = array(
			'diretor' => null
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		$k = 1;
		$forca_footer = false;
		if (count($this->_dados)) {
			$nomesDisciplinas = array(-1 => 'Global');
			$nomesDisciplinasTMP = $this->_analizadorSimulado1->nomesDisciplinas;
			$nomesDisciplinas += $nomesDisciplinasTMP;

			foreach ( $nomesDisciplinas as $dID => $dNome ){
				if($k == 1){
					include '15_pf_proporcao_instituicao_series_turmas_por_disciplina.relatorio.tabela.header.html.php';
					$k += 1;
				}

				include '15_pf_proporcao_instituicao_series_turmas_por_disciplina.relatorio.tabela.html.php';
				$forca_footer = true;
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		$k = 1;
		$forca_footer = false;
		if (count($this->_dados)) {
			$nomesDisciplinas = array(-1 => 'Global');
			$nomesDisciplinasTMP = $this->_analizadorSimulado1->nomesDisciplinas;
			$nomesDisciplinas += $nomesDisciplinasTMP;

			foreach ( $nomesDisciplinas as $dID => $dNome ){
				if($k == 1){
					include '15_pf_proporcao_instituicao_series_turmas_por_disciplina.relatorio.tabela.header.html.php';
					$k += 1;
				}

				include '15_pf_proporcao_instituicao_series_turmas_por_disciplina.relatorio.tabela.html.php';
				$forca_footer = true;
			}
		}

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR &&
			Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			$this->_config['diretor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( Core::registro('usuario') == null)
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_socioeconomico', 'listar'), 'Diretor inválido!');
			else
				$this->_config['diretor'] = Core::registro('usuario');
		}

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();

		$this->gerarGrafico();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritos(false);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorTurma();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorProporcao4();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorProporcao4();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorProporcao4PorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorProporcao4PorDisciplina();

		$this->_analizadorSimulado2->carregarInscritos(false);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorTurma();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorTurmaPorProporcao4();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorProporcao4();
		$this->_analizadorSimulado2->calcularRendimentoPorTurmaPorProporcao4PorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorProporcao4PorDisciplina();

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado3->carregarInscritos(false);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorTurma();
		$this->_analizadorSimulado3->calcularRendimentoPorSerie();
		$this->_analizadorSimulado3->calcularRendimentoPorTurmaPorProporcao4();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorProporcao4();
		$this->_analizadorSimulado3->calcularRendimentoPorTurmaPorProporcao4PorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorProporcao4PorDisciplina();

		// TURMAS
		foreach($this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);

			if (isset($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']);

			if (isset($this->_analizadorSimulado1->rendimentoPorTurmaPorProporcao[$tID])){
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado1->rendimentoPorTurmaPorProporcao[$tID]->obterProporcoesPorcentagens();
			}

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] )) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados['t'.$tID][1] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado1->rendimentoPorSeriePorProporcao[$sID])){
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado1->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();
			}

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado1->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados['s'.$sID][1] = $dado;
		}



		// TURMAS
		foreach($this->_analizadorSimulado2->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);

			if (isset($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']);

			if (isset($this->_analizadorSimulado2->rendimentoPorTurmaPorProporcao[$tID])){
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado2->rendimentoPorTurmaPorProporcao[$tID]->obterProporcoesPorcentagens();
			}

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado2->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] )) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados['t'.$tID][2] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado2->rendimentoPorSeriePorProporcao[$sID])){
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado2->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();
			}

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado2->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados['s'.$sID][2] = $dado;
		}

		

		// TURMAS
		foreach($this->_analizadorSimulado3->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);

			if (isset($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']);

			if (isset($this->_analizadorSimulado3->rendimentoPorTurmaPorProporcao[$tID])){
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado3->rendimentoPorTurmaPorProporcao[$tID]->obterProporcoesPorcentagens();
			}

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado3->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] )) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados['t'.$tID][3] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado3->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado3->rendimentoPorSeriePorProporcao[$sID])){
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado3->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();
			}

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado3->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dados['s'.$sID][3] = $dado;
		}
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		$nomesDisciplinas = $this->_analizadorSimulado1->nomesDisciplinas;
		foreach ( $nomesDisciplinas as $dID => $dNome ) {
			$disciplina = new Disciplina($dID);
			$disciplina->carregar();
			$ordem = $disciplina->obterOrdem();

			$nomesDisciplinas[$ordem] = $dNome;
		}

		$nomesDisciplinas[-1] = 'Global';
		foreach ( $nomesDisciplinas as $dID => $dNome ) {
			$grafico = new Graph( 700 , count($this->_dados) * 80 + 26, $this->_tituloCache.'_'.$dID.'.png', (int) CACHE_RELATORIOS / 60, false);
			$grafico->SetMargin(34, 0, 0, 30);
			$grafico->setFrame(false);
			//$grafico->SetMarginColor('white');
			$grafico->SetScale('textint', 0, 100, 0, 0);
			$grafico->yaxis->scale->ticks->Set(100);
			$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 10);
			//$grafico->yaxis->title->Set('Quantidade de alunos (%)');
			$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD); 
			$grafico->yaxis->HideLabels();
			$grafico->xaxis->SetFont(FF_ARIAL,FS_NORMAL, 10);
			$grafico->Set90AndMargin(250, 5, 5, 5);

			$labelsX = $barras = array();
			$dadosProporcoes = array(
				ProporcaoDeDesempenho4::PROPORCAO_1 => array(),
				ProporcaoDeDesempenho4::PROPORCAO_2 => array(),
				ProporcaoDeDesempenho4::PROPORCAO_3 => array(),
				ProporcaoDeDesempenho4::PROPORCAO_4 => array()
			);

			foreach($this->_dados as $k => &$d) {
				if ($dID == -1) {
					$labelsX[] = 'Global: '.$d[1]['nome'] . ' ('. $d[1]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d[1]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_1];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d[1]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_2];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d[1]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_3];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d[1]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_4];

					$labelsX[] = 'Grupo 2 - Racial: '.$d[2]['nome'] . ' ('. $d[2]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d[2]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_1];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d[2]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_2];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d[2]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_3];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d[2]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_4];

					$labelsX[] = 'Grupo 2 - SE: '.$d[3]['nome'] . ' ('. $d[3]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d[3]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_1];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d[3]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_2];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d[3]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_3];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d[3]['rendimento_por_proporcao'][ProporcaoDeDesempenho4::PROPORCAO_4];
				} 
				elseif (isset( $d[1]['rendimento_por_proporcao_por_disciplina'][$dID] )) {
					$labelsX[] = 'Global: '.$d[1]['nome'] . ' ('. $d[1]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d[1]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_1];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d[1]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_2];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d[1]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_3];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d[1]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_4];

					$labelsX[] = 'Grupo 2 - Racial: '.$d[2]['nome'] . ' ('. $d[2]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d[2]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_1];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d[2]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_2];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d[2]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_3];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d[2]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_4];

					$labelsX[] = 'Grupo 2 - SE: '.$d[3]['nome'] . ' ('. $d[3]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = $d[3]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_1];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = $d[3]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_2];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = $d[3]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_3];
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = $d[3]['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho4::PROPORCAO_4];
				}
				else{
					$labelsX[] = 'Global: '.$d[1]['nome'] . ' ('. $d[1]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = 0;

					$labelsX[] = 'Grupo 2 - Racial: '.$d[2]['nome'] . ' ('. $d[2]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = 0;

					$labelsX[] = 'Grupo 2 - SE: '.$d[3]['nome'] . ' ('. $d[3]['rendimento'] .'%)'; 
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ][] = 0;
					$dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ][] = 0;
				}
			}

			$grafico->xaxis->SetTickLabels($labelsX);
			
			$barras[ ProporcaoDeDesempenho4::PROPORCAO_1 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_1 ] );
			$barras[ ProporcaoDeDesempenho4::PROPORCAO_1 ]->SetFillColor(ProporcaoDeDesempenho4::PROPORCAO_COR_1);

			$barras[ ProporcaoDeDesempenho4::PROPORCAO_2 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_2 ] );
			$barras[ ProporcaoDeDesempenho4::PROPORCAO_2 ]->SetFillColor(ProporcaoDeDesempenho4::PROPORCAO_COR_2);

			$barras[ ProporcaoDeDesempenho4::PROPORCAO_3 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_3 ] );
			$barras[ ProporcaoDeDesempenho4::PROPORCAO_3 ]->SetFillColor(ProporcaoDeDesempenho4::PROPORCAO_COR_3);

			$barras[ ProporcaoDeDesempenho4::PROPORCAO_4 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho4::PROPORCAO_4 ] );
			$barras[ ProporcaoDeDesempenho4::PROPORCAO_4 ]->SetFillColor(ProporcaoDeDesempenho4::PROPORCAO_COR_4);

			// new GroupBarPlot
			$gruposBarras = new AccBarPlot( array(
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_1 ],
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_2 ],
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_3 ],
				$barras[ ProporcaoDeDesempenho4::PROPORCAO_4 ]
			) );

			$grafico->Add($gruposBarras);

			$gruposBarras->SetWidth(0.64);

			foreach ($barras as &$barra) {
				$barra->value->Show();
				$barra->value->SetFormat('%d%%');
				$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
				$barra->SetValuePos('center');
				$barra->value->SetFont(FF_ARIAL,FS_BOLD, 11);
			}

			$grafico->Stroke();
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
			'rendimento' ),
			'linhas' => array(),
			'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>