<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'extra_antes_td_extra' => 0,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome().'<br />'.ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();

if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Proporção de alunos por faixa de rendimento - prova(produção textual) e seus aspectos'.'<br />';
	//Proporção de alunos por faixa de rendimento - prova e disciplinas
}

$tabela['titulo']  = $titulo;

$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
	%s',
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome()
);
$tabela['nao_quebrar_pagina'] = true;
$tabela['quebra_invisivel'] = true;
$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>