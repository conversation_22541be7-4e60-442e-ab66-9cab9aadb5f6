<?
if ($this->_ordenacao->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_ordenacao->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>

<table width="100%" border="0" align="center" cellpadding="4" cellspacing="0" class="rlt_cd">
    <tr>
      <td class="rlt_cd_Titulo" style="padding: 16px;" valign="top" width="60%"><div class="maior"><strong><?= $this->_relatorio->obterNome(); ?></strong></div>
<?
	if ( @$this->_botaoVoltar['habilitado'] ) {
?>
	  <div><a href="<?= @$this->_botaoVoltar['endereco']; ?>"><?= @$this->_botaoVoltar['nome']; ?></a></div>
<?
	}
?>
        </td>
<?
if ( Core::moduloCarregado('_seletor_relatorios') ) {
?>
      <td nowrap="nowrap" class="rlt_cd_TituloDireita" style="padding: 16px;"><strong>Selecionar um relat&oacute;rio</strong><br /><?= Core::modulo('_seletor_relatorios')->obterSaida(); ?></td>
<?
}
?>
<?= $this->_ordenacao->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<?
if ( $this->_ordenacao->campo('seletor_socioeconomico') !== false ) {
?>
      <td nowrap="nowrap" class="rlt_cd_TituloDireita" style="padding: 16px;"><strong><?= $this->_ordenacao->obterHTML('seletor_socioeconomico', Formulario::HTML_LABEL, true); ?></strong><br /><?= $this->_ordenacao->obterHTML('seletor_socioeconomico', Formulario::HTML_CAMPO, true); ?></td>
<?
}
?>
<?
if ( $this->_ordenacao->campo('seletor_serie') !== false ) {
?>
      <td nowrap="nowrap" class="rlt_cd_TituloDireita" style="padding: 16px;"><strong><?= $this->_ordenacao->obterHTML('seletor_serie', Formulario::HTML_LABEL, true); ?></strong><br /><?= $this->_ordenacao->obterHTML('seletor_serie', Formulario::HTML_CAMPO, true); ?></td>
<?
}
?>
    </tr>
</table>

<?
if (count($this->_ordenacao->obterItensMultiplaOrdenacao()) > 0 || count($this->_ordenacao->obterPosicoes()) > 1) {
?>
<div class="rlt_scd_Espacador">
<table width="100%" border="0" align="center" cellpadding="4" cellspacing="0" class="rlt_scd">
  <tr>
<?
if ( count($this->_ordenacao->obterItensMultiplaOrdenacao()) ) {
?>
    <td width="1%" rowspan="2" valign="top" class="rlt_scd_EspacadorVertical" style="padding: 8px;">
<?
	for ( $i = 0; $i < count($this->_ordenacao->obterItensMultiplaOrdenacao()); $i++ ) {
		$espacamento = 16 * $i;
		if ( $i > 4 )
			$espacamento = 16 * 4;
	
		echo '<table border="0" cellpadding="0" cellspacing="2" style="padding-left: '. $espacamento .'px;"><tr>';
		echo '<td nowrap="nowrap">' . $this->_ordenacao->obterHTML( ($i == 0 ? 'ordenar_por' : 'multiplo_ordenar_por_' . $i) , Formulario::HTML_LABEL, true) . '<br />';
		echo $this->_ordenacao->obterHTML( ($i == 0 ? 'ordenar_por' : 'multiplo_ordenar_por_' . $i) , Formulario::HTML_CAMPO, true) . '</td>';
		echo '<td class="direita" nowrap="nowrap">' . $this->_ordenacao->obterHTML('multiplo_tipo_ordem_' . $i, Formulario::HTML_LABEL, true) . '<br />';
		echo $this->_ordenacao->obterHTML( ($i == 0 ? 'tipo_ordem' : 'multiplo_tipo_ordem_' . $i) , Formulario::HTML_CAMPO, true) . '</td>';
		echo '</tr></table>';
	}
?>	</td>
<?
}
?>
    <td valign="top" style="padding: 8px;">
		<table border="0" cellpadding="0" cellspacing="2" align="center">
<?
	$escondidos = array();
	foreach ($this->_ordenacao->obterPosicoes() as $i => $posicao) {
		$novaLinha = 0;
		foreach ($posicao as $j => $nomeCampo) {
			if ($this->_ordenacao->campo($nomeCampo)->obter('html_tipo') != Campo::HTML_ESCONDIDO) {
				if ($novaLinha === 0) {
					echo '<tr>';
					$novaLinha = 1;
				} else if ( $novaLinha === 1 ) {
					$novaLinha = 2;
				}

				echo '<td '. ($j > 0 ? 'class="direita"' : '') .'>' . $this->_ordenacao->obterHTML($nomeCampo, Formulario::HTML_LABEL, true) . '<br />';
				echo $this->_ordenacao->obterHTML($nomeCampo, Formulario::HTML_CAMPO, true) . '</td>';
			} else {
				$escondidos[] = $nomeCampo;
			}

			if ($novaLinha === 2) {
				echo '</tr>';
				$novaLinha = -1;
			}
		}
		
		if ( count($this->_ordenacao->obterPosicoes()) == $i ) {
			foreach ($escondidos as $nomeCampo) {
				echo $this->_ordenacao->obterHTML($nomeCampo, Formulario::HTML_CAMPO, true);
			}
		}
	}
?>
	</table>
	</td>
  </tr>
  <tr>
    <td align="right" valign="bottom" style="padding: 8px; padding-top: 0px;"><?= $this->_ordenacao->obterHTML('atualizar', Formulario::HTML_CAMPO, true); ?></td>
  </tr>
</table>
</div>
<?= $this->_htmlAnexo; ?>
<?
}
?>
<?= $this->_ordenacao->obterHTML(null, Formulario::HTML_FORM_FIM); ?>