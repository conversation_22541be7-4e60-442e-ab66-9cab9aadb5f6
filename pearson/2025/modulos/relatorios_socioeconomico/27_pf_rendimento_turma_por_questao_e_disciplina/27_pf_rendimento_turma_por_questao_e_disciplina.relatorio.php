<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_socioeconomico/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoTurmaPorQuestaoEDisciplina extends RelListPesquisaSEc {
	const QUESTAO_CORRETA_COR = '#ABFFFB';//'#8be37d'; //'#9ed0ff';
	const OPCAO_INVALIDA = 999;

	public $orientacao_PDF = ExportadorPDF::PAISAGEM;

	protected $_nomesTurmas = array();
	protected $_turmasMostrar = array();

	protected $_questoesPorDisciplina = array();

	protected $_dadosRendQuestao = array();
	protected $_modeloRendQuestao;

	protected $_dadosOpcaoResp = array();
	protected $_modeloOpcaoResp;
	protected $_numeroProposicoes = 0;

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false,
			'_instituicao' => false,
			'_turma' => false,
			'_turma_id' => null,
			'_portador' => false
		);

		$this->_modeloRendQuestao = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_questao' => array(),
			'rendimento_por_disciplina' => array(),
			'_rede' => false,
			'_instituicao' => false,
			'_turma' => false
		);

		$this->_modeloOpcaoResp = array(
			'nome' => null,
			'disciplina' => null,
			'opcoes_marcadas' => array(),
			'_opcao_correta' => null,
			'_disciplina_id' => null
		);

		$this->_config = array(
			'professor' => null
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado3 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '27_pf_rendimento_turma_por_questao_e_disciplina.relatorio.tabela.html.php';
		
		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::PAISAGEM);
	}

	public function prepararRelatorioParaPacotePDF ($id_turma, Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		include '27_pf_rendimento_turma_por_questao_e_disciplina.relatorio.tabela.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, ExportadorPDF::PAISAGEM, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR &&
				Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$this->_config['professor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( CarregadorUsuarioEspecifico::obterProfessor() == null )
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Professor inválido!');
			else
				$this->_config['professor'] = CarregadorUsuarioEspecifico::obterProfessor();
		}

		$this->_turmasMostrar = $this->_config['professor']->obterArrayTurmasComAulas();

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado3->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_socioeconomico',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorioSocioeconomico',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio_socioeconomico/_painel_relatorio_socioeconomico.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosSocioeconomico('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		if ( $this->_config['professor'] !== null )
			$this->_tituloCache .= '_pro'.$this->_config['professor']->obterID();

		
		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarRespostasPorItem();
		$this->_analizadorPesquisa->calcularRendimentoPorItemDosParticipantes();
		$this->_analizadorPesquisa->calcularRendimentoGlobalDosParticipantes();

		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado1->carregarInscritos(true);
		$this->_analizadorSimulado1->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado1->calcularRendimentoPorTurma();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorQuestao();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorQuestao();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorQuestao();
		$this->_analizadorSimulado1->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorDisciplina();

		$this->_analizadorSimulado2->carregarInscritos(true);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado2->limitarInscritosPorCor(array(4,5,6));
		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado2->calcularRendimentoPorTurma();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorTurmaPorQuestao();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorQuestao();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorQuestao();
		$this->_analizadorSimulado2->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorDisciplina();

		$estatistica = $this->_analizadorPesquisa->procEstatistica(1);
		$this->_analizadorPesquisa->limitarParticipantesPorQuartil1($estatistica['1quartil']);
		
		$pIDs = array();
		foreach ($this->_analizadorPesquisa->obterParticipantes() as $aIDsK => $aIDsV) {
			if(array_key_exists($aIDsV->obterAluno()->obterID(), $this->_analizadorPesquisa->obterNaoResponderam()) !== false){
				continue;
			}
			$pIDs[] = $aIDsV->obterAluno()->obterID();
		}

		$this->_analizadorSimulado3->carregarInscritos(true);
		$this->_analizadorSimulado3->limitarInscritosPorAlunos($pIDs);
		$this->_analizadorSimulado3->carregarRespostasDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado3->calcularRendimentoPorDisciplinaDosInscritos();
		$this->_analizadorSimulado3->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado3->calcularRendimentoPorTurma();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado3->calcularRendimentoPorSerie();
		$this->_analizadorSimulado3->calcularRendimentoPorTurmaPorQuestao();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorQuestao();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorQuestao();
		$this->_analizadorSimulado3->calcularRendimentoPorTurmaPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado3->calcularRendimentoPorSeriePorDisciplina();

		// NOMES TURMAS
		foreach ( $this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			if (!isset($this->_nomesTurmas[$tID]))
				$this->_nomesTurmas[$tID] = ProvaFloripa::obterTurmaComNome($tNome);
		}

		asort($this->_nomesTurmas, SORT_LOCALE_STRING);

		// QUESTOES POR DISCIPLINA
		foreach ( $this->_analizadorSimulado1->simuladoQuestoesPorID as $qID => &$q) {
			$dID = $q->obterDisciplina()->obterID();

			if (!isset($this->_questoesPorDisciplina[$dID]))
				$this->_questoesPorDisciplina[$dID] = array();

			$this->_questoesPorDisciplina[$dID][] = $qID;
		}

				

		// RENDIMENTO POR DISCIPLINA
		// TURMAS
		foreach($this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;
			$dado['_turma_id'] = $tID;

			if (isset($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['t'.$tID][1] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado1->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modelo;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')'; //$instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['i'.$instID][1] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) . ')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['s'.$sID][1] = $dado;
		}

		// RENDIMENTO POR QUESTÃO
		// TURMAS
		foreach($this->_analizadorSimulado1->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorQuestao[$tID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorQuestao[$tID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['t'.$tID][1] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado1->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) . ')'; //$instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorQuestao[$instID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorQuestao[$instID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['i'.$instID][1] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado1->rendimentoPorSeriePorQuestao[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorQuestao[$sID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['s'.$sID][1] = $dado;
		}

		// OPÇÃO DE RESPOSTA MARCADA
		$this->_numeroProposicoes = 0;
		foreach ( $this->_analizadorSimulado1->simuladoQuestoesPorID as &$q) {
			if ($q->obterNumeroProposicoes() > $this->_numeroProposicoes)
				$this->_numeroProposicoes = $q->obterNumeroProposicoes();
		}

		$opcoesValidas = array(); $kMaximo = pow(2, $this->_numeroProposicoes);
		foreach (MultiplaEscolha::$opcoes as $k => $v) {
			if ($k < $kMaximo) {
				$this->_modelo['opcoes_marcadas'][$k] = 0;
				$opcoesValidas[$k] = $v;
			} else {
				$this->_modelo['opcoes_marcadas'][self::OPCAO_INVALIDA] = 0;
				break;
			}
		}

		foreach ( $this->_analizadorSimulado1->simuladoQuestoesPorID as $qID => &$q) {
			if($q->obterTipo() != 'DISCURSIVA') {
				$dado = $this->_modeloOpcaoResp;

				$dado['nome'] = !$q->foiAnulada() ? $q->obterIdentificador() : $q->obterIdentificador() . ' (anulada)';
				$dado['disciplina'] = $q->obterDisciplina()->obterNome();
				$dado['_disciplina_id'] = $q->obterDisciplina()->obterID();

				// acha opção correta
				foreach ($q->obterGabaritos() as $g)
					$dado['_opcao_correta'] = (int)$g->obterValor();

				foreach ($this->_analizadorSimulado1->respostas as $iID => $respostasMarcadas) {
					// não entra na média se for portador de necessidades especiais
					if ($this->_analizadorSimulado1->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
						continue;

					if ($this->_analizadorSimulado1->inscritos[$iID]->obterNaoAlfabetico())
						continue;

					// não entra se não for da turma
					$tID = @$this->_analizadorSimulado1->inscritos[$iID]->obterAluno()->obterTurma()->obterID();
					if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

					foreach ($respostasMarcadas as $subQuestaoID => $resposta) {
						if ($subQuestaoID != $qID) continue;

						if (isset($resposta['valor']) && isset($opcoesValidas[(int)$resposta['valor']]))
							@$dado['opcoes_marcadas'][(int)$resposta['valor']]++;
						else
							@$dado['opcoes_marcadas'][self::OPCAO_INVALIDA]++;
					}
				}

				$total = array_sum($dado['opcoes_marcadas']);
				if ($total > 0) {
					foreach ($dado['opcoes_marcadas'] as &$marcacoes) {
						$marcacoes = $marcacoes . ' ( <strong>' . round(100 * $marcacoes / $total) . '%</strong> )';
					}
				}

				$this->_dadosOpcaoResp['q'.$qID][1] = $dado;
			}
		}		

		// RENDIMENTO POR DISCIPLINA
		// TURMAS
		foreach($this->_analizadorSimulado2->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;
			$dado['_turma_id'] = $tID;

			if (isset($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['t'.$tID][2] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado2->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modelo;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')'; //$instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['i'.$instID][2] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) . ')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['s'.$sID][2] = $dado;
		}

		// RENDIMENTO POR QUESTÃO
		// TURMAS
		foreach($this->_analizadorSimulado2->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado2->rendimentoPorTurmaPorQuestao[$tID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorTurmaPorQuestao[$tID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['t'.$tID][2] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado2->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) . ')'; //$instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorQuestao[$instID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorQuestao[$instID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['i'.$instID][2] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado2->rendimentoPorSeriePorQuestao[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorQuestao[$sID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['s'.$sID][2] = $dado;
		}

		// OPÇÃO DE RESPOSTA MARCADA
		$this->_numeroProposicoes = 0;
		foreach ( $this->_analizadorSimulado2->simuladoQuestoesPorID as &$q) {
			if ($q->obterNumeroProposicoes() > $this->_numeroProposicoes)
				$this->_numeroProposicoes = $q->obterNumeroProposicoes();
		}

		$opcoesValidas = array(); $kMaximo = pow(2, $this->_numeroProposicoes);
		foreach (MultiplaEscolha::$opcoes as $k => $v) {
			if ($k < $kMaximo) {
				$this->_modelo['opcoes_marcadas'][$k] = 0;
				$opcoesValidas[$k] = $v;
			} else {
				$this->_modelo['opcoes_marcadas'][self::OPCAO_INVALIDA] = 0;
				break;
			}
		}

		foreach ( $this->_analizadorSimulado2->simuladoQuestoesPorID as $qID => &$q) {
			if($q->obterTipo() != 'DISCURSIVA') {
				$dado = $this->_modeloOpcaoResp;

				$dado['nome'] = !$q->foiAnulada() ? $q->obterIdentificador() : $q->obterIdentificador() . ' (anulada)';
				$dado['disciplina'] = $q->obterDisciplina()->obterNome();
				$dado['_disciplina_id'] = $q->obterDisciplina()->obterID();

				// acha opção correta
				foreach ($q->obterGabaritos() as $g)
					$dado['_opcao_correta'] = (int)$g->obterValor();

				foreach ($this->_analizadorSimulado2->respostas as $iID => $respostasMarcadas) {
					// não entra na média se for portador de necessidades especiais
					if ($this->_analizadorSimulado2->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
						continue;

					if ($this->_analizadorSimulado2->inscritos[$iID]->obterNaoAlfabetico())
						continue;

					// não entra se não for da turma
					$tID = @$this->_analizadorSimulado2->inscritos[$iID]->obterAluno()->obterTurma()->obterID();
					if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

					foreach ($respostasMarcadas as $subQuestaoID => $resposta) {
						if ($subQuestaoID != $qID) continue;

						if (isset($resposta['valor']) && isset($opcoesValidas[(int)$resposta['valor']]))
							@$dado['opcoes_marcadas'][(int)$resposta['valor']]++;
						else
							@$dado['opcoes_marcadas'][self::OPCAO_INVALIDA]++;
					}
				}

				$total = array_sum($dado['opcoes_marcadas']);
				if ($total > 0) {
					foreach ($dado['opcoes_marcadas'] as &$marcacoes) {
						$marcacoes = $marcacoes . ' ( <strong>' . round(100 * $marcacoes / $total) . '%</strong> )';
					}
				}

				$this->_dadosOpcaoResp['q'.$qID][2] = $dado;
			}
		}		

		// RENDIMENTO POR DISCIPLINA
		// TURMAS
		foreach($this->_analizadorSimulado3->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;
			$dado['_turma_id'] = $tID;

			if (isset($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado3->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['t'.$tID][3] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado3->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modelo;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')'; //$instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado3->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['i'.$instID][3] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado3->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) . ')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados['s'.$sID][3] = $dado;
		}

		// RENDIMENTO POR QUESTÃO
		// TURMAS
		foreach($this->_analizadorSimulado3->nomesTurmas as $tID => $tNome) {
			if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);
			$dado['_turma'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado3->rendimentoPorTurmaPorQuestao[$tID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorTurmaPorQuestao[$tID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado3->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['t'.$tID][3] = $dado;
		}

		// ESCOLAS
		foreach($this->_analizadorSimulado3->nomesInstituicoes as $instID => $instNome) {
			if (Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil() != $instID) continue;

			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = 'ESCOLA ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) . ')'; //$instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado3->rendimentoPorInstituicaoPorQuestao[$instID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorInstituicaoPorQuestao[$instID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado3->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['i'.$instID][3] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado3->nomesSeries as $sID => $sNome) {
			$dado = $this->_modeloRendQuestao;

			$dado['nome'] = 'REDE ('. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ) .')';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado3->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( isset( $this->_analizadorSimulado3->rendimentoPorSeriePorQuestao[$sID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorQuestao[$sID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado3->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dadosRendQuestao['s'.$sID][3] = $dado;
		}

		// OPÇÃO DE RESPOSTA MARCADA
		$this->_numeroProposicoes = 0;
		foreach ( $this->_analizadorSimulado3->simuladoQuestoesPorID as &$q) {
			if ($q->obterNumeroProposicoes() > $this->_numeroProposicoes)
				$this->_numeroProposicoes = $q->obterNumeroProposicoes();
		}

		$opcoesValidas = array(); $kMaximo = pow(2, $this->_numeroProposicoes);
		foreach (MultiplaEscolha::$opcoes as $k => $v) {
			if ($k < $kMaximo) {
				$this->_modelo['opcoes_marcadas'][$k] = 0;
				$opcoesValidas[$k] = $v;
			} else {
				$this->_modelo['opcoes_marcadas'][self::OPCAO_INVALIDA] = 0;
				break;
			}
		}

		foreach ( $this->_analizadorSimulado3->simuladoQuestoesPorID as $qID => &$q) {
			if($q->obterTipo() != 'DISCURSIVA') {
				$dado = $this->_modeloOpcaoResp;

				$dado['nome'] = !$q->foiAnulada() ? $q->obterIdentificador() : $q->obterIdentificador() . ' (anulada)';
				$dado['disciplina'] = $q->obterDisciplina()->obterNome();
				$dado['_disciplina_id'] = $q->obterDisciplina()->obterID();

				// acha opção correta
				foreach ($q->obterGabaritos() as $g)
					$dado['_opcao_correta'] = (int)$g->obterValor();

				foreach ($this->_analizadorSimulado3->respostas as $iID => $respostasMarcadas) {
					// não entra na média se for portador de necessidades especiais
					if ($this->_analizadorSimulado3->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
						continue;

					if ($this->_analizadorSimulado3->inscritos[$iID]->obterNaoAlfabetico())
						continue;

					// não entra se não for da turma
					$tID = @$this->_analizadorSimulado3->inscritos[$iID]->obterAluno()->obterTurma()->obterID();
					if (!array_key_exists($tID, $this->_turmasMostrar)) continue;

					foreach ($respostasMarcadas as $subQuestaoID => $resposta) {
						if ($subQuestaoID != $qID) continue;

						if (isset($resposta['valor']) && isset($opcoesValidas[(int)$resposta['valor']]))
							@$dado['opcoes_marcadas'][(int)$resposta['valor']]++;
						else
							@$dado['opcoes_marcadas'][self::OPCAO_INVALIDA]++;
					}
				}

				$total = array_sum($dado['opcoes_marcadas']);
				if ($total > 0) {
					foreach ($dado['opcoes_marcadas'] as &$marcacoes) {
						$marcacoes = $marcacoes . ' ( <strong>' . round(100 * $marcacoes / $total) . '%</strong> )';
					}
				}

				$this->_dadosOpcaoResp['q'.$qID][3] = $dado;
			}
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>