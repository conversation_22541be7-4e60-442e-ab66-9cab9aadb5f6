<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => 'Rendimento na prova e nas disciplinas',
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => true,
	'quebra_invisivel' => true
);
$titulo = 'Rendimento na prova e nas disciplinas / Opção de resposta marcada'.'<br />';
$titulo .= ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome()).' - '.$this->_seletorPesquisas->socioeconomico->obterNome();


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento na prova(produção textual) e seus aspectos';

}
$tabela['titulo']  = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
	%s<br />
	%s - %s',
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome(),
	ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() ),
	ProvaFloripa::obterTurmaComNome( @$this->_nomesTurmas[array_pop(array_keys($this->_turmasMostrar))] )
);

ob_start();
?>
	<tr>
		<th style=""></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Global</th>
		<th style="background-color:#FFFFFF;"></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Grupo 2 - Racial</th>
		<th style="background-color:#FFFFFF;"></th>
		<th colspan=<?= count($this->_analizadorSimulado1->nomesPequenosDisciplinas)+1; ?>>Grupo 2 - SE</th>
	</tr>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="25%"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
		<?
		if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
		?>
				<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
		<?
			}
		}
		?>
		<th style="background-color:#FFFFFF;"></th>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
		<?
		if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado2->nomesDisciplinas as $dID => $dNome ) {
		?>
				<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
		<?
			}
		}
		?>
		<th style="background-color:#FFFFFF;"></th>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
		<?
		if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado3->nomesDisciplinas as $dID => $dNome ) {
		?>
				<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
		<?
			}
		}
		?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr <?= $d[1]['_rede'] || $d[1]['_instituicao'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= @$d[1]['nome']; ?> <?= $d[1]['_portador'] ? '*' : ''; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d[1]['rendimento']; ?>%</td><? } ?>
		<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[1]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d[1]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[1]['_rede'] ? 'bgcolor="#dddddd"' : ($d[1]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
			}
		}
		?>
		<td></td>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[2]['_rede'] ? 'bgcolor="#dddddd"' : ($d[2]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d[2]['rendimento']; ?>%</td><? } ?>
		<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[2]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[2]['_rede'] ? 'bgcolor="#dddddd"' : ($d[2]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d[2]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[2]['_rede'] ? 'bgcolor="#dddddd"' : ($d[2]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
			}
		}
		?>
		<td></td>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d[3]['_rede'] ? 'bgcolor="#dddddd"' : ($d[3]['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d[3]['rendimento']; ?>%</td><? } ?>
		<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d[3]['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d[3]['_rede'] ? 'bgcolor="#dddddd"' : ($d[3]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d[3]['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d[3]['_rede'] ? 'bgcolor="#dddddd"' : ($d[3]['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
			}
		}
		?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;












if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => 'Rendimento por questão',
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => true,
	'quebra_invisivel' => true
);

//$tabela['titulo'] = '';

ob_start();
?>
	<tr>
		<th style=""></th>
		<th style=""></th>
		<th style=""></th>
<?
if ( count($this->_dadosRendQuestao) ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		if (!count($this->_questoesPorDisciplina[$dID])) continue;
?>
		<th colspan="<?= count($this->_questoesPorDisciplina[$dID]) /*+ 1*/; ?>" align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
}
?>
	</tr>
	<tr>
	<th style="background-color:#FFFFFF;"></th>
		<th><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th>
		<th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th>
<?
if ( count($this->_dadosRendQuestao) ) {
	foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
		if (!count($this->_questoesPorDisciplina[$dID])) continue;

		foreach($this->_questoesPorDisciplina[$dID] as $qID) {
?>
			<th align="center" nowrap="nowrap"> <?= $this->_analizadorSimulado1->nomesQuestoes[$qID] ;?> </th>
<?
		}
	}
}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dadosRendQuestao) ) {
	$i = 0;
	foreach ( $this->_dadosRendQuestao as $k => $dd) {
		foreach ($dd as $dk => $d) {
			if($dk == 1){
				$tipo = 'Global';
			}
			elseif($dk == 2){
				$tipo = 'Grupo 2 - Racial';
			}
			elseif($dk == 3){
				$tipo = 'Grupo 2 - SE';
			}

			if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
				echo $tabela['th'];
?>
	<tr <?= $d['_rede'] || $d['_instituicao'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= $tipo; ?></td>
		<td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= @$d['nome']; ?></td>
		<td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d['rendimento']; ?></td>
<?
			foreach ( $this->_analizadorSimulado1->nomesPequenosDisciplinas as $dID => $dNome ) {
				if (!count($this->_questoesPorDisciplina[$dID])) continue;

				foreach($this->_questoesPorDisciplina[$dID] as $qID) {
					if (isset( $d['rendimento_por_questao'][$qID] ))
						echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .' align="center">'. $d['rendimento_por_questao'][$qID] .'</td>';
					else
						echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : '')) .'>&nbsp;</td>';
				}
			}
?>
	</tr>
<?
			$i++;
		}
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
//$tabela['td'] = null;
	//<div align="center" style="margin-top: 5px;">Valores em porcentagem</div>
?>
	<div style="margin-top: 15px; border: 1px #cccccc solid; padding: 10px;">
<?
foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
	if (!count($this->_questoesPorDisciplina[$dID])) continue;

	echo ''. $dNome . ': ';
	$questoesTemp = array();
	foreach ( $this->_questoesPorDisciplina[$dID] as $qID)
		$questoesTemp[] = $this->_analizadorSimulado1->nomesQuestoes[$qID];

	echo implode(', ', $questoesTemp) . '<br />';
}
?>
	</div>
<?
$tabela['extra'] = ob_get_clean(); $tabela['extra'] = null;
$relHTML[] = $tabela;











if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => 'Opção de resposta marcada',
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? false : false
);


ob_start();
?>
	<tr>
		<th style=""></th>
		<th style=""></th>
		<th colspan=<?= $this->_numeroProposicoes; ?>>Global</th>
		<th style="background-color:#FFFFFF;"></th>
		<th colspan=<?= $this->_numeroProposicoes; ?>>Grupo 2 - Racial</th>
		<th style="background-color:#FFFFFF;"></th>
		<th colspan=<?= $this->_numeroProposicoes; ?>>Grupo 2 - SE</th>
	</tr>
	<tr>
		<th width="25%"> <?= $this->obterBotaoAlterador('nome', false, 'Questão') ;?> </th>
		<th width="25%"> <?= $this->obterBotaoAlterador('disciplina', false, 'Disciplina') ;?> </th>
<?
	$j = 0;
	foreach (MultiplaEscolha::$opcoes as $k => $v) {
		if ($j++ < $this->_numeroProposicoes)
			echo '<th align="center" nowrap="nowrap"> '. $v .' </th>';
	}
?>
<th style="background-color:#FFFFFF;"></th>
<?
	$j = 0;
	foreach (MultiplaEscolha::$opcoes as $k => $v) {
		if ($j++ < $this->_numeroProposicoes)
			echo '<th align="center" nowrap="nowrap"> '. $v .' </th>';
	}
?>
<th style="background-color:#FFFFFF;"></th>
<?
	$j = 0;
	foreach (MultiplaEscolha::$opcoes as $k => $v) {
		if ($j++ < $this->_numeroProposicoes)
			echo '<th align="center" nowrap="nowrap"> '. $v .' </th>';
	}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dadosOpcaoResp) ) {
	$i = 0;
	foreach ( $this->_dadosOpcaoResp as $k => &$d ) {
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td align="center" nowrap="nowrap"> <?= @$d[1]['nome']; ?> </td>
		<td align="center" nowrap="nowrap"> <?= @$d[1]['disciplina']; ?> </td>
<?
		$j = 0;
		foreach (MultiplaEscolha::$opcoes as $k => $v) {
			if ($j++ < $this->_numeroProposicoes){
				if(@$d[1]['opcoes_marcadas'][$k] != null){
					echo '<td '. ($d[1]['_opcao_correta'] == $k ? 'bgcolor="'. self::QUESTAO_CORRETA_COR .'"' : '') .' align="center" nowrap="nowrap">'. @$d[1]['opcoes_marcadas'][$k] .'</td>';
				}else{
					echo '<td '. ($d[1]['_opcao_correta'] == $k ? 'bgcolor="'. self::QUESTAO_CORRETA_COR .'"' : '') .' align="center" nowrap="nowrap">0 (<span style=" font-weight: bold;"> 0% </span>)</td>';
				}
			}
		}
?>
<td></td>
<?
		$j = 0;
		foreach (MultiplaEscolha::$opcoes as $k => $v) {
			if ($j++ < $this->_numeroProposicoes){
				if(@$d[2]['opcoes_marcadas'][$k] != null){
					echo '<td '. ($d[2]['_opcao_correta'] == $k ? 'bgcolor="'. self::QUESTAO_CORRETA_COR .'"' : '') .' align="center" nowrap="nowrap">'. @$d[2]['opcoes_marcadas'][$k] .'</td>';
				}else{
					echo '<td '. ($d[2]['_opcao_correta'] == $k ? 'bgcolor="'. self::QUESTAO_CORRETA_COR .'"' : '') .' align="center" nowrap="nowrap">0 (<span style=" font-weight: bold;"> 0% </span>)</td>';
				}
			}
		}
?>
<td></td>
<?
		$j = 0;
		foreach (MultiplaEscolha::$opcoes as $k => $v) {
			if ($j++ < $this->_numeroProposicoes){
				if(@$d[3]['opcoes_marcadas'][$k] != null){
					echo '<td '. ($d[3]['_opcao_correta'] == $k ? 'bgcolor="'. self::QUESTAO_CORRETA_COR .'"' : '') .' align="center" nowrap="nowrap">'. @$d[3]['opcoes_marcadas'][$k] .'</td>';
				}else{
					echo '<td '. ($d[3]['_opcao_correta'] == $k ? 'bgcolor="'. self::QUESTAO_CORRETA_COR .'"' : '') .' align="center" nowrap="nowrap">0 (<span style=" font-weight: bold;"> 0% </span>)</td>';
				}
			}
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div style="margin-top: 15px; border: 1px #cccccc solid; padding: 5px;">
	<table align="center" cellpadding="1" cellspacing="2" class="rlt_tabela_extra">
		<tr>
			<td valign="middle" width="60px" nowrap="nowrap" style="background-color: <?= self::QUESTAO_CORRETA_COR; ?>; text-align: center; color: #000000;"> T ( <strong>YY%</strong> ) </td>
			<td valign="middle" nowrap="nowrap">Opção correta</td>
			<td width="20px" nowrap="nowrap"> &nbsp; </td>
			<td valign="middle" width="60px" nowrap="nowrap" style="text-align: center; color: #000000; border: 1px solid #000000;"> T ( <strong>YY%</strong> ) </td>
			<td nowrap="nowrap">T - número de alunos que maracaram a opção na turma<br />YY - proporção de alunos que maracaram a opção na turma</td>
		</tr>
	</table>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>