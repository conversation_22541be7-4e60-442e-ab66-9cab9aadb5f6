<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false
);

$tabela['titulo'] = $this->_relatorio->obterNome().'<br />'.$this->_seletorPesquisas->socioeconomico->obterNome();
$tabela['descricao'] = sprintf(
	'<strong>%s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio())
);

ob_start();

$tabela['th'] = ob_get_clean(); ob_start();

$tabela['td'] = ob_get_clean(); $tabela['td'] = null; ob_start();
?>
	<div style="margin-top: 10px; border: 0px #cccccc solid; padding: 5px; text-align: center;">
		<?php
			if ( count($this->_imagem) ) {
				foreach ($this->_imagem as $ik => $iv) {
					echo '<h3>'.$iv['ttl'].'</h3>';
					if(array_key_exists('img', $iv)){
						echo '<img src="upload/graficos/'.$iv['img'].'" border="0" />';
					}
					else{
						echo '<p>Sem dados!</p>';
					}
					echo '<hr style="border: 1px solid #eee;"/>';
				}
			}
		?>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>