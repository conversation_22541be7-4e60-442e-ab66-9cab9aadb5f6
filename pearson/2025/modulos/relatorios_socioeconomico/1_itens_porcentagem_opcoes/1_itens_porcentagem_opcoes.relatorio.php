<?
if (!defined('CORE_INCLUIDO')) exit(); 

Core::incluir('RelListPesquisaSEc', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

class Rel_1ItensPorcentagemOpcoes extends RelListPesquisaSEc {
	public function __construct () {
		parent::__construct();

		$this->gtCor = '#000000';

		$this->opcoesCores = array(
			'0'=>'#eeeeee',
			'1'=>'#f1501f',
			'2'=>'#f4d826',
			'4'=>'#8be37d',
			'8'=>'#539e15',
			'16'=>'#61A9F3',
			'32'=>'#1173d6',
			'64'=>'#7c10d3',
		);

		$this->_dados = null;

		$this->_imagem = array();

		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '1_itens_porcentagem_opcoes.relatorio.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF();
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao, true);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();
		$this->_seletorPesquisas->ajustarPesquisaSelecionado();

		$this->_obterDados();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '1_itens_porcentagem_opcoes.relatorio.html.php';

		include 'modulos/relatorios_socioeconomico/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _obterDados () {
		if ( $this->_seletorPesquisas->socioeconomico->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->socioeconomico);

		$this->_obterDadosInscricoes();

		$this->gerarGrafico();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_socioeconomico/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_socioeconomico/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorPesquisas = new MSeletorSocioeconomico($this->_ordenacao, true);
		$this->_seletorPesquisas->configurarCampoSeletorSocioeconomico();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorPesquisas->ajustarSocioeconomicoSelecionado();

		/*$this->_seletorSeries = new MSeletorSerie($this->_ordenacao, true);
		$this->_seletorSeries->fixarPesquisa($this->_seletorPesquisas->pesquisa);
		$this->_seletorSeries->configurarCampoSeletorSeries();
		$this->_seletorSeries->ajustarSeriePesquisaSelecionada();*/
	}

	protected function _obterDadosInscricoes () { 
		$this->_analizadorPesquisa->carregarParticipantes();
		
		//$this->_analizadorPesquisa->limitarParticipantesPorSerieID($this->_seletorSeries->serieID);

		if(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR){
			$poloID = Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();
			$this->_analizadorPesquisa->limitarParticipantesPorPoloID($poloID);
		}

		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarRespostasPorItem();

		$dado = array();
		$tapOIPII = $this->_analizadorPesquisa->obterItensPesquisaII();
		foreach ($tapOIPII as $iid => $iclass) {
			$iclass->carregarCompetencias();

			$clista = '';
			$last_key = end(array_keys($iclass->obterCompetencias()));
			foreach ($iclass->obterCompetencias() as $ock => $ocv) {
				$clista .= $ocv->obterNome();
				if ($ock !== $last_key) {
			        $clista .= ', ';
			    }
			}

			$dado[$iid]['identificador'] = $iclass->obterIdentificador();
			$dado[$iid]['enunciado'] = $iclass->obterEnunciado();
			$dado[$iid]['competencias'] = $clista;
			$dado[$iid]['temResposta'] = 0;

			$dado[$iid]['opcoes'][0]['nome'] = 'EM BRANCO';
			$dado[$iid]['opcoes'][0]['qtd'] = 0;
			foreach ($iclass->obterOpcoes() as $k1 => $oclass) {
				$dado[$iid]['opcoes'][$oclass->obterNumero()]['nome'] = $oclass->obterTexto();
				$dado[$iid]['opcoes'][$oclass->obterNumero()]['qtd'] = 0;
			}

			foreach ($this->_analizadorPesquisa->obterRespostasPorItem() as $pid => $participantes) {
				$rval = $participantes[$iid];

				if($rval !== NULL){
					$dado[$iid]['opcoes'][$rval]['qtd'] += 1;
					$dado[$iid]['temResposta'] = 1;
				}
				else{
					$dado[$iid]['opcoes'][0]['qtd'] += 1;
				}
			}
		}

		$this->_dados = $dado;
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_pie.php';
		require_once 'jpgraph/jpgraph_pie3d.php';

		foreach ($this->_dados as $dk => $dv) {
			$this->_imagem[$dk]['ttl'] = '<p style="font-size: 15px;">'.strip_tags($dv['enunciado']).'</p>';

			if(!$dv['temResposta']){ continue; }

			$titulo = CACHE_PREFIXO.'_r'.$this->_relatorio->obterID().'_p'.$this->_seletorPesquisas->socioeconomico->obterID().'_i'.$dk.'.png';
			$this->_imagem[$dk]['img'] = $titulo;

			$grafico = new PieGraph(500, 500, $titulo, 0, false);
			$grafico->SetAntiAliasing(true);
			$grafico->setFrame(false);

			$dados = array();
			$labels = array();
			$legends = array();
			$labelsCor = array();
			foreach ($dv['opcoes'] as $ok => $ov) {
				$labels[] = $ov['qtd']." (%d%%)";
				$dados[] = $ov['qtd'];
				$legends[] = $ov['nome'].' - '.$ov['qtd']." (%d%%)";
				$labelsCor[] = $this->opcoesCores[$ok];
			}

			$dados = RelListPesquisaSEc::calcularPorcentagemPerfeita($dados);

			$pie = new PiePlot( $dados );
			$grafico->Add($pie);
			
			$pie->SetGuideLines(true, true, true);
			$pie->SetGuideLinesAdjust(1.5);
			$pie->value->SetFont(FF_ARIAL,FS_BOLD, 12);
			$pie->value->SetColor( $this->gtCor);
			$pie->SetLabelType(PIE_VALUE_ADJPER);
			$pie->SetLabels( $labels, 1.0 );
			$pie->SetSliceColors( $labelsCor );
			$pie->SetSize(0.20);
			$pie->SetStartAngle(90);
			$pie->SetCenter(0.5, 0.4);

			$grafico->legend->Pos(0,0.999,'left','bottom');
			$grafico->legend->SetLayout(LEGEND_VERT);
			$grafico->legend->SetFillColor('white');
			$grafico->legend->SetFrameWeight(0);
			$grafico->legend->SetShadow(false);
			$grafico->legend->SetReverse(true);
			$grafico->legend->SetFont(FF_ARIAL,FS_NORMAL, 9);

			$pie->SetLegends($legends); 

			$grafico->Stroke();
		}
	}
}

?>