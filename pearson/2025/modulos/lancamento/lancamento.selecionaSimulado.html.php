<style>
	img {
		border:none;
	}

	#logo-av{
		float:left;
	}

	#logo-pb{
		float:right;
		margin-right: 10px;
	}

	.header{
		padding: 35px;
		height: 20%;
	}

	.content{
		display: block;
		height: auto;
	}

	.left{
		display: block;
	    height: 28%;
	}

	.left p {
		font-family: helvetica;
	    font-size: 30px;
	    font-weight: bold;
	    color: #454545;
	    line-height: 150px;
	}

	.right{
		display: block;
	    float: left;
	    height: auto;
	    width: 100%;
	}

	.back{
		display: block;
	    float: left;
	    height: auto;
	    width: 100%;
	    margin-top:50px;
	}

	a#back {
		height: 62px;
	}

	a#back img{
		float: left;
		height: 62px;
	}

	a#back span{
		float: left;
	    font-size: 30px;
	    height: 62px;
	    line-height: 55px;
	    margin-left: 15px;
	    overflow: hidden;
	}

	a#back span.data{
		float: none;
	    font-size: 12px;
	    line-height: 0; 
	    overflow: hidden;
	    margin-left:-70px;
	}

	a.button img{
		float: left;
		height: 100px;
	}

	a.button span{
		float: left;
	    font-size: 60px;
	    height: 98px;
	    line-height: 100px;
	    margin-left: 20px;
	    overflow: hidden;
	}

	a.button,
	button,
	input[type="submit"],
	input[type="reset"],
	input[type="button"] {
		background-color: #07003B;
		width: 120px;
		height: 40px;
		border-radius: 8px 8px 8px 8px;
		color: #FFFFFF;
		display: inline-block;
		font-size: 14px;
		font-weight: lighter;
		text-decoration: none;
		text-shadow: 0 1px rgba(255, 255, 255, .75);
		cursor: pointer;
		margin: 20px;
		font-family: "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; }

	a.button:hover,
	button:hover,
	input[type="submit"]:hover,
	input[type="reset"]:hover,
	input[type="button"]:hover {
	  	color: #FFFFFF;
		border: 0;
		background-color: #8B008B;
		-webkit-transition: background-color 0.7s ease;
		-moz-transition: background-color 0.7s ease;
		transition: background-color 0.7s ease;}

	a.button:active,
	button:active,
	input[type="submit"]:active,
	input[type="reset"]:active,
	input[type="button"]:active {
		border: 1px solid #666;
		background: #ccc; /* Old browsers */
		background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
		background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
		background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
		background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
		background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
		background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ }

	.button.full-width,
	button.full-width,
	input[type="submit"].full-width,
	input[type="reset"].full-width,
	input[type="button"].full-width {
		width: 100%;
		padding-left: 0 !important;
		padding-right: 0 !important;
		text-align: center; }

	.botao_avaliacao {
		width: 300px;
		height: 70px;
		color: #07003B;
		text-decoration: none;
		font-size: 16px;
		font-weight: bold;
		border: 1px solid #b8b6b6;
		border-radius: 10px 10px 10px 10px;
		background-image: -webkit-gradient(linear, left top, left bottom, color-stop(100%, #edeef2), color-stop(100%, #edeef2));
		background-color: #FFFFFF;
	}

	.botao_avaliacao:hover {
		width: 300px;
		height: 70px;
		appearance: none;
		border: 2px solid #b8b6b6;
		color: #000;
		text-decoration: none;
		font-size: 16px;
		font-weight: bold;
		border: 0;
		box-shadow: rgba(139,0,139) 0px 0px 10px;		
		border-radius: 10px 10px 10px 10px;
		background-image: #8B008B;
		-webkit-transition: box-shadow 0.7s ease;
		-moz-transition: box-shadow 0.7s ease;
		transition: box-shadow 0.5s ease;
	}
	

</style>

<center>
	<h3 style="font-size: 22px;">Escolha a avalia&ccedil;&atilde;o para lan&ccedil;ar as respostas: </h3>
		<?php
		if ( count($this->_dados) ) {
			foreach ( $this->_dados as $kd => $vd ) {
				$disc = $vd['disc'];

				if($disc == 'MAT'){
					$disc = 'Matem&aacute;tica';
					$img = 'mt_mini.png';
				}
				else if($disc == 'PORT'){
					$disc = 'Portugu&ecirc;s';
					$img = 'pt_mini.png';
				}
		?>

				<br>	
				<fieldset style="text-align: center; padding: 10px; width: 1100px; border-radius: 10px 10px 10px 10px; border: 4px solid #E8E8E8">
					<legend style="text-align:center; border-radius: 10px 10px 10px 10px; width: 70px; height: 22px">
						<label style="font-weight: bold; width: 30px; height: 10px; font-size: 16px; background-color: #07003B; color: #FFFFFF; border-radius: 5px 5px 5px 5px; padding: 8px; padding-top: 5px"><?php echo $kd;?></label>
					</legend>
					<div>
						<?php 
							foreach($vd as $s_nome => $s) { ?>	
								<form id="form_seletor_lancamento<?php echo$s_nome; ?>" name="form_seletor_lancamento<?php echo$s_nome; ?>" method="post" action="./?m=lancamento&a=lancamento" style="display:block; float: left;">
									<input type="hidden" name="id_instituicao_selecionada_lancamento" value="<?php echo$_intituicao; ?>" />
									<input type="hidden" name="id_simulado_selecionada_lancamento" value="<?php echo$s_nome; ?>" />
									<button class="botao_avaliacao" onclick="document.getElementById('form_seletor_lancamento<?php echo$s_nome; ?>').submit(); return false"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
educacao-online.png" width="50px" height="50px" style="float:left" /><?php echo $s['nome']; ?><label style="font-size:12px; cursor: pointer; font-weight: bold;"><br><?php echo $s['data']; ?></label></button>														
								</form>
							<?php } ?>
					</div>
				</fieldset>
		<?php
			}	
		}
		else{
		?>
		<p>N&atilde;o tem nenhum teste com alunos inscritos para lan&ccedil;amento de notas.</p>
		<?php
			}
		?>
	<br/><br/><br/>
		<input type="button" value="Voltar" onclick="javascript:history.go(-1)"/>
</center>