<?php if (isset($_SESSION['avaliacao_online']['inscricao_id'])) {
    $simulado['inscricao_id'] = $_SESSION['avaliacao_online']['inscricao_id'];
} else {
    // Fallback caso não tenha na sessão
    $simulado['inscricao_id'] = 0; // ou busque de outra forma
}

$simulado['usuario_id'] = $_SESSION['avaliacao_online']['usuario_id'] ?? 0;
$simulado['aluno_id'] = $_SESSION['avaliacao_online']['aluno_id'] ?? 0;
$simulado['simulado_id'] = $_SESSION['avaliacao_online']['simulado_id'] ?? 0;
$simulado['simulado_fase'] = $_SESSION['avaliacao_online']['simulado_fase'] ?? 1;

// No início do seu arquivo HTML, adicione o seguinte código PHP para obter a diretiva
$javaUrlBase = Core::$gerenciadorDeDiretivas->obterDiretiva('JAVA_URL_BASE');
$apiScan = Core::$gerenciadorDeDiretivas->obterDiretiva('API_SCAN');
?>

<script>
    const JAVA_URL_BASE = "<?php echo $javaUrlBase; ?>";
    const API_SCAN = "<?php echo $apiScan; ?>";
</script>


<script>
	//Remove a aba lateral de Contato
	jQuery(function(){
		jQuery('#contactable').remove();

		jQuery('html').attr({
			lang: 'en',
			translate: 'no'
		});
	});

	// Disable right-click context menu
	document.addEventListener('contextmenu', event => event.preventDefault());

	// Disable text selection
	document.addEventListener('selectstart', event => event.preventDefault());

	// Disable F12, FN, and CTRL keys
	/*document.addEventListener('keydown', function(event) {
		if (event.key === 'F12' || event.key === 'Control' || event.key === 'Fn') {
			event.preventDefault();
		}
	});*/

	document.onreadystatechange = function() { 
        if (document.readyState === "complete") { 
            jQuery('#modal_load').hide();
        } 
    }; 

	function isMobile() {
		return /Mobi|Android/i.test(navigator.userAgent);
	}

	if (isMobile()) {
		document.body.classList.add('mobile');
	} else {
		document.body.classList.add('desktop');
	}
</script>
<script src="https://kit.fontawesome.com/544f3b41ce.js" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>

<link rel="stylesheet" href="https://dyubuzjbgoyjh.cloudfront.net/simulados_online/css/simulado_avaliacao.css"/>
<link rel="stylesheet" href="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/swiper/package/css/swiper.css"/>

<div id="modal_load">
	<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/loader6.gif" style="width: 15%;display: block;margin: 15% auto;" />
</div>

<div lang="en" class="geral" translate="no">
	<div class="title">
		<?php echo$simulado['nome']; ?>
	</div>

	<div lang="en" translate="no" id="div_btn_start" class="questionario_general_start" style="padding: 20px;">
	<p id="fsalert" style="background: none repeat scroll 0 0 #8B008B;border: 1px dashed #ccc;border-radius: 3px 3px 3px 3px;color: #FFFFFF;font-size: 20px;padding: 10px;text-align: center;">
			Bem-vindo! &#128515;
			<br>
			Se tiver d&#250;vidas, chame o professor!
		</p>
		<table style="width: 100%; background-color: #FFFFFF;">
			<tr style="display: inline-block; align-itens: center; width: 70%;padding-left: 20%">
				<td style=" float: left;">
					<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/5836.jpg" width="500px" height="350px;"/>
				</td>
				<td style="padding-top: 30%">
					<h1 style="text-align: center; font-size: 22px;">
						<?php echo$simulado['tinicio']; ?>
					</h1>
				<div id="btn_start" style='cursor:pointer; padding-top: 80px;' class="wrap">
						
						<?php
							$txt_start = 'Iniciar';
							if(isset($simulado['todasRespostas'])){
								if(count($simulado['todasRespostas'])>0){
									$txt_start = 'Continuar';
								}
							}
						?>
						<button class="button"><?php echo$txt_start; ?> Avaliação</button>
						
					</div>
				<td>
				<br>
			<tr>
		</table>
	</div>

	<div id='cnt_questoes' class="conteudo" style="display:none;" translate="no" lang="en">
		<div>
			<table class='tbl_conteudo'>
				<tr>
					<td style="width: 33%;">
						<div class='num_questao'>
							N&ordm; <span id='num_questao'>1</span>
						</div>
					</td>
					<td style="width: 33%;">
						<div class="crono" style="">
							<div style="float:left;">
							    <img style="float:left;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/clock.png"/>
								<span id="crono_time">00:00:00</span>
								<input id="tempo-total" name="tempo-total" type="hidden" value="<?php echo$simulado['tempoTotalHide']; ?>"/>
							</div>
							<div style="float:right;">
							<img class="crono_start" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_play.png';?>"/>
							<img class="crono_stop" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_pause.png';?>"/>
							</div>
						</div>
					</td>
					<td style="width: 33%;">
						<div class="crono_questao">
							<?php
								$num = 1;
								foreach($simulado['questoes'] as $sqk => $sqv){
									$idq = $sqv->obterID();

									if(!isset($simulado['respostas'][$idq])){
										$qTime = '00:00:00';
									}
									else{
										$qTime = $simulado['respostas'][$idq]['tempo'];
									}
							?>
								<span style="display:none;" id="crono_questao<?php echo$num; ?>"><?php echo$qTime; ?></span>
							<?php
									$num++;
								}
							?>
						</div>
					</td>
				</tr>
			</table>
		</div>

		<div style="padding: 5px;">
			<!-- <table class='tbl_conteudo_questao'>
				<tr>
					<td> -->
					  <div class="swiper-container">
					    <div class="swiper-wrapper">

					    <?php
							$num = 1;
							foreach($simulado['questoes'] as $sqk => $sqv){
								$title = $sqv->obterEnunciado();
								$sqv->carregarProposicoes();
								$questoesTextos = $sqv->obterProposicoes();
								$ans = $sqv->obterTipo();
								$idq = $sqv->obterID();

								$resps = "";

								$qOptionsRespostas = "";
								$qTextosRespostas = "";
								foreach($questoesTextos as $qtk => $qtv){
									$valorqtr = $qtv->obterNumero();
									if($sqv->obterTipo() == Questao::MULTIPLAESCOLHA){
										$valorqtr = MultiplaEscolha::letra($valorqtr);
									}
									$textoqtr = $qtv->obterTexto();

									$qOptionsRespostas .= "<div class='wrapper' translate='no' lang='en'>";
									$qOptionsRespostas .= "<input class='resposta_questao_valor' spellcheck='false' type='radio' name='rqID-".$idq."' id='resposta-".$num."' value=".$valorqtr." ".$checked." />";
									$qOptionsRespostas .= "<label style='padding-top: 8px; padding-left: 7px;' for='resposta-".$num."'>".$valorqtr.' - '.$textoqtr."</label><br>";
									$qOptionsRespostas .= "</div>";
									$qOptionsRespostas .= "<div style='padding-top: 8px;'></div>";

									$qTextosRespostas .= $valorqtr." - ".$textoqtr."</br>";
								}

								if(!isset($simulado['respostas'][$idq])){
									$simulado['respostas'][$idq]['tempo'] = '';
									$simulado['respostas'][$idq]['tempoJS'] = '';
									$simulado['respostas'][$idq]['valor'] = '';
								}								

								switch ($ans) {
									case 'ABERTA':
										$resps = "<textarea spellcheck='false' class='sr_ctexto aberta' maxlenght='1020' name='rqID-".$idq."' id='resposta-".$num."'>".$simulado['respostas'][$idq]['valor']."</textarea>";
										break;
									case 'DISCURSIVA':
										//$resps = "<textarea spellcheck='false' class='sr_ctexto discursiva' maxlenght='1020' name='rqID-".$idq."' id='resposta-".$num."'>".$simulado['respostas'][$idq]['valor']."</textarea>";
										$resps = "
											<textarea spellcheck='false' class='sr_ctexto discursiva' maxlenght='1020' name='rqID-".$idq."' id='resposta-".$num."' style='display:none;'>".$simulado['respostas'][$idq]['valor']."</textarea>
											<p>Por favor, consulte o professor para o preenchimento desta quest&#227;o.</p>
										";
										break;
									case 'SOMATORIO':
										$resps = "<input spellcheck='false' class='sr_cvalor somatorio' name='rqID-".$idq."' id='resposta-".$num."' size='1' maxlength='3' value='".$simulado['respostas'][$idq]['valor']."' />";
										break;
									case 'MULTIPLAESCOLHA':
										$resps .= '<table style="width: 100%;" translate="no" lang="en">';
										$resps .= '<tr>';
										$resps .= '<td id="td_resposta" style="float: left;width: 45%">';
										$resps .= '</td>';
										$resps .= '<td id="td_resposta2" style="float: right;width: 65%">';
										$resps .= $qOptionsRespostas;
										$resps .= '</td>';
										$resps .= '</tr>';
										$resps .= '</table>';
										break;
								}
						?>

						<div class="swiper-slide">
							<div class='questao' id="questao<?php echo$num; ?>" translate="no">

								<input id="tempo-<?php echo$num; ?>" name="tempo[<?php echo$num; ?>]" type="hidden" value="<?php echo$simulado['respostas'][$idq]['tempo']; ?>"/>
								
								<div class="sr_texto_questao">
									<p style="font-size: 24px"><?php echo $title; ?></p>
								</div>
								<div class="resposta_questao">
									<p></p>
									<div ><?php echo$resps; ?></div>
								</div>
							</div>
						</div>

						<?php
								$num++;
							}
						?>
					    </div>
					    <!-- Add Pagination -->
					    <div class="swiper-pagination"></div>
					    <!-- Add Arrows -->
					    <!-- <div class="swiper-button-next"></div>
					    <div class="swiper-button-prev"></div> -->
					  </div>
			<!-- 		</td>
				</tr>
			</table> -->
		</div>

		<div class="botoes"> <!-- style="display:none;"> -->
			<table border="0" style=" width: 100%;">
				<tr>
					<td>
						<button class="sr_pesquisa_retornar" id="btn_prev">
							<label>Anterior</label>
							<img style="display: inline-block; align:itens: center" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_start.png';?>">
						</button>
					</td>
					<td>
						<button class="sr_pesquisa_retornar" id="btn_next">
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_end.png';?>">
							<span>Pr&oacute;xima</span>
						</button>
					</td>
					<td>
						<button class="sr_pesquisa_retornar" id="btn_finalizar" style='display: none;'>
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'sucesso-big.png';?>">
							<label>Finalizar</label>
						</button>
					</td>
				</tr>
			</table>
		</div>
	</div>

	<div id="div_btn_end" class="questionario_general_end" style="padding: 20px; display:none;">
		<div id="alert_box" class="alert_box alert_box_erro" style="display:none;"></div>
		<div class="sr_right" style="width: 100%;">
			<!-- Success message container, initially hidden -->
			<div id='msg_fim' style="margin: 0 auto;margin-bottom:3%;margin-top:3%;text-align: center; display:none;">
				<h1 style="font-size: 24px; color: #28a745; margin-bottom: 20px;">
					<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'sucesso-big.png';?>" style="vertical-align: middle; margin-right: 10px;">
					Avaliação entregue com sucesso!
				</h1>
				
				<!-- Results container - will be populated via  -->
				<div id="results_container" style="margin-top: 20px;"></div>
				
				<div style="margin-top: 30px;">
					<a class="sr_pesquisa" style="cursor:pointer; padding: 15px 25px; font-size: 18px; background-color: #8B008B; color: white; text-decoration: none; border-radius: 5px; display: inline-block;" href="<?= Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:logout')); ?>">
					<i class="fa-solid fa-door-open"></i> Sair do Sistema
					</a>
				</div>
			</div>
			
			<!-- Submission button, shown initially -->
			<?php if (Core::registro('usuario')->obterGrupo() == 4): ?>
				<div style="margin: 0 auto;margin-bottom:10%;margin-top:8%;text-align: center;">
					<h2 id="warning_entregar" style="font-size: 24px; color:rgb(0, 0, 0);">&#x2193; Clique no botão abaixo para entregar a avaliação &#x2193;</h2>
					<a id="btn_end" style="cursor:pointer; width: 55%; height:70px; font-size: 20px; padding-top: 35px;" class="sr_pesquisa">
						<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'stock_task20.png';?>">
						<span>Entregar Avaliação</span>
					</a>
				</div>
			<?php else: ?>
				<div style="margin: 0 auto; margin-bottom: 8%; margin-top: 8%; text-align: center; max-width: 600px; background: #fff8fc; border: 2px solid #8B008B; border-radius: 12px; box-shadow: 0 4px 18px rgba(139,0,139,0.08); padding: 32px 24px;">
					<h2 style="font-size: 26px; color: #dc3545; margin-bottom: 16px; font-weight: 600; letter-spacing: 0.4px;">
						<i class="fa-solid fa-user-shield" style="margin-right: 10px; color: #8B008B; font-size: 28px; vertical-align: middle;"></i>
						Acesso restrito à entrega da avaliação
					</h2>
					<p style="font-size: 17px; color: #333; margin: 0 0 12px 0; line-height: 1.6;">
						<i class="fa-solid fa-circle-info" style="margin-right: 8px; color: #8B008B; font-size: 16px; vertical-align: middle;"></i>
						Seu perfil não corresponde ao de um aluno. Por isso, a funcionalidade de envio da avaliação está desabilitada para você.
					</p>
					<p style="font-size: 17px; color: #333; margin: 0; line-height: 1.6;">
						<i class="fa-solid fa-eye" style="margin-right: 8px; color: #8B008B; font-size: 16px; vertical-align: middle;"></i>
						No entanto, é possível visualizar o conteúdo das questões normalmente.
					</p>
				</div>
			<?php endif; ?>
		</div>
		<div id="lista_questoes_respondidas" class="sr_right" style="width: 100%; float: left; margin-right: 2%;">
			<!-- <p style="font-size: 18px;margin: 0;">Tempo de Prova: 
				<span id='tempo_realizacao' style="font-weight: bold;"><?php echo$simulado['tfim']; ?></span>
			</p>
			<br> -->
			<div style="background-color: #8B008B; margin: 0; font-weight: bold; color: #FFFFFF; height: 30px; border-radius: 4px 4px 4px 4px;">
				<p style="padding-left: 10px; padding-top: 5px; margin: 0; font-size: 18px;">Quest&#245;es:</p>
			</div>
			<div style="background-color: #FFD700; margin: 10px 0; font-weight: bold; color: #000000; height: 40px; border-radius: 4px 4px 4px 4px; text-align: center;">
				<p style="padding-top: 10px; margin: 0; font-size: 20px;">Veja se todas as questões foram respondidas, caso queira revisar ou voltar em alguma questão, clique abaixo:</p>
			</div>
			<br>
			<?php 
			$num = 1;

			$agrupamento_pordois = array_chunk($simulado['questoes'], 20);
			echo '<table>';
			echo '<tr>';
			foreach ($agrupamento_pordois as $chave_dogrupo => $conteudo_dogrupo) { 

				echo '<td style="vertical-align: top;">';
				echo '<ul style="padding: 0 10px 0 0;">';
				
				foreach ($conteudo_dogrupo as $chave_doconteudo => $elemento_doconteudo) {
					$_CADA_id_doelemento = $elemento_doconteudo->ObterID();
					echo '<li class="pendente" style="list-style-type: none; padding-bottom: 6px;">';
					echo "<button id='lquestao$num' class='troca_questao' style='cursor:pointer;font-size: 16px;color: #FFFFFF; width: 160px; height: 35px; border-radius: 5px; border-color: #8B008B ; background-color: #8B008B;'>$num | Pendente</button>";
					echo '</li>';
					$num++;
				}
				
				echo '</ul>';
				echo '</td>';
			?>
			<? }
				echo '</tr>';
				echo '</table>';
			?>
			
		</div>
	</div>
</div>


<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/swiper/package/js/swiper.min.js"></script>
<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/jquery-stopwatch/jquery.stopwatch.js"></script>

<script type="text/javascript">
	// Add this before your document.ready function
	function loadAndDisplayResults() {
		jQuery.ajax({
			type: "GET",
			url: "./?m=simulados_online&a=get_resultados",
			dataType: 'json',
			success: function(data) {
				if (data.success) {
					renderResults(data);
				} else {
					jQuery('#results_container').html('<p>Não foi possível carregar os resultados.</p>');
				}
			},
			error: function() {
				jQuery('#results_container').html('<p>Erro ao carregar os resultados.</p>');
			},
			complete: function() {
				jQuery('#results_container .spinner').remove();
			}
		});
	}

	function renderResults(data) {
		const correctAnswers = data.correctAnswers || 0;
		const incorrectAnswers = data.incorrectAnswers || 0;
		const unansweredQuestions = data.unansweredQuestions || 0;
		const annulledQuestions = data.annulledQuestions || 0;
		const totalQuestions = correctAnswers + incorrectAnswers + unansweredQuestions + annulledQuestions;
		
		// Calculate percentages
		const correctPercentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
		const incorrectPercentage = totalQuestions > 0 ? Math.round((incorrectAnswers / totalQuestions) * 100) : 0;
		
		// Build HTML for results
		let resultsHTML = `
			<div class="debug-panel" style="margin: 10px; padding: 10px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;">
				<div style="display: flex; justify-content: space-between; align-items: center;">
					<h3 style="color: #8B008B; margin-top: 0;">Resultado da Avaliação</h3>
				</div>
				
				<!-- Chart and results container -->
				<div style="display: flex; flex-wrap: wrap; margin-bottom: 20px;">
					<!-- Chart -->
					<div style="flex: 1; min-width: 300px; padding: 15px;">
						<div style="margin-bottom: 10px; text-align: center;">
							<label style="margin-right: 10px; cursor: pointer; display: inline-flex; align-items: center;">
								<input type="radio" name="chartType" value="pie" checked style="margin-right: 5px; transform: scale(1.5);">
								<span style="margin-left: 5px;">Gráfico Pizza</span>
							</label>
							<label style="cursor: pointer; display: inline-flex; align-items: center;">
								<input type="radio" name="chartType" value="bar" style="margin-right: 5px; transform: scale(1.5);">
								<span style="margin-left: 5px;">Gráfico Barras</span>
							</label>
						</div>
						<div style="position: relative; width: 100%; max-width: 300px; height: 300px; margin: 0 auto;">
							<canvas id="performanceChart"></canvas>
						</div>
					</div>
					
					<!-- Results table -->
					<div style="flex: 2; min-width: 400px;">
						<table style="width: 100%; border-collapse: collapse;">
							<thead>
								<tr style="background-color: #8B008B; color: white;">
									<th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Questão ID</th>
									<th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Sua Resposta</th>
									<th style="padding: 8px; text-align: center; border: 1px solid #ddd;">Resultado</th>
								</tr>
							</thead>
							<tbody>`;
		
		// Add rows for each question
		data.questions.forEach(q => {
			const bgColor = q.respostaValor === '' ? 'background-color: #fff3cd;' : '';
			resultsHTML += `
				<tr style="border: 1px solid #ddd;">
					<td style="padding: 8px; text-align: center; border: 1px solid #ddd;">${q.questaoId}</td>
					<td style="padding: 8px; text-align: center; border: 1px solid #ddd; ${bgColor}">${q.respostaLetra}</td>
					<td style="padding: 8px; text-align: center; border: 1px solid #ddd;">${q.status}</td>
				</tr>`;
		});
		
		// Add summary row
		resultsHTML += `
								<tr style="border: 1px solid #ddd; font-weight: bold; background-color: #e9ecef;">
									<td style="padding: 8px; text-align: center; border: 1px solid #ddd;">Resultado</td>
									<td style="padding: 8px; text-align: center; border: 1px solid #ddd;"></td>
									<td style="padding: 8px; text-align: center; border: 1px solid #ddd;">Acertos: ${correctAnswers}/${totalQuestions} (${correctPercentage}%)</td>
									<!--<td style="padding: 8px; text-align: center; border: 1px solid #ddd;">Erros: ${incorrectAnswers}/${totalQuestions} (${incorrectPercentage}%)</td>-->
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>`;
		
		// Add the HTML to the container
		jQuery('#results_container').html(resultsHTML);
		
		// Initialize the chart
		loadChartLibraryAndCreateChart(correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions);
	}

	function loadChartLibraryAndCreateChart(correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions) {
		// Check if Chart.js is already loaded
		if (typeof Chart === 'undefined') {
			// Load Chart.js
			const script = document.createElement('script');
			script.src = 'https://dyubuzjbgoyjh.cloudfront.net/simulados_online/js/chart.js';
			script.onload = function() {
				createChart('pie', correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions);
			};
			document.head.appendChild(script);
		} else {
			// Chart.js already loaded
			createChart('pie', correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions);
		}
	}

	function createChart(type, correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions) {
		// Chart instance variable
		let chartInstance = null;
		
		// Destroy existing chart if it exists
		if (window.resultChart) {
			window.resultChart.destroy();
		}
		
		// Get canvas context
		const ctx = document.getElementById("performanceChart");
		if (!ctx) {
			console.error("Canvas element not found");
			return;
		}
		
		// Create data object
		const data = {
			labels: ["Corretas", "Incorretas", "Não respondidas", "Anuladas"],
			datasets: [{
				data: [correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions],
				backgroundColor: [
					"#28a745",  // green for correct
					"#dc3545",  // red for incorrect
					"#ffc107",  // yellow for unanswered
					"#6c757d"   // gray for annulled
				],
				borderWidth: 1
			}]
		};
		
		// Specific config for bar chart
		const barOptions = {
			responsive: true,
			maintainAspectRatio: true,
			indexAxis: "y",
			plugins: {
				legend: {
					display: false
				},
				tooltip: {
					callbacks: {
						label: function(context) {
							const value = context.raw;
							const total = context.dataset.data.reduce((a, b) => a + b, 0);
							const percentage = Math.round((value / total) * 100) || 0;
							return `${value} (${percentage}%)`;
						}
					}
				}
			},
			scales: {
				x: {
					beginAtZero: true,
					ticks: {
						precision: 0
					}
				}
			}
		};
		
		// Specific config for pie chart
		const pieOptions = {
			responsive: true,
			maintainAspectRatio: true,
			plugins: {
				legend: {
					position: "bottom",
					align: "start", // This adds left alignment to the legends
					labels: {
						font: {
							size: 12
						},
						generateLabels: function(chart) {
							const original = Chart.overrides.pie.plugins.legend.labels.generateLabels;
							const labels = original.call(this, chart);
							
							const data = chart.data.datasets[0].data;
							const total = data.reduce((a, b) => a + b, 0);
							
							labels.forEach((label, i) => {
								const value = data[i];
								const percentage = Math.round((value / total) * 100) || 0;
								label.text = `${label.text}: ${value} (${percentage}%)`;
							});
							
							return labels;
						}
					}
				},
				tooltip: {
					callbacks: {
						label: function(context) {
							const value = context.raw;
							const total = context.dataset.data.reduce((a, b) => a + b, 0);
							const percentage = Math.round((value / total) * 100) || 0;
							return `${context.label}: ${value} (${percentage}%)`;
						}
					}
				}
			}
		};
		
		// Choose options based on chart type
		const options = type === "pie" ? pieOptions : barOptions;
		
		// Create the chart
		window.resultChart = new Chart(ctx, {
			type: type,
			data: data,
			options: options
		});
		
		// Set up chart type toggle
		jQuery(document).ready(function() {
			jQuery("input[name='chartType']").on("change", function() {
				const chartType = jQuery("input[name='chartType']:checked").val();
				createChart(chartType, correctAnswers, incorrectAnswers, unansweredQuestions, annulledQuestions);
			});
		});
	}
	var mySwiper;
	var entregou = false;

	function cronoStart(){
		jQuery('.sr_cvalor').prop('disabled', false);
		jQuery('.sr_ctexto').prop('disabled', false);

		btnFinalizar();

		jQuery('#btn_prev').show();
		jQuery('#pause-mgs').hide();
		jQuery('.crono_stop').show();
		jQuery('.crono_start').hide();
		jQuery('#crono_time').stopwatch().stopwatch('start');

		var currQ = mySwiper.activeIndex;
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('start');
	}

	function cronoStop(){
		jQuery('.sr_cvalor').prop('disabled', true);
		jQuery('.sr_ctexto').prop('disabled', true);

		jQuery('#btn_prev').hide();
		jQuery('#btn_next').hide();
		jQuery('#btn_finalizar').hide();
		jQuery('#pause-mgs').show();
		jQuery('.crono_stop').hide();
		jQuery('.crono_start').show();
		jQuery('#crono_time').stopwatch().stopwatch('stop');

		var currQ = mySwiper.activeIndex;
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');

		var crono_time = jQuery('#crono_time').html();
		jQuery('#tempo-total').val(crono_time);
	}

	function btnFinalizar(){
		mySwiper.updateAutoHeight(1);
		var currQ = mySwiper.activeIndex;
		var countQ = jQuery('.swiper-slide').length;

		if(parseInt(currQ) == parseInt(countQ-1)){
			jQuery('#btn_next').hide();
			jQuery('#btn_finalizar').show();
		}
		else{
			jQuery('#btn_next').show();
			jQuery('#btn_finalizar').hide();
		}
	}

	function nextQuest(move = false, index = false){
		var countQ = jQuery('.swiper-slide').length;
		currQ = mySwiper.activeIndex;

		if((currQ+1) >= countQ){
			return;
		}

		if(currQ === index){
			return;
		}

    	jQuery('#crono_questao'+(currQ+1)).hide();
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');
		timeQ = jQuery('#crono_questao'+(currQ+1)).html();
		jQuery('#tempo-'+(currQ+1)).val(timeQ);

		jQuery('#crono_questao'+(currQ+2)).stopwatch().stopwatch('start');
    	jQuery('#crono_questao'+(currQ+2)).show();

		jQuery('#num_questao').html(currQ+2);
		if(move){
			mySwiper.slideNext('150');
		}

		btnFinalizar();
	}

	function prevQuest(move = false, index = false){
		currQ = mySwiper.activeIndex;

		if((currQ+1) <= 1){
			return;
		}

		if(currQ === index){
			return;
		}   

    	jQuery('#crono_questao'+(currQ+1)).hide();
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');
		timeQ = jQuery('#crono_questao'+(currQ+1)).html();
		jQuery('#tempo-'+(currQ+1)).val(timeQ);

		jQuery('#crono_questao'+(currQ)).stopwatch().stopwatch('start');
    	jQuery('#crono_questao'+(currQ)).show();

		jQuery('#num_questao').html(currQ);
		if(move){
			mySwiper.slidePrev('150');
		}

		btnFinalizar();
	}

	jQuery(document).ready(function() { 
		jQuery('#btn_start').click(function() {
			jQuery('#div_btn_start').hide();
			jQuery('#cnt_questoes').show();

			mySwiper = new Swiper('.swiper-container', {
				autoHeight: true,
				allowTouchMove: false,
				pagination: {
					el: '.swiper-pagination',
					type: 'progressbar',
				},
				on: {
					slideNextTransitionStart: function () {
						currQ = mySwiper.activeIndex; 
						nextQuest(false, currQ);
					},
					slidePrevTransitionStart: function () {
						currQ = mySwiper.activeIndex; 
						prevQuest(false, currQ);
					}
				}
			});

			jQuery('.crono_start').hide();
			jQuery('.crono_stop').show();
			jQuery('#crono_time').stopwatch().stopwatch('start');
			jQuery('#crono_questao1').show();
			jQuery('#crono_questao1').stopwatch().stopwatch('start');
		});

		jQuery('.crono_start').click(function () {
			cronoStart();
		});

		jQuery('.crono_stop').click(function () {
			cronoStop();
		});

		jQuery('#btn_next').click(function() {
			nextQuest(true,false);
		});

		jQuery('#btn_prev').click(function() {
			prevQuest(true,false);
		});   	

		jQuery('.resposta_questao_valor > textarea').keyup(function(){
			num = jQuery(this).attr('id')+'a<?php echo$simulado['aluno']; ?>';
			val = jQuery(this).val();

			if(val != ''){
				window.localStorage.setItem(num, val);
			}
			else{
				window.localStorage.removeItem(num);
			}
		}).each(function(){
			num = jQuery(this).attr('id')+'a<?php echo$simulado['aluno']; ?>';
			cache = window.localStorage.getItem(num);
			if(cache != ''){
				jQuery(this).val(cache);
			}
		});

		jQuery('.resposta_questao_valor > input').keyup(function(){
			num = jQuery(this).attr('id')+'a<?php echo$simulado['aluno']; ?>';
			val = jQuery(this).val();

			if(val != ''){
				window.localStorage.setItem(num, val);
			}
			else{
				window.localStorage.removeItem(num);
			}		
		}).each(function(){
			num = jQuery(this).attr('id')+'a<?php echo$simulado['aluno']; ?>';
			cache = window.localStorage.getItem(num);
			if(cache != ''){
				jQuery(this).val(cache);
			}
		});

		jQuery('.resposta_questao_valor').change(function(){
			num = jQuery(this).attr('id')+'a<?php echo$simulado['aluno']; ?>';
			val = jQuery(this).val();

			jQuery('.wrapper').css("border","");
			
			if (jQuery(this).is(':checked')) { 
				jQuery(this).parent('.wrapper').css('border','3px solid #8B008B');
			}

			if(val != 0){
				window.localStorage.setItem(num, val);
			}
			else{
				window.localStorage.removeItem(num);
			}
		}).each(function(){
			num = jQuery(this).attr('id')+'a<?php echo$simulado['aluno']; ?>';
			cache = window.localStorage.getItem(num);			
			
			if(cache != ''){
				jQuery(this).find('input[value="'+cache+'"]').prop("checked", true).attr('checked','checked');
			}
		});

		jQuery('#btn_finalizar').click(function() {
			var crono_time = jQuery('#crono_time').html();
			jQuery('#tempo_realizacao').html(crono_time);

			jQuery('.resposta_questao_valor > textarea').each(function(){
				num = jQuery(this).attr('id').replace("resposta-", "");
				val = jQuery(this).val();

				if(val != ''){
					jQuery('#lquestao'+num).parent().removeClass('pendente');
					jQuery('#lquestao'+num).parent().addClass('realizada');
					jQuery('#lquestao'+num).html(num+' - Respondida');
				}
				else{
					jQuery('#lquestao'+num).parent().removeClass('realizada');
					jQuery('#lquestao'+num).parent().addClass('pendente');
					jQuery('#lquestao'+num).html(num+' - Pendente');
				}
			});

			jQuery('.resposta_questao_valor > input').each(function(){
				num = jQuery(this).attr('id').replace("resposta-", "");
				val = jQuery(this).val();

				if(val != ''){
					jQuery('#lquestao'+num).parent().removeClass('pendente');
					jQuery('#lquestao'+num).parent().addClass('realizada');
					jQuery('#lquestao'+num).html(num+' - Respondida');
				}
				else{
					jQuery('#lquestao'+num).parent().removeClass('realizada');
					jQuery('#lquestao'+num).parent().addClass('pendente');
					jQuery('#lquestao'+num).html(num+' - Pendente');
				}
			});

			jQuery('.resposta_questao_valor').each(function(){
				jQuery('.resposta_questao_valor').each(function(){
					num = jQuery(this).attr('id').replace("resposta-", "");
					val = jQuery(this).is(':checked');

					if(val == true){
						jQuery('#lquestao'+num).parent().removeClass('pendente');
						jQuery('#lquestao'+num).parent().addClass('realizada');
						jQuery('#lquestao'+num).html(num+' | Respondida');
					}
				});
			});

			//cronoStop();
			jQuery('#cnt_questoes').hide();
			jQuery('#div_btn_end').show();
		});

		jQuery('.troca_questao').click(function() {
			//cronoStart();
			idV = jQuery(this).attr('id');
			id = idV.replace('lquestao','');
			mySwiper.slideTo(id-1, '150');
			jQuery('#num_questao').html(id);
			jQuery('#cnt_questoes').show();
			jQuery('#div_btn_end').hide();
			btnFinalizar();
		});

		<?php if (Core::registro('usuario')->obterGrupo() == 4): ?>
				window.onbeforeunload = function(){ 
					if(entregou == false){
						jQuery('.alert_box').html('Voc&#234; n&#227;o entregou a avalia&#231;&#227;o! Deseja sair dessa p&#225;gina?').fadeIn('fast');
						return "Você não entregou a avaliação! Deseja sair dessa página?";
					}
				}
		<?php endif; ?>

		jQuery('#btn_end').click(function(){
			jQuery('#modal_load').show();

			var tempoTotal = jQuery('#tempo-total').val();

			// Coleta as respostas no formato atual
			var rValores = Array();
			jQuery('.questao').each(function(){
				var qId = jQuery(this).attr('id');
				var curr = qId.replace("questao", "");

				var rValor = '';
				var rTagName = jQuery(this).find('#resposta-'+curr).is(':checked');

				var rqID = jQuery('#resposta-'+curr).attr('name');
				var rqID = rqID.replace("rqID-", "");

				if(rTagName === true){
					var rValor = jQuery(this).find('#resposta-'+curr+':checked').val();
				}

				vTmp = new Object();
				vTmp.qid = rqID;
				vTmp.valor = rValor;
				rValores.push(vTmp);
			});

			// Adapta os dados para o formato esperado pela nova API
			var dadosParaAPI = {
				dados: rValores,
				inscricao_id: <?php echo $simulado['inscricao_id']; ?>
			};

			axios({
				method: 'POST',
				url: `${JAVA_URL_BASE}/efetuar_lancamento_simulado_online?m=simulados_online`,
				data: dadosParaAPI,
				headers: {
					'Content-Type': 'application/json',
					'X-Requested-With': 'XMLHttpRequest'
				},
				timeout: 30000
			})
			.then(function(response) {
				console.log('Resposta recebida:', response.data);
				
				// A API agora retorna {ret: "1", msg: "Sucesso"} ou true
				if(response.data === true || (response.data.ret && response.data.ret === "1")){
					localStorage.clear();
					entregou = true;
					jQuery('.alert_box').removeClass('alert_box_erro');
					jQuery('.alert_box').addClass('alert_box_sucesso');
					jQuery('#lista_questoes_respondidas').hide();

					jQuery('#btn_end').hide();
					jQuery('#warning_entregar').hide();
					
					jQuery('#modal_load').hide();
					
					jQuery('#results_container').html('<div class="spinner" style="text-align: center; margin-top: 20px;"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/loader6.gif" alt="Loading..." style="width: 50px; height: 50px;"></div>');

					loadAndDisplayResults();
					
					jQuery('#msg_fim').show();
				} else {
					// Caso response não seja sucesso
					var userMessage = 'Erro ao finalizar a avaliação.';
					var finalMessage = userMessage;
					if (response.data.msg) {
						finalMessage += '<br><br><strong>Mensagem do servidor:</strong><br>' + response.data.msg;
					} else {
						finalMessage += '<br><br><strong>Resposta do servidor:</strong><br>' + JSON.stringify(response.data);
					}
					
					window.scroll(0,0);
					jQuery('.alert_box').html(finalMessage).fadeIn('fast');
					jQuery('#modal_load').hide();
				}
			})
			.catch(function(error) {
				var userMessage = 'Erro ao finalizar a avaliação.';
				var techMessage = '';
				
				if (error.response) {
					// O servidor respondeu com um status de erro
					console.log('Erro de resposta:', {
						status: error.response.status,
						statusText: error.response.statusText,
						data: error.response.data
					});
					
					if (error.response.data) {
						if (error.response.data.detail) {
							// FastAPI retorna erros em 'detail'
							techMessage = error.response.data.detail;
						} else if (error.response.data.msg) {
							techMessage = error.response.data.msg;
						} else if (typeof error.response.data === 'string') {
							techMessage = error.response.data;
						} else {
							techMessage = JSON.stringify(error.response.data);
						}
					}
				} else if (error.request) {
					// A requisição foi feita mas não houve resposta (problema de rede)
					console.log('Erro de rede:', error.request);
					window.scroll(0,0);
					jQuery('.alert_box').html('Os dados não foram enviados! Por favor, verifique sua conexão com a internet e tente novamente.').fadeIn('fast');
					jQuery('#modal_load').hide();
					return;
				} else {
					// Algo deu errado na configuração da requisição
					console.log('Erro de configuração:', error.message);
					techMessage = error.message;
				}
				
				var finalMessage = userMessage;
				if (techMessage) {
					finalMessage += '<br><strong>Mensagem para o Suporte:</strong><br><br>' + techMessage;
				}
				
				window.scroll(0,0);
				jQuery('.alert_box').html(finalMessage).fadeIn('fast');
				jQuery('#modal_load').hide();
			});
		});
	});
</script>

<script type="text/javascript">
	jQuery(document).ready(function() { 
		jQuery('audio').each(function(){
			const audioElement = jQuery(this)[0];
			audioElement.playbackRate = 1;

			jQuery(this).on('contextmenu', (e) => {
				e.preventDefault();
			});

			//jQuery(this).attr('controlsList', 'nodownload noplaybackrate');
			jQuery(this).attr('controlsList', 'nodownload');

			/* jQuery(this).on('ratechange', (e) => {
				audioElement.playbackRate = 1;
				e.preventDefault();
			}); */
		});
	});
</script>