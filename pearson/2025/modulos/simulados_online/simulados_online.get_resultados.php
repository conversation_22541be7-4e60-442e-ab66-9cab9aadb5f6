<?php
// This function converts numeric values to letters
function convertToLetter($value) {
    switch ($value) {
        case '1': return 'A';
        case '2': return 'B';
        case '4': return 'C';
        case '8': return 'D';
        case '16': 
        case '32': return 'E';
        default: return $value; // Keep original if not matched
    }
}

// Get user ID or session info
$idUsuario = $_SESSION['id_usuario']; // Adjust based on your session structure

// Get the inscricao object
$inscricao = new InscricaoSimuladoOnline($idUsuario); // Adjust constructor as needed

// Extract responses and statistics
$respostas = $inscricao->obterRespostasMarcadas($inscricao->obterId());
$result = array(
    'success' => true,
    'correctAnswers' => 0,
    'incorrectAnswers' => 0,
    'unansweredQuestions' => 0,
    'annulledQuestions' => 0,
    'questions' => array()
);

if (!empty($respostas)) {
    foreach ($respostas as $resposta) {
        try {
            // Access to private properties via ReflectionClass
            $reflectionResposta = new ReflectionClass($resposta);
            $dadosProperty = $reflectionResposta->getProperty('_dados');
            $dadosProperty->setAccessible(true);
            $respData = $dadosProperty->getValue($resposta);

            // Get student answer
            $respostaValor = isset($respData['valor']) ? $respData['valor'] : '';
            // Convert to letter
            $respostaLetra = convertToLetter($respostaValor);
            
            // Get question ID and gabarito info
            $questaoId = '';
            $gabaritoValor = '';
            $gabaritoLetra = '';
            $anulada = false;
            
            if (isset($respData['questao'])) {
                $questao = $respData['questao'];
                $reflectionQuestao = new ReflectionClass($questao);
                $questaoProperty = $reflectionQuestao->getProperty('_dados');
                $questaoProperty->setAccessible(true);
                $questaoData = $questaoProperty->getValue($questao);
                
                $questaoId = isset($questaoData['identificador']) ? $questaoData['identificador'] : '';
                $anulada = !empty($questaoData['anulada']);
                
                // Get gabarito value
                if (isset($questaoData['gabaritos']) && is_array($questaoData['gabaritos']) && !empty($questaoData['gabaritos'])) {
                    $gabarito = $questaoData['gabaritos'][0];
                    $reflectionGabarito = new ReflectionClass($gabarito);
                    $gabaritoProperty = $reflectionGabarito->getProperty('_dados');
                    $gabaritoProperty->setAccessible(true);
                    $gabaritoData = $gabaritoProperty->getValue($gabarito);
                    
                    $gabaritoValor = isset($gabaritoData['valor']) ? $gabaritoData['valor'] : '';
                    // Convert gabarito to letter as well
                    $gabaritoLetra = convertToLetter($gabaritoValor);
                }
            }
            
            // Determine if answer is correct
            $status = '';
            
            if ($anulada) {
                $status = '<span style="color: #6c757d;">Anulada</span>';
                $result['annulledQuestions']++;
            } elseif ($respostaValor === '') {
                $status = '<span style="color: #dc3545;">Não respondida</span>';
                $result['unansweredQuestions']++;
            } elseif ($respostaValor == $gabaritoValor) {
                $status = '<span style="color: #28a745;">Correta</span>';
                $result['correctAnswers']++;
            } else {
                $status = '<span style="color: #dc3545;">Incorreta</span>';
                $result['incorrectAnswers']++;
            }
            
            // Add question to result array
            $result['questions'][] = array(
                'questaoId' => $questaoId,
                'respostaValor' => $respostaValor,
                'respostaLetra' => $respostaLetra,
                'gabaritoValor' => $gabaritoValor,
                'gabaritoLetra' => $gabaritoLetra,
                'status' => $status
            );
            
        } catch (Exception $e) {
            // Handle any errors without crashing the response
            continue;
        }
    }
}

// Return the result as JSON
header('Content-Type: application/json');
echo json_encode($result);
exit;
?>