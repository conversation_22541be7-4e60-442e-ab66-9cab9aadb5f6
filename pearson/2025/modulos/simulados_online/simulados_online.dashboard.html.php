<?php
if (!defined('CORE_INCLUIDO')) { exit(); }
?>

<style>
.dashboard-container {
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.header-dashboard {
    background: linear-gradient(135deg, #8B008B, #A020F0);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 6px 20px rgba(139, 0, 139, 0.3);
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 5px solid #8B008B;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #8B008B;
    margin-bottom: 8px;
    display: block;
}

.stat-label {
    color: #6c757d;
    font-size: 16px;
    font-weight: 600;
}

.insights-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.insight-card {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border-left: 5px solid #28a745;
}

.graficos-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.graficos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 30px;
    margin-top: 25px;
}

.grafico-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.grafico-card:hover {
    border-color: #8B008B;
}

.grafico-card h4 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

.filtros-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.filtros-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filtro-grupo {
    display: flex;
    flex-direction: column;
}

.filtro-grupo label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
}

.filtro-grupo select,
.filtro-grupo input {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filtro-grupo select:focus,
.filtro-grupo input:focus {
    outline: none;
    border-color: #8B008B;
    box-shadow: 0 0 0 3px rgba(139, 0, 139, 0.1);
}

.botoes-dashboard {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #8B008B, #A020F0);
    color: white;
    box-shadow: 0 4px 15px rgba(139, 0, 139, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #7a007a, #9010e0);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 0, 139, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.filtros-ativos {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.filtro-ativo {
    display: inline-flex;
    align-items: center;
    background-color: #e9ecef;
    padding: 8px 15px;
    border-radius: 20px;
    margin: 3px;
    font-size: 13px;
}

.filtro-ativo .remover {
    margin-left: 8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 12px;
}

@media (max-width: 768px) {
    .filtros-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .graficos-grid {
        grid-template-columns: 1fr;
    }
    
    .insights-grid {
        grid-template-columns: 1fr;
    }
    
    .botoes-dashboard {
        justify-content: center;
    }
}
</style>

<div class="dashboard-container">
    <!-- Header -->
    <div class="header-dashboard">
        <h1 style="margin: 0; font-size: 32px; font-weight: bold;">📊 Dashboard de Análise Avançada</h1>
        <p style="margin: 15px 0 0 0; font-size: 18px; opacity: 0.9;">
            Monitoramento completo e visualização de dados dos simulados online
        </p>
    </div>

    <!-- Estatísticas Principais -->
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number"><?= number_format($this->_total_registros) ?></span>
            <div class="stat-label">📋 Total de processados</div>
        </div>
        <div class="stat-card">
            <span class="stat-number"><?= count($this->_simulados_disponiveis) ?></span>
            <div class="stat-label">📝 Simulados Processados</div>
        </div>
    </div>

    <!-- Insights Rápidos -->
    <?php if (isset($this->_estatisticas_extras)): ?>
    <div class="insights-section">
        <h3 style="margin-top: 0; color: #333; margin-bottom: 5px; display: flex; align-items: center; gap: 10px;">
            💡 Insights Estratégicos
        </h3>
        <p style="color: #6c757d; margin-bottom: 20px; font-size: 16px;">
            Análise automática dos principais indicadores de performance
        </p>
        
        <div class="insights-grid">
            <div class="insight-card" style="border-left-color: #28a745;">
                <h5 style="color: #28a745; margin: 0 0 10px 0; display: flex; align-items: center; gap: 8px;">
                    📅 Período de Maior Atividade
                </h5>
                <p style="margin: 0; color: #333; font-size: 16px; font-weight: 600;">
                    <?= $this->_estatisticas_extras['periodo_atividade'] ?>
                </p>
                <small style="color: #6c757d;">Dia com maior número de realizações</small>
            </div>
            
            <div class="insight-card" style="border-left-color: #17a2b8;">
                <h5 style="color: #17a2b8; margin: 0 0 10px 0; display: flex; align-items: center; gap: 8px;">
                    🕒 Horário de Pico
                </h5>
                <p style="margin: 0; color: #333; font-size: 16px; font-weight: 600;">
                    <?= $this->_estatisticas_extras['horario_pico'] ?>
                </p>
                <small style="color: #6c757d;">Horário com maior concentração de atividades</small>
            </div>
            
            <?php if (isset($this->_dados_graficos['status_finalizacao'])): ?>
            <?php 
            $finalizados = $this->_dados_graficos['status_finalizacao']['finalizados'] ?? 0;
            $nao_finalizados = $this->_dados_graficos['status_finalizacao']['nao_finalizados'] ?? 0;
            $total_inscricoes = $finalizados + $nao_finalizados;
            $taxa_finalizacao = $total_inscricoes > 0 ? round(($finalizados / $total_inscricoes) * 100, 1) : 0;
            ?>
            <div class="insight-card" style="border-left-color: #8B008B;">
                <h5 style="color: #8B008B; margin: 0 0 10px 0; display: flex; align-items: center; gap: 8px;">
                    ✅ Progresso de participação Global
                </h5>
                <p style="margin: 0; color: #333; font-size: 16px; font-weight: 600;">
                    <?= $taxa_finalizacao ?>% (<?= number_format($finalizados) ?>/<?= number_format($total_inscricoes) ?>)
                </p>
                <small style="color: #6c757d;">Percentual de avaliações concluídas</small>
            </div>
            
            <div class="insight-card" style="border-left-color: #ffc107;">
                <h5 style="color: #856404; margin: 0 0 10px 0; display: flex; align-items: center; gap: 8px;">
                    ⏳ Inscritos sem resposta
                </h5>
                <p style="margin: 0; color: #333; font-size: 16px; font-weight: 600;">
                    <?= number_format($nao_finalizados) ?> alunos
                </p>
                <small style="color: #6c757d;">Inscrições ainda não finalizadas</small>
            </div>

            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Filtros Avançados -->
    <div class="filtros-container">
        <h3 style="margin-top: 0; color: #333; margin-bottom: 20px;">
            🎛️ Filtros de Análise
        </h3>
        
        <form method="GET" action="">
            <input type="hidden" name="m" value="simulados_online">
            <input type="hidden" name="a" value="dashboard_auditoria">
            
            <div class="filtros-grid">
                <div class="filtro-grupo">
                    <label for="filtro_simulado">📝 Simulado:</label>
                    <select name="filtro_simulado" id="filtro_simulado">
                        <option value="">Todos os simulados</option>
                        <?php foreach ($this->_simulados_disponiveis as $id => $nome): ?>
                            <option value="<?= $id ?>" <?= isset($this->_filtros_ativos['simulado']) && $this->_filtros_ativos['simulado'] == $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($nome) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filtro-grupo">
                    <label for="data_inicio">📅 Data Início:</label>
                    <input type="date" name="data_inicio" id="data_inicio" 
                           value="<?= isset($this->_filtros_ativos['data_inicio']) ? htmlspecialchars($this->_filtros_ativos['data_inicio']) : '' ?>">
                </div>

                <div class="filtro-grupo">
                    <label for="data_fim">📅 Data Fim:</label>
                    <input type="date" name="data_fim" id="data_fim" 
                           value="<?= isset($this->_filtros_ativos['data_fim']) ? htmlspecialchars($this->_filtros_ativos['data_fim']) : '' ?>">
                </div>

                <div class="filtro-grupo">
                    <label for="bimestre">📚 Aplicação:</label>
                    <select name="bimestre" id="bimestre">
                        <option value="">Todos as aplicações</option>
                        <?php foreach ($this->_bimestres_disponiveis as $id => $nome): ?>
                            <option value="<?= $id ?>" <?= isset($this->_filtros_ativos['bimestre']) && $this->_filtros_ativos['bimestre'] == $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($nome) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filtro-grupo">
                    <label for="tipo_rede">🏫 Tipo de Rede:</label>
                    <select name="tipo_rede" id="tipo_rede">
                        <option value="todos">Todas as redes</option>
                        <option value="E" <?= isset($this->_filtros_ativos['tipo_rede']) && $this->_filtros_ativos['tipo_rede'] == 'E' ? 'selected' : '' ?>>Estadual</option>
                        <option value="M" <?= isset($this->_filtros_ativos['tipo_rede']) && $this->_filtros_ativos['tipo_rede'] == 'M' ? 'selected' : '' ?>>Municipal</option>
                        <option value="0" <?= isset($this->_filtros_ativos['tipo_rede']) && $this->_filtros_ativos['tipo_rede'] == '0' ? 'selected' : '' ?>>Particular/Outro</option>
                    </select>
                </div>

                <?php if (Core::registro('usuario')->obterGrupo() == 1): ?>
                <div class="filtro-grupo">
                    <label for="instituicao">🏢 Instituição:</label>
                    <select name="instituicao" id="instituicao">
                        <option value="">Todas as instituições</option>
                        <?php foreach ($this->_instituicoes_disponiveis as $id => $nome): ?>
                            <option value="<?= $id ?>" <?= isset($this->_filtros_ativos['instituicao']) && $this->_filtros_ativos['instituicao'] == $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($nome) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
            </div>

            <div class="botoes-dashboard">
                <button type="submit" class="btn btn-primary">
                    🔍 Aplicar Filtros
                </button>
                <a href="?m=simulados_online&a=dashboard_auditoria" class="btn btn-secondary">
                    🗑️ Limpar Filtros
                </a>
                <a href="?m=simulados_online&a=listar_auditoria" class="btn btn-success">
                    📋 Ver Lista Detalhada
                </a>
            </div>

            <!-- Filtros Ativos -->
            <?php if (!empty($this->_filtros_ativos)): ?>
            <div class="filtros-ativos">
                <strong>Filtros aplicados:</strong>
                <?php foreach ($this->_filtros_ativos as $tipo => $valor): ?>
                    <?php 
                    $label = '';
                    $valor_exibir = $valor;
                    switch($tipo) {
                        case 'simulado': 
                            $label = 'Simulado'; 
                            $valor_exibir = isset($this->_simulados_disponiveis[$valor]) ? $this->_simulados_disponiveis[$valor] : $valor;
                            break;
                        case 'data_inicio': $label = 'Data Início'; break;
                        case 'data_fim': $label = 'Data Fim'; break;
                        case 'tipo_rede': 
                            $label = 'Tipo Rede'; 
                            $tipos = ['E' => 'Estadual', 'M' => 'Municipal', '0' => 'Particular/Outro'];
                            $valor_exibir = isset($tipos[$valor]) ? $tipos[$valor] : $valor;
                            break;
                        case 'instituicao': 
                            $label = 'Instituição'; 
                            $valor_exibir = isset($this->_instituicoes_disponiveis[$valor]) ? $this->_instituicoes_disponiveis[$valor] : $valor;
                            break;
                        case 'busca_aluno': $label = 'Busca'; break;
                    }
                    
                    // Criar URL para remover filtro
                    $url_params = $_GET;
                    unset($url_params[$tipo]);
                    $url_remover = '?' . http_build_query($url_params);
                    ?>
                    <span class="filtro-ativo">
                        <?= htmlspecialchars($label) ?>: <?= htmlspecialchars($valor_exibir) ?>
                        <a href="<?= htmlspecialchars($url_remover) ?>" class="remover" title="Remover filtro">×</a>
                    </span>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </form>
    </div>

    <!-- Seção de Gráficos -->
    <div class="graficos-section">
        <h3 style="margin-top: 0; color: #333; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
            📊 Análise Visual Completa
        </h3>
        <p style="color: #6c757d; margin-bottom: 25px; font-size: 16px;">
            Gráficos interativos com dados em tempo real dos simulados online
        </p>
        
        <div class="graficos-grid">
            <!-- Gráfico de Atividade por Data -->
            <div class="grafico-card">
                <h4>📈 Evolução de Atividades (30 dias)</h4>
                <div class="chart-container">
                    <canvas id="graficoAtividade"></canvas>
                </div>
            </div>

            <!-- Gráfico de Atividade por Horário -->
            <div class="grafico-card">
                <h4>🕐 Distribuição por Horário</h4>
                <div class="chart-container">
                    <canvas id="graficoHorario"></canvas>
                </div>
            </div>

            <!-- Gráfico de Status de Finalização -->
            <div class="grafico-card">
                <h4>✅ Status das Avaliações</h4>
                <div class="chart-container">
                    <canvas id="graficoFinalizacao"></canvas>
                </div>
            </div>

            <!-- Gráfico de Distribuição por Tipo de Rede -->
            <div class="grafico-card">
                <h4>🏫 Distribuição por Tipo de Rede</h4>
                <div class="chart-container">
                    <canvas id="graficoDistribuicaoRede"></canvas>
                </div>
            </div>

            <!-- Gráfico de Top Simulados -->
            <div class="grafico-card">
                <h4>🏆 Top 10 Simulados Mais Realizados</h4>
                <div class="chart-container">
                    <canvas id="graficoTopSimulados"></canvas>
                </div>
            </div>

            <!-- Gráfico de Top Instituições -->
            <div class="grafico-card">
                <h4>🏢 Participação das Instituições</h4>
                <div class="chart-container">
                    <canvas id="graficoTopInstituicoes"></canvas>
                </div>
            </div>

            <!-- Gráfico de Finalização por Simulado -->
            <div class="grafico-card" style="grid-column: 1 / -1;">
                <h4>📊 Performance por Simulado (Taxa de Finalização)</h4>
                <div class="chart-container" style="height: 400px;">
                    <canvas id="graficoFinalizacaoSimulado"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

<script>
// Dados dos gráficos vindos do PHP
const dadosGraficos = {
    atividadePorData: <?= isset($this->_dados_graficos['atividade_por_data']) ? json_encode($this->_dados_graficos['atividade_por_data']) : '{}' ?>,
    atividadeHora: <?= isset($this->_dados_graficos['atividade_hora']) ? json_encode($this->_dados_graficos['atividade_hora']) : json_encode(array_fill(0, 24, 0)) ?>,
    statusFinalizacao: <?= isset($this->_dados_graficos['status_finalizacao']) ? json_encode($this->_dados_graficos['status_finalizacao']) : '{"finalizados":0,"nao_finalizados":0}' ?>,
    finalizacaoPorSimulado: <?= isset($this->_dados_graficos['finalizacao_por_simulado']) ? json_encode($this->_dados_graficos['finalizacao_por_simulado']) : '[]' ?>,
    distribuicaoRede: <?= isset($this->_dados_graficos['distribuicao_rede']) ? json_encode($this->_dados_graficos['distribuicao_rede']) : '{"E":0,"M":0,"outros":0}' ?>,
    topSimulados: <?= isset($this->_dados_graficos['top_simulados']) ? json_encode($this->_dados_graficos['top_simulados']) : '{}' ?>,
    instituicoesAtivas: <?= isset($this->_dados_graficos['instituicoes_ativas']) ? json_encode($this->_dados_graficos['instituicoes_ativas']) : '[]' ?>
};

console.log('📊 Dashboard carregado com dados:', dadosGraficos);

// Verificar se Chart.js foi carregado
if (typeof Chart === 'undefined') {
    console.error('❌ Chart.js não foi carregado!');
} else {
    // Configurações globais do Chart.js
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.plugins.legend.position = 'bottom';
    Chart.defaults.plugins.legend.labels.padding = 20;

    // Cores personalizadas
    const coresPrimarias = [
        '#8B008B', '#A020F0', '#9932CC', '#BA55D3', '#DA70D6',
        '#EE82EE', '#DDA0DD', '#D8BFD8', '#E6E6FA', '#F0E68C'
    ];

    // Cores para gráficos específicos
    const coresRede = ['#28a745', '#17a2b8', '#ffc107'];
    const coresStatus = ['#28a745', '#dc3545'];

    // Inicializar gráficos quando a página carregar
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Iniciando criação dos gráficos do dashboard...');
        
        // Criar gráficos com delays progressivos
        setTimeout(() => criarGraficoAtividade(), 100);
        setTimeout(() => criarGraficoHorario(), 200);
        setTimeout(() => criarGraficoFinalizacao(), 300);
        setTimeout(() => criarGraficoDistribuicaoRede(), 400);
        setTimeout(() => criarGraficoTopSimulados(), 500);
        setTimeout(() => criarGraficoTopInstituicoes(), 600);
        setTimeout(() => criarGraficoFinalizacaoSimulado(), 700);
    });

    // 1. Gráfico de Atividade por Data
    function criarGraficoAtividade() {
        const canvas = document.getElementById('graficoAtividade');
        if (!canvas) return;
        
        const hoje = new Date();
        const labels = [];
        const dados = [];
        
        for (let i = 29; i >= 0; i--) {
            const data = new Date(hoje);
            data.setDate(data.getDate() - i);
            const dataStr = data.toISOString().split('T')[0];
            const dataLabel = data.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            
            labels.push(dataLabel);
            dados.push(dadosGraficos.atividadePorData[dataStr] || 0);
        }
        
        new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Atividades por Dia',
                    data: dados,
                    borderColor: '#8B008B',
                    backgroundColor: 'rgba(139, 0, 139, 0.1)',
                    borderWidth: 4,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#8B008B',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { stepSize: 1, font: { size: 12 } }
                    },
                    x: {
                        grid: { color: 'rgba(0,0,0,0.05)' },
                        ticks: { font: { size: 11 } }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#8B008B',
                        borderWidth: 2,
                        cornerRadius: 8,
                        displayColors: false
                    }
                }
            }
        });
    }

    // 2. Gráfico de Atividade por Horário
    function criarGraficoHorario() {
        const canvas = document.getElementById('graficoHorario');
        if (!canvas) return;
        
        const labels = [];
        for (let i = 0; i < 24; i++) {
            labels.push(i.toString().padStart(2, '0') + ':00');
        }
        
        new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Atividades por Hora',
                    data: dadosGraficos.atividadeHora,
                    backgroundColor: 'rgba(139, 0, 139, 0.8)',
                    borderColor: '#8B008B',
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { stepSize: 1, font: { size: 12 } }
                    },
                    x: {
                        grid: { display: false },
                        ticks: {
                            maxRotation: 45,
                            font: { size: 10 }
                        }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        cornerRadius: 8,
                        displayColors: false
                    }
                }
            }
        });
    }

    // 3. Gráfico de Status de Finalização
    function criarGraficoFinalizacao() {
        const canvas = document.getElementById('graficoFinalizacao');
        if (!canvas) return;
        
        const finalizados = dadosGraficos.statusFinalizacao.finalizados || 0;
        const naoFinalizados = dadosGraficos.statusFinalizacao.nao_finalizados || 0;
        const total = finalizados + naoFinalizados;
        
        if (total === 0) {
            const ctx = canvas.getContext('2d');
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('Sem dados de', canvas.width/2, canvas.height/2 - 15);
            ctx.fillText('inscrições disponíveis', canvas.width/2, canvas.height/2 + 15);
            return;
        }
        
        new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: ['Realizadas', 'Pendentes'],
                datasets: [{
                    data: [finalizados, naoFinalizados],
                    backgroundColor: coresStatus,
                    borderWidth: 4,
                    borderColor: '#ffffff',
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 25,
                            usePointStyle: true,
                            font: { size: 14, weight: '600' },
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map((label, i) => {
                                        const value = data.datasets[0].data[i];
                                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                                        return {
                                            text: `${label}: ${value} (${percentage}%)`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            strokeStyle: data.datasets[0].borderColor,
                                            lineWidth: data.datasets[0].borderWidth,
                                            pointStyle: 'circle',
                                            hidden: false,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                                return `${context.label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 4. Gráfico de Distribuição por Rede
    function criarGraficoDistribuicaoRede() {
        const canvas = document.getElementById('graficoDistribuicaoRede');
        if (!canvas) return;
        
        const estadual = dadosGraficos.distribuicaoRede.E || 0;
        const municipal = dadosGraficos.distribuicaoRede.M || 0;
        const outros = dadosGraficos.distribuicaoRede.outros || 0;
        const total = estadual + municipal + outros;
        
        if (total === 0) {
            const ctx = canvas.getContext('2d');
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('Sem dados', canvas.width/2, canvas.height/2);
            return;
        }
        
        new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: ['Estadual', 'Municipal', 'Particular/Outros'],
                datasets: [{
                    data: [estadual, municipal, outros],
                    backgroundColor: coresRede,
                    borderWidth: 4,
                    borderColor: '#ffffff',
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 25,
                            usePointStyle: true,
                            font: { size: 14, weight: '600' }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : '0.0';
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    // 5. Gráfico de Top Simulados
    function criarGraficoTopSimulados() {
        const canvas = document.getElementById('graficoTopSimulados');
        if (!canvas) return;
        
        const simulados = Object.keys(dadosGraficos.topSimulados);
        if (simulados.length === 0) {
            const ctx = canvas.getContext('2d');
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('Sem dados disponíveis', canvas.width/2, canvas.height/2);
            return;
        }
        
        const labels = simulados.slice(0, 10).map(nome => {
            return nome.length > 25 ? nome.substring(0, 25) + '...' : nome;
        });
        const dados = Object.values(dadosGraficos.topSimulados).slice(0, 10);
        
        new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Realizações',
                    data: dados,
                    backgroundColor: coresPrimarias.slice(0, dados.length),
                    borderColor: coresPrimarias.slice(0, dados.length),
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { stepSize: 1, font: { size: 12 } }
                    },
                    y: {
                        grid: { display: false },
                        ticks: { font: { size: 11 } }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                return simulados[context[0].dataIndex];
                            }
                        }
                    }
                }
            }
        });
    }

    // 6. Gráfico de Top Instituições
    function criarGraficoTopInstituicoes() {
        const canvas = document.getElementById('graficoTopInstituicoes');
        if (!canvas) return;
        
        if (!dadosGraficos.instituicoesAtivas || dadosGraficos.instituicoesAtivas.length === 0) {
            const ctx = canvas.getContext('2d');
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('Sem dados disponíveis', canvas.width/2, canvas.height/2);
            return;
        }
        
        const top10 = dadosGraficos.instituicoesAtivas.slice(0, 10);
        const labels = top10.map(inst => {
            const nome = inst.nome;
            return nome.length > 20 ? nome.substring(0, 20) + '...' : nome;
        });
        
        // Dados apenas da taxa de finalização
        const dadosTaxaFinalizacao = top10.map(inst => inst.taxa_finalizacao || 0);
        
        // Cores em gradiente baseado na performance
        const cores = dadosTaxaFinalizacao.map(taxa => {
            if (taxa >= 80) return '#28a745'; // Verde para taxa alta
            else if (taxa >= 60) return '#ffc107'; // Amarelo para taxa média
            else if (taxa >= 40) return '#fd7e14'; // Laranja para taxa baixa
            else return '#dc3545'; // Vermelho para taxa muito baixa
        });
        
        new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Participação (%)',
                    data: dadosTaxaFinalizacao,
                    backgroundColor: cores,
                    borderColor: cores,
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y', // Gráfico horizontal
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Participação (%)',
                            font: { weight: 'bold', size: 14 }
                        },
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { 
                            callback: function(value) {
                                return value + '%';
                            },
                            font: { size: 12 }
                        }
                    },
                    y: {
                        grid: { display: false },
                        ticks: { font: { size: 11 } }
                    }
                },
                plugins: {
                    legend: { 
                        display: false // Esconder legenda já que é auto-explicativo
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        cornerRadius: 8,
                        callbacks: {
                            title: function(context) {
                                return top10[context[0].dataIndex].nome;
                            },
                            label: function(context) {
                                const inst = top10[context.dataIndex];
                                return [
                                    `Participação: ${context.parsed.x}%`,
                                    `Total de avaliações finalizadas: ${inst.finalizados || 0}`,
                                    //`Total de Inscrições: ${inst.total_inscricoes || 0}`
                                ];
                            },
                            afterBody: function(context) {
                                const inst = top10[context[0].dataIndex];
                                const tipoRede = inst.tipo_rede === 'E' ? 'Estadual' : 
                                            inst.tipo_rede === 'M' ? 'Municipal' : 'Particular/Outro';
                                return `Tipo: ${tipoRede}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Atualizar título do gráfico de instituições
    function atualizarTituloGraficoInstituicoes() {
        const elemento = document.querySelector('#graficoTopInstituicoes').closest('.grafico-card').querySelector('h4');
        if (elemento) {
            elemento.innerHTML = '🏆 Ranking de Instituições por Taxa de Finalização';
        }
    }

    // 7. Gráfico de Finalização por Simulado
    function criarGraficoFinalizacaoSimulado() {
        const canvas = document.getElementById('graficoFinalizacaoSimulado');
        if (!canvas) return;
        
        if (!dadosGraficos.finalizacaoPorSimulado || dadosGraficos.finalizacaoPorSimulado.length === 0) {
            const ctx = canvas.getContext('2d');
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('Sem dados de simulados', canvas.width/2, canvas.height/2 - 15);
            ctx.fillText('com inscrições', canvas.width/2, canvas.height/2 + 15);
            return;
        }
        
        const labels = dadosGraficos.finalizacaoPorSimulado.map(item => {
            const nome = item.simulado_nome;
            return nome.length > 30 ? nome.substring(0, 30) + '...' : nome;
        });
        
        const dadosFinalizados = dadosGraficos.finalizacaoPorSimulado.map(item => item.finalizados);
        const dadosPendentes = dadosGraficos.finalizacaoPorSimulado.map(item => item.nao_finalizados);
        
        new Chart(canvas, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Realizadas',
                        data: dadosFinalizados,
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: '#28a745',
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false,
                    },
                    {
                        label: 'Pendentes',
                        data: dadosPendentes,
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: '#dc3545',
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false,
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        grid: { display: false },
                        ticks: {
                            maxRotation: 45,
                            font: { size: 12 }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { stepSize: 1, font: { size: 12 } }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            padding: 25,
                            usePointStyle: true,
                            font: { size: 14, weight: '600' }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.9)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        cornerRadius: 8,
                        callbacks: {
                            title: function(context) {
                                const index = context[0].dataIndex;
                                return dadosGraficos.finalizacaoPorSimulado[index].simulado_nome;
                            },
                            afterBody: function(context) {
                                const index = context[0].dataIndex;
                                const item = dadosGraficos.finalizacaoPorSimulado[index];
                                return [
                                    `Total de inscrições: ${item.total_inscricoes}`,
                                    `Taxa de finalização: ${item.taxa_finalizacao}%`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }
}

// UX com loading nos botões
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '⏳ Aplicando filtros...';
                submitBtn.disabled = true;
                
                // Re-habilitar o botão após um tempo (fallback)
                setTimeout(() => {
                    atualizarTituloGraficoInstituicoes();
                    if (submitBtn.disabled) {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 10000);
            }
        });
    });
});

// Função para atualizar dashboard
function atualizarDashboard() {
    location.reload();
}

// Log de finalização
console.log('✅ Dashboard carregado com sucesso!');
</script>