<?php
if (!defined('CORE_INCLUIDO')) { exit(); }
?>

<style>
/* Loading Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(139, 0, 139, 0.9), rgba(160, 32, 240, 0.9));
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.loading-subtext {
    font-size: 14px;
    opacity: 0.8;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade out animation */
.loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

/* Main container initially hidden */
.auditoria-container {
    opacity: 0;
    transition: opacity 0.5s ease-in;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.auditoria-container.loaded {
    opacity: 1;
}

.header-auditoria {
    background: linear-gradient(135deg, #8B008B, #A020F0);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #8B008B;
}

.stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #8B008B;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
}

.dashboard-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    text-align: center;
}

.dashboard-placeholder {
    padding: 60px 20px;
    color: #6c757d;
}

.dashboard-placeholder-icon {
    font-size: 64px;
    color: #dee2e6;
    margin-bottom: 20px;
}

.filtros-container {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.filtros-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filtro-grupo {
    display: flex;
    flex-direction: column;
}

.filtro-grupo label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
}

.filtro-grupo select,
.filtro-grupo input {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.filtro-grupo select:focus,
.filtro-grupo input:focus {
    outline: none;
    border-color: #8B008B;
    box-shadow: 0 0 0 3px rgba(139, 0, 139, 0.1);
}

.botoes-filtro {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #8B008B;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #7a007a;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #218838;
    transform: translateY(-1px);
}

.btn-dashboard {
    background: linear-gradient(45deg, #8B008B, #A020F0);
    color: white;
    font-size: 16px;
    padding: 15px 30px;
    box-shadow: 0 4px 15px rgba(139, 0, 139, 0.3);
}

.btn-dashboard:hover:not(:disabled) {
    background: linear-gradient(45deg, #7a007a, #9010e0);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 0, 139, 0.4);
}

.btn-loading {
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.filtros-ativos {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.filtro-ativo {
    display: inline-flex;
    align-items: center;
    background-color: #e9ecef;
    padding: 5px 10px;
    border-radius: 15px;
    margin: 2px;
    font-size: 12px;
}

.filtro-ativo .remover {
    margin-left: 5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 10px;
}

.tabela-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.auditoria-table {
    width: 100%;
    border-collapse: collapse;
}

.auditoria-table th {
    background: linear-gradient(135deg, #8B008B, #A020F0);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.auditoria-table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
    font-size: 13px;
}

.auditoria-table tbody tr:hover {
    background-color: #f8f9fa;
}

.auditoria-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

.tipo-rede-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.tipo-rede-estadual {
    background-color: #d4edda;
    color: #155724;
}

.tipo-rede-municipal {
    background-color: #d1ecf1;
    color: #0c5460;
}

.tipo-rede-outro {
    background-color: #f8d7da;
    color: #721c24;
}

.sem-dados {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.sem-dados-icon {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 15px;
}

.paginacao {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f8f9fa;
    margin-top: 20px;
    border-radius: 8px;
}

.paginacao-info {
    color: #6c757d;
    font-size: 14px;
}

.paginacao-botoes {
    display: flex;
    gap: 5px;
}

.paginacao-botoes a,
.paginacao-botoes span {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-decoration: none;
    color: #495057;
    font-size: 14px;
}

.paginacao-botoes a:hover {
    background-color: #e9ecef;
}

.paginacao-botoes .ativo {
    background-color: #8B008B;
    color: white;
    border-color: #8B008B;
}

.btn-detalhes {
    padding: 4px 8px;
    background-color: #17a2b8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
}

.btn-detalhes:hover {
    background-color: #138496;
}

@media (max-width: 768px) {
    .filtros-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tabela-container {
        overflow-x: auto;
    }
    
    .botoes-filtro {
        justify-content: center;
    }
}
</style>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">Carregando Monitoramento</div>
        <div class="loading-subtext">Preparando dados de auditoria...</div>
    </div>
</div>

<div class="auditoria-container" id="mainContent">
    <!-- Header -->
    <div class="header-auditoria">
        <h1 style="margin: 0; font-size: 24px;">🔍 Monitoramento de Simulados Online</h1>
        <p style="margin: 10px 0 0 0; opacity: 0.9;">Sistema de auditoria e monitoramento em tempo real</p>
    </div>

    <!-- Estatísticas -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number"><?= number_format($this->_total_registros) ?></div>
            <div class="stat-label">Total Processados</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= count($this->_simulados_disponiveis) ?></div>
            <div class="stat-label">Simulados Processados</div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="filtros-container">
        <h3 style="margin-top: 0; color: #333; margin-bottom: 20px;">
            🎛️ Filtros de Monitoramento
        </h3>
        
        <form method="GET" action="">
            <input type="hidden" name="m" value="simulados_online">
            <input type="hidden" name="a" value="listar_auditoria">
            
            <div class="filtros-grid">
                <div class="filtro-grupo">
                    <label for="filtro_simulado">📝 Simulado:</label>
                    <select name="filtro_simulado" id="filtro_simulado">
                        <option value="">Todos os simulados</option>
                        <?php foreach ($this->_simulados_disponiveis as $id => $nome): ?>
                            <option value="<?= $id ?>" <?= isset($this->_filtros_ativos['simulado']) && $this->_filtros_ativos['simulado'] == $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($nome) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filtro-grupo">
                    <label for="data_inicio">📅 Data Início:</label>
                    <input type="date" name="data_inicio" id="data_inicio" 
                           value="<?= isset($this->_filtros_ativos['data_inicio']) ? htmlspecialchars($this->_filtros_ativos['data_inicio']) : '' ?>">
                </div>

                <div class="filtro-grupo">
                    <label for="data_fim">📅 Data Fim:</label>
                    <input type="date" name="data_fim" id="data_fim" 
                           value="<?= isset($this->_filtros_ativos['data_fim']) ? htmlspecialchars($this->_filtros_ativos['data_fim']) : '' ?>">
                </div>

                <div class="filtro-grupo">
                    <label for="bimestre">📚 Aplicação:</label>
                    <select name="bimestre" id="bimestre">
                        <option value="">Todas as aplicações</option>
                        <?php foreach ($this->_bimestres_disponiveis as $id => $nome): ?>
                            <option value="<?= $id ?>" <?= isset($this->_filtros_ativos['bimestre']) && $this->_filtros_ativos['bimestre'] == $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($nome) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filtro-grupo">
                    <label for="tipo_rede">🏫 Tipo de Rede:</label>
                    <select name="tipo_rede" id="tipo_rede">
                        <option value="todos">Todas as redes</option>
                        <option value="E" <?= isset($this->_filtros_ativos['tipo_rede']) && $this->_filtros_ativos['tipo_rede'] == 'E' ? 'selected' : '' ?>>Estadual</option>
                        <option value="M" <?= isset($this->_filtros_ativos['tipo_rede']) && $this->_filtros_ativos['tipo_rede'] == 'M' ? 'selected' : '' ?>>Municipal</option>
                        <option value="0" <?= isset($this->_filtros_ativos['tipo_rede']) && $this->_filtros_ativos['tipo_rede'] == '0' ? 'selected' : '' ?>>Particular/Outro</option>
                    </select>
                </div>

                <?php if (Core::registro('usuario')->obterGrupo() == 1): ?>
                <div class="filtro-grupo">
                    <label for="instituicao">🏢 Instituição:</label>
                    <select name="instituicao" id="instituicao">
                        <option value="">Todas as instituições</option>
                        <?php foreach ($this->_instituicoes_disponiveis as $id => $nome): ?>
                            <option value="<?= $id ?>" <?= isset($this->_filtros_ativos['instituicao']) && $this->_filtros_ativos['instituicao'] == $id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($nome) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>

                <div class="filtro-grupo">
                    <label for="busca_aluno">👤 Buscar Aluno/Matrícula:</label>
                    <input type="text" name="busca_aluno" id="busca_aluno" 
                           placeholder="Nome do aluno ou matrícula..."
                           value="<?= isset($this->_filtros_ativos['busca_aluno']) ? htmlspecialchars($this->_filtros_ativos['busca_aluno']) : '' ?>">
                </div>
            </div>

            <div class="botoes-filtro">
                <button type="submit" class="btn btn-primary">
                    🔍 Aplicar Filtros
                </button>
                <a href="?m=simulados_online&a=listar_auditoria" class="btn btn-secondary">
                    🗑️ Limpar Filtros
                </a>
                <a href="?m=simulados_online&a=dashboard_auditoria" class="btn btn-success">
                    📊 Ver Dashboard Completo
                </a>
            </div>

            <!-- Filtros Ativos -->
            <?php if (!empty($this->_filtros_ativos)): ?>
                <div class="filtros-ativos">
                    <strong>Filtros aplicados:</strong>
                    <?php foreach ($this->_filtros_ativos as $tipo => $valor): ?>
                        <?php 
                        $label = '';
                        $valor_exibir = $valor;
                        switch($tipo) {
                            case 'simulado': 
                                $label = 'Simulado'; 
                                $valor_exibir = isset($this->_simulados_disponiveis[$valor]) ? $this->_simulados_disponiveis[$valor] : $valor;
                                break;
                            case 'bimestre': 
                                $label = 'Bimestre'; 
                                $valor_exibir = isset($this->_bimestres_disponiveis[$valor]) ? $this->_bimestres_disponiveis[$valor] : $valor . 'º Aplicação';
                                break;
                            case 'data_inicio': $label = 'Data Início'; break;
                            case 'data_fim': $label = 'Data Fim'; break;
                            case 'tipo_rede': 
                                $label = 'Tipo Rede'; 
                                $tipos = ['E' => 'Estadual', 'M' => 'Municipal', '0' => 'Particular/Outro'];
                                $valor_exibir = isset($tipos[$valor]) ? $tipos[$valor] : $valor;
                                break;
                            case 'instituicao': 
                                $label = 'Instituição'; 
                                $valor_exibir = isset($this->_instituicoes_disponiveis[$valor]) ? $this->_instituicoes_disponiveis[$valor] : $valor;
                                break;
                            case 'busca_aluno': $label = 'Busca'; break;
                        }
                        
                        // Criar URL para remover filtro
                        $url_params = $_GET;
                        unset($url_params[$tipo]);
                        $url_remover = '?' . http_build_query($url_params);
                        ?>
                        <span class="filtro-ativo">
                            <?= htmlspecialchars($label) ?>: <?= htmlspecialchars($valor_exibir) ?>
                            <a href="<?= htmlspecialchars($url_remover) ?>" class="remover" title="Remover filtro">×</a>
                        </span>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </form>
    </div>

    <!-- Tabela de Resultados -->
    <div class="tabela-container">
        <?php if (!empty($this->_dados_auditoria)): ?>
            <table class="auditoria-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>📅 Data/Hora</th>
                        <th>👤 Aluno</th>
                        <th>🎫 Matrícula</th>
                        <th>📝 Simulado</th>
                        <th>🏢 Instituição</th>
                        <th>🏫 Tipo Rede</th>
                        <th>🔧 Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($this->_dados_auditoria as $registro): ?>
                        <tr>
                            <td>
                                <strong style="color: #8B008B;">#<?= htmlspecialchars($registro['id']) ?></strong>
                            </td>
                            <td>
                                <div style="font-weight: 600;"><?= htmlspecialchars(explode(' ', $registro['data_formatada'])[0]) ?></div>
                                <div style="font-size: 11px; color: #6c757d;"><?= htmlspecialchars($registro['hora_formatada']) ?></div>
                            </td>
                            <td>
                                <div style="font-weight: 600;"><?= htmlspecialchars($registro['aluno_nome']) ?></div>
                            </td>
                            <td>
                                <span style="font-family: monospace; background-color: #f8f9fa; padding: 2px 6px; border-radius: 3px;">
                                    <?= htmlspecialchars($registro['matricula']) ?>
                                </span>
                            </td>
                            <td>
                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                                     title="<?= htmlspecialchars($registro['simulado_nome']) ?>">
                                    <?= htmlspecialchars($registro['simulado_nome']) ?>
                                </div>
                            </td>
                            <td>
                                <div style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                                     title="<?= htmlspecialchars($registro['instituicao_nome']) ?>">
                                    <?= htmlspecialchars($registro['instituicao_nome']) ?>
                                </div>
                            </td>
                            <td>
                                <span class="tipo-rede-badge tipo-rede-<?= $registro['tipo_rede'] == 'E' ? 'estadual' : ($registro['tipo_rede'] == 'M' ? 'municipal' : 'outro') ?>">
                                    <?= htmlspecialchars($registro['tipo_rede_descricao']) ?>
                                </span>
                            </td>
                            <td>
                                <button type="button" class="btn-detalhes" onclick="verDetalhes(<?= $registro['id'] ?>)">
                                    🔍 Ver
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="sem-dados">
                <div class="sem-dados-icon">📊</div>
                <h3>Nenhum registro encontrado</h3>
                <p>Não foram encontrados registros de auditoria com os filtros aplicados.</p>
                <p><small>Tente ajustar os filtros ou verificar se há dados na tabela de auditoria.</small></p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Paginação -->
    <?php if (!empty($this->_dados_auditoria) && isset($this->_paginacao)): ?>
    <div class="paginacao">
        <div class="paginacao-info">
            Mostrando <?= number_format($this->_paginacao['inicio']) ?> - <?= number_format($this->_paginacao['fim']) ?> 
            de <?= number_format($this->_paginacao['total_registros']) ?> registros
        </div>
        
        <div class="paginacao-botoes">
            <?php if ($this->_paginacao['pagina_atual'] > 1): ?>
                <a href="?<?= http_build_query(array_merge($_GET, ['pagina' => 1])) ?>">« Primeira</a>
                <a href="?<?= http_build_query(array_merge($_GET, ['pagina' => $this->_paginacao['pagina_atual'] - 1])) ?>">‹ Anterior</a>
            <?php endif; ?>

            <?php 
            $inicio = max(1, $this->_paginacao['pagina_atual'] - 2);
            $fim = min($this->_paginacao['total_paginas'], $this->_paginacao['pagina_atual'] + 2);
            
            for ($i = $inicio; $i <= $fim; $i++): 
            ?>
                <?php if ($i == $this->_paginacao['pagina_atual']): ?>
                    <span class="ativo"><?= $i ?></span>
                <?php else: ?>
                    <a href="?<?= http_build_query(array_merge($_GET, ['pagina' => $i])) ?>"><?= $i ?></a>
                <?php endif; ?>
            <?php endfor; ?>

            <?php if ($this->_paginacao['pagina_atual'] < $this->_paginacao['total_paginas']): ?>
                <a href="?<?= http_build_query(array_merge($_GET, ['pagina' => $this->_paginacao['pagina_atual'] + 1])) ?>">Próxima ›</a>
                <a href="?<?= http_build_query(array_merge($_GET, ['pagina' => $this->_paginacao['total_paginas']])) ?>">Última »</a>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Modal para detalhes -->
<div id="modalDetalhes" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div style="background-color: white; margin: 2% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 900px; max-height: 90%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; border-bottom: 2px solid #8B008B; padding-bottom: 15px;">
            <h3 style="color: #8B008B; margin: 0; display: flex; align-items: center; gap: 10px;">
                🔍 Detalhes da Auditoria
            </h3>
            <button onclick="fecharModal()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 35px; height: 35px; cursor: pointer; font-size: 18px; display: flex; align-items: center; justify-content: center;">
                ×
            </button>
        </div>
        <div id="conteudoModal" style="line-height: 1.6;">
            <!-- Conteúdo será carregado via AJAX -->
        </div>
    </div>
</div>

<script>
// Variáveis globais
let dashboardCarregado = false;

// Função de loading inicial da página
document.addEventListener('DOMContentLoaded', function() {
    // Simular loading inicial (remover delay em produção)
    setTimeout(function() {
        // Esconder loading overlay
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        
        loadingOverlay.classList.add('fade-out');
        mainContent.classList.add('loaded');
        
        // Remover elemento loading do DOM após a animação
        setTimeout(function() {
            loadingOverlay.style.display = 'none';
        }, 500);
        
    }, 1500); // 1.5 segundos de loading inicial
});



// Funções para o modal e interações
function verDetalhes(id) {
    document.getElementById('conteudoModal').innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 24px; margin-bottom: 10px;">⏳</div>Carregando detalhes...</div>';
    document.getElementById('modalDetalhes').style.display = 'block';
    
    fetch('?m=simulados_online&a=obter_detalhes_auditoria&id=' + encodeURIComponent(id))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('conteudoModal').innerHTML = formatarDetalhesReais(data.dados);
            } else {
                document.getElementById('conteudoModal').innerHTML = '<div style="color: red; text-align: center;">❌ Erro ao carregar os detalhes: ' + (data.message || 'Erro desconhecido') + '</div>';
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            document.getElementById('conteudoModal').innerHTML = '<div style="color: red; text-align: center;">❌ Erro de conexão ao carregar os detalhes.</div>';
        });
}

function formatarDetalhesReais(dados) {
    let html = '<div style="display: grid; gap: 20px;">';
    
    // Informações básicas
    html += '<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #8B008B;">';
    html += '<h4 style="margin-top: 0; color: #8B008B;">📋 Informações do Registro</h4>';
    html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';
    html += '<div><strong>ID:</strong> ' + htmlEscape(dados.id) + '</div>';
    html += '<div><strong>Data/Hora:</strong> ' + htmlEscape(dados.data_formatada) + '</div>';
    html += '<div><strong>Aluno:</strong> ' + htmlEscape(dados.aluno_nome) + '</div>';
    html += '<div><strong>Matrícula:</strong> ' + htmlEscape(dados.matricula) + '</div>';
    html += '<div><strong>Simulado:</strong> ' + htmlEscape(dados.simulado_nome) + '</div>';
    html += '<div><strong>Instituição:</strong> ' + htmlEscape(dados.instituicao_nome) + '</div>';
    html += '<div><strong>Tipo de Rede:</strong> ' + htmlEscape(dados.tipo_rede_descricao) + '</div>';
    html += '</div>';
    html += '</div>';
    
    // Dados de Auditoria
    if (dados.dados_json || (dados.dados_brutos && dados.dados_brutos.trim() !== '')) {
        html += '<div style="background-color: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 4px solid #0c5460;">';
        html += '<h4 style="margin-top: 0; color: #0c5460;">📊 Dados de Auditoria</h4>';
        
        if (dados.dados_json) {
            html += '<div style="background-color: white; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">';
            html += '<pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 12px; line-height: 1.4;">' + JSON.stringify(dados.dados_json, null, 2) + '</pre>';
            html += '</div>';
        } else {
            html += '<div style="background-color: white; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">';
            html += '<pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 12px; line-height: 1.4;">' + htmlEscape(dados.dados_brutos) + '</pre>';
            html += '</div>';
        }
        html += '</div>';
    }
    
    // Dados da sessão
    if (dados.sessao && dados.sessao.trim() !== '') {
        html += '<div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #856404;">';
        html += '<h4 style="margin-top: 0; color: #856404;">🔧 Dados da Sessão</h4>';
        html += '<div style="background-color: white; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto;">';
        html += '<pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 12px; line-height: 1.4;">' + htmlEscape(dados.sessao) + '</pre>';
        html += '</div>';
        html += '</div>';
    }
    
    html += '</div>';
    return html;
}

// Função auxiliar para escape HTML
function htmlEscape(str) {
    return String(str)
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
}

// Função para fechar modal
function fecharModal() {
    document.getElementById('modalDetalhes').style.display = 'none';
}

// Fechar modal clicando fora
window.onclick = function(event) {
    const modal = document.getElementById('modalDetalhes');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}

// Atalhos de teclado
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        fecharModal();
    }
});

// UX com loading nos botões
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '⏳ Carregando...';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>