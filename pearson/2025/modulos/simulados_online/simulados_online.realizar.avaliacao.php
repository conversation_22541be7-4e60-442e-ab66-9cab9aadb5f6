<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');

class LSimuladosParaRealizar extends Listagem
{

	protected $_procuraSQL = null;

	public function prepararListagem ()
	{
		$this->_prepararBusca();

		$this->_prepararOrdenacao();

		$this->_montaProcuraSQL();

		$this->_preObterDados();

		$this->_prepararPaginacao();

		$this->_preparaTituloOrdenacao();

		$this->_obterDados();

		$this->_prepararFormularioAcoes();

		
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.realizar.listagem.html.php');
		$this->_finalizarRenderizacao();
	}

	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_simulados')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_simulados')->termoProcurado != null && Core::modulo('procurar_simulados')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_simulados')->termoProcurado) .'%';
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_simulados')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}

			if ( Core::modulo('procurar_simulados')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(s_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_simulados')->letraSelecionada);
			}

			if ( is_array($this->_procuraSQL) ) {
				$this->_procuraSQL = ' AND '. implode(' AND ', $this->_procuraSQL);
			}
		}
	}

	protected function _preObterDados ()
	{
		$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM simulados WHERE s_instituicao = %s %s',
				Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ), //Core::registro('instituicao')->obterID() ),
				$this->_procuraSQL ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}

	protected function _obterDados ()
	{
		$rs = Core::registro('db')->query( sprintf(
			'SELECT simulados.*,
			(SELECT COUNT(0) FROM simulados_inscricoes WHERE si_simulado = simulados.s_id) AS numero_inscritos,
			(SELECT COUNT(0) FROM questoes WHERE q_simulado = simulados.s_id) AS numero_questoes,
			(SELECT SUM(q_pontos) FROM questoes WHERE q_simulado = simulados.s_id) AS pontuacao_total
			FROM simulados
			WHERE s_instituicao = %s %s
			ORDER BY %s %s
			LIMIT %s',
				Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ), //Core::registro('instituicao')->obterID() ),
				$this->_procuraSQL,
				$this->_ordenacao->ordenarPor,
				$this->_ordenacao->tipoOrdem,
				$this->_paginacao->paginador->obterLimitesSQL() ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$sim = new Simulado($row['s_id']);

				$sim->fixarNome($row['s_nome']);
				$sim->fixarNumeroDeTipos($row['s_tipos']);
				$sim->fixarInstituicao( new Instituicao(ProvaFloripa::$instituicao->obterID()); //$row['s_instituicao']) );
				$sim->fixarDataRealizacao($row['s_data']);
				$sim->fixarDataRealizacaoFim($row['s_data_fim']);
				$sim->fixarEscoresTotais($row['s_escores_totais']);

				$sim->fixarNumeroInscritos($row['numero_inscritos']);
				$sim->fixarNumeroQuestoes($row['numero_questoes']);
				$sim->fixarPontuacaoTotal($row['pontuacao_total']);

				$sim->fixarTextoInicio($row['s_texto_inicio']);
				$sim->fixarTextoFim($row['s_texto_fim']);

				$aulas = array();
				foreach(explode(';', $row['s_aulas']) as $aID) {
					if ( ! (int) $aID)
						continue;

					$aulas[$aID] = new Aula($aID);
					$aulas[$aID]->carregar();
				}

				$sim->fixarAulas( $aulas );

				$this->_dados[] = $sim;
			}
		}
		$rs->free();
	}

	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_simulados'));

		Core::modulo('procurar_simulados')->prepararProcura('form_procurar_simulados', 'caixa_procurar_simulados');
		Core::modulo('procurar_simulados')->configurarTermo();
		Core::modulo('procurar_simulados')->configurarOndeProcurar( array('s_nome' => 'Nome da simulado') );
		Core::modulo('procurar_simulados')->configurarAlfabeto();

		Core::modulo('procurar_simulados')->carregarProcura();

		Core::modulo('procurar_simulados')->prepararAtalho();
	}

	protected function _prepararOrdenacao ()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_simulados'));

		$this->_ordenacao = new Ordenacao_Padrao('form_ord_simulados');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('s_nome' => 'Nome', 's_data' => 'Data inicial de realização', 'numero_inscritos' => 'Alunos', 'inscricoes_abertas' => 'Inscrições abertas'), 's_data' );
		$this->_ordenacao->configurarCampoTipoOrdem(NULL, Ordenacao::DESC);
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();

		Core::modulo('ord_simulados')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_simulados')->carregarOrdenacao();
		Core::modulo('ord_simulados')->prepararAtalho(null, 'caixa_ord_simulados');

		if ( Core::modulo('procurar_simulados')->procuraSolicitada() ) {
			Core::modulo('ord_simulados')->anexarHTML( Core::modulo('procurar_simulados')->obterHTMLParaAnexar() );
		}
	}

	protected function _prepararPaginacao ()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_simulados'));

		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}

	protected function _prepararFormularioAcoes ()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_simulados', 'acao' => Gerenciador_URL::gerarLink('simulados_online', 'listar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();

		$ids = array();
		foreach ($this->_dados as &$v) {
			$ids[] = $v->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('simular', 'Iniciar Avalia&ccedil;&atilde;o', 'Deseja iniciar essa avalia&ccedil;&atilde;o agora?', 'simular');

		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();

				if ( $this->_formulario->obterBotaoEnviador() == 'simular' ) {
					$ids = $this->_formulario->obterIDsSelecionados();
					if (is_array($ids))
					{
						Core::modulo('solicitado')->aIniciarSimulado($ids);
					}
				}
			}
			catch (Formulario_Exception $e)	{ }
		}
	}

	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;

		$this->_ordenacao->configurarTituloDeTotais('simulados', $totais);
	}

}

?>