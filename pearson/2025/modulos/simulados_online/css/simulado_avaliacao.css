.wrap {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button {
  min-width: 300px;
  min-height: 60px;
  font-family: 'Nuni<PERSON>', sans-serif;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1.3px;
  font-weight: 700;
  color: #FFFFFF;
  background: #07003B;
  background: linear-gradient(90deg, rgba(7,0,59) 0%, rgba(139,0,139) 100%);
  border: none;
  border-radius: 1000px;
  box-shadow: 12px 12px 24px rgba(115,111,145);
  transition: all 0.3s ease-in-out 0s;
  cursor: pointer;
  outline: none;
  position: relative;
  padding: 10px;
  }

.button::before {
  content: '';
  border-radius: 1000px;
  min-width: calc(300px + 12px);
  min-height: calc(60px + 12px);
  border: 6px solid #8B008B;
  box-shadow: 0 0 60px rgba(143,188,143);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all .3s ease-in-out 0s;
}

.button:hover, .button:focus {
  color: #FFFFFF;
  transform: translateY(-6px);
}

.button:hover::before, .button:focus::before {
  opacity: 1;
}

.button::after {
  content: '';
  width: 30px; height: 30px;
  border-radius: 100%;
  border: 6px solid #8B008B;
  position: absolute;
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: ring 1.5s infinite;
}

.button:hover::after, .button:focus::after {
  animation: none;
  display: none;
}

@keyframes ring {
  0% {
    width: 30px;
    height: 30px;
    opacity: 1;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

.swiper-container {
  width: 95vw;
  /*width: 1450px;*/
  height: 100%;
}
.swiper-slide {
  min-height: 200px;

  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

div.geral{
	display: block;
	height: auto;
	width: 100%;
}

div.title{
	background-color: #eee;
    border-radius: 10px 10px 0px 0px;
    font-size: 20px;
    margin-bottom: 0;
    padding: 10px;
    font-weight: bold;
}

.crono{
	border-bottom: 1px solid #DEDEDE;
	border-left: 1px solid #DEDEDE;
	border-right: 1px solid #DEDEDE;
    border-radius: 0px 0px 5px 5px;
    color: #555555;
    font-size: 13px;
    margin: 0px auto;
    padding: 5px 5px 2px;
    width: 125px;
    overflow: hidden;
    display:none;
}

img.crono_start, img.crono_stop{
	cursor: pointer;
	display:none;
}

#crono_time{
	display: block;
    float: right;
    margin-left: 5px;
}

.conteudo {
    border: 1px solid #DEDEDE;
    border-radius: 0px 0px 5px 5px;
    margin-top: 0;
    background: #fff;
}

.num_questao{
	border-bottom: 1px solid #DEDEDE;
    border-radius: 5px 0 5px 0;
    border-right: 1px solid #DEDEDE;
    color: #333333;
    font-size: 14px;
    padding: 5px 10px;
    float:left;
}

.crono_questao{
	border-bottom: 1px solid #DEDEDE;
    border-left: 1px solid #DEDEDE;
    border-radius: 0 5px 0 5px;
    float: right;
    font-size: 14px;
    padding: 5px;
    color:#333333;
    display:none;
}

.tbl_conteudo{
	width: 100%;
	border-collapse: collapse; 
	border-spacing: 0;
}

.tbl_conteudo td{
	border-spacing: 0;
	padding: 0;
	line-height: 20px;
}

.botoes{
	padding: 5px;
	border-top: 1px solid #DEDEDE;
}

.resposta_questao {
    padding: 15px;
	width: 71%;
	text-align: right;
}

.questao{
	width: 100%;
}

.simulado_general_start{
	border: 1px solid #DEDEDE;
	border-radius: 0px 0px 5px 5px;
}

.simulado_general_end{
	border: 1px solid #DEDEDE;
	border-radius: 0px 0px 5px 5px;
	background: #fff;
}

#modal_load{
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	background: rgba(150, 150, 150, 0.8);
	width: 100%;
	height: 100%;
	z-index: 999999999999999;
}

.pergunta {
	font-size:30px;
}

.multiplaescolha {
    width: 60px;
    height: 35px;
    font-size: 18px;
    text-align: center;
}

.wrapper {
    border-radius: 0.5rem;
    border: 1px solid #ccc;
    padding: 15px;
    color: #000;
    display: flex;
    align-items: flex-start; 
    font-weight: bold;
    background-color: #ddd;
    font-size: 14px;
    text-align: justify;
    width: 100%;
    min-height: 50px;
    gap: 10px; 
}

li.realizada button{
	color:#FFFFFF!important;
	background-color: #70AF71!important;
	border: 1px solid #70AF71!important;
}

li.pendente button{
	color:#FFFFFF!important;
	background-color: #8B008B!important;
	border: 1px solid #8B008B!important;
	opacity: 80%!important;
}

.botoes{
	padding: 5px;
	border-top: 1px solid #DEDEDE;
}

.resposta_questao_valor {
    cursor: pointer !important;
    appearance: none !important;
    width: 30px !important;
    height: 30px !important;
    min-width: 30px !important; 
    min-height: 30px !important; 
    max-width: 30px !important; 
    max-height: 30px !important; 
    border: 2px solid #8B008B !important;
    border-radius: 50% !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    outline: none !important;
    flex-shrink: 0 !important; 
    margin-top: 2px; 
}

.resposta_questao_valor:before  {
content: '';
width: 15px;
height: 15px;
border: 1px solid #8B008B;
background: #8B008B;
border-radius: 50%;
opacity: 0%;
transition: all 200ms ease-in-out;
position: absolute;
}

.resposta_questao_valor:checked:before {
opacity: 100%;
}

.wrapper .resposta_questao_valor:focus {
box-shadow: 0 0 5px #D8BFD8;
}

.resposta_questao_valor2:before  {
content: '';
width: 15px;
height: 15px;
border: 1px solid #FFF;
background: #FFF;
border-radius: 50%;
opacity: 0%;
transition: all 200ms ease-in-out;
position: absolute;
}

.resposta_questao_valor2:checked:before {
opacity: 100%;
}

.resposta_questao_valor2:focus {
box-shadow: 0 0 5px #FFF;
}


#modal_load2{
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	background: rgba(150, 150, 150, 0.3);
	width: 100%;
	height: 100%;
	z-index: 999999999999999;
}

#modal_select{
	background-color: #8B008B;
	padding: 15px;
	border-radius: 10px;
	height: 55%;
	border: 2px solid #EEE;
	width: 35%;
	display: block;
	margin: 10% auto;
	color:#FFF;
}

#modal_select p{
	font-size: 24px;
}

#modal_select ul li p{
	font-size: 22px;
}

#td_resposta {
  float: left;
  width: 45%
}

#td_resposta2 {
  float: right;
  width: 65%
}

.botoes td button {
  width: 100%;
  height: 40px;
}

/* Estilos para dispositivos móveis */
body.mobile .wrapper {
  font-size: 30px;
  padding: 10px;
}

body.mobile #td_resposta {
  width: 100%;
}

body.mobile #td_resposta2 {
  width: 100%;
}

body.mobile .resposta_questao {
  padding: 15px;
  width: 100%;
}

body.mobile .botoes td button {
  height: 80px!important;
  font-size: 30px!important;
}

body.mobile .questao{
	font-size: 30px;
}

body.mobile audio {
  width: 50%!important;
  height: 50px!important;
}