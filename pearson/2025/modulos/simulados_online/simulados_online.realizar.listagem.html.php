<!DOCTYPE html>
<html lang="en" translate="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulados Online - Realizar Listagem</title>
    <link rel="stylesheet" href="https://dyubuzjbgoyjh.cloudfront.net/simulados_online/simulado.css"> <!-- Certifique-se de ajustar o caminho para o arquivo CSS -->
</head>
<body>
    <table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
        <tr>
            <td width="1%" nowrap="nowrap" class="sr_simulado_ColHeader" align="center"><b>Realizado</b></td>
            <td width="5%" nowrap="nowrap" class="sr_simulado_ColHeader" align="center"><b>Realizar</b></td>
            <td width="1%" nowrap="nowrap" class="sr_simulado_ColHeader" align="center"><b>Data inicial de realização</b></td>
            <td width="1%" nowrap="nowrap" class="sr_simulado_ColHeader" align="center"><b>Data final de realização</b></td>
            <td class="sr_simulado_ColHeader" nowrap="nowrap" align="center"><b>Nome</b</td>
            <td class="sr_simulado_ColHeader" nowrap="nowrap" align="center"><b>Série</b</td>
            <td class="sr_simulado_ColHeader" nowrap="nowrap" align="center"><b>Aplicação</b</td>
            <td class="sr_simulado_ColHeader" nowrap="nowrap" align="center"><b>Disciplinas</b</td>
        </tr>
        <?
        if ( count($this->_dados) ) {
            $i = 0;
            foreach ( $this->_dados as &$d ) {
        ?>
            <tr onMouseOver="this.className='sr_simulado_ColDataHover'" onMouseOut="this.className='sr_simulado_ColData'" class="sr_simulado_ColData">
                <td nowrap="nowrap" align="center" style="font-size: 14px;">
                <?php 
                    if($d['saf']){
                        echo '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'ajax_sucesso.png"/>';
                    }
                    else{
                        echo '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'ajax_falha.png"/>';
                    }
                ?>
                </td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;">
                <?php 
                    if($d['soru'] && $d['saf']){
                        echo '';                
                    }
                    else{
                        echo '
                            <a href="'.Gerenciador_URL::gerarLink('simulados_online', 'realizar', array('id' => $d['soID'])).'" class="sr_simulado_button">
                                <img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'start.png">
                                <span>Realizar avalia&ccedil;&atilde;o</span>
                            </a>
                        ';
                    }
                ?>
                </td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;"><strong><?= strftime('%d/%m/%Y %H:%M', $d['sodri']); ?></strong></td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;"><strong><?= strftime('%d/%m/%Y %H:%M', $d['sodrf']); ?></strong></td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;">
                <?php 
                    echo $d['snome'];                
                ?>
                </td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;">
                    <?php echo $d['sserie']; ?>
                </td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;">
                    <?php echo $d['sbi']; ?>
                </td>
                <td nowrap="nowrap" align="center" style="font-size: 14px;">
                    <?php echo @implode(', ', $d['sdps']); ?>
                </td>
            </tr>
        <?
            }
        } else {
            Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
            Core::modulo('alerta')->prepararAlerta('Nenhuma avalia&ccedil;&atilde;o encontrada!');
        ?>
            <tr>
                <td height="200" colspan="99" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
            </tr>
        <?
        }
        ?>
    </table>
</body>
</html>