<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);

class MSimuladosOnline extends Modulo
{
	protected $_listagem;

	public function __construct ()
	{
		parent::__construct();
	}

	public function aListarSimuladosParaRealizar () 
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->remover<PERSON><PERSON>ho('adicionar');
		Core::modulo('navegador')->removerAtalho('procurar');
		Core::modulo('navegador')->remover<PERSON><PERSON>ho('importar');
		Core::modulo('navegador')->remover<PERSON><PERSON><PERSON>('exportar');
		Core::modulo('navegador')->remover<PERSON><PERSON><PERSON>('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();
		
		$simulados = array();
		$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();

		$usuario = new UsuarioInstituido($user_logado_id);
		$usuario->carregar();

		$aluno_id = Aluno::obterIDAlunoPeloUsuario($usuario);

		if($aluno_id){
			$aluno = new Aluno($aluno_id);
			$aluno->carregar();

			$dados = Simulado::obterSimuladosPeloAluno($aluno);
		}
		else{
			//verificar grupos q podem acessar todas
			$dados = Simulado::obterSimuladosParaFormularioIV();
		}

		$this->_dados = array();
		if(count($dados)>0){
			foreach ($dados as $k => $simulado) {
				$sim = new Simulado($simulado->obterID());
				$sim->carregar();

				$sid = $sim->obterID();
				$snome = $sim->obterNome();
				$sbi = $sim->obterBimestre();
				$sodri = $sim->obterDataRealizacao();
				$sodrf = $sim->obterDataRealizacaoFim();
				$soru = $sim->obterRealizacaoUnica();
				$sdps = $sim->obterDisciplinas();

				if(time() < $sodri && Core::registro('usuario')->obterGrupo() != 1){
					continue;
				}

				if(time() > $sodrf && Core::registro('usuario')->obterGrupo() != 1){
					continue;
				}

				if(!is_null($sim->obterSerieAvaliacao())){ 
					$sserie = $sim->obterSerieAvaliacao()->obterNome(); 
				}

				$qs = $sim->obterNumeroQuestoes(true,false);

				if(is_array($qs)){
					foreach ($qs as $qsK => $qsV) {
						$soID = base64_encode('soar_'.$sid.'_'.$qsK);

						$saf = $sim->obterSeOAlunoFez(); 
						if(!empty($saf)){
							$saf = json_decode($saf,true);					
							$saf = $saf[$qsK];
						}

						$this->_dados[$soID]['soID'] = $soID;
						$this->_dados[$soID]['sid'] = $sid;
						$this->_dados[$soID]['snome'] = $snome;
						$this->_dados[$soID]['sbi'] = $sbi;
						$this->_dados[$soID]['sfase'] = $qsK;
						$this->_dados[$soID]['sodri'] = $sodri;
						$this->_dados[$soID]['sodrf'] = $sodrf;
						$this->_dados[$soID]['soru'] = $soru;
						$this->_dados[$soID]['sserie'] = $sserie;
						$this->_dados[$soID]['saf'] = $saf;
						$this->_dados[$soID]['sdps'] = @array_unique($sdps[$qsK]);
					}
				}
				else{
					$fase = 1;

					$soID = base64_encode('soar_'.$sid.'_'.$fase);

					$saf = $sim->obterSeOAlunoFez(); 
					if(!empty($saf)){
						$saf = json_decode($saf,true);					
						$saf = $saf[$fase];
					}

					$this->_dados[$soID]['soID'] = $soID;
					$this->_dados[$soID]['sid'] = $sid;
					$this->_dados[$soID]['snome'] = $snome;
					$this->_dados[$soID]['sbi'] = $sbi;
					$this->_dados[$soID]['sfase'] = $fase;
					$this->_dados[$soID]['sodri'] = $sodri;
					$this->_dados[$soID]['sodrf'] = $sodrf;
					$this->_dados[$soID]['soru'] = $soru;
					$this->_dados[$soID]['sserie'] = $sserie;
					$this->_dados[$soID]['saf'] = $saf;
					$this->_dados[$soID]['sdps'] = @array_unique($sdps[$fase]);
				}				
			}

			if(count($this->_dados) == 1){
				$fev = reset($this->_dados);
				$link = Gerenciador_URL::gerarLink('simulados_online', 'realizar', array('id' => $fev['soID']));

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco($link);
				Core::modulo('redirecionador')->redirecionarDireto();
			}
		}

		//echo'<pre>';print_r($this->_dados);echo'</pre>';

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.realizar.listagem.html.php');
		$this->_finalizarRenderizacao();
	}

	public function aRealizarSimulado ()
	{
		$_SESSION['avaliacao_online']['usuario_id'] 		= 0;
		$_SESSION['avaliacao_online']['inscricao_id'] 		= 0;
		$_SESSION['avaliacao_online']['aluno_id'] 			= 0;
		$_SESSION['avaliacao_online']['simulado_id'] 		= 0;
		$_SESSION['avaliacao_online']['simulado_fase'] 		= 0;
		$_SESSION['avaliacao_online']['simulado_questoes'] 	= array();

		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->removerAtalho('adicionar');
		Core::modulo('navegador')->removerAtalho('procurar');
		Core::modulo('navegador')->removerAtalho('importar');
		Core::modulo('navegador')->removerAtalho('exportar');
		Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();

		$soID = base64_decode(@$_GET['id']);
		$soID = explode('_', $soID);

		$fase = $soID[2];

		$simu = new Simulado((int) $soID[1]);

		if (!$simu->carregar()) {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'Avalia&ccedil;&atilde;o inv&aacute;lida!');
		} 
		else {
			if(time() < $simu->obterDataRealizacao(false) && Core::registro('usuario')->obterGrupo() != 1){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o desta avalia&ccedil;&atilde;o on-line ainda n&atilde;o come&ccedil;ou.');
			}
			elseif(time() > $simu->obterDataRealizacaoFim(false) && Core::registro('usuario')->obterGrupo() != 1){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o desta avalia&ccedil;&atilde;o on-line j&aacute; acabou.');
			}
			else{
				$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();
				
				$usuario = new UsuarioInstituido($user_logado_id);
				$usuario->carregar();

				$msg = "";

				$idInsc = $aluno_id = 0;

				$aluno_pre_id = Aluno::obterIDAlunoPeloUsuario($usuario);

				$simu_serie = $simu->obterSerieAvaliacao()->obterID();

				if($aluno_pre_id){
					$aluno_id = $aluno_pre_id;

					$aluno = new Aluno($aluno_pre_id);
					$aluno->carregar();

					$alu_serie = $aluno->obterTurma()->obterSerie()->obterID();

					if($simu_serie == $alu_serie) {
						$idInsc = Inscricao::obterIDInscricaoPeloAluno($simu, $aluno);

						if (!$idInsc) {
							Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'lista'), 'Voc&ecirc; n&atilde;o pode realizar essa avalia&ccedil;&atilde;o.');
						}
					}
					else{
						Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'lista'), 'Voc&ecirc; s&oacute; pode realizar simulados de sua s&eacute;rie.');
					}
				}
				else{
					$msg = "Seu perfil de usu&aacute;rio n&atilde;o permite o registro de respostas. Somente alunos podem registr&aacute;-las, por&eacute;m voc&ecirc; pode interagir com a avalia&ccedil;&atilde;o da mesma forma que eles.";
				}

				$simulado['numq'] = 0;
				$simulado['tempoTotalHide'] = '00:00:00';

				    $simulado['nome'] = $simu->obterNome();
				    $simulado['numq'] = $simu->obterNumeroQuestoes();
				$simulado['questoes'] = $simu->obterQuestoesIDPorFase($fase);
				 $simulado['tinicio'] = $simu->obterTextoInicio();
				    $simulado['tfim'] = $simu->obterTextoFim();
					$simulado['realizacaoUnica'] = $simu->obterRealizacaoUnica();
				    $simulado['simu'] = $simu->obterID();
				   $simulado['aluno'] = $aluno_id;
				    $simulado['user'] = $user_logado_id;
				     $simulado['msg'] = $msg;
				     		$tempoArr = array();

				//criar aqui um esquema de salvar os dados do aluno em uma session e talvez as questoes tbm para otimizar os dados de salvar.
				$_SESSION['avaliacao_online']['usuario_id'] 		= $user_logado_id;
				$_SESSION['avaliacao_online']['inscricao_id'] 		= $idInsc;
				$_SESSION['avaliacao_online']['aluno_id'] 			= $aluno_id;
				$_SESSION['avaliacao_online']['simulado_id'] 		= $simu->obterID();
				$_SESSION['avaliacao_online']['simulado_fase'] 		= $fase;				     		

				if(isset($idInsc)){
					$inscricao = new Inscricao($idInsc);
					$inscricao->carregar();

					$inscOSF = json_decode($inscricao->obterSimuladoFinalizado(),true);

					if($inscricao->carregar($simu, $aluno)) {		
						$inscricao->limparRespostasMarcadas();
						$inscricao->carregarRespostasMarcadasPorFase($fase);

						if ($simulado['realizacaoUnica'] == 1 && @$inscOSF[$fase] == 1){

							Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('login', 'logout'), 'Voc&ecirc; n&atilde;o pode mais fazer essa Avalia&ccedil;&atilde;o!');
							//Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'Voc&ecirc; n&atilde;o pode mais fazer essa Avalia&ccedil;&atilde;o!');
						}

						$simulado['tempoTotalHide'] = $inscricao->obterTempoTotalDeRealizacaoDoAlunoNoSimulado();
						if(!$simulado['tempoTotalHide']){
					     	$simulado['tempoTotalHide'] = '00:00:00';
					    }
					    else{
					     	$ttmp = explode(":", $simulado['tempoTotalHide']);
					    	$simulado['tempoTotalJS'] = $ttmp[2]+($ttmp[1]*60)+($ttmp[0]*60*60);
					    }

						$simulado['finalizadoSim'] = @$inscOSF[$fase];
						if($simulado['finalizadoSim']){
							$simulado['finalizadoData'] = $inscricao->obterSimuladoFinalizadoData();
						}

						$simulado['todasRespostas'] = $inscricao->obterRespostasMarcadas();

						$n = 1;
						$rTmp = array();

						foreach($simulado['todasRespostas'] as $k => $v){
							if($n == 1){
								$cacheIDq = $v->obterQuestao()->obterID();
							}

							$v->carregar();

						    $rTmp['tempoJS'] = 1;
						    $rTmp['tempo'] = $v->obterTempo();
						    if(!$rTmp['tempo']){
						    	$rTmp['tempo'] = '00:00:00';
						    }
						    else{
						    	$tttmp = explode(":", $rTmp['tempo']);
						    	$rTmp['tempoJS'] = $tttmp[2]+($tttmp[1]*60)+($tttmp[0]*60*60);
						    }

						    $qID = ($v->obterQuestao()->obterID())-$cacheIDq;
						    $tempoArr[$qID] = $rTmp['tempoJS'];

						    $rTmp['valor'] = $v->obterValor();
						    $tipoQ = $v->obterQuestao()->obterTipo();
						    switch ($tipoQ) {
								case Questao::MULTIPLAESCOLHA:
									$rTmp['valor'] = MultiplaEscolha::letra($v->obterValor());
									break;
								case Questao::DISCURSIVA:
								case Questao::ABERTA:
									$rTmp['valor'] = $v->obterTexto();
									break;
								case Questao::SOMATORIO:
									$rTmp['valor'] = $v->obterValor();
									break;
								default:
									break;
							}

							$simulado['respostas'][$v->obterQuestao()->obterID()] = $rTmp;


							$_SESSION['avaliacao_online']['simulado_questoes'][$n] = $v->obterQuestao()->obterID();

							$n++;
						}
					}
				}

				//echo'-><pre>';print_r($simulado);echo'</pre><br>---<br>';

				$this->_iniciarRenderizacao(Modulo::HTML);
					include('simulados_online.realizar.avaliacao.html.php');
				$this->_finalizarRenderizacao();
			}	
		}

		return true;
	}

	public function aResponderQuestaoSimulado()
	{					
		#--- fazer auditoria de lançamento de simulado online

		//Puxa e verifica dados do post.
		$dados = Array();
		if(array_key_exists('d', $_POST)){
			$dados = json_decode($_POST['d'],true);
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Dados n&#227;o recebidos.')));
		}

		//$fase = $soID[2];

		//puxa sessão do aluno q foi gravada na abertura da avaliação.
		$sess_usuario_id = $_SESSION['avaliacao_online']['usuario_id'];
		$sess_aluno_id = $_SESSION['avaliacao_online']['aluno_id'];
		$sess_inscricao_id = $_SESSION['avaliacao_online']['inscricao_id'];
		$sess_simulado_id = $_SESSION['avaliacao_online']['simulado_id'];
		$sess_simulado_fase = $_SESSION['avaliacao_online']['simulado_fase'];

		//Puxa dados do usuário no AvRede.
		$usuario_id = Core::registro('autenticador')->obterUsuario()->obterID();
		$usuario = new UsuarioInstituido($usuario_id);
		$usuario->carregar();

		//Verifica compatibilidade de informãção.
		if($usuario_id !== $sess_usuario_id){
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar o usu&#225;rio.')));
		}

		//Verifica compatibilidade de informãção.
		$aluno_id = Aluno::obterIDAlunoPeloUsuario($usuario);
		if($aluno_id !== $sess_aluno_id){
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar o aluno.')));
		}

		//Puxa avaliação e verifica data.
		$simulado = new Simulado($sess_simulado_id);
		if($simulado->carregar()) {
			if (time() > $simulado->obterDataRealizacaoFim(false)) {
				exit(json_encode(array('ret' => '0', 'msg' => 'O tempo para a realiza&ccedil;&atilde;o deste simulado on-line acabou.')));
				//exit('0#O tempo para a realiza&ccedil;&atilde;o deste simulado on-line acabou.<br>1-'.time().'<br>2-'.$simu->obterDataRealizacaoFim(false));
			}
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar a avalia&#231;&#227;o.')));
		}

		//Puxa aluno, verifica compatibilidade de informãção.
		$aluno = new Aluno($aluno_id);
		if($aluno->carregar()) {
			$idInscricao = Inscricao::obterIDInscricaoPeloAluno($simulado, $aluno);

			if($idInscricao !== $sess_inscricao_id){
				exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar a inscri&#231;&#227;o do aluno.')));
			}
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar o aluno.')));
		}

		//Puxa inscrição e verifica realização.
		$inscricao = new Inscricao($idInscricao);
		
		if($inscricao->carregar($simulado, $aluno)) {
			$inscOSF = json_decode($inscricao->obterSimuladoFinalizado(),true);

			if ($simulado->obterRealizacaoUnica() == 1 && @$inscOSF[$sess_simulado_fase] == 1){
				exit(json_encode(array('ret' => '0', 'msg' => 'Essa avalia&ccedil;&atilde;o j&aacute; foi realizada.')));
			}
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar a inscri&ccedil;&atilde;o.')));
		}

		//Limpar respostas antigas.
		$inscricao->limparRespostasMarcadas();

		//Fixar tempo total de realização da prova.
		//$inscricao->fixarTempoTotalDeRealizacaoDoAlunoNoSimulado($tempoTotal);

		//Processar respostas enviadas.
		foreach ($dados as $dk => $dv) {
			$qid = $dv['qid'];
			$tempo = $dv['tempo'];
			$valor = $dv['valor'];

			$questao = new Questao($qid);
			if($questao->carregar()) {
				$qFase = $questao->obterFaseDuracao();
				if($qFase != $sess_simulado_fase){
					exit('0#Houve um erro ao identificar a questão pela fase.');
				}

				$resposta = Resposta::obterRespostaPorQuestaoEInscicao($questao, $inscricao);
				if($resposta == null) {
					$resposta = Resposta::obterNovaResposta($questao, $inscricao, 'SIMULADOS_ONLINE');
					if(!$resposta->carregar()) {
						exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar os dados da quest&atilde;o.')));
					}
				}

				$resposta->fixarValor(null);
				$resposta->fixarTexto(null);
				$resposta->fixarTempo($tempo);

				if(!empty($valor)){
					switch ($questao->obterTipo()) {
						case Questao::MULTIPLAESCOLHA:
							if($valor != 'A' && $valor != 'B' && $valor != 'C' && $valor != 'D' && $valor != 'E' && $valor != 'F' && $valor != 'G'){
								exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao salvar sua resposta.')));
							}

							$valor = MultiplaEscolha::inteiro($valor);
							if ($valor === false)
								$valor = null;

							$resposta->fixarValor($valor);

							break;
						case Questao::DISCURSIVA:
						case Questao::ABERTA:
							if ( empty($valor) || !Filtrador::texto($valor) )
								$valor = null;
							else
								$valor = $valor;

							$resposta->fixarTexto($valor);

							break;
						case Questao::SOMATORIO:
							if ( empty($valor) || !Filtrador::natural((int) $valor) )
								$valor = null;
							else
								$valor = (int) $valor;

							$resposta->fixarValor($valor);

							break;

						default:
							break;
					}
				}

				$salvaResposta = $resposta->salvar();
				if(!$salvaResposta){
					exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao salvar os dados da resposta.')));
				}
			}
		}

		//Definir avaliação realizada e fixando data.
		$osf = (string) $inscricao->obterSimuladoFinalizado();
		$decoded = json_decode($osf, true);
		
		// Make sure $osf becomes an array regardless of json_decode result
		if (!is_array($decoded)) {
			$osf = array();
		} else {
			$osf = $decoded;
		}
		
		$osf[$qFase] = '1';
		$osf = json_encode($osf);
		$inscricao->fixarSimuladoFinalizado($osf);
		

		$osfd = (string) $inscricao->obterSimuladoFinalizadoData();
		$osfd = json_decode($osfd,true);
		$osfd[$qFase] = date('d/m/Y H:i:s');
		$osfd = json_encode($osfd);
		$inscricao->fixarSimuladoFinalizadoData($osfd); 

		//print_r($inscricao);

		//Salvando registros de inscrição.
		$salvaFim = $inscricao->salvar();

		if($salvaFim){
			exit(json_encode(array('ret' => '1', 'msg' => 'Sucesso.')));
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao salvar os dados da resposta.')));
		}
	}

	// Add this new method to your MSimuladosOnline class
	public function aGetResultados()
	{
		// Get session data
		$sess_usuario_id = $_SESSION['avaliacao_online']['usuario_id'];
		$sess_aluno_id = $_SESSION['avaliacao_online']['aluno_id'];
		$sess_inscricao_id = $_SESSION['avaliacao_online']['inscricao_id'];
		$sess_simulado_id = $_SESSION['avaliacao_online']['simulado_id'];
		$sess_simulado_fase = $_SESSION['avaliacao_online']['simulado_fase'];
		
		// Initialize result structure
		$result = array(
			'success' => false,
			'correctAnswers' => 0,
			'incorrectAnswers' => 0,
			'unansweredQuestions' => 0,
			'annulledQuestions' => 0,
			'questions' => array()
		);
		
		// Get user and verify session
		$usuario_id = Core::registro('autenticador')->obterUsuario()->obterID();
		if ($usuario_id !== $sess_usuario_id) {
			echo json_encode($result);
			exit;
		}
		
		// Get simulado and aluno objects
		$simulado = new Simulado($sess_simulado_id);
		if (!$simulado->carregar()) {
			echo json_encode($result);
			exit;
		}
		
		$aluno = new Aluno($sess_aluno_id);
		if (!$aluno->carregar()) {
			echo json_encode($result);
			exit;
		}
		
		// Get inscription and responses
		$inscricao = new Inscricao($sess_inscricao_id);
		if (!$inscricao->carregar($simulado, $aluno)) {
			echo json_encode($result);
			exit;
		}
		
		// Make sure responses are loaded
		$inscricao->limparRespostasMarcadas();
		$inscricao->carregarRespostasMarcadasPorFase($sess_simulado_fase);
		
		// Get all responses
		$respostas = $inscricao->obterRespostasMarcadas();
		
		// Process all responses
		foreach ($respostas as $resposta) {
			try {
				// Access to private properties via ReflectionClass
				$reflectionResposta = new ReflectionClass($resposta);
				$dadosProperty = $reflectionResposta->getProperty('_dados');
				$dadosProperty->setAccessible(true);
				$respData = $dadosProperty->getValue($resposta);

				// Get student answer
				$respostaValor = isset($respData['valor']) ? $respData['valor'] : '';
				// Convert to letter
				$respostaLetra = $this->convertToLetter($respostaValor);
				
				// Get question ID and gabarito info
				$questaoId = '';
				$gabaritoValor = '';
				$gabaritoLetra = '';
				$anulada = false;
				
				if (isset($respData['questao'])) {
					$questao = $respData['questao'];
					$reflectionQuestao = new ReflectionClass($questao);
					$questaoProperty = $reflectionQuestao->getProperty('_dados');
					$questaoProperty->setAccessible(true);
					$questaoData = $questaoProperty->getValue($questao);
					
					$questaoId = isset($questaoData['identificador']) ? $questaoData['identificador'] : '';
					$anulada = !empty($questaoData['anulada']);
					
					// Get gabarito value
					if (isset($questaoData['gabaritos']) && is_array($questaoData['gabaritos']) && !empty($questaoData['gabaritos'])) {
						$gabarito = $questaoData['gabaritos'][0];
						$reflectionGabarito = new ReflectionClass($gabarito);
						$gabaritoProperty = $reflectionGabarito->getProperty('_dados');
						$gabaritoProperty->setAccessible(true);
						$gabaritoData = $gabaritoProperty->getValue($gabarito);
						
						$gabaritoValor = isset($gabaritoData['valor']) ? $gabaritoData['valor'] : '';
						// Convert gabarito to letter as well
						$gabaritoLetra = $this->convertToLetter($gabaritoValor);
					}
				}
				
				// Determine if answer is correct
				$status = '';
				
				if ($anulada) {
					$status = '<span style="color: #6c757d;">Anulada</span>';
					$result['annulledQuestions']++;
				} elseif ($respostaValor === '') {
					$status = '<span style="color: #dc3545;">Não respondida</span>';
					$result['unansweredQuestions']++;
				} elseif ($respostaValor == $gabaritoValor) {
					$status = '<span style="color: #28a745;">Correta</span>';
					$result['correctAnswers']++;
				} else {
					$status = '<span style="color: #dc3545;">Incorreta</span>';
					$result['incorrectAnswers']++;
				}
				
				// Add question details to result
				$result['questions'][] = array(
					'questaoId' => $questaoId,
					'respostaValor' => $respostaValor,
					'respostaLetra' => $respostaLetra,
					'gabaritoValor' => $gabaritoValor,
					'gabaritoLetra' => $gabaritoLetra,
					'status' => $status
				);
				
			} catch (Exception $e) {
				// Continue to next question if there's an error
				continue;
			}
		}
		
		// Mark success if we've processed responses
		$result['success'] = true;
		
		// Output as JSON
		header('Content-Type: application/json');
		echo json_encode($result);
		exit;
	}

	// Add helper function for letter conversion
	private function convertToLetter($value) {
		switch ($value) {
			case '1': return 'A';
			case '2': return 'B';
			case '4': return 'C';
			case '8': return 'D';
			case '16': 
			case '32': return 'E';
			default: return $value; // Keep original if not matched
		}
	}
}

?>