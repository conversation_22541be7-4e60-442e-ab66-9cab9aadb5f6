<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);

class MSimuladosOnline extends Modulo
{
	protected $_listagem;

	public function __construct ()
	{
		parent::__construct();
	}

	public function aListarSimuladosParaRealizar () 
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->remover<PERSON><PERSON>ho('adicionar');
		Core::modulo('navegador')->removerAtalho('procurar');
		Core::modulo('navegador')->remover<PERSON><PERSON>ho('importar');
		Core::modulo('navegador')->remover<PERSON><PERSON><PERSON>('exportar');
		Core::modulo('navegador')->remover<PERSON><PERSON><PERSON>('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();
		
		$simulados = array();
		$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();

		$usuario = new UsuarioInstituido($user_logado_id);
		$usuario->carregar();

		$aluno_id = Aluno::obterIDAlunoPeloUsuario($usuario);

		if($aluno_id){
			$aluno = new Aluno($aluno_id);
			$aluno->carregar();

			$dados = Simulado::obterSimuladosPeloAluno($aluno);
		}
		else{
			//verificar grupos q podem acessar todas
			$dados = Simulado::obterSimuladosParaFormularioIV();
		}

		$this->_dados = array();
		if(count($dados)>0){
			foreach ($dados as $k => $simulado) {
				$sim = new Simulado($simulado->obterID());
				$sim->carregar();

				$sid = $sim->obterID();
				$snome = $sim->obterNome();
				$sbi = $sim->obterBimestre();
				$sodri = $sim->obterDataRealizacao();
				$sodrf = $sim->obterDataRealizacaoFim();
				$soru = $sim->obterRealizacaoUnica();
				$sdps = $sim->obterDisciplinas();

				if(time() < $sodri && Core::registro('usuario')->obterGrupo() != 1){
					continue;
				}

				if(time() > $sodrf && Core::registro('usuario')->obterGrupo() != 1){
					continue;
				}

				if(!is_null($sim->obterSerieAvaliacao())){ 
					$sserie = $sim->obterSerieAvaliacao()->obterNome(); 
				}

				$qs = $sim->obterNumeroQuestoes(true,false);

				if(is_array($qs)){
					foreach ($qs as $qsK => $qsV) {
						$soID = base64_encode('soar_'.$sid.'_'.$qsK);

						$saf = $sim->obterSeOAlunoFez(); 
						if(!empty($saf)){
							$saf = json_decode($saf,true);					
							$saf = $saf[$qsK];
						}

						$this->_dados[$soID]['soID'] = $soID;
						$this->_dados[$soID]['sid'] = $sid;
						$this->_dados[$soID]['snome'] = $snome;
						$this->_dados[$soID]['sbi'] = $sbi;
						$this->_dados[$soID]['sfase'] = $qsK;
						$this->_dados[$soID]['sodri'] = $sodri;
						$this->_dados[$soID]['sodrf'] = $sodrf;
						$this->_dados[$soID]['soru'] = $soru;
						$this->_dados[$soID]['sserie'] = $sserie;
						$this->_dados[$soID]['saf'] = $saf;
						$this->_dados[$soID]['sdps'] = @array_unique($sdps[$qsK]);
					}
				}
				else{
					$fase = 1;

					$soID = base64_encode('soar_'.$sid.'_'.$fase);

					$saf = $sim->obterSeOAlunoFez(); 
					if(!empty($saf)){
						$saf = json_decode($saf,true);					
						$saf = $saf[$fase];
					}

					$this->_dados[$soID]['soID'] = $soID;
					$this->_dados[$soID]['sid'] = $sid;
					$this->_dados[$soID]['snome'] = $snome;
					$this->_dados[$soID]['sbi'] = $sbi;
					$this->_dados[$soID]['sfase'] = $fase;
					$this->_dados[$soID]['sodri'] = $sodri;
					$this->_dados[$soID]['sodrf'] = $sodrf;
					$this->_dados[$soID]['soru'] = $soru;
					$this->_dados[$soID]['sserie'] = $sserie;
					$this->_dados[$soID]['saf'] = $saf;
					$this->_dados[$soID]['sdps'] = @array_unique($sdps[$fase]);
				}				
			}

			if(count($this->_dados) == 1){
				$fev = reset($this->_dados);
				$link = Gerenciador_URL::gerarLink('simulados_online', 'realizar', array('id' => $fev['soID']));

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco($link);
				Core::modulo('redirecionador')->redirecionarDireto();
			}
		}

		//echo'<pre>';print_r($this->_dados);echo'</pre>';

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.realizar.listagem.html.php');
		$this->_finalizarRenderizacao();
	}

	public function aRealizarSimulado ()
	{
		$_SESSION['avaliacao_online']['usuario_id'] 		= 0;
		$_SESSION['avaliacao_online']['inscricao_id'] 		= 0;
		$_SESSION['avaliacao_online']['aluno_id'] 			= 0;
		$_SESSION['avaliacao_online']['simulado_id'] 		= 0;
		$_SESSION['avaliacao_online']['simulado_fase'] 		= 0;
		$_SESSION['avaliacao_online']['simulado_questoes'] 	= array();

		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->removerAtalho('adicionar');
		Core::modulo('navegador')->removerAtalho('procurar');
		Core::modulo('navegador')->removerAtalho('importar');
		Core::modulo('navegador')->removerAtalho('exportar');
		Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();

		$soID = base64_decode(@$_GET['id']);
		$soID = explode('_', $soID);

		$fase = $soID[2];

		$simu = new Simulado((int) $soID[1]);

		if (!$simu->carregar()) {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'Avalia&ccedil;&atilde;o inv&aacute;lida!');
		} 
		else {
			if(time() < $simu->obterDataRealizacao(false) && Core::registro('usuario')->obterGrupo() != 1){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o desta avalia&ccedil;&atilde;o on-line ainda n&atilde;o come&ccedil;ou.');
			}
			elseif(time() > $simu->obterDataRealizacaoFim(false) && Core::registro('usuario')->obterGrupo() != 1){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o desta avalia&ccedil;&atilde;o on-line j&aacute; acabou.');
			}
			else{
				$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();
				
				$usuario = new UsuarioInstituido($user_logado_id);
				$usuario->carregar();

				$msg = "";

				$idInsc = $aluno_id = 0;

				$aluno_pre_id = Aluno::obterIDAlunoPeloUsuario($usuario);

				$simu_serie = $simu->obterSerieAvaliacao()->obterID();

				if($aluno_pre_id){
					$aluno_id = $aluno_pre_id;

					$aluno = new Aluno($aluno_pre_id);
					$aluno->carregar();

					$alu_serie = $aluno->obterTurma()->obterSerie()->obterID();

					if($simu_serie == $alu_serie) {
						$idInsc = Inscricao::obterIDInscricaoPeloAluno($simu, $aluno);

						if (!$idInsc) {
							Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'lista'), 'Voc&ecirc; n&atilde;o pode realizar essa avalia&ccedil;&atilde;o.');
						}
					}
					else{
						Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'lista'), 'Voc&ecirc; s&oacute; pode realizar simulados de sua s&eacute;rie.');
					}
				}
				else{
					$msg = "Seu perfil de usu&aacute;rio n&atilde;o permite o registro de respostas. Somente alunos podem registr&aacute;-las, por&eacute;m voc&ecirc; pode interagir com a avalia&ccedil;&atilde;o da mesma forma que eles.";
				}

				$simulado['numq'] = 0;
				$simulado['tempoTotalHide'] = '00:00:00';

				    $simulado['nome'] = $simu->obterNome();
				    $simulado['numq'] = $simu->obterNumeroQuestoes();
				$simulado['questoes'] = $simu->obterQuestoesIDPorFase($fase);
				 $simulado['tinicio'] = $simu->obterTextoInicio();
				    $simulado['tfim'] = $simu->obterTextoFim();
					$simulado['realizacaoUnica'] = $simu->obterRealizacaoUnica();
				    $simulado['simu'] = $simu->obterID();
				   $simulado['aluno'] = $aluno_id;
				    $simulado['user'] = $user_logado_id;
				     $simulado['msg'] = $msg;
				     		$tempoArr = array();

				//criar aqui um esquema de salvar os dados do aluno em uma session e talvez as questoes tbm para otimizar os dados de salvar.
				$_SESSION['avaliacao_online']['usuario_id'] 		= $user_logado_id;
				$_SESSION['avaliacao_online']['inscricao_id'] 		= $idInsc;
				$_SESSION['avaliacao_online']['aluno_id'] 			= $aluno_id;
				$_SESSION['avaliacao_online']['simulado_id'] 		= $simu->obterID();
				$_SESSION['avaliacao_online']['simulado_fase'] 		= $fase;				     		

				if(isset($idInsc)){
					$inscricao = new Inscricao($idInsc);
					$inscricao->carregar();

					$inscOSF = json_decode($inscricao->obterSimuladoFinalizado(),true);

					if($inscricao->carregar($simu, $aluno)) {		
						$inscricao->limparRespostasMarcadas();
						$inscricao->carregarRespostasMarcadasPorFase($fase);

						if ($simulado['realizacaoUnica'] == 1 && @$inscOSF[$fase] == 1){

							Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('login', 'logout'), 'Voc&ecirc; n&atilde;o pode mais fazer essa Avalia&ccedil;&atilde;o!');
							//Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'Voc&ecirc; n&atilde;o pode mais fazer essa Avalia&ccedil;&atilde;o!');
						}

						$simulado['tempoTotalHide'] = $inscricao->obterTempoTotalDeRealizacaoDoAlunoNoSimulado();
						if(!$simulado['tempoTotalHide']){
					     	$simulado['tempoTotalHide'] = '00:00:00';
					    }
					    else{
					     	$ttmp = explode(":", $simulado['tempoTotalHide']);
					    	$simulado['tempoTotalJS'] = $ttmp[2]+($ttmp[1]*60)+($ttmp[0]*60*60);
					    }

						$simulado['finalizadoSim'] = @$inscOSF[$fase];
						if($simulado['finalizadoSim']){
							$simulado['finalizadoData'] = $inscricao->obterSimuladoFinalizadoData();
						}

						$simulado['todasRespostas'] = $inscricao->obterRespostasMarcadas();

						$n = 1;
						$rTmp = array();

						foreach($simulado['todasRespostas'] as $k => $v){
							if($n == 1){
								$cacheIDq = $v->obterQuestao()->obterID();
							}

							$v->carregar();

						    $rTmp['tempoJS'] = 1;
						    $rTmp['tempo'] = $v->obterTempo();
						    if(!$rTmp['tempo']){
						    	$rTmp['tempo'] = '00:00:00';
						    }
						    else{
						    	$tttmp = explode(":", $rTmp['tempo']);
						    	$rTmp['tempoJS'] = $tttmp[2]+($tttmp[1]*60)+($tttmp[0]*60*60);
						    }

						    $qID = ($v->obterQuestao()->obterID())-$cacheIDq;
						    $tempoArr[$qID] = $rTmp['tempoJS'];

						    $rTmp['valor'] = $v->obterValor();
						    $tipoQ = $v->obterQuestao()->obterTipo();
						    switch ($tipoQ) {
								case Questao::MULTIPLAESCOLHA:
									$rTmp['valor'] = MultiplaEscolha::letra($v->obterValor());
									break;
								case Questao::DISCURSIVA:
								case Questao::ABERTA:
									$rTmp['valor'] = $v->obterTexto();
									break;
								case Questao::SOMATORIO:
									$rTmp['valor'] = $v->obterValor();
									break;
								default:
									break;
							}

							$simulado['respostas'][$v->obterQuestao()->obterID()] = $rTmp;


							$_SESSION['avaliacao_online']['simulado_questoes'][$n] = $v->obterQuestao()->obterID();

							$n++;
						}
					}
				}

				//echo'-><pre>';print_r($simulado);echo'</pre><br>---<br>';

				$this->_iniciarRenderizacao(Modulo::HTML);
					include('simulados_online.realizar.avaliacao.html.php');
				$this->_finalizarRenderizacao();
			}	
		}

		return true;
	}

	public function aResponderQuestaoSimulado()
	{					
		#--- fazer auditoria de lançamento de simulado online

		//Puxa e verifica dados do post.
		$dados = Array();
		if(array_key_exists('d', $_POST)){
			$dados = json_decode($_POST['d'],true);
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Dados n&#227;o recebidos.')));
		}

		//$fase = $soID[2];

		//puxa sessão do aluno q foi gravada na abertura da avaliação.
		$sess_usuario_id = $_SESSION['avaliacao_online']['usuario_id'];
		$sess_aluno_id = $_SESSION['avaliacao_online']['aluno_id'];
		$sess_inscricao_id = $_SESSION['avaliacao_online']['inscricao_id'];
		$sess_simulado_id = $_SESSION['avaliacao_online']['simulado_id'];
		$sess_simulado_fase = $_SESSION['avaliacao_online']['simulado_fase'];

		//Puxa dados do usuário no AvRede.
		$usuario_id = Core::registro('autenticador')->obterUsuario()->obterID();
		$usuario = new UsuarioInstituido($usuario_id);
		$usuario->carregar();

		//Verifica compatibilidade de informãção.
		if($usuario_id !== $sess_usuario_id){
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar o usu&#225;rio.')));
		}

		//Verifica compatibilidade de informãção.
		$aluno_id = Aluno::obterIDAlunoPeloUsuario($usuario);
		if($aluno_id !== $sess_aluno_id){
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar o aluno.')));
		}

		//Puxa avaliação e verifica data.
		$simulado = new Simulado($sess_simulado_id);
		if($simulado->carregar()) {
			if (time() > $simulado->obterDataRealizacaoFim(false)) {
				exit(json_encode(array('ret' => '0', 'msg' => 'O tempo para a realiza&ccedil;&atilde;o deste simulado on-line acabou.')));
				//exit('0#O tempo para a realiza&ccedil;&atilde;o deste simulado on-line acabou.<br>1-'.time().'<br>2-'.$simu->obterDataRealizacaoFim(false));
			}
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar a avalia&#231;&#227;o.')));
		}

		//Puxa aluno, verifica compatibilidade de informãção.
		$aluno = new Aluno($aluno_id);
		if($aluno->carregar()) {
			$idInscricao = Inscricao::obterIDInscricaoPeloAluno($simulado, $aluno);

			if($idInscricao !== $sess_inscricao_id){
				exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar a inscri&#231;&#227;o do aluno.')));
			}
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar o aluno.')));
		}

		//Puxa inscrição e verifica realização.
		$inscricao = new Inscricao($idInscricao);
		
		if($inscricao->carregar($simulado, $aluno)) {
			$inscOSF = json_decode($inscricao->obterSimuladoFinalizado(),true);

			if ($simulado->obterRealizacaoUnica() == 1 && @$inscOSF[$sess_simulado_fase] == 1){
				exit(json_encode(array('ret' => '0', 'msg' => 'Essa avalia&ccedil;&atilde;o j&aacute; foi realizada.')));
			}
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar a inscri&ccedil;&atilde;o.')));
		}

		//Limpar respostas antigas.
		$inscricao->limparRespostasMarcadas();

		//Fixar tempo total de realização da prova.
		//$inscricao->fixarTempoTotalDeRealizacaoDoAlunoNoSimulado($tempoTotal);

		//Processar respostas enviadas.
		foreach ($dados as $dk => $dv) {
			$qid = $dv['qid'];
			$tempo = $dv['tempo'];
			$valor = $dv['valor'];

			$questao = new Questao($qid);
			if($questao->carregar()) {
				$qFase = $questao->obterFaseDuracao();
				if($qFase != $sess_simulado_fase){
					exit('0#Houve um erro ao identificar a questão pela fase.');
				}

				$resposta = Resposta::obterRespostaPorQuestaoEInscicao($questao, $inscricao);
				if($resposta == null) {
					$resposta = Resposta::obterNovaResposta($questao, $inscricao, 'SIMULADOS_ONLINE');
					if(!$resposta->carregar()) {
						exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao carregar os dados da quest&atilde;o.')));
					}
				}

				$resposta->fixarValor(null);
				$resposta->fixarTexto(null);
				$resposta->fixarTempo($tempo);

				if(!empty($valor)){
					switch ($questao->obterTipo()) {
						case Questao::MULTIPLAESCOLHA:
							if($valor != 'A' && $valor != 'B' && $valor != 'C' && $valor != 'D' && $valor != 'E' && $valor != 'F' && $valor != 'G'){
								exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao salvar sua resposta.')));
							}

							$valor = MultiplaEscolha::inteiro($valor);
							if ($valor === false)
								$valor = null;

							$resposta->fixarValor($valor);

							break;
						case Questao::DISCURSIVA:
						case Questao::ABERTA:
							if ( empty($valor) || !Filtrador::texto($valor) )
								$valor = null;
							else
								$valor = $valor;

							$resposta->fixarTexto($valor);

							break;
						case Questao::SOMATORIO:
							if ( empty($valor) || !Filtrador::natural((int) $valor) )
								$valor = null;
							else
								$valor = (int) $valor;

							$resposta->fixarValor($valor);

							break;

						default:
							break;
					}
				}

				$salvaResposta = $resposta->salvar();
				if(!$salvaResposta){
					exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao salvar os dados da resposta.')));
				}
			}
		}

		//Definir avaliação realizada e fixando data.
		$osf = (string) $inscricao->obterSimuladoFinalizado();
		$decoded = json_decode($osf, true);
		
		// Make sure $osf becomes an array regardless of json_decode result
		if (!is_array($decoded)) {
			$osf = array();
		} else {
			$osf = $decoded;
		}
		
		$osf[$qFase] = '1';
		$osf = json_encode($osf);
		$inscricao->fixarSimuladoFinalizado($osf);
		

		$osfd = (string) $inscricao->obterSimuladoFinalizadoData();
		$osfd = json_decode($osfd,true);
		$osfd[$qFase] = date('d/m/Y H:i:s');
		$osfd = json_encode($osfd);
		$inscricao->fixarSimuladoFinalizadoData($osfd); 

		//print_r($inscricao);

		//Salvando registros de inscrição.
		$salvaFim = $inscricao->salvar();

		if($salvaFim){
			exit(json_encode(array('ret' => '1', 'msg' => 'Sucesso.')));
		}
		else{
			exit(json_encode(array('ret' => '0', 'msg' => 'Houve um erro ao salvar os dados da resposta.')));
		}
	}

	// Add this new method to your MSimuladosOnline class
	public function aGetResultados()
	{
		// Get session data
		$sess_usuario_id = $_SESSION['avaliacao_online']['usuario_id'];
		$sess_aluno_id = $_SESSION['avaliacao_online']['aluno_id'];
		$sess_inscricao_id = $_SESSION['avaliacao_online']['inscricao_id'];
		$sess_simulado_id = $_SESSION['avaliacao_online']['simulado_id'];
		$sess_simulado_fase = $_SESSION['avaliacao_online']['simulado_fase'];
		
		// Initialize result structure
		$result = array(
			'success' => false,
			'correctAnswers' => 0,
			'incorrectAnswers' => 0,
			'unansweredQuestions' => 0,
			'annulledQuestions' => 0,
			'questions' => array()
		);
		
		// Get user and verify session
		$usuario_id = Core::registro('autenticador')->obterUsuario()->obterID();
		if ($usuario_id !== $sess_usuario_id) {
			echo json_encode($result);
			exit;
		}
		
		// Get simulado and aluno objects
		$simulado = new Simulado($sess_simulado_id);
		if (!$simulado->carregar()) {
			echo json_encode($result);
			exit;
		}
		
		$aluno = new Aluno($sess_aluno_id);
		if (!$aluno->carregar()) {
			echo json_encode($result);
			exit;
		}
		
		// Get inscription and responses
		$inscricao = new Inscricao($sess_inscricao_id);
		if (!$inscricao->carregar($simulado, $aluno)) {
			echo json_encode($result);
			exit;
		}
		
		// Make sure responses are loaded
		$inscricao->limparRespostasMarcadas();
		$inscricao->carregarRespostasMarcadasPorFase($sess_simulado_fase);
		
		// Get all responses
		$respostas = $inscricao->obterRespostasMarcadas();
		
		// Process all responses
		foreach ($respostas as $resposta) {
			try {
				// Access to private properties via ReflectionClass
				$reflectionResposta = new ReflectionClass($resposta);
				$dadosProperty = $reflectionResposta->getProperty('_dados');
				$dadosProperty->setAccessible(true);
				$respData = $dadosProperty->getValue($resposta);

				// Get student answer
				$respostaValor = isset($respData['valor']) ? $respData['valor'] : '';
				// Convert to letter
				$respostaLetra = $this->convertToLetter($respostaValor);
				
				// Get question ID and gabarito info
				$questaoId = '';
				$gabaritoValor = '';
				$gabaritoLetra = '';
				$anulada = false;
				
				if (isset($respData['questao'])) {
					$questao = $respData['questao'];
					$reflectionQuestao = new ReflectionClass($questao);
					$questaoProperty = $reflectionQuestao->getProperty('_dados');
					$questaoProperty->setAccessible(true);
					$questaoData = $questaoProperty->getValue($questao);
					
					$questaoId = isset($questaoData['identificador']) ? $questaoData['identificador'] : '';
					$anulada = !empty($questaoData['anulada']);
					
					// Get gabarito value
					if (isset($questaoData['gabaritos']) && is_array($questaoData['gabaritos']) && !empty($questaoData['gabaritos'])) {
						$gabarito = $questaoData['gabaritos'][0];
						$reflectionGabarito = new ReflectionClass($gabarito);
						$gabaritoProperty = $reflectionGabarito->getProperty('_dados');
						$gabaritoProperty->setAccessible(true);
						$gabaritoData = $gabaritoProperty->getValue($gabarito);
						
						$gabaritoValor = isset($gabaritoData['valor']) ? $gabaritoData['valor'] : '';
						// Convert gabarito to letter as well
						$gabaritoLetra = $this->convertToLetter($gabaritoValor);
					}
				}
				
				// Determine if answer is correct
				$status = '';
				
				if ($anulada) {
					$status = '<span style="color: #6c757d;">Anulada</span>';
					$result['annulledQuestions']++;
				} elseif ($respostaValor === '') {
					$status = '<span style="color: #dc3545;">Não respondida</span>';
					$result['unansweredQuestions']++;
				} elseif ($respostaValor == $gabaritoValor) {
					$status = '<span style="color: #28a745;">Correta</span>';
					$result['correctAnswers']++;
				} else {
					$status = '<span style="color: #dc3545;">Incorreta</span>';
					$result['incorrectAnswers']++;
				}
				
				// Add question details to result
				$result['questions'][] = array(
					'questaoId' => $questaoId,
					'respostaValor' => $respostaValor,
					'respostaLetra' => $respostaLetra,
					'gabaritoValor' => $gabaritoValor,
					'gabaritoLetra' => $gabaritoLetra,
					'status' => $status
				);
				
			} catch (Exception $e) {
				// Continue to next question if there's an error
				continue;
			}
		}
		
		// Mark success if we've processed responses
		$result['success'] = true;
		
		// Output as JSON
		header('Content-Type: application/json');
		echo json_encode($result);
		exit;
	}

	// Add helper function for letter conversion
	private function convertToLetter($value) {
		switch ($value) {
			case '1': return 'A';
			case '2': return 'B';
			case '4': return 'C';
			case '8': return 'D';
			case '16': 
			case '32': return 'E';
			default: return $value; // Keep original if not matched
		}
	}

	// <=============== Método para listar auditoria de avaliações online ===============>
	public function aListarAuditoriaAvaliacaoOnline () 
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();
		
		// Processamento dos filtros
		$filtros = array();
		$whereConditions = array();
		
		// Filtro por simulado
		if (!empty($_GET['filtro_simulado'])) {
			$filtros['simulado'] = (int)$_GET['filtro_simulado'];
			$whereConditions[] = 'aso.sid = ' . Core::registro('db')->formatarValor($filtros['simulado']);
		}

		// Filtro por bimestre
		if (!empty($_GET['bimestre'])) {
			$filtros['bimestre'] = (int)$_GET['bimestre'];
			$whereConditions[] = 's.s_bimestre = ' . Core::registro('db')->formatarValor($filtros['bimestre']);
		}
		
		// Filtro por data inicial
		if (!empty($_GET['data_inicio'])) {
			$filtros['data_inicio'] = $_GET['data_inicio'];
			$whereConditions[] = 'DATE(aso.data) >= ' . Core::registro('db')->formatarValor($filtros['data_inicio']);
		}
		
		// Filtro por data final
		if (!empty($_GET['data_fim'])) {
			$filtros['data_fim'] = $_GET['data_fim'];
			$whereConditions[] = 'DATE(aso.data) <= ' . Core::registro('db')->formatarValor($filtros['data_fim']);
		}
		
		// Filtro por tipo de rede
		if (!empty($_GET['tipo_rede']) && $_GET['tipo_rede'] != 'todos') {
			$filtros['tipo_rede'] = $_GET['tipo_rede'];
			$whereConditions[] = 'i.i_tipo_rede = ' . Core::registro('db')->formatarValor($filtros['tipo_rede']);
		}
		
		// Filtro por instituição
		if (!empty($_GET['instituicao'])) {
			$filtros['instituicao'] = (int)$_GET['instituicao'];
			$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($filtros['instituicao']);
		}
		
		// Filtro por aluno/matrícula
		if (!empty($_GET['busca_aluno'])) {
			$termo = '%' . Core::registro('db')->escape($_GET['busca_aluno']) . '%';
			$whereConditions[] = '(u.u_nome LIKE ' . Core::registro('db')->formatarValor($termo) . ' OR aso.matricula LIKE ' . Core::registro('db')->formatarValor($termo) . ')';
			$filtros['busca_aluno'] = $_GET['busca_aluno'];
		}

		// Sempre filtrar pela instituição do usuário logado (se não for admin)
		if (Core::registro('usuario')->obterGrupo() != 1) {
			// Verificar se ProvaFloripa existe, senão usar alternativa
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				// Alternativa: buscar instituição do usuário logado diretamente
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}
		
		$whereSQL = '';
		if (!empty($whereConditions)) {
			$whereSQL = ' AND ' . implode(' AND ', $whereConditions);
		}

		// AGORA podemos obter dados para os gráficos (DEPOIS de definir $whereSQL)
		$dados_graficos = $this->_obterDadosParaGraficos($whereSQL);

		// Calcular estatísticas extras
		$estatisticas_extras = array(
			'total_processamentos_hoje' => $this->_obterProcessamentosHoje($whereSQL),
			'instituicoes_ativas' => count($dados_graficos['instituicoes_ativas']),
			'periodo_atividade' => $this->_calcularPeriodoAtividade($dados_graficos['atividade_por_data']),
			'horario_pico' => $this->_calcularHorarioPico($dados_graficos['atividade_hora'])
		);

		// Paginação
		$pagina = isset($_GET['pagina']) ? (int)$_GET['pagina'] : 1;
		$registros_por_pagina = 50;
		$offset = ($pagina - 1) * $registros_por_pagina;

		// Contar total de registros
		$sql_count = sprintf('
			SELECT COUNT(*) as total
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s',
			$whereSQL
		);

		$rs_count = Core::registro('db')->query($sql_count);
		$total_registros = 0;
		if ($rs_count->num_rows) {
			$row_count = $rs_count->fetch_assoc();
			$total_registros = $row_count['total'];
		}
		$rs_count->free();

		// Query principal para buscar dados
		$sql = sprintf('
			SELECT 
				aso.as_id,
				aso.sid,
				aso.data,
				aso.dados,
				aso.sessao,
				aso.aluno,
				aso.matricula,
				s.s_nome as simulado_nome,
				s.s_bimestre as bimestre,
				u.u_nome as aluno_nome,
				i.i_nome as instituicao_nome,
				i.i_tipo_rede,
				DATE_FORMAT(aso.data, "%%d/%%m/%%Y %%H:%%i:%%s") as data_formatada,
				DATE_FORMAT(aso.data, "%%Y-%%m-%%d") as data_iso,
				TIME_FORMAT(aso.data, "%%H:%%i:%%s") as hora_formatada,
				CASE 
					WHEN i.i_tipo_rede = "E" THEN "Estadual"
					WHEN i.i_tipo_rede = "M" THEN "Municipal" 
					ELSE "Particular/Outro"
				END as tipo_rede_descricao
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s
			ORDER BY aso.data DESC
			LIMIT %d OFFSET %d',
			$whereSQL,
			$registros_por_pagina,
			$offset
		);

		$dados_auditoria = array();
		$rs = Core::registro('db')->query($sql);
		
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				// Processar dados JSON se existir
				$dados_detalhados = '';
				if (!empty($row['dados'])) {
					$dados_json = json_decode($row['dados'], true);
					if (json_last_error() === JSON_ERROR_NONE && is_array($dados_json)) {
						$dados_detalhados = $this->_formatarDadosJSON($dados_json);
					}
				}
				
				$dados_auditoria[] = array(
					'id' => $row['as_id'],
					'simulado_id' => $row['sid'],
					'simulado_nome' => $row['simulado_nome'],
					'bimestre' => $row['bimestre'] ?: 'N/A',
					'data_formatada' => $row['data_formatada'],
					'data_iso' => $row['data_iso'],
					'hora_formatada' => $row['hora_formatada'],
					'aluno_nome' => $row['aluno_nome'],
					'matricula' => $row['matricula'],
					'instituicao_nome' => $row['instituicao_nome'],
					'tipo_rede' => $row['i_tipo_rede'],
					'tipo_rede_descricao' => $row['tipo_rede_descricao'],
					'dados_detalhados' => $dados_detalhados,
					'tem_sessao' => !empty($row['sessao'])
				);
			}
		}
		$rs->free();

		// Buscar dados para os filtros
		$simulados_disponiveis = $this->_obterSimuladosParaFiltro();
		$bimestres_disponiveis = $this->_obterBimestresParaFiltro();
		$instituicoes_disponiveis = $this->_obterInstituicoesParaFiltro();

		// Calcular paginação
		$total_paginas = ceil($total_registros / $registros_por_pagina);
		$paginacao = array(
			'pagina_atual' => $pagina,
			'total_paginas' => $total_paginas,
			'total_registros' => $total_registros,
			'registros_por_pagina' => $registros_por_pagina,
			'inicio' => $offset + 1,
			'fim' => min($offset + $registros_por_pagina, $total_registros)
		);

		// Passar dados para o template (INCLUINDO os dados dos gráficos)
		$this->_dados_auditoria = $dados_auditoria;
		$this->_filtros_ativos = $filtros;
		$this->_simulados_disponiveis = $simulados_disponiveis;
		$this->_bimestres_disponiveis = $bimestres_disponiveis;
		$this->_instituicoes_disponiveis = $instituicoes_disponiveis;
		$this->_paginacao = $paginacao;
		$this->_total_registros = $total_registros;
		
		// ADICIONAR dados dos gráficos
		$this->_dados_graficos = $dados_graficos;
		$this->_estatisticas_extras = $estatisticas_extras;

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.auditoria.html.php');
		$this->_finalizarRenderizacao();
	}

	// Método para exibir apenas o dashboard com gráficos
	public function aDashboardAuditoria() 
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();
		
		// Processamento dos filtros (mesmo código da função principal)
		$filtros = array();
		$whereConditions = array();
		
		// Filtro por simulado
		if (!empty($_GET['filtro_simulado'])) {
			$filtros['simulado'] = (int)$_GET['filtro_simulado'];
			$whereConditions[] = 'aso.sid = ' . Core::registro('db')->formatarValor($filtros['simulado']);
		}

		// Filtro por bimestre
		if (!empty($_GET['bimestre'])) {
			$filtros['bimestre'] = (int)$_GET['bimestre'];
			$whereConditions[] = 's.s_bimestre = ' . Core::registro('db')->formatarValor($filtros['bimestre']);
		}
		
		// Filtro por data inicial
		if (!empty($_GET['data_inicio'])) {
			$filtros['data_inicio'] = $_GET['data_inicio'];
			$whereConditions[] = 'DATE(aso.data) >= ' . Core::registro('db')->formatarValor($filtros['data_inicio']);
		}
		
		// Filtro por data final
		if (!empty($_GET['data_fim'])) {
			$filtros['data_fim'] = $_GET['data_fim'];
			$whereConditions[] = 'DATE(aso.data) <= ' . Core::registro('db')->formatarValor($filtros['data_fim']);
		}
		
		// Filtro por tipo de rede
		if (!empty($_GET['tipo_rede']) && $_GET['tipo_rede'] != 'todos') {
			$filtros['tipo_rede'] = $_GET['tipo_rede'];
			$whereConditions[] = 'i.i_tipo_rede = ' . Core::registro('db')->formatarValor($filtros['tipo_rede']);
		}
		
		// Filtro por instituição
		if (!empty($_GET['instituicao'])) {
			$filtros['instituicao'] = (int)$_GET['instituicao'];
			$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($filtros['instituicao']);
		}
		
		// Filtro por aluno/matrícula
		if (!empty($_GET['busca_aluno'])) {
			$termo = '%' . Core::registro('db')->escape($_GET['busca_aluno']) . '%';
			$whereConditions[] = '(u.u_nome LIKE ' . Core::registro('db')->formatarValor($termo) . ' OR aso.matricula LIKE ' . Core::registro('db')->formatarValor($termo) . ')';
			$filtros['busca_aluno'] = $_GET['busca_aluno'];
		}

		// Sempre filtrar pela instituição do usuário logado (se não for admin)
		if (Core::registro('usuario')->obterGrupo() != 1) {
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}
		
		$whereSQL = '';
		if (!empty($whereConditions)) {
			$whereSQL = ' AND ' . implode(' AND ', $whereConditions);
		}

		// Obter dados para os gráficos
		$dados_graficos = $this->_obterDadosParaGraficos($whereSQL);

		// Calcular estatísticas extras
		$estatisticas_extras = array(
			'total_processamentos_hoje' => $this->_obterProcessamentosHoje($whereSQL),
			'instituicoes_ativas' => count($dados_graficos['instituicoes_ativas']),
			'periodo_atividade' => $this->_calcularPeriodoAtividade($dados_graficos['atividade_por_data']),
			'horario_pico' => $this->_calcularHorarioPico($dados_graficos['atividade_hora'])
		);

		// Buscar dados para os filtros
		$simulados_disponiveis = $this->_obterSimuladosParaFiltro();
		$bimestres_disponiveis = $this->_obterBimestresParaFiltro();
		$instituicoes_disponiveis = $this->_obterInstituicoesParaFiltro();

		// Contar total de registros para estatísticas básicas
		$sql_count = sprintf('
			SELECT COUNT(*) as total
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s',
			$whereSQL
		);

		$rs_count = Core::registro('db')->query($sql_count);
		$total_registros = 0;
		if ($rs_count->num_rows) {
			$row_count = $rs_count->fetch_assoc();
			$total_registros = $row_count['total'];
		}
		$rs_count->free();

		// Passar dados para o template
		$this->_filtros_ativos = $filtros;
		$this->_simulados_disponiveis = $simulados_disponiveis;
		$this->_bimestres_disponiveis = $bimestres_disponiveis;
		$this->_instituicoes_disponiveis = $instituicoes_disponiveis;
		$this->_total_registros = $total_registros;
		$this->_dados_graficos = $dados_graficos;
		$this->_estatisticas_extras = $estatisticas_extras;

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.dashboard.html.php');
		$this->_finalizarRenderizacao();
	}

	// Método API para carregar dados dos gráficos via AJAX
	public function aCarregarGraficosAjax() {
		header('Content-Type: application/json');
		
		// Verificar permissões
		$usuario_grupo = Core::registro('usuario')->obterGrupo();
		$tem_permissao = ($usuario_grupo == 1); // Admin
		
		if (!$tem_permissao) {
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$tem_permissao = true;
			} else {
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$tem_permissao = true;
				}
			}
		}
		
		if (!$tem_permissao) {
			echo json_encode(['success' => false, 'message' => 'Acesso negado']);
			exit;
		}
		
		// Simular delay para demonstrar loading (remover em produção)
		sleep(1);
		
		// Processar filtros
		$whereConditions = array();
		
		if (!empty($_GET['filtro_simulado'])) {
			$whereConditions[] = 'aso.sid = ' . Core::registro('db')->formatarValor((int)$_GET['filtro_simulado']);
		}

		if (!empty($_GET['bimestre'])) {
			$whereConditions[] = 's.s_bimestre = ' . Core::registro('db')->formatarValor((int)$_GET['bimestre']);
		}
		
		if (!empty($_GET['data_inicio'])) {
			$whereConditions[] = 'DATE(aso.data) >= ' . Core::registro('db')->formatarValor($_GET['data_inicio']);
		}
		
		if (!empty($_GET['data_fim'])) {
			$whereConditions[] = 'DATE(aso.data) <= ' . Core::registro('db')->formatarValor($_GET['data_fim']);
		}
		
		if (!empty($_GET['tipo_rede']) && $_GET['tipo_rede'] != 'todos') {
			$whereConditions[] = 'i.i_tipo_rede = ' . Core::registro('db')->formatarValor($_GET['tipo_rede']);
		}
		
		if (!empty($_GET['instituicao'])) {
			$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor((int)$_GET['instituicao']);
		}
		
		if (!empty($_GET['busca_aluno'])) {
			$termo = '%' . Core::registro('db')->escape($_GET['busca_aluno']) . '%';
			$whereConditions[] = '(u.u_nome LIKE ' . Core::registro('db')->formatarValor($termo) . ' OR aso.matricula LIKE ' . Core::registro('db')->formatarValor($termo) . ')';
		}

		// Sempre filtrar pela instituição do usuário logado (se não for admin)
		if (Core::registro('usuario')->obterGrupo() != 1) {
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}
		
		$whereSQL = '';
		if (!empty($whereConditions)) {
			$whereSQL = ' AND ' . implode(' AND ', $whereConditions);
		}
		
		try {
			// Obter dados dos gráficos
			$dados_graficos = $this->_obterDadosParaGraficos($whereSQL);
			
			// Calcular estatísticas extras
			$estatisticas_extras = array(
				'total_processamentos_hoje' => $this->_obterProcessamentosHoje($whereSQL),
				'instituicoes_ativas' => count($dados_graficos['instituicoes_ativas']),
				'periodo_atividade' => $this->_calcularPeriodoAtividade($dados_graficos['atividade_por_data']),
				'horario_pico' => $this->_calcularHorarioPico($dados_graficos['atividade_hora'])
			);
			
			echo json_encode([
				'success' => true,
				'dados_graficos' => $dados_graficos,
				'estatisticas_extras' => $estatisticas_extras
			]);
			
		} catch (Exception $e) {
			echo json_encode([
				'success' => false,
				'message' => 'Erro ao carregar dados: ' . $e->getMessage()
			]);
		}
		
		exit;
	}

	// Método auxiliar para formatar dados JSON na auditoria
	private function _formatarDadosJSON($dados) {
		if (!is_array($dados)) return '';
		
		$output = '';
		$contador = 0;
		foreach ($dados as $key => $value) {
			if ($contador >= 3) break; // Limitar para não sobrecarregar
			
			if (is_array($value)) {
				$output .= '<strong>' . htmlspecialchars($key) . ':</strong> [Array]<br>';
			} else {
				$valor_limitado = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
				$output .= '<strong>' . htmlspecialchars($key) . ':</strong> ' . htmlspecialchars($valor_limitado) . '<br>';
			}
			$contador++;
		}
		
		if (count($dados) > 3) {
			$output .= '<em>... e mais ' . (count($dados) - 3) . ' campos</em>';
		}
		
		return $output;
	}

	// Método auxiliar para obter simulados para filtro na auditoria
	private function _obterSimuladosParaFiltro() {
		$whereInst = '';
		if (Core::registro('usuario')->obterGrupo() != 1) {
			// Verificar se ProvaFloripa existe
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereInst = ' AND s.s_instituicao = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				// Alternativa: buscar instituição do usuário logado
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereInst = ' AND s.s_instituicao = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}

		$sql = sprintf('
			SELECT DISTINCT s.s_id, s.s_nome 
			FROM simulados s
			INNER JOIN auditoria_simulado_online aso ON aso.sid = s.s_id
			WHERE 1=1 %s
			ORDER BY s.s_nome',
			$whereInst
		);

		$rs = Core::registro('db')->query($sql);
		$simulados = array();
		
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$simulados[$row['s_id']] = $row['s_nome'];
			}
			$rs->free();
		}
		
		return $simulados;
	}

	// Método auxiliar para obter bimestres para filtro na auditoria
	private function _obterBimestresParaFiltro() {
		$whereInst = '';
		if (Core::registro('usuario')->obterGrupo() != 1) {
			// Verificar se ProvaFloripa existe
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereInst = ' AND s.s_instituicao = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				// Alternativa: buscar instituição do usuário logado
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereInst = ' AND s.s_instituicao = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}

		$sql = sprintf('
			SELECT DISTINCT s.s_bimestre 
			FROM simulados s
			INNER JOIN auditoria_simulado_online aso ON aso.sid = s.s_id
			WHERE s.s_bimestre IS NOT NULL AND s.s_bimestre != "" AND s.s_bimestre > 0 %s
			ORDER BY s.s_bimestre ASC',
			$whereInst
		);

		$rs = Core::registro('db')->query($sql);
		$bimestres = array();
		
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$bimestre = (int)$row['s_bimestre'];
				if ($bimestre > 0) {
					$bimestres[$bimestre] = $bimestre . 'º Aplicação';
				}
			}
			$rs->free();
		}
		
		return $bimestres;
	}

	// Método auxiliar para obter instituições para filtro na auditoria
	private function _obterInstituicoesParaFiltro() {
		$whereInst = '';
		if (Core::registro('usuario')->obterGrupo() != 1) {
			// Verificar se ProvaFloripa existe
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereInst = ' AND i.i_id = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				// Alternativa: buscar instituição do usuário logado
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereInst = ' AND i.i_id = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}

		$sql = sprintf('
			SELECT DISTINCT i.i_id, i.i_nome, i.i_tipo_rede
			FROM instituicoes i
			INNER JOIN usuarios u ON u.u_instituicao = i.i_id
			INNER JOIN alunos a ON a.a_usuario = u.u_id
			INNER JOIN auditoria_simulado_online aso ON aso.matricula = a.a_matricula
			WHERE 1=1 %s
			ORDER BY i.i_nome',
			$whereInst
		);

		$rs = Core::registro('db')->query($sql);
		$instituicoes = array();
		
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$tipo_descricao = '';
				switch($row['i_tipo_rede']) {
					case 'E': $tipo_descricao = ' (Estadual)'; break;
					case 'M': $tipo_descricao = ' (Municipal)'; break;
					default: $tipo_descricao = ' (Particular/Outro)'; break;
				}
				$instituicoes[$row['i_id']] = $row['i_nome'] . $tipo_descricao;
			}
			$rs->free();
		}
		
		return $instituicoes;
	}

	// Método para obter detalhes de uma auditoria específica (modal)
	public function aObterDetalhesAuditoria() {
		$id = isset($_GET['id']) ? $_GET['id'] : '';
		
		if (empty($id)) {
			echo json_encode(['success' => false, 'message' => 'ID não fornecido']);
			exit;
		}

		// Buscar detalhes do registro
		$whereConditions = array();
		$whereConditions[] = 'aso.as_id = ' . Core::registro('db')->formatarValor($id);

		// Sempre filtrar pela instituição do usuário logado (se não for admin)
		if (Core::registro('usuario')->obterGrupo() != 1) {
			// Verificar se ProvaFloripa existe
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				// Alternativa: buscar instituição do usuário logado
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}
		
		$whereSQL = ' AND ' . implode(' AND ', $whereConditions);

		$sql = sprintf('
			SELECT 
				aso.*,
				s.s_nome as simulado_nome,
				s.s_bimestre as bimestre,
				u.u_nome as aluno_nome,
				i.i_nome as instituicao_nome,
				i.i_tipo_rede,
				DATE_FORMAT(aso.data, "%%d/%%m/%%Y %%H:%%i:%%s") as data_formatada
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s
			LIMIT 1',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql);
		
		if ($rs && $rs->num_rows) {
			$row = $rs->fetch_assoc();
			
			// Processar dados JSON com melhor tratamento
			$dados_json = null;
			$dados_brutos = $row['dados'];
			
			if (!empty($row['dados'])) {
				// Tentar fazer parse do JSON
				$dados_json = json_decode($row['dados'], true);
				
				// Se falhou o JSON, tentar interpretar como string Python/PHP
				if (json_last_error() !== JSON_ERROR_NONE) {
					// Converter formato Python para JSON válido
					$dados_convertidos = str_replace("'", '"', $row['dados']); // Aspas simples para duplas
					$dados_convertidos = preg_replace('/(\w+):\s/', '"$1": ', $dados_convertidos); // Adicionar aspas nas chaves
					
					$dados_json = json_decode($dados_convertidos, true);
					
					// Se ainda falhou, manter dados brutos
					if (json_last_error() !== JSON_ERROR_NONE) {
						$dados_json = array('dados_brutos' => $row['dados']);
					}
				}
			}
			
			$response = array(
				'success' => true,
				'dados' => array(
					'id' => $id,
					'data_formatada' => $row['data_formatada'],
					'aluno_nome' => $row['aluno_nome'],
					'matricula' => $row['matricula'],
					'simulado_nome' => $row['simulado_nome'],
					'bimestre' => $row['bimestre'] ?: 'N/A',
					'instituicao_nome' => $row['instituicao_nome'],
					'tipo_rede_descricao' => $row['i_tipo_rede'] == 'E' ? 'Estadual' : ($row['i_tipo_rede'] == 'M' ? 'Municipal' : 'Particular/Outro'),
					'dados_json' => $dados_json,
					'dados_brutos' => $dados_brutos,
					'sessao' => $row['sessao']
				)
			);
			$rs->free();
		} else {
			$response = array('success' => false, 'message' => 'Registro não encontrado');
			if ($rs) $rs->free();
		}

		header('Content-Type: application/json');
		echo json_encode($response);
		exit;
	}

	// Método para obter processamentos de hoje
	private function _obterProcessamentosHoje($whereSQL = '') {
		$sql = sprintf('
			SELECT COUNT(*) as total_hoje
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE DATE(aso.data) = CURDATE() %s',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql);
		$total_hoje = 0;
		
		if ($rs && $rs->num_rows) {
			$row = $rs->fetch_assoc();
			$total_hoje = (int)$row['total_hoje'];
			$rs->free();
		}
		
		return $total_hoje;
	}

	private function _obterDadosParaGraficos($whereSQL = '') {
		$dados_graficos = array(
			'atividade_por_data' => array(),
			'atividade_hora' => array_fill(0, 24, 0),
			'status_finalizacao' => array('finalizados' => 0, 'nao_finalizados' => 0),
			'finalizacao_por_simulado' => array(),
			'total_usuarios_unicos' => 0,
			'distribuicao_rede' => array('E' => 0, 'M' => 0, 'outros' => 0),
			'top_simulados' => array(),
			'instituicoes_ativas' => array()
		);

		// 1. Query para atividade por data (últimos 30 dias)
		$sql_atividade = sprintf('
			SELECT 
				DATE(aso.data) as data_registro,
				COUNT(*) as total_atividades
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE aso.data >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) %s
			GROUP BY DATE(aso.data)
			ORDER BY data_registro',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql_atividade);
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$dados_graficos['atividade_por_data'][$row['data_registro']] = (int)$row['total_atividades'];
			}
			$rs->free();
		}

		// 2. Query para atividade por hora do dia
		$sql_atividade_hora = sprintf('
			SELECT 
				HOUR(aso.data) as hora_dia,
				COUNT(*) as total_atividades
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s
			GROUP BY HOUR(aso.data)
			ORDER BY hora_dia',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql_atividade_hora);
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$hora = (int)$row['hora_dia'];
				$dados_graficos['atividade_hora'][$hora] = (int)$row['total_atividades'];
			}
			$rs->free();
		}

		// 3. Query para distribuição por tipo de rede
		$sql_distribuicao_rede = sprintf('
			SELECT 
				i.i_tipo_rede as tipo_rede,
				COUNT(*) as total
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s
			GROUP BY i.i_tipo_rede
			ORDER BY total DESC',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql_distribuicao_rede);
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$tipo = $row['tipo_rede'];
				if ($tipo == 'E') {
					$dados_graficos['distribuicao_rede']['E'] = (int)$row['total'];
				} elseif ($tipo == 'M') {
					$dados_graficos['distribuicao_rede']['M'] = (int)$row['total'];
				} else {
					$dados_graficos['distribuicao_rede']['outros'] = (int)$row['total'];
				}
			}
			$rs->free();
		}

		// 4. Query para top simulados
		$sql_top_simulados = sprintf('
			SELECT 
				s.s_nome as simulado_nome,
				COUNT(*) as total_atividades
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s
			GROUP BY s.s_id, s.s_nome
			ORDER BY total_atividades DESC
			LIMIT 10',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql_top_simulados);
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$dados_graficos['top_simulados'][$row['simulado_nome']] = (int)$row['total_atividades'];
			}
			$rs->free();
		}

		// 5. Query para contar usuários únicos
		$sql_usuarios_unicos = sprintf('
			SELECT COUNT(DISTINCT a.a_usuario) as total_usuarios
			FROM auditoria_simulado_online aso
			INNER JOIN simulados s ON s.s_id = aso.sid
			INNER JOIN alunos a ON a.a_matricula = aso.matricula
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s',
			$whereSQL
		);

		$rs = Core::registro('db')->query($sql_usuarios_unicos);
		if ($rs && $rs->num_rows) {
			$row = $rs->fetch_assoc();
			$dados_graficos['total_usuarios_unicos'] = (int)$row['total_usuarios'];
			$rs->free();
		}

		// 6. Query SIMPLIFICADA para instituições ativas - apenas com dados básicos
		$sql_instituicoes = sprintf('
			SELECT 
				i.i_id as instituicao_id,
				i.i_nome as nome,
				i.i_tipo_rede as tipo_rede,
				COUNT(DISTINCT si.si_id) as total_inscricoes,
				COUNT(DISTINCT CASE 
					WHEN si.si_finalizado IS NOT NULL 
						AND si.si_finalizado != ""
						AND si.si_finalizado != "0" 
						AND si.si_finalizado != "null"
						AND JSON_VALID(si.si_finalizado) = 1
						AND JSON_EXTRACT(si.si_finalizado, "$") != "{}"
					THEN si.si_id
				END) as finalizados_contagem,
				COALESCE(ROUND(
					(COUNT(DISTINCT CASE 
						WHEN si.si_finalizado IS NOT NULL 
							AND si.si_finalizado != "" 
							AND si.si_finalizado != "0" 
							AND si.si_finalizado != "null"
							AND JSON_VALID(si.si_finalizado) = 1
							AND JSON_EXTRACT(si.si_finalizado, "$") != "{}"
						THEN si.si_id
					END) * 100.0 / COUNT(DISTINCT si.si_id)), 1
				), 0) as taxa_finalizacao
			FROM instituicoes i
			INNER JOIN usuarios u ON u.u_instituicao = i.i_id
			INNER JOIN alunos a ON a.a_usuario = u.u_id
			INNER JOIN simulados_inscricoes si ON si.si_aluno = a.a_id
			INNER JOIN simulados s ON s.s_id = si.si_simulado
			WHERE 1=1 %s
			GROUP BY i.i_id, i.i_nome, i.i_tipo_rede
			HAVING total_inscricoes >= 5
			ORDER BY taxa_finalizacao DESC, total_inscricoes DESC
			LIMIT 10',
			str_replace(array('aso.sid', 'aso.matricula', 'aso.data'), 
					array('si.si_simulado', 'a.a_matricula', 'si.si_data_cadastro'), 
					$whereSQL)
		);

		$rs = Core::registro('db')->query($sql_instituicoes);
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$total_inscricoes = (int)$row['total_inscricoes'];
				$finalizados = (int)$row['finalizados_contagem'];
				
				$dados_graficos['instituicoes_ativas'][] = array(
					'instituicao_id' => (int)$row['instituicao_id'],
					'nome' => $row['nome'],
					'tipo_rede' => $row['tipo_rede'],
					'total_inscricoes' => $total_inscricoes,
					'finalizados' => $finalizados,
					'taxa_finalizacao' => (float)$row['taxa_finalizacao']
				);
			}
			$rs->free();
		}

		// 7. Query para status de finalização GERAL
		$sql_finalizacao_geral = sprintf('
			SELECT 
				SUM(CASE 
					WHEN si.si_finalizado IS NOT NULL 
						AND si.si_finalizado != "" 
						AND si.si_finalizado != "0"
						AND si.si_finalizado != "null"
						AND JSON_VALID(si.si_finalizado) = 1
						AND JSON_EXTRACT(si.si_finalizado, "$") != "{}"
						AND JSON_EXTRACT(si.si_finalizado, "$") IS NOT NULL
					THEN 1 ELSE 0
				END) as finalizados,
				SUM(CASE 
					WHEN si.si_finalizado IS NULL 
						OR si.si_finalizado = "" 
						OR si.si_finalizado = "0"
						OR si.si_finalizado = "null"
						OR JSON_VALID(si.si_finalizado) = 0
						OR JSON_EXTRACT(si.si_finalizado, "$") = "{}"
						OR JSON_EXTRACT(si.si_finalizado, "$") IS NULL
					THEN 1 ELSE 0
				END) as nao_finalizados,
				COUNT(*) as total_inscricoes
			FROM simulados_inscricoes si
			INNER JOIN simulados s ON s.s_id = si.si_simulado
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s',
			str_replace(array('aso.sid', 'aso.matricula', 'aso.data'), 
					array('si.si_simulado', 'a.a_matricula', 'si.si_data_cadastro'), 
					$whereSQL)
		);

		$rs = Core::registro('db')->query($sql_finalizacao_geral);
		if ($rs && $rs->num_rows) {
			$row = $rs->fetch_assoc();
			$dados_graficos['status_finalizacao']['finalizados'] = (int)$row['finalizados'];
			$dados_graficos['status_finalizacao']['nao_finalizados'] = (int)$row['nao_finalizados'];
			
			// Log para debug (remover em produção)
			error_log("DEBUG SQL Finalização: " . print_r($row, true));
			
			$rs->free();
		} else {
			// Se não há dados, zerar os valores
			$dados_graficos['status_finalizacao']['finalizados'] = 0;
			$dados_graficos['status_finalizacao']['nao_finalizados'] = 0;
			
			error_log("DEBUG: Nenhum resultado na query de finalização");
		}

		// 8. Query para finalização POR SIMULADO
		$sql_finalizacao_simulado = sprintf('
			SELECT 
				s.s_nome as simulado_nome,
				s.s_id as simulado_id,
				COUNT(*) as total_inscricoes,
				COUNT(CASE 
					WHEN si.si_finalizado IS NOT NULL 
						AND si.si_finalizado != "" 
						AND si.si_finalizado != "0"
						AND si.si_finalizado != "null"
						AND JSON_VALID(si.si_finalizado) = 1
						AND JSON_EXTRACT(si.si_finalizado, "$") != "{}"
					THEN 1
				END) as finalizados,
				COUNT(CASE 
					WHEN si.si_finalizado IS NULL 
						OR si.si_finalizado = "" 
						OR si.si_finalizado = "0"
						OR si.si_finalizado = "null"
						OR JSON_VALID(si.si_finalizado) = 0
						OR JSON_EXTRACT(si.si_finalizado, "$") = "{}"
					THEN 1
				END) as nao_finalizados
			FROM simulados_inscricoes si
			INNER JOIN simulados s ON s.s_id = si.si_simulado
			INNER JOIN alunos a ON a.a_id = si.si_aluno
			INNER JOIN usuarios u ON u.u_id = a.a_usuario
			INNER JOIN instituicoes i ON i.i_id = u.u_instituicao
			WHERE 1=1 %s
			GROUP BY s.s_id, s.s_nome
			HAVING total_inscricoes > 0
			ORDER BY total_inscricoes DESC
			LIMIT 15',
			str_replace(array('aso.sid', 'aso.matricula', 'aso.data'), 
					array('si.si_simulado', 'a.a_matricula', 'si.si_data_cadastro'), 
					$whereSQL)
		);

		$rs = Core::registro('db')->query($sql_finalizacao_simulado);
		if ($rs && $rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$total = (int)$row['total_inscricoes'];
				$finalizados = (int)$row['finalizados'];
				$nao_finalizados = (int)$row['nao_finalizados'];
				$taxa_finalizacao = $total > 0 ? round(($finalizados / $total) * 100, 1) : 0;
				
				$dados_graficos['finalizacao_por_simulado'][] = array(
					'simulado_id' => (int)$row['simulado_id'],
					'simulado_nome' => $row['simulado_nome'],
					'total_inscricoes' => $total,
					'finalizados' => $finalizados,
					'nao_finalizados' => $nao_finalizados,
					'taxa_finalizacao' => $taxa_finalizacao
				);
			}
			$rs->free();
		}

		return $dados_graficos;
	}

	private function _calcularPeriodoAtividade($atividade_por_data) {
		if (empty($atividade_por_data)) {
			return 'Sem dados';
		}
		
		$max_atividade = max($atividade_por_data);
		$data_pico = array_search($max_atividade, $atividade_por_data);
		
		return date('d/m/Y', strtotime($data_pico)) . ' (' . $max_atividade . ' atividades)';
	}

	private function _calcularHorarioPico($atividade_hora) {
		if (empty($atividade_hora)) {
			return 'Sem dados';
		}
		
		$max_atividade = max($atividade_hora);
		$hora_pico = array_search($max_atividade, $atividade_hora);
		
		return sprintf('%02d:00 - %02d:59 (%d atividades)', $hora_pico, $hora_pico, $max_atividade);
	}

	public function aObterDadosGraficos() {
		// Verificar permissões
		$usuario_grupo = Core::registro('usuario')->obterGrupo();
		$tem_permissao = ($usuario_grupo == 1); // Admin
		
		// Se não for admin, verificar se tem instituição
		if (!$tem_permissao) {
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$tem_permissao = true;
			} else {
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$tem_permissao = true;
				}
			}
		}
		
		if (!$tem_permissao) {
			echo json_encode(['success' => false, 'message' => 'Acesso negado']);
			exit;
		}
		
		// Processar filtros (mesmo código da função principal)
		$whereConditions = array();
		
		if (!empty($_GET['filtro_simulado'])) {
			$whereConditions[] = 'aso.sid = ' . Core::registro('db')->formatarValor((int)$_GET['filtro_simulado']);
		}

		if (!empty($_GET['bimestre'])) {
			$whereConditions[] = 's.s_bimestre = ' . Core::registro('db')->formatarValor((int)$_GET['bimestre']);
		}
		
		if (!empty($_GET['data_inicio'])) {
			$whereConditions[] = 'DATE(aso.data) >= ' . Core::registro('db')->formatarValor($_GET['data_inicio']);
		}
		
		if (!empty($_GET['data_fim'])) {
			$whereConditions[] = 'DATE(aso.data) <= ' . Core::registro('db')->formatarValor($_GET['data_fim']);
		}
		
		if (!empty($_GET['tipo_rede']) && $_GET['tipo_rede'] != 'todos') {
			$whereConditions[] = 'i.i_tipo_rede = ' . Core::registro('db')->formatarValor($_GET['tipo_rede']);
		}
		
		if (!empty($_GET['instituicao'])) {
			$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor((int)$_GET['instituicao']);
		}
		
		if (!empty($_GET['busca_aluno'])) {
			$termo = '%' . Core::registro('db')->escape($_GET['busca_aluno']) . '%';
			$whereConditions[] = '(u.u_nome LIKE ' . Core::registro('db')->formatarValor($termo) . ' OR aso.matricula LIKE ' . Core::registro('db')->formatarValor($termo) . ')';
		}

		// Sempre filtrar pela instituição do usuário logado (se não for admin)
		if (Core::registro('usuario')->obterGrupo() != 1) {
			if (class_exists('ProvaFloripa') && isset(ProvaFloripa::$instituicao)) {
				$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor(ProvaFloripa::$instituicao->obterID());
			} else {
				$usuario_logado = Core::registro('autenticador')->obterUsuario();
				if ($usuario_logado && method_exists($usuario_logado, 'obterInstituicao')) {
					$instituicao_id = $usuario_logado->obterInstituicao()->obterID();
					$whereConditions[] = 'i.i_id = ' . Core::registro('db')->formatarValor($instituicao_id);
				}
			}
		}
		
		$whereSQL = '';
		if (!empty($whereConditions)) {
			$whereSQL = ' AND ' . implode(' AND ', $whereConditions);
		}
		
		// Obter dados dos gráficos
		$dados_graficos = $this->_obterDadosParaGraficos($whereSQL);
		
		// Retornar como JSON
		header('Content-Type: application/json');
		echo json_encode([
			'success' => true,
			'dados' => $dados_graficos
		]);
		exit;
	}

}

?>