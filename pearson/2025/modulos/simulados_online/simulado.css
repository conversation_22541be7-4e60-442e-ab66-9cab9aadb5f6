.sr_simulado_ColHeader {
    background-color: #8B008B; /* Cor de fundo */
    color: #FFFFFF; /* Cor do texto */
    text-align: center;
    padding: 8px;
}

.sr_simulado_ColData {
    background-color: #ffffff; /* Cor de fundo */
    font-size: 13px;
    color: #708090;
    text-align: center;
    padding: 8px;
    height: 60px;
}

.sr_simulado_ColDataHover {
    background-color: #eef3f8; /* Cor de fundo ao passar o mouse */
    height: 60px;

}

.sr_simulado_button {
    width: 150px;
    line-height: 30px;
    text-align: center;
    display: inline-block;
    background-color: #07003B; /* Cor de fundo do botão */
    color: #FFFFFF; /* Cor do texto do botão */
    border: none;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
    padding: 5px 10px;
}

.sr_simulado_button:hover {
    background-color: #800080; /* Cor de fundo do botão */
    color: #FFFFFF; /* Cor do texto do botão */
    border: none;
    cursor: pointer;
    text-decoration: none;
    text-decoration: none;
    -webkit-transition: background-color 0.7s ease;
    -moz-transition: background-color 0.7s ease;
    transition: background-color 0.7s ease;
}


.sr_simulado_button img {
    float: left;
    padding-top: 3px;
    margin-right: 5px;
    text-decoration: none;

}

.sr_simulado_button span {
    font-size: 13px;
    line-height: 30px;
    text-decoration: none;

}