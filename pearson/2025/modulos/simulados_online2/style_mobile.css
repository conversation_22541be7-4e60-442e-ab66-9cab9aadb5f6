/* Large devices (laptops/desktops, 992px and up) */
@media (max-width: 991px) and (orientation:portrait) {  
  /* Força para usar apenas LANDSCAPE
  @media only screen and (orientation:portrait){
    html {
     height: 100vw;
     width: 100vw;
     -webkit-transform: rotate(90deg);
     -moz-transform: rotate(90deg);
     -o-transform: rotate(90deg);
     -ms-transform: rotate(90deg);
     transform: rotate(90deg);
    }
  }*/

  table#cabecalho table.menu tr td:nth-child(1),
  table#cabecalho table.menu tr td:nth-child(2),
  table#cabecalho table.menu tr td:nth-child(5),
  table#cabecalho table.menu tr td:nth-child(4)
  {
    display: none;
  }

  /* Default Font-Size 16px */
  :root {
    font-size: 62.5%;
    body {
      margin: 0;
      font-size: 1.6rem;
    }
  }

  #cabecalho .menu td {
      font-size: 3rem;
  }

  #cabecalho #logo a {
      font-size: 4rem;
  }

  #cabecalho #logo .esquerda {
      background-size: cover;
      padding: 0.2rem;
  }

  #cabecalho #logo .direita {
      padding: 0.4rem;
      background-size: cover;
  }

  #cabecalho #logo .centro {
      background-size: contain;
      padding-right: 2px;
  }

  .swiper-container {
    width: 90vw;
    height: 70vh;
  }
  .swiper-slide {
    min-height: 200px;

    text-align: center;
    font-size: 18px;
    background: #fff;

    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
  }

  div.geral{
    display: block;
    height: auto;
    width: 100%;
  }

  div.title{
    background-color: #eee;
      border-radius: 10px 10px 0px 0px;
      font-size: 4rem;
      margin-bottom: 0;
      padding: 10px;
      font-weight: bold;
  }

  .crono{
      border-bottom: 0.5rem solid #EEE;
      border-left: 0.5rem solid #EEE;
      border-right: 0.5rem solid #EEE;
      border-radius: 0px 0px 1rem 1rem;
      color: #555555;
      font-size: 3rem;
      margin: 0px auto;
      padding: 1rem 1rem 0.5rem;
      width: 18rem;
      overflow: hidden;
  }

  img.crono_start, img.crono_stop{
    cursor: pointer;
    display:none;
  }

  #crono_time{
    display: block;
      float: right;
      margin-left: 5px;
  }

  .conteudo {
      border: 0.5rem solid #EEE;
      border-radius: 0px 0px 5px 5px;
      margin-top: 0;
      background: #fff;
  }

  .num_questao{
    border-bottom: 0.5rem solid #EEE;
      border-right: 0.5rem solid #EEE;
      border-radius: 5rem 0 2rem 0;
      float:left;
      color: #333333;
      font-size: 3rem;
      padding: 1rem 2rem;
  }

  .crono_questao{
    border-bottom: 0.5rem solid #EEE;
      border-left: 0.5rem solid #EEE;
      border-radius: 0 5rem 0 2rem;
      float: right;
      color:#333333;
      font-size: 3rem;
      padding: 1rem 1rem;
  }

  .tbl_conteudo{
    width: 100%;
    border-collapse: collapse; 
    border-spacing: 0;
  }

  .tbl_conteudo td{
    border-spacing: 0;
    padding: 0;
    line-height: 20px;
  }

  .botoes{
    padding: 5px;
    border-top: 0.5rem solid #EEE;
  }

  .resposta_questao {
      padding: 15px;
  }

  .questao{
    width: 100%;
  }

  .questionario_general_start{
    border: 0.5rem solid #EEE;
    border-radius: 0px 0px 5px 5px;
    background: #fff;
  }

  .questionario_general_end{
    border: 0.5rem solid #EEE;
    border-radius: 0px 0px 5px 5px;
    background: #fff;
  }

  #modal_load{
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(150, 150, 150, 0.8);
    width: 100%;
    height: 100%;
    z-index: 999999999999999;
  }

  .sr_button span {
      float: left;
      font-size: 4rem;
      overflow: hidden;
  }

  .sr_questao_texto {
      font-size: 4rem;
      margin-top: 10px;
  }

  .sr_questao_titulo, .sr_questao_titulo > p {
      font-size: 4rem;
  }

  .multiplaescolha {
      width: 4em;
      height: 2em;
      font-size: 1.8em;
      text-align: center;
      border: 0.5rem solid #EEE;
  }

  .discursiva, .aberta {
      font-size: 1.4rem;
      height: 15rem;
      width: 95%;
      border: 0.5rem solid #EEE;
  }

  .somatorio {
    width: 10rem;
      height: 5rem;
      font-size: 5rem;
      border: 0.5rem solid #EEE;
      text-align: center;
  }

  .sr_right {
      border: 0.5rem solid #EEE;
      border-radius: 1rem;
  }

  .alert_box {
      border: 0.5rem dashed #888888;
      border-radius: 2rem;
      font-size: 3rem;
  }

  .alert_box_sucesso {
      border: 0.5rem dashed #558E2F;
  }

  .alert_box_erro {
      border: 0.5rem dashed #EE1111;
  }

  /* CSS GERAL */
  .img_load{
    width: 17%;
    display: block;
    margin: 25% auto;
  }

  #div_btn_start{
    padding: 20px;
    height: 100%;/*70vh;*/
  }

  #fsalert{
    background: none repeat scroll 0 0 #F7EECD;
    border: 0.4rem dashed #8C7416;
    border-radius: 3px 3px 3px 3px;
    color: #896E00;
    font-size: 3.6rem;
    padding: 10px;
    text-align: center;
  }

  #btn_start{
    line-height:8rem;
    margin-left:25%;
    margin-bottom:10%;
    margin-top:8%;
    cursor:pointer;
  }

  #btn_start > img{
    margin-top: 3%;
  }

  #cnt_questoes{
    display:none;
  }

  .tbl_conteudo > tr > td{
    width: 33%; 
  }

  .img_clock{
    float:left;
    height: 2.6rem;
  }

  .crono_start{
    height: 2rem;
  }

  .crono_stop{
    height: 2rem;
  }

  #crono_div_left{
    float:left;
  }

  #crono_div_right{
    float:right;
  }

  .display_none{
    display:none;
  }

  .div_questoes{
    padding: 5px;
    position:relative;
  }

  .p_resposta{
    font-size:4rem;
  }

  .botoes > table{
    text-align:
    center;
    width: 100%;
  }

  .td_btn_anterior{
    width: 50%;
  }

  #btn_prev, #btn_next{
    line-height: 5rem;
    cursor:pointer;
  }

  #btn_finalizar{
    line-height: 5rem;
    cursor:pointer;
    display:none;
  }

  #btn_prev > img, #btn_next > img{
    height: 4rem;
  }

  #btn_finalizar > img{
    height: 4rem;
  }

  #btn_finalizar > span{
    font-size: 3rem;
  }

  #pause-mgs{
    text-align:center;
    display:none;
  }

  #div_btn_end{
    height: 70vh; 
    padding: 20px;
    display:none;
  }

  #alert_box{
    display:none;
  }

  #detalhes_respostas{
    width: 45%; 
    float: left; 
    margin-right: 2%;
  }

  #detalhes_respostas > p{
    font-size: 4em;
    margin: 0;
  }

  #tempo_realizacao{
    font-weight: bold;
  }

  .troca_questao{
    cursor:pointer;
    font-size: 3em;
    line-height: 5rem;
  }

  .painel_btns_finais{
    width: 45%;
  }

  .div_painel_btns_finais{
    margin: 0 auto;
    margin-bottom:10%;
    margin-top:8%;
    text-align: center;
  }

  #btn_end{
    line-height:51px;
    cursor:pointer;
  }

  #btn_end > span{
    width: 65%;
  }

  #btn_end > img{
    height: 10rem;
  }

  #voltar_prova{
    cursor:pointer;
    line-height: 3rem;
  }

  #voltar_prova > span{
    width: 75%; 
    font-size: 3rem;
  }

  #voltar_prova > img{
    height: 5rem;
  }

  #voltar_listagem{
    line-height: 3rem; 
    cursor:pointer; 
    display:none;
  }

  #voltar_listagem > span{
    width: 75%; font-size: 3rem;
  }

  #voltar_listagem > img{
    height: 5rem;
  }
}

/* =================================================================================== */

@media (max-width: 991px) and (orientation:landscape) {  
  /* Força para usar apenas LANDSCAPE
  @media only screen and (orientation:portrait){
    html {
     height: 100vw;
     width: 100vw;
     -webkit-transform: rotate(90deg);
     -moz-transform: rotate(90deg);
     -o-transform: rotate(90deg);
     -ms-transform: rotate(90deg);
     transform: rotate(90deg);
    }
  }*/

  table#cabecalho table.menu tr td:nth-child(1),
  table#cabecalho table.menu tr td:nth-child(2),
  table#cabecalho table.menu tr td:nth-child(5),
  table#cabecalho table.menu tr td:nth-child(4)
  {
    display: none;
  }

  /* Default Font-Size 16px */
  :root {
    font-size: 62.5%;
    body {
      margin: 0;
      font-size: 1.6rem;
    }
  }

  #cabecalho .menu td {
      font-size: 3rem;
  }

  #cabecalho #logo a {
      font-size: 4rem;
  }

  #cabecalho #logo .esquerda {
      background-size: cover;
      padding: 0.2rem;
  }

  #cabecalho #logo .direita {
      padding: 0.4rem;
      background-size: cover;
  }

  #cabecalho #logo .centro {
      background-size: contain;
      padding-right: 2px;
  }

  .swiper-container {
    width: 90vw;
    height: 100%;
  }
  .swiper-slide {
    min-height: 200px;

    text-align: center;
    font-size: 18px;
    background: #fff;

    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
  }

  div.geral{
    display: block;
    height: auto;
    width: 100%;
  }

  div.title{
    background-color: #eee;
      border-radius: 10px 10px 0px 0px;
      font-size: 4rem;
      margin-bottom: 0;
      padding: 10px;
      font-weight: bold;
  }

  .crono{
      border-bottom: 0.5rem solid #EEE;
      border-left: 0.5rem solid #EEE;
      border-right: 0.5rem solid #EEE;
      border-radius: 0px 0px 1rem 1rem;
      color: #555555;
      font-size: 3rem;
      margin: 0px auto;
      padding: 1rem 1rem 0.5rem;
      width: 18rem;
      overflow: hidden;
  }

  img.crono_start, img.crono_stop{
    cursor: pointer;
    display:none;
  }

  #crono_time{
    display: block;
      float: right;
      margin-left: 5px;
  }

  .conteudo {
      border: 0.5rem solid #EEE;
      border-radius: 0px 0px 5px 5px;
      margin-top: 0;
      background: #fff;
  }

  .num_questao{
    border-bottom: 0.5rem solid #EEE;
      border-right: 0.5rem solid #EEE;
      border-radius: 5rem 0 2rem 0;
      float:left;
      color: #333333;
      font-size: 3rem;
      padding: 1rem 2rem;
  }

  .crono_questao{
    border-bottom: 0.5rem solid #EEE;
      border-left: 0.5rem solid #EEE;
      border-radius: 0 5rem 0 2rem;
      float: right;
      color:#333333;
      font-size: 3rem;
      padding: 1rem 1rem;
  }

  .tbl_conteudo{
    width: 100%;
    border-collapse: collapse; 
    border-spacing: 0;
  }

  .tbl_conteudo td{
    border-spacing: 0;
    padding: 0;
    line-height: 20px;
  }

  .botoes{
    padding: 5px;
    border-top: 0.5rem solid #EEE;
  }

  .resposta_questao {
      padding: 15px;
  }

  .questao{
    width: 100%;
  }

  .questionario_general_start{
    border: 0.5rem solid #EEE;
    border-radius: 0px 0px 5px 5px;
    background: #fff;
  }

  .questionario_general_end{
    border: 0.5rem solid #EEE;
    border-radius: 0px 0px 5px 5px;
    background: #fff;
  }

  #modal_load{
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(150, 150, 150, 0.8);
    width: 100%;
    height: 100%;
    z-index: 999999999999999;
  }

  .sr_button span {
      float: left;
      font-size: 4rem;
      overflow: hidden;
  }

  .sr_questao_texto {
      font-size: 4rem;
      margin-top: 10px;
  }

  .sr_questao_titulo, .sr_questao_titulo > p {
      font-size: 4rem;
  }

  .multiplaescolha {
      width: 4em;
      height: 2em;
      font-size: 1.8em;
      text-align: center;
      border: 0.5rem solid #EEE;
  }

  .discursiva, .aberta {
      font-size: 1.4rem;
      height: 15rem;
      width: 95%;
      border: 0.5rem solid #EEE;
  }

  .somatorio {
    width: 10rem;
      height: 5rem;
      font-size: 5rem;
      border: 0.5rem solid #EEE;
      text-align: center;
  }

  .sr_right {
      border: 0.5rem solid #EEE;
      border-radius: 1rem;
  }

  .alert_box {
      border: 0.5rem dashed #888888;
      border-radius: 2rem;
      font-size: 3rem;
  }

  .alert_box_sucesso {
      border: 0.5rem dashed #558E2F;
  }

  .alert_box_erro {
      border: 0.5rem dashed #EE1111;
  }

  /* CSS GERAL */
  .img_load{
    width: 17%;
    display: block;
    margin: 25% auto;
  }

  #div_btn_start{
    padding: 20px;
    height: 100%;/*70vh;*/
  }

  #fsalert{
    background: none repeat scroll 0 0 #F7EECD;
    border: 0.4rem dashed #8C7416;
    border-radius: 3px 3px 3px 3px;
    color: #896E00;
    font-size: 3.6rem;
    padding: 10px;
    text-align: center;
  }

  #btn_start{
    line-height:8rem;
    margin-left:25%;
    margin-bottom:10%;
    margin-top:8%;
    cursor:pointer;
  }

  #btn_start > img{
    margin-top: 3%;
  }

  #cnt_questoes{
    display:none;
    height: 100%;
  }

  .tbl_conteudo > tr > td{
    width: 33%; 
  }

  .img_clock{
    float:left;
    height: 2.6rem;
  }

  .crono_start{
    height: 2rem;
  }

  .crono_stop{
    height: 2rem;
  }

  #crono_div_left{
    float:left;
  }

  #crono_div_right{
    float:right;
  }

  .display_none{
    display:none;
  }

  .div_questoes{
    padding: 5px;
    position:relative;
    height: 100%;
  }

  .p_resposta{
    font-size:4rem;
  }

  .botoes > table{
    text-align:
    center;
    width: 100%;
  }

  .td_btn_anterior{
    width: 50%;
  }

  #btn_prev, #btn_next{
    line-height: 5rem;
    cursor:pointer;
  }

  #btn_finalizar{
    line-height: 5rem;
    cursor:pointer;
    display:none;
  }

  #btn_prev > img, #btn_next > img{
    height: 4rem;
  }

  #btn_finalizar > img{
    height: 4rem;
  }

  #btn_finalizar > span{
    font-size: 3rem;
  }

  #pause-mgs{
    text-align:center;
    display:none;
  }

  #div_btn_end{
    height: 100%; 
    padding: 20px;
    display:none;
  }

  #alert_box{
    display:none;
  }

  #detalhes_respostas{
    width: 45%; 
    float: left; 
    margin-right: 2%;
  }

  #detalhes_respostas > p{
    font-size: 4em;
    margin: 0;
  }

  #tempo_realizacao{
    font-weight: bold;
  }

  .troca_questao{
    cursor:pointer;
    font-size: 3em;
  }

  .painel_btns_finais{
    width: 45%;
  }

  .div_painel_btns_finais{
    margin: 0 auto;
    margin-bottom:10%;
    margin-top:8%;
    text-align: center;
  }

  #btn_end{
    line-height:51px;
    cursor:pointer;
  }

  #btn_end > span{
    width: 65%;
  }

  #btn_end > img{
    height: 10rem;
  }

  #voltar_prova{
    cursor:pointer;
    line-height: 3rem;
  }

  #voltar_prova > span{
    width: 75%; 
    font-size: 3rem;
  }

  #voltar_prova > img{
    height: 5rem;
  }

  #voltar_listagem{
    line-height: 3rem; 
    cursor:pointer; 
    display:none;
  }

  #voltar_listagem > span{
    width: 75%; font-size: 3rem;
  }

  #voltar_listagem > img{
    height: 5rem;
  }
}