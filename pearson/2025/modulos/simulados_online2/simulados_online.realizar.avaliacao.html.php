<script type="text/javascript" src="modulos/simulados_online/is.js"></script>
<script type="text/javascript" src="modulos/simulados_online/fs_start.js"></script>

<link rel="stylesheet" href="modulos/simulados_online/style_geral.css"/>
<link rel="stylesheet" href="modulos/simulados_online/style_mobile.css"/>

<link rel="stylesheet" href="includes/JavaScript/swiper/package/css/swiper.css"/>

<div id="modal_load">
	<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/loader6.gif" class="img_load" />
</div>

<div class="geral">
	<div class="title">
		<?php echo$simulado['nome']; ?>
	</div>

	<div id="div_btn_start" class="questionario_general_start">
		<p id="fsalert">
			Bem-vindo!
			<br><br>
			A partir deste momento, voc&#234; j&#225; pode desconectar seu celular da internet para economizar seus dados. 
			<span class="">&#128515;</span>
			<br><br>
			Fique tranquilo! Eu vou te lembrar de conectar novamente no momento de entregar a avalia&ccedil;&atilde;o. 
			<span class="">&#128521;</span>
		</p>
		<p>
			<?php echo$simulado['tinicio']; ?>
		</p>
		<a id="btn_start" class="sr_button button">
			<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'start.png';?>">
			<?php
				$txt_start = 'Iniciar';
				if(isset($simulado['todasRespostas'])){
					if(count($simulado['todasRespostas'])>0){
						$txt_start = 'Continuar';
					}
				}
			?>
			<span><?php echo$txt_start; ?> Avalia&ccedil;&atilde;o</span>
		</a>
	</div>

	<div id='cnt_questoes' class="conteudo">
		<div>
			<table class='tbl_conteudo'>
				<tr>
					<td>
						<div class='num_questao'>
							N&ordm; <span id='num_questao'>1</span>
						</div>
					</td>
					<td>
						<div class="crono">
							<div id="crono_div_left">
							    <img class="img_clock" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'clock.png';?>"/>
								<span id="crono_time">00:00:00</span>
								<input id="tempo-total" name="tempo-total" type="hidden" value="<?php echo$simulado['tempoTotalHide']; ?>"/>
							</div>
							<div id="crono_div_right">
							<img class="crono_start" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_play.png';?>"/>
								<img class="crono_stop" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_pause.png';?>"/>
							</div>
						</div>
					</td>
					<td>
						<div class="crono_questao">
							<?php
								$num = 1;
								foreach($simulado['questoes'] as $sqk => $sqv){
									$idq = $sqv->obterID();

									if(!isset($simulado['respostas'][$idq])){
										$qTime = '00:00:00';
									}
									else{
										$qTime = $simulado['respostas'][$idq]['tempo'];
									}
							?>
								<span class="display_none" id="crono_questao<?php echo$num; ?>"><?php echo$qTime; ?></span>
							<?php
									$num++;
								}
							?>
						</div>
					</td>
				</tr>
			</table>
		</div>

		<div class="div_questoes">
			<!-- <table class='tbl_conteudo_questao'>
				<tr>
					<td> -->
					  <div class="swiper-container">
					    <div class="swiper-wrapper">

					    <?php
							$num = 1;
							foreach($simulado['questoes'] as $sqk => $sqv){
								$title = $sqv->obterEnunciado();
								$sqv->carregarProposicoes();
								$questoesTextos = $sqv->obterProposicoes();
								$ans = $sqv->obterTipo();
								$idq = $sqv->obterID();

								$resps = "";

								$qOptionsRespostas = "";
								$qTextosRespostas = "";
								foreach($questoesTextos as $qtk => $qtv){
									$valorqtr = $qtv->obterNumero();
									if($sqv->obterTipo() == Questao::MULTIPLAESCOLHA){
										$valorqtr = MultiplaEscolha::letra($valorqtr);
									}
									$textoqtr = $qtv->obterTexto();

									$selected = '';
									if(isset($simulado['respostas'][$idq])){
										if($simulado['respostas'][$idq]['valor'] == $valorqtr){
											$selected = 'selected="selected"';
										}
									}

									$qOptionsRespostas .= "<option value=".$valorqtr." ".$selected.">".$valorqtr."</option>";
									$qTextosRespostas .= $valorqtr." - ".$textoqtr."</br>";
								}

								if(!isset($simulado['respostas'][$idq])){
									$simulado['respostas'][$idq]['tempo'] = '';
									$simulado['respostas'][$idq]['tempoJS'] = '';
									$simulado['respostas'][$idq]['valor'] = '';
								}

								switch ($ans) {
									case 'ABERTA':
										$resps = "<textarea spellcheck='false' class='sr_ctexto aberta' maxlenght='1020' name='resposta[".$num."]' id='resposta-".$num."'>".$simulado['respostas'][$idq]['valor']."</textarea>";
										break;
									case 'DISCURSIVA':
										$resps = "<textarea spellcheck='false' class='sr_ctexto discursiva' maxlenght='1020' name='resposta[".$num."]' id='resposta-".$num."'>".$simulado['respostas'][$idq]['valor']."</textarea>";
										break;
									case 'SOMATORIO':
										$resps = "<input spellcheck='false' class='sr_cvalor somatorio' name='resposta[".$num."]' id='resposta-".$num."' size='1' maxlength='3' value='".$simulado['respostas'][$idq]['valor']."' />";
										break;
									case 'MULTIPLAESCOLHA':
										$resps = "<select spellcheck='false' class='sr_cvalor multiplaescolha' name='resposta[".$num."]' id='resposta-".$num."'>";
										$resps .= "<option value='0'> </option>";
											$resps .= $qOptionsRespostas;
										$resps .= "</select>";
										break;
								}
						?>

					      <div class="swiper-slide">
					      	<div class='questao' id="questao<?php echo$num; ?>">

								<input id="tempo-<?php echo$num; ?>" name="tempo[<?php echo$num; ?>]" type="hidden" value="<?php echo$simulado['respostas'][$idq]['tempo']; ?>"/>
								
								<div class="sr_texto_questao">
									<div class="sr_questao_titulo"><?php echo$title; ?></div>
									<p class="sr_questao_texto"><?php echo$qTextosRespostas; ?></p>
								</div>
								<div class="resposta_questao">
									<p class="p_resposta">Resposta:</p>
									<div class="resposta_questao_valor"><?php echo$resps; ?></div>
								</div>
							</div>
					      </div>

						<?php
								$num++;
							}
						?>
					    </div>
					    <!-- Add Pagination -->
					    <div class="swiper-pagination"></div>
					    <!-- Add Arrows -->
					    <!-- <div class="swiper-button-next"></div>
					    <div class="swiper-button-prev"></div> -->
					  </div>
			<!-- 		</td>
				</tr>
			</table> -->
		</div>

		<div class="botoes">
			<table border="0">
				<tr>
					<td class="td_btn_anterior">
						<a class="sr_button" id="btn_prev">
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_start.png';?>">
							<span>Anterior</span>
						</a>
					</td>
					<td>
						<a class="sr_button" id="btn_next">
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_end.png';?>">
							<span>Pr&oacute;xima</span>
						</a>
						<a class="sr_button" id="btn_finalizar">
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'sucesso-big.png';?>">
							<span>Verificar e Entregar</span>
						</a>
					</td>
				</tr>
			</table>
			<p id="pause-mgs">A avalia&#231;&#227;o est&#225; <b>Pausada</b>. Clique sobre o bot&#227;o de <b>Play</b> para retomar.</p>
		</div>
	</div>

	<div id="div_btn_end" class="questionario_general_end">
		<div id="alert_box" class="alert_box alert_box_erro"></div>
		<div id="detalhes_respostas" class="sr_right">
			<p>Tempo da Avalia&ccedil;&atilde;o: 
				<span id='tempo_realizacao'><?php echo$simulado['tfim']; ?></span>
			</p>
			<br><br>
			<p>Quest&#245;es:</p>
			<ul class="sr_list">
				<?php
					$num = 1;
					foreach($simulado['questoes'] as $sqk => $sqv){
						$idq = $sqv->obterID();

						$class = "pendente";
						$text = "Pendente";

						$val = @$simulado['respostas'][$idq]['valor'];
						if(!empty($val)){
							$class = "realizada";
							$text = "Respondida";
						}
				?>
				<li class="<?php echo$class; ?>">
					<a id="lquestao<?php echo$num; ?>" class="troca_questao"><?php echo$num; ?> - <?php echo$text; ?></a>
				</li>
				<?php
						$num++;
					}
				?>
			</ul>
		</div>
		<div class="sr_right painel_btns_finais">
			<div class="div_painel_btns_finais">
				<a id="btn_end" class="sr_button button">
					<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'stock_task20.png';?>">
					<span>Entregar Avalia&ccedil;&atilde;o</span>
				</a>
			</div> 
			<div class="div_painel_btns_finais">
				<a class="sr_button button" id="voltar_prova">
					<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_start.png';?>">
					<span>Continuar a Avalia&ccedil;&atilde;o</span>
				</a>
			</div>
			<div class="div_painel_btns_finais">
				<a href="./?m=simulados_online&a=listar" class="sr_button button" id="voltar_listagem">
					<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'dynamic_blue_left.png';?>">
					<span>Lista de Avalia&ccedil;&atilde;o</span>
				</a>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/swiper/package/js/swiper.min.js"></script>
<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/jquery-stopwatch/jquery.stopwatch.js"></script>

<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/simulados_online2/fs_end.js"></script>