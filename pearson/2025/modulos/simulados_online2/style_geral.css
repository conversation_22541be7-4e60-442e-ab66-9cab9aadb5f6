@font-face {
  font-family: "SegoeUIEmoji";
  src: url("./segoe-ui-emoji.ttf");
}

.emoji{
  font-family: "SegoeUIEmoji";
}

/* Large devices (laptops/desktops, 992px and up) */
@media (min-width: 992px) { 
  .swiper-container {
    width: 90vw;
    height: 100%;
  }
  .swiper-slide {
    min-height: 200px;

    text-align: center;
    font-size: 18px;
    background: #fff;

    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
  }

  div.geral{
    display: block;
    height: auto;
    width: 100%;
  }

  div.title{
    background-color: #eee;
      border-radius: 10px 10px 0px 0px;
      font-size: 20px;
      margin-bottom: 0;
      padding: 10px;
      font-weight: bold;
  }

  .crono{
    border-bottom: 4px solid #EEE;
    border-left: 4px solid #EEE;
    border-right: 4px solid #EEE;
      border-radius: 0px 0px 5px 5px;
      color: #555555;
      font-size: 13px;
      margin: 0px auto;
      padding: 8px 5px 2px;
      width: 125px;
      overflow: hidden;
  }

  img.crono_start, img.crono_stop{
    cursor: pointer;
    display:none;
  }

  #crono_time{
    display: block;
      float: right;
      margin-left: 5px;
  }

  .conteudo {
      border: 8px solid #EEE;
      border-radius: 0px 0px 5px 5px;
      margin-top: 0;
      background: #fff;
  }

  .num_questao{
    border-bottom: 4px solid #EEE;
      border-right: 4px solid #EEE;
      border-radius: 5px 0 5px 0;
      float:left;
      color: #333333;
      font-size: 14px;
      padding: 5px 10px;
  }

  .crono_questao{
    border-bottom: 4px solid #EEE;
      border-left: 4px solid #EEE;
      border-radius: 0 5px 0 5px;
      float: right;
      color:#333333;
      font-size: 14px;
      padding: 5px;
  }

  .tbl_conteudo{
    width: 100%;
    border-collapse: collapse; 
    border-spacing: 0;
  }

  .tbl_conteudo td{
    border-spacing: 0;
    padding: 0;
    line-height: 20px;
  }

  .botoes{
    padding: 5px;
    border-top: 8px solid #EEE;
  }

  .resposta_questao {
      padding: 15px;
  }

  .questao{
    width: 100%;
  }

  .questionario_general_start{
    border: 8px solid #EEE;
    border-radius: 0px 0px 5px 5px;
    background: #fff;
  }

  .questionario_general_end{
    border: 8px solid #EEE;
    border-radius: 0px 0px 5px 5px;
    background: #fff;
  }

  #modal_load{
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(150, 150, 150, 0.8);
    width: 100%;
    height: 100%;
    z-index: 999999999999999;
  }

  .sr_button span {
      float: left;
      font-size: 24px;
      overflow: hidden;
  }

  .sr_questao_texto {
      font-size: 16px;
      margin-top: 10px;
  }

  .sr_questao_titulo, .sr_questao_titulo > p {
      font-size: 22px;
  }

  .multiplaescolha {
    width: 100px;
    height: 50px;
    font-size: 16px;
    text-align: center;
    border: 8px solid #EEE;
  }

  .discursiva, .aberta {
      font-size: 16px;
      height: 150px;
      width: 95%;
      border: 8px solid #EEE;
  }

  .somatorio {
    width: 100px;
    height: 50px;
    font-size: 16px;
    text-align: center;
    border: 8px solid #EEE;
  }

  .sr_right {
      border: 8px solid #EEE;
      border-radius: 1rem;
  }

  .alert_box {
      border: 4px dashed #888888;
      border-radius: 2rem;
      font-size: 18px;
  }

  .alert_box_sucesso {
      border: 4px dashed #558E2F;
  }

  .alert_box_erro {
      border: 4px dashed #EE1111;
  }

  /* CSS GERAL */
  .img_load{
    width: 17%;
    display: block;
    margin: 15% auto;
  }

  #div_btn_start{
    padding: 20px;
    height: 70vh;
  }

  #fsalert{
    background: none repeat scroll 0 0 #F7EECD;
    border: 1px dashed #8C7416;
    border-radius: 3px 3px 3px 3px;
    color: #896E00;
    font-size: 14px;
    padding: 10px;
    text-align: center;
  }

  #btn_start{
    line-height:50px;
    margin: 10% 0 10% 38%;
    cursor:pointer;
  }

  #btn_start > img{
  }

  #cnt_questoes{
    display:none;
  }

  .tbl_conteudo > tr > td{
    width: 33%; 
  }

  .img_clock{
    float:left;
    height: 20px;
  }

  .crono_start{
    height: 20px;
  }

  .crono_stop{
    height: 18px;
  }

  #crono_div_left{
    float:left;
  }

  #crono_div_right{
    float:right;
  }

  .display_none{
    display:none;
  }

  .div_questoes{
    padding: 5px;
    position:relative;
  }

  .p_resposta{
    font-size:14px;
  }

  .botoes > table{
    text-align:
    center;
    width: 100%;
  }

  .td_btn_anterior{
    width: 50%;
  }

  #btn_prev, #btn_next{
    line-height: 25px;
    cursor:pointer;
  }

  #btn_prev > img, #btn_next > img{
    height: 20px;
  }

  #btn_finalizar{
    line-height: 25px;
    cursor:pointer;
    display:none;
  }

  #btn_finalizar > img{
    height: 20px;
  }

  #btn_finalizar > span{
    float: left;
    font-size: 24px;
    overflow: hidden;
  }

  #pause-mgs{
    text-align:center;
    display:none;
  }

  #div_btn_end{
    height: 100%; 
    padding: 20px;
    display:none;
  }

  #alert_box{
    display:none;
  }

  #detalhes_respostas{
    width: 45%; 
    float: left; 
    margin-right: 2%;
  }

  #detalhes_respostas > p{
    font-size:18px;
    margin: 0;
  }

  #tempo_realizacao{
    font-weight: bold;
  }

  .troca_questao{
    cursor:pointer;
    font-size:13px;
  }

  .painel_btns_finais{
    width: 45%;
  }

  .div_painel_btns_finais{
    margin: 0 auto;
    margin-bottom:10%;
    margin-top:8%;
    text-align: center;
  }

  #btn_end{
    line-height:25px;
    cursor:pointer;
  }

  #btn_end > span{
    width: 65%;
  }

  #btn_end > img{
    height: 40px;
  }

  #voltar_prova{
    cursor:pointer;
    line-height: 25px;
  }

  #voltar_prova > span{
    width: 75%; 
    font-size: 24px;
  }

  #voltar_prova > img{
    height: 20px;
    margin-top: 15px;
  }

  #voltar_listagem{
    cursor:pointer;
    line-height: 25px;
    display:none;
  }

  #voltar_listagem > span{
    width: 75%; 
    font-size: 24px;
  }

  #voltar_listagem > img{
    height: 25px;
    margin-top: 15px;
  }
}