<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Data inicial de realização</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Data final de realização</td>
	  <td class="lp_ColHeader" nowrap="nowrap" align="center">Nome</td>
	  <td class="lp_ColHeader" nowrap="nowrap" align="center">Série</td>
	  <td class="lp_ColHeader" nowrap="nowrap" align="center">Aplicação</td>
	  <td class="lp_ColHeader" nowrap="nowrap" align="center">Fases</td>
	  <td class="lp_ColHeader" nowrap="nowrap" align="center">Disciplinas</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Realizado</td>
	  <!-- <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Seus resultados</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Impresso</td> -->
	</tr>
<?
if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as &$d ) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
		<td nowrap="nowrap" align="center"><strong><?= strftime('%d/%m/%Y %H:%M', $d['sodri']); ?></strong></td>
		<td nowrap="nowrap" align="center"><strong><?= strftime('%d/%m/%Y %H:%M', $d['sodrf']); ?></strong></td>
		<td nowrap="nowrap" align="center">
		<?php 
			if($d['soru'] && $d['saf']){
				echo $d['snome'];				
			}
			else{
				echo '<a title="Realizar o simulado on-line" href="'.Gerenciador_URL::gerarLink('simulados_online', 'realizar', array('id' => $d['soID'])).'">'.$d['snome'].'</a>';
			}
		?>
		</td>
		<td nowrap="nowrap" align="center">
			<?php echo $d['sserie']; ?>
		</td>
		<td nowrap="nowrap" align="center">
			<?php echo $d['sbi']; ?>
		</td>
		<td nowrap="nowrap" align="center">
			<?php echo $d['sfase']; ?>
		</td>
		<td nowrap="nowrap" align="center">
			<?php echo @implode(', ', $d['sdps']); ?>
		</td>
		<td nowrap="nowrap" align="center">
		<?php 
			if($d['saf']){
				echo '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'ajax_sucesso.gif"/>';
			}
			else{
				echo '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'ajax_falha.gif"/>';
			}
		?>
		</td>
		<?php 
			/*<td align="center"><a title="Visualize os relatórios de seus resultados" href="<?=Gerenciador_URL::gerarLink('relatorios', 'listar');?>">Visualizar</a></td>
			<td align="center"><a title="Gerar o simulado em PDF para impressão" href="<?=Gerenciador_URL::gerarLink('simulados_online', 'pdf', array('id' => $d->obterID()));?>">Gerar PDF</a></td>*/
		?>
    </tr>
<?
	}
} else {
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('Nenhuma avalia&ccedil;&atilde;o encontrada!');
?>
	<tr>
		<td height="200" colspan="99" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
<?
}
?>
</table>