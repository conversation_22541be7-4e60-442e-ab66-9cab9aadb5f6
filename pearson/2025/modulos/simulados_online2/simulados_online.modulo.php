<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);

class MSimuladosOnline extends Modulo
{
	protected $_listagem;

	public function __construct ()
	{
		parent::__construct();
	}

	public function aListarSimuladosParaRealizar () 
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->remover<PERSON><PERSON>ho('adicionar');
		Core::modulo('navegador')->removerAtalho('procurar');
		Core::modulo('navegador')->remover<PERSON><PERSON>ho('importar');
		Core::modulo('navegador')->remover<PERSON><PERSON><PERSON>('exportar');
		Core::modulo('navegador')->remover<PERSON><PERSON><PERSON>('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();
		
		$simulados = array();
		$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();

		$usuario = new UsuarioInstituido($user_logado_id);
		$usuario->carregar();

		$aluno_id = Aluno::obterIDAlunoPeloUsuario($usuario);

		if($aluno_id){
			$aluno = new Aluno($aluno_id);
			$aluno->carregar();

			$dados = Simulado::obterSimuladosPeloAluno($aluno);
		}
		else{
			//verificar grupos q podem acessar todas
			$dados = Simulado::obterSimuladosParaFormularioIV();
		}

		$this->_dados = array();

		if(count($dados)>0){
			foreach ($dados as $k => $simulado) {
				$sim = new Simulado($simulado->obterID());
				$sim->carregar();

				$sid = $sim->obterID();
				$snome = $sim->obterNome();
				$sbi = $sim->obterBimestre();
				$sodri = $sim->obterDataRealizacao();
				$sodrf = $sim->obterDataRealizacaoFim();
				$soru = $sim->obterRealizacaoUnica();
				$sdps = $sim->obterDisciplinas();

				if(!is_null($sim->obterSerieAvaliacao())){ 
					$sserie = $sim->obterSerieAvaliacao()->obterNome(); 
				}

				$saf = $sim->obterSeOAlunoFez(); //aqui ainda tem q trabalhar como fica essa questão, talvez um json.

				$qs = $sim->obterNumeroQuestoes(true,false);

				if(is_array($qs)){
					foreach ($qs as $qsK => $qsV) {
						$soID = base64_encode('soar_'.$sid.'_'.$qsK);

						if(!empty($saf)){
							$saf = json_decode($saf,true);					
							$saf = $saf[0][$qsK];
						}

						$this->_dados[$soID]['soID'] = $soID;
						$this->_dados[$soID]['sid'] = $sid;
						$this->_dados[$soID]['snome'] = $snome;
						$this->_dados[$soID]['sbi'] = $sbi;
						$this->_dados[$soID]['sfase'] = $qsK;
						$this->_dados[$soID]['sodri'] = $sodri;
						$this->_dados[$soID]['sodrf'] = $sodrf;
						$this->_dados[$soID]['soru'] = $soru;
						$this->_dados[$soID]['sserie'] = $sserie;
						$this->_dados[$soID]['saf'] = $saf;
						$this->_dados[$soID]['sdps'] = @array_unique($sdps[$qsK]);
					}
				}
				else{
					$fase = 1;

					$soID = base64_encode('soar_'.$sid.'_'.$fase);

					if(!empty($saf)){
						$saf = json_decode($saf,true);					
						$saf = $saf[0][$qsK];
					}

					$this->_dados[$soID]['soID'] = $soID;
					$this->_dados[$soID]['sid'] = $sid;
					$this->_dados[$soID]['snome'] = $snome;
					$this->_dados[$soID]['sbi'] = $sbi;
					$this->_dados[$soID]['sfase'] = $fase;
					$this->_dados[$soID]['sodri'] = $sodri;
					$this->_dados[$soID]['sodrf'] = $sodrf;
					$this->_dados[$soID]['soru'] = $soru;
					$this->_dados[$soID]['sserie'] = $sserie;
					$this->_dados[$soID]['saf'] = $saf;
					$this->_dados[$soID]['sdps'] = @array_unique($sdps[$fase]);
				}				
			}
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.realizar.listagem.html.php');
		$this->_finalizarRenderizacao();
	}

	public function aRealizarSimulado ()
	{
		$_SESSION['avaliacao_online']['usuario_id'] 		= 0;
		$_SESSION['avaliacao_online']['inscricao_id'] 		= 0;
		$_SESSION['avaliacao_online']['aluno_id'] 			= 0;
		$_SESSION['avaliacao_online']['simulado_id'] 		= 0;
		$_SESSION['avaliacao_online']['simulado_fase'] 		= 0;
		$_SESSION['avaliacao_online']['simulado_questoes'] 	= array();

		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador'));
		Core::modulo('navegador')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		Core::modulo('navegador')->removerAtalhosRecarregarPadroes();
		Core::modulo('navegador')->removerAtalho('adicionar');
		Core::modulo('navegador')->removerAtalho('procurar');
		Core::modulo('navegador')->removerAtalho('importar');
		Core::modulo('navegador')->removerAtalho('exportar');
		Core::modulo('navegador')->removerAtalho('opcoes_visualizacao');
		Core::modulo('navegador')->removerTodosItensNavegacao();
		Core::modulo('navegador')->setMostrarNavegacao(FALSE);
		Core::modulo('navegador')->setMostrarAtalhos(FALSE);
		Core::modulo('navegador')->carregarNavegador();

		$soID = base64_decode(@$_GET['id']);
		$soID = explode('_', $soID);

		$fase = $soID[2];

		$simu = new Simulado((int) $soID[1]);

		if (!$simu->carregar()) {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'Avalia&ccedil;&atilde;o inv&aacute;lida!');
		} 
		else {
			if(time() < $simu->obterDataRealizacao(false)){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o desta avalia&ccedil;&atilde;o on-line ainda n&atilde;o come&ccedil;ou.');
			}
			elseif(time() > $simu->obterDataRealizacaoFim(false)){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o desta avalia&ccedil;&atilde;o on-line j&aacute; acabou.');
			}
			else{
				$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();
				
				$usuario = new UsuarioInstituido($user_logado_id);
				$usuario->carregar();

				$msg = "";

				$idInsc = $aluno_id = 0;

				$aluno_pre_id = Aluno::obterIDAlunoPeloUsuario($usuario);

				$simu_serie = $simu->obterSerieAvaliacao()->obterID();

				if($aluno_pre_id){
					$aluno_id = $aluno_pre_id;

					$aluno = new Aluno($aluno_pre_id);
					$aluno->carregar();

					$alu_serie = $aluno->obterTurma()->obterSerie()->obterID();

					if($simu_serie == $alu_serie) {
						$idInsc = Inscricao::obterIDInscricaoPeloAluno($simu, $aluno);

						if (!$idInsc) {
							Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'lista'), 'Voc&ecirc; n&atilde;o pode realizar essa avalia&ccedil;&atilde;o.');
						}
					}
					else{
						Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'lista'), 'Voc&ecirc; s&oacute; pode realizar simulados de sua s&eacute;rie.');
					}
				}
				else{
					$msg = "Seu perfil de usu&aacute;rio n&atilde;o permite o registro de respostas. Somente alunos podem registr&aacute;-las, por&eacute;m voc&ecirc; pode interagir com a avalia&ccedil;&atilde;o da mesma forma que eles.";
				}

				$simulado['numq'] = 0;
				$simulado['tempoTotalHide'] = '00:00:00';

				    $simulado['nome'] = $simu->obterNome();
				    $simulado['numq'] = $simu->obterNumeroQuestoes();
				$simulado['questoes'] = $simu->obterQuestoesIDPorFase($fase);
				 $simulado['tinicio'] = $simu->obterTextoInicio();
				    $simulado['tfim'] = $simu->obterTextoFim();
				    $simulado['simu'] = $simu->obterID();
				   $simulado['aluno'] = $aluno_id;
				    $simulado['user'] = $user_logado_id;
				     $simulado['msg'] = $msg;
				     		$tempoArr = array();

				$_SESSION['avaliacao_online']['usuario_id'] 		= $user_logado_id;
				$_SESSION['avaliacao_online']['inscricao_id'] 		= $idInsc;
				$_SESSION['avaliacao_online']['aluno_id'] 			= $aluno_id;
				$_SESSION['avaliacao_online']['simulado_id'] 		= $simu->obterID();
				$_SESSION['avaliacao_online']['simulado_fase'] 		= $fase;

				foreach ($simulado['questoes'] as $sqk => $sqv) {
					$_SESSION['avaliacao_online']['simulado_questoes'][] = $sqk;
				}

				if(isset($idInsc)){
					$inscricao = new Inscricao($idInsc);
					$inscricao->carregar();

					$inscOSF = json_decode($inscricao->obterSimuladoFinalizado(),true);

					if($inscricao->carregar($simu, $aluno)) {		
						$inscricao->limparRespostasMarcadas();
						$inscricao->carregarRespostasMarcadasPorFase($fase);

						if ($simu->obterRealizacaoUnica() == 1 && @$inscOSF[0][$fase] == 1){
							Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_online', 'listar'), 'Voc&ecirc; n&atilde;o pode mais fazer essa Avalia&ccedil;&atilde;o!');
						}

						$simulado['tempoTotalHide'] = $inscricao->obterTempoTotalDeRealizacaoDoAlunoNoSimulado();
						if(!$simulado['tempoTotalHide']){
					     	$simulado['tempoTotalHide'] = '00:00:00';
					    }
					    else{
					     	$ttmp = explode(":", $simulado['tempoTotalHide']);
					    	$simulado['tempoTotalJS'] = $ttmp[2]+($ttmp[1]*60)+($ttmp[0]*60*60);
					    }

						$simulado['finalizadoSim'] = @$inscOSF[0][$fase];
						if($simulado['finalizadoSim']){
							$simulado['finalizadoData'] = $inscricao->obterSimuladoFinalizadoData();
						}

						$simulado['todasRespostas'] = $inscricao->obterRespostasMarcadas();

						$n = 1;
						$rTmp = array();

						foreach($simulado['todasRespostas'] as $k => $v){
							if($n == 1){
								$cacheIDq = $v->obterQuestao()->obterID();
							}

							$v->carregar();

						    $rTmp['tempoJS'] = 1;
						    $rTmp['tempo'] = $v->obterTempo();
						    if(!$rTmp['tempo']){
						    	$rTmp['tempo'] = '00:00:00';
						    }
						    else{
						    	$tttmp = explode(":", $rTmp['tempo']);
						    	$rTmp['tempoJS'] = $tttmp[2]+($tttmp[1]*60)+($tttmp[0]*60*60);
						    }

						    $qID = ($v->obterQuestao()->obterID())-$cacheIDq;
						    $tempoArr[$qID] = $rTmp['tempoJS'];

						    $rTmp['valor'] = $v->obterValor();
						    $tipoQ = $v->obterQuestao()->obterTipo();
						    switch ($tipoQ) {
								case Questao::MULTIPLAESCOLHA:
									$rTmp['valor'] = MultiplaEscolha::letra($v->obterValor());
									break;
								case Questao::DISCURSIVA:
								case Questao::ABERTA:
									$rTmp['valor'] = $v->obterTexto();
									break;
								case Questao::SOMATORIO:
									$rTmp['valor'] = $v->obterValor();
									break;
								default:
									break;
							}

							$simulado['respostas'][$v->obterQuestao()->obterID()] = $rTmp;

							$n++;
						}
					}
				}

				//caso precise detectar mobile pelo php
				//http://mobiledetect.net/

				$this->_iniciarRenderizacao(Modulo::HTML);
					include('simulados_online.realizar.avaliacao.html.php');
				$this->_finalizarRenderizacao();
			}	
		}

		return true;
	}

	public function aResponderQuestaoSimulado()
	{
		//echo'<pre>';print_r(($_POST));echo'</pre><br>---<br>';echo'<pre>';print_r($_SESSION);exit;

		//Valida usuário.
		$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();
		$sessUserId = $_SESSION['avaliacao_online']['usuario_id'];
		if($user_logado_id != $sessUserId){
			exit('0#Houve um erro ao validar a seu usu&#225;rio.');
		}

		//Valida aluno.
		$usuario = new UsuarioInstituido($user_logado_id);
		$usuario->carregar();
		$alunoUId = Aluno::obterIDAlunoPeloUsuario($usuario);
		$sessAlunoId = $_SESSION['avaliacao_online']['aluno_id'];
		if($alunoUId != $sessAlunoId){
			exit('0#Houve um erro ao carregar a sua inscri&ccedil;&atilde;o.');
		}

		$simuID = $_SESSION['avaliacao_online']['simulado_id'];
		$simu = new Simulado($simuID);
		if($simu->carregar()){
			if (time() > $simu->obterDataRealizacaoFim(false)) {
				print('<br>1-'.time().'<br>2-'.$simu->obterDataRealizacaoFim(false).'<br><br>');
				exit('0#O tempo para a realiza&ccedil;&atilde;o deste simulado on-line acabou.');
			}

			$aluno = new Aluno($alunoUId);
			if($aluno->carregar()) {
				$idInscricao = Inscricao::obterIDInscricaoPeloAluno($simu, $aluno);
				$sessInscId = $_SESSION['avaliacao_online']['inscricao_id'];
				if($idInscricao != $sessInscId){
					exit('0#Houve um erro ao identificar a sua inscri&ccedil;&atilde;o.');
				}

				$inscricao = new Inscricao($idInscricao);
				if($inscricao->carregar($simu, $aluno)) {		
					$inscricao->limparRespostasMarcadas();

					if ($simu->obterRealizacaoUnica() == 1 && $inscricao->obterSimuladoFinalizado() == 1){
						exit('0#Essa avalia&ccedil;&atilde;o só pode ser feita uma vez e j&aacute; foi realizada.');
					}

					if(array_key_exists('qs', $_POST)){
						$dadosQuestoes = $_POST['qs'];
						$dadosQuestoes = json_decode($_POST['qs'],true);

						//$simu->obterQuestoesIDPorFase($fase);
						$respostasSalvas = Array();
						foreach ($dadosQuestoes as $dqk => $dqv) {
							$qID = $_SESSION['avaliacao_online']['simulado_questoes'][$dqk];
					
							$questao = new Questao($qID);
							if($questao->carregar()) {
								$qFase = $questao->obterFaseDuracao();
								$sessFase = $_SESSION['avaliacao_online']['simulado_fase'];
								if($qFase != $sessFase){
									exit('0#Houve um erro ao identificar a questão pela fase.');
								}

								$resposta = Resposta::obterRespostaPorQuestaoEInscicao($questao, $inscricao);
								if($resposta == null) {
									$resposta = Resposta::obterNovaResposta($questao, $inscricao, 'SIMULADOS_ONLINE2');
									if(!$resposta->carregar()) {
										exit('0#Houve um erro ao carregar os dados das quest&#245;es.');
									}
								}

								$rqT = $dqv['tempo'];
								if(!empty($rqT)){
									$resposta->fixarTempo($rqT);
								}

								$respostaQ = $dqv['valor'];
								if(!empty($respostaQ)){
									switch ($questao->obterTipo()) {
										case Questao::MULTIPLAESCOLHA:
											if($respostaQ != 'A' && $respostaQ != 'B' && $respostaQ != 'C' && $respostaQ != 'D' && $respostaQ != 'E' && $respostaQ != 'F' && $respostaQ != 'G'){
												exit('0#Houve um erro ao salvar sua resposta.'.$respostaQ);
											}

											$respostaQ = MultiplaEscolha::inteiro($respostaQ);
											if ($respostaQ === false)
												$respostaQ = null;
											$resposta->fixarValor($respostaQ);

											break;
										case Questao::DISCURSIVA:
										case Questao::ABERTA:
											if (empty($respostaQ) || !Filtrador::texto($respostaQ) )
												$respostaQ = null;
											$resposta->fixarTexto($respostaQ);

											break;
										case Questao::SOMATORIO:
											if ( empty($respostaQ) || !Filtrador::natural((int) $respostaQ) )
												$respostaQ = null;
											$resposta->fixarValor((int) $respostaQ);

											break;

										default:
											break;
									}
								}
								else{
									$resposta->fixarValor(null);
									$resposta->fixarTexto(null);
								}

								$respostasSalvas[] = $resposta->salvar();
							}
						}

						$ottdrdans = $inscricao->obterTempoTotalDeRealizacaoDoAlunoNoSimulado();
						if(array_key_exists('tt', $_POST)){
							$ottdrdans = json_decode($ottdrdans,true);
							$ottdrdans[$qFase] = $_POST['tt'];
							$ottdrdans = json_encode($ottdrdans);
							$inscricao->fixarTempoTotalDeRealizacaoDoAlunoNoSimulado($ottdrdans);
						}

						$osf = $inscricao->obterSimuladoFinalizado();
						$osf = json_decode($osf,true);
						$osf[$qFase] = '1';
						$osf = json_encode($osf);
						$inscricao->fixarSimuladoFinalizado($osf);				

						$osfd = $inscricao->obterSimuladoFinalizadoData();
						$osfd = json_decode($osfd,true);
						$osfd[$qFase] = date('d/m/Y H:i:s');
						$osfd = json_encode($osfd);
						$inscricao->fixarSimuladoFinalizadoData($osfd); 

						$salvaFim = $inscricao->salvar();

						if($salvaFim && !array_search(0,$respostasSalvas)){
							exit('2#Sucesso.');
						}
						else{
							exit('0#Falha a salvar alguma questão.');
						}
					}
					else{
						exit('0#Falha ao obter dados.');
					}
				}
				else{
					exit('0#Falha ao encontrar o inscrição do aluno.');
				}
			}
			else{
				exit('0#Falha ao encontrar o aluno.');
			}
		}
		else{
			exit('0#Falha ao encontrar o simulado on-line.');
		}

		exit('0#Falha ao processar.');
	}

	/*protected function saidaPDF ($titulo) {
		error_reporting(0);
		set_time_limit(0);
		ini_set('memory_limit', '2048M');
		ini_set('output_buffering','on');

		$titulo = substr($titulo, 0, 251);
		$titulo = $titulo." - ".date('d.m.y')." - ".rand(15, 225).".pdf";
		$exportador = new ExportadorPDF($titulo);
		$exportador->exportarParaArquivo($this->obterSaida(Modulo::HTML), ExportadorPDF::RETRATO, 'upload/pdfs/'.$titulo);
		
		header('Content-type: application/x-unknown');
		header('Content-Disposition: attachment; filename="'. $titulo .'"');
		header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); 
		header('Last-Modified: ' . gmdate("D, d M Y H:i:s") . ' GMT');
		header('Cache-Control: no-store, no-cache, must-revalidate'); 
		header('Cache-Control: post-check=0, pre-check=0', FALSE); 
		header('Pragma: no-cache');

		readfile('upload/pdfs/'.$titulo);
		unlink('upload/pdfs/'.$titulo);
	}

	public function aGerarPDF ()
	{
		$s_nome = 'Simulado'; 
		$s_fase = '1'; 
		$s_universidade = 'Facul'; 
		$s_instituicao = 'Escolinha';
		$s_turma = '2B';
		$s_prof = 'Elias'; 
		$s_data = '14/07/1990';

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados_online.realizar.avaliacao.pdf.php');
		$this->_finalizarRenderizacao();
		
		Core::incluir('ExportadorPDF', null, true);
		$this->saidaPDF('simulado_online_impresso');
	}*/
}

?>