var mySwiper;
var entregou = false;

function cronoStart(){
	jQuery('.sr_cvalor').prop('disabled', false);
	jQuery('.sr_ctexto').prop('disabled', false);

	jQuery('#btn_prev').show();
	jQuery('#pause-mgs').hide();
	jQuery('.crono_stop').show();
	jQuery('.crono_start').hide();
	jQuery('#crono_time').stopwatch().stopwatch('start');

	var currQ = mySwiper.activeIndex;
	jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('start');

	btnFinalizar();
}

function cronoStop(){
	jQuery('.sr_cvalor').prop('disabled', true);
	jQuery('.sr_ctexto').prop('disabled', true);

	jQuery('#btn_prev').hide();
	jQuery('#btn_next').hide();
	jQuery('#btn_finalizar').hide();
	jQuery('#pause-mgs').show();
	jQuery('.crono_stop').hide();
	jQuery('.crono_start').show();
	jQuery('#crono_time').stopwatch().stopwatch('stop');

	var currQ = mySwiper.activeIndex;
	jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');
	timeQ = jQuery('#crono_questao'+(currQ+1)).html();
	jQuery('#tempo-'+(currQ+1)).val(timeQ);

	var crono_time = jQuery('#crono_time').html();
	jQuery('#tempo-total').val(crono_time);
}

function btnFinalizar(){
	var currQ = mySwiper.activeIndex;
	var countQ = jQuery('.swiper-slide').length;

	if(parseInt(currQ) == parseInt(countQ-1)){
		jQuery('#btn_next').hide();
		jQuery('#btn_finalizar').css('display','inline-block');
	}
	else{
		jQuery('#btn_next').show();
		jQuery('#btn_finalizar').hide();
	}
}

function nextQuest(index = false){
	currQ = mySwiper.activeIndex;

	jQuery('#crono_questao'+(currQ)).hide();
	jQuery('#crono_questao'+(currQ)).stopwatch().stopwatch('stop');
	timeQ = jQuery('#crono_questao'+(currQ)).html();
	jQuery('#tempo-'+(currQ)).val(timeQ);

	jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('start');
	jQuery('#crono_questao'+(currQ+1)).show();

	jQuery('#num_questao').html(currQ+1);

	btnFinalizar();
}

function prevQuest(index = false){
	currQ = mySwiper.activeIndex;

	jQuery('#crono_questao'+(currQ+2)).hide();
	jQuery('#crono_questao'+(currQ+2)).stopwatch().stopwatch('stop');
	timeQ = jQuery('#crono_questao'+(currQ+2)).html();
	jQuery('#tempo-'+(currQ+2)).val(timeQ);

	jQuery('#crono_questao'+((currQ+1))).stopwatch().stopwatch('start');
	jQuery('#crono_questao'+((currQ+1))).show();

	jQuery('#num_questao').html((currQ+1));

	btnFinalizar();
}

jQuery(document).ready(function() { 
	jQuery('#btn_start').click(function() {
		jQuery('#div_btn_start').hide();
		jQuery('#cnt_questoes').show();

		mySwiper = new Swiper('.swiper-container', {
	      pagination: {
	        el: '.swiper-pagination',
	        type: 'progressbar',
	      },
	      on: {
		    slideNextTransitionEnd: function () {
    		  currQ = mySwiper.activeIndex; 
		      nextQuest(currQ);
		    },
		    slidePrevTransitionEnd: function () {
    		  currQ = mySwiper.activeIndex; 
		      prevQuest(currQ);
		    }
		  }
	    });

		jQuery('.crono_start').hide();
		jQuery('.crono_stop').show();
		jQuery('#crono_time').stopwatch().stopwatch('start');
		jQuery('#crono_questao1').show();
		jQuery('#crono_questao1').stopwatch().stopwatch('start');
	});

	jQuery('.crono_start').click(function () {
		cronoStart();
	});

	jQuery('.crono_stop').click(function () {
		cronoStop();
	});

	jQuery('#btn_next').click(function() {
		mySwiper.slideNext();
	});

	jQuery('#btn_prev').click(function() {
		mySwiper.slidePrev();
	});   	

	jQuery('.resposta_questao_valor > textarea').keyup(function(){
		num = jQuery(this).attr('id');
		val = jQuery(this).val();

		if(val != ''){
			window.localStorage.setItem(num, val);
		}
		else{
			window.localStorage.removeItem(num);
		}
	}).each(function(){
		num = jQuery(this).attr('id');
		cache = window.localStorage.getItem(num);

		if(cache != '' && cache != null){
			jQuery(this).val(cache);
		}
	});
	jQuery('.resposta_questao_valor > input').keyup(function(){
		num = jQuery(this).attr('id');
		val = jQuery(this).val();

		if(val != ''){
			window.localStorage.setItem(num, val);
		}
		else{
			window.localStorage.removeItem(num);
		}
	}).each(function(){
		num = jQuery(this).attr('id');
		cache = window.localStorage.getItem(num);

		if(cache != '' && cache != null){
			jQuery(this).val(cache);
		}
	});
	jQuery('.resposta_questao_valor > select').change(function(){
		num = jQuery(this).attr('id');
		val = jQuery(this).find('option:selected').val();

		if(val != 0){
			window.localStorage.setItem(num, val);
		}
		else{
			window.localStorage.removeItem(num);
		}
	}).each(function(){
		num = jQuery(this).attr('id');
		cache = window.localStorage.getItem(num);

		if(cache != '' && cache != null){
			jQuery(this).find('option[value="'+cache+'"]').prop("selected", true).attr('selected','selected');
		}
	});

	jQuery('#btn_finalizar').click(function() {
		var crono_time = jQuery('#crono_time').html();
		jQuery('#tempo_realizacao').html(crono_time);

		jQuery('.resposta_questao_valor > textarea').each(function(){
			num = jQuery(this).attr('id').replace("resposta-", "");
			val = jQuery(this).val();

			if(val != ''){
				jQuery('#lquestao'+num).parent().removeClass('pendente');
				jQuery('#lquestao'+num).parent().addClass('realizada');
				jQuery('#lquestao'+num).html(num+' - Respondida');
			}
			else{
				jQuery('#lquestao'+num).parent().removeClass('realizada');
				jQuery('#lquestao'+num).parent().addClass('pendente');
				jQuery('#lquestao'+num).html(num+' - Pendente');
			}
		});
		jQuery('.resposta_questao_valor > input').each(function(){
			num = jQuery(this).attr('id').replace("resposta-", "");
			val = jQuery(this).val();

			if(val != ''){
				jQuery('#lquestao'+num).parent().removeClass('pendente');
				jQuery('#lquestao'+num).parent().addClass('realizada');
				jQuery('#lquestao'+num).html(num+' - Respondida');
			}
			else{
				jQuery('#lquestao'+num).parent().removeClass('realizada');
				jQuery('#lquestao'+num).parent().addClass('pendente');
				jQuery('#lquestao'+num).html(num+' - Pendente');
			}
		});
		jQuery('.resposta_questao_valor > select').each(function(){
			num = jQuery(this).attr('id').replace("resposta-", "");
			val = jQuery(this).find('option:selected').val();

			if(val != 0){
				jQuery('#lquestao'+num).parent().removeClass('pendente');
				jQuery('#lquestao'+num).parent().addClass('realizada');
				jQuery('#lquestao'+num).html(num+' - Respondida');
			}
			else{
				jQuery('#lquestao'+num).parent().removeClass('realizada');
				jQuery('#lquestao'+num).parent().addClass('pendente');
				jQuery('#lquestao'+num).html(num+' - Pendente');
			}
		});

		cronoStop();
		jQuery('#cnt_questoes').hide();
		jQuery('#div_btn_end').show();
	});

	jQuery('.troca_questao').click(function() {
		jQuery('span[id^="crono_questao"]').hide();

		idV = jQuery(this).attr('id');
		id = idV.replace('lquestao','');

		mySwiper.slideTo(id-1, '150');
		jQuery('#cnt_questoes').show();
		jQuery('#div_btn_end').hide();
		
		jQuery('#crono_questao'+(id)).show();
		jQuery('#num_questao').html(id);

		cronoStart();
	});

	jQuery('#voltar_prova').click(function() {
		mySwiper.slideTo(0, '150');

		jQuery('#cnt_questoes').show();
		jQuery('#div_btn_end').hide();

		jQuery('span[id^="crono_questao"]').hide();
		jQuery('#crono_questao'+(1)).show();
		jQuery('#num_questao').html(1);

		cronoStart();
	});

	window.onbeforeunload = function(){ 
		if(entregou == false){
			jQuery('.alert_box').html('Voc&#234; n&#227;o entregou a avalia&#231;&#227;o! Deseja sair dessa p&#225;gina?').fadeIn('fast');
			return "Voc&#234; n&#227;o entregou a avalia&#231;&#227;o! Deseja sair dessa p&#225;gina?";
		}
	}

	jQuery('#btn_end').click(function(){
		jQuery('#modal_load').show();

		var tempoTotal = jQuery('#tempo-total').val();
		/*if(!tempoTotal){
			jQuery('.alert_box').html('Não foi encontrado o tempo total da avalia&ccedil;&atilde;o on-line.').fadeIn('fast');
			window.scroll(0,0);
			return false;
		}*/

		var rValores = new Array(0);
		jQuery('.questao').each(function(){
			var qId = jQuery(this).attr('id');
			var curr = qId.replace("questao", "");

			var rTempo = jQuery('#tempo-'+curr).val();
			/*if(!rTempo){
				jQuery('.alert_box').html('Não foi encontrado o tempo da questão '+curr+' que está sendo respondida.').fadeIn('fast');
				window.scroll(0,0);
				return false;
			}*/

			var rValor = null;
			var rTagName = document.getElementById('resposta-'+curr).tagName;
			if(rTagName == 'TEXTAREA'){
				var rValor = jQuery('#resposta-'+curr).val();
			}
			else if(rTagName == 'INPUT'){
				var rValor = jQuery('#resposta-'+curr).val();
			}
			else if(rTagName == 'SELECT'){
				var rValor = jQuery('#resposta-'+curr).find('option:selected').val();
			}

			var objQ = new Object();
			objQ.tempo = rTempo;
			objQ.valor = rValor;

			curr = parseInt(curr)-1;
			rValores[curr] = objQ;
		});

		var url = "./?m=simulados_online&a=responder";
		var param = {tt:tempoTotal,qs:JSON.stringify(rValores)};

		jQuery.ajax({
	        type: "POST",
	        async: true,
	        cache: false,
	        url: url,
	        data: param,
			success: function(result,status,xhr){
				if (is.online() && (xhr.readyState == 4 && xhr.status == 200)) {
					if(result.indexOf('Sem perm') >= 0){
						alert('Sua sess&#227;o de usu&#225;rio expirou. Por favor fa&#231;a login novamente.');
					}

					jQuery('.alert_box').removeClass('alert_box_sucesso').addClass('alert_box_erro');
					var emoji = ' <span>&#128557;</span>';

					var split = result.split('#');
					if(split[0] == '2'){
						jQuery('.alert_box').removeClass('alert_box_erro').addClass('alert_box_sucesso');
						entregou = true;
						jQuery('#voltar_listagem').css('display','inline-block');
						emoji = ' <span>&#129321;</span>';
					}

					jQuery('.alert_box').html(split[1]+emoji).fadeIn('fast');
				}
				else{
					jQuery('.alert_box').removeClass('alert_box_sucesso').addClass('alert_box_erro');
					jQuery('.alert_box').html('Por favor, verifique sua internet e tente novamente. <span>&#128561;</span>').fadeIn('fast');
				}					

				window.scroll(0,0);
				return;
			},
			error: function(xhr,status,error){
				jQuery('.alert_box').removeClass('alert_box_sucesso').addClass('alert_box_erro');
				jQuery('.alert_box').html('Por favor, verifique sua internet e tente novamente. <span>&#128561;</span>').fadeIn('fast');
				window.scroll(0,0);
				return;
			},
			complete: function(xhr,status){
				jQuery('#modal_load').hide();
				window.localStorage.clear();
				return;
			}
		});
   	});
});