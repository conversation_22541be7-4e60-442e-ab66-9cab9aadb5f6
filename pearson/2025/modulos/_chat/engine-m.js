jQuery("#chat-label").click(function(){jQuery(this).hide();jQuery("#chat-list").show()});jQuery("#chat-list-close").click(function(){jQuery("#chat-list").hide();jQuery("#chat-label").show()});jQuery("#chat-gump-close").click(function(){jQuery("#chat-gump").hide()});
jQuery("#chat-msg").keypress(function(evt){evt=evt?evt:event;var charCode=evt.keyCode;if(charCode==13){var time=getTimeCurr();var nome=jQuery("#chat-msg-curr-nome").val();var name="<b>"+nome+"</b> ["+time+"]<b>:</b><br>";var txt_start='<p style="font-weight: normal;word-wrap: break-word;">';var txt_end="</p>";var msg=jQuery(this).val();var id=jQuery("#chat-msg-curr").val();if(msg=="")return false;sendChat(id,msg);jQuery("#chat-msg").val("");jQuery("#chat-msg-"+id).append(txt_start+name+msg+txt_end);
var chars=jQuery("#chat-msg-all").html().length;var to=chars*chars;jQuery("#chat-msg-"+id).scrollTo(to,300,{queue:false});return false}});
jQuery("#chat-msg-btn").click(function(){var time=getTimeCurr();var nome=jQuery("#chat-msg-curr-nome").val();var name="<b>"+nome+"</b> ["+time+"]<b>:</b><br>";var txt_start='<p style="font-weight: normal;word-wrap: break-word;">';var txt_end="</p>";var msg=jQuery("#chat-msg").val();var id=jQuery("#chat-msg-curr").val();if(msg=="")return false;sendChat(id,msg);jQuery("#chat-msg").val("");jQuery("#chat-msg-"+id).append(txt_start+name+msg+txt_end);var chars=jQuery("#chat-msg-"+id).html().length;var to=
chars*chars;jQuery("#chat-msg-"+id).scrollTo(to,300,{queue:false});return false});jQuery("#chat-msg").click(function(){currentChat(jQuery("#chat-msg-curr").val())});
function getTimeCurr(){var data=new Date;var dia=data.getDate();var dia_sem=data.getDay();var mes=data.getMonth();var ano2=data.getYear();var ano4=data.getFullYear();var hora=data.getHours();var min=data.getMinutes();var seg=data.getSeconds();var mseg=data.getMilliseconds();var tz=data.getTimezoneOffset();var str_data=dia+"/"+(mes+1)+"/"+ano4;var str_hora=hora+":"+min;return str_data+" "+str_hora}
function currentChat(cid){jQuery(".chat-msg-all").hide();jQuery("#chat-msg-"+cid).show();var nome=jQuery("#nome-"+cid).html();jQuery("#chat-msg-curr-nome-2").val(nome);jQuery("#chat-msg-curr").val(cid);jQuery("#chat-msg").removeAttr("disabled");jQuery("#curr-chats").find("a").find("li").each(function(){if(jQuery(this).css("background-color")!="rgb(255, 165, 0)")jQuery(this).css("background-color","#ddd")});jQuery("#curr-user-"+cid).find("li").css("background-color","#ccc")}
function openChat(cid){if(jQuery("#curr-user-"+cid).length<=0){var close_button='<img onclick="javascript:closeChat('+cid+');" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472136_delete2.png" style="margin-right: 10px; cursor: pointer; height: 12px; float: right; margin-top: 2px;">';var click='<a id="curr-user-'+cid+'" href="javascript:void(0);" onclick="javascript:currentChat('+cid+');">';var item=jQuery("#user-"+cid).html();var item1=item.replace("</li>",close_button+"</li>");jQuery("#curr-chats").append(click+
item1+"</a>");var box='<div class="chat-msg-all" id="chat-msg-'+cid+'" style="overflow-x:hidden;display:none;color: #333333;height: 325px;padding: 5px;overflow-y: auto; "><p style="font-size: 9px;margin: 1px;text-align: center;"><a href="javascript:void(0);" onclick="javascript:backChat('+cid+');" >Clique aqui para carregar mensagens anteriores (+20)</a></p></div>';jQuery("#chat-box-all").prepend(box);jQuery(".chat-msg-all").hide();jQuery("#chat-msg").removeAttr("disabled")}jQuery("#chat-msg-"+cid).show();
jQuery("#chat-gump").show();backChat(cid);currentChat(cid)}function closeChat(cid){jQuery("#chat-msg-"+cid).remove();jQuery("#curr-user-"+cid).remove();jQuery(".chat-msg-all").hide();jQuery("#chat-msg-all").show();if(jQuery(".chat-msg-all").size()<=1){jQuery("#chat-gump").hide();jQuery("#chat-msg").attr("disabled","disabled")}}
function sendChat(id,msg){var token=Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*1E5)+Math.ceil(Math.random()*0xc09ee9a0e1fc100);var param="token="+token+"&id="+id+'&msg="'+msg+'"';jQuery.ajax({type:"POST",async:false,cache:false,url:"./?m=chat&a=send",data:param,success:function(xhr){if(xhr.indexOf("Sem perm")>=0){alert("Sua sess\u00e3o de usu\u00e1rio expirou. Por favor fa\u00e7a login novamente.");expirado=1;return}if(xhr)console.log("enviado chat!");else console.log("erro envio chat!")}})}
function trocaStatus(){var token=Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*1E5)+Math.ceil(Math.random()*0xc09ee9a0e1fc100);var status=jQuery("#status").find(":selected").val();var param="token="+token+"&status="+status;jQuery.ajax({type:"POST",async:false,cache:false,url:"./?m=chat&a=status",data:param,success:function(xhr){if(xhr.indexOf("Sem perm")>=0){alert("Sua sess\u00e3o de usu\u00e1rio expirou. Por favor fa\u00e7a login novamente.");expirado=1;return}if(status=="occ"){jQuery("#chat-curr-img").attr("src",
"estilos/azul/media/offline_2.png");jQuery("#chat-curr-img-label").attr("src","estilos/azul/media/offline_2.png")}else if(status=="off"){jQuery("#chat-curr-img").attr("src","estilos/azul/media/sphere.png");jQuery("#chat-curr-img-label").attr("src","estilos/azul/media/offline_2.png")}else{jQuery("#chat-curr-img").attr("src","estilos/azul/media/online.png");jQuery("#chat-curr-img-label").attr("src","estilos/azul/media/offline_2.png")}if(xhr)console.log("enviado status!");else console.log("erro status!")}})}
function atualizaLista(){var token=Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*1E5)+Math.ceil(Math.random()*0xc09ee9a0e1fc100);var param="token="+token;jQuery.ajax({type:"POST",async:false,cache:false,url:"./?m=chat&a=lista",data:param,success:function(xhr){if(xhr.indexOf("Sem perm")>=0){alert("Sua sess\u00e3o de usu\u00e1rio expirou. Por favor fa\u00e7a login novamente.");expirado=1;return}if(xhr)console.log("enviado lista!");else console.log("erro lista!");var ret=xhr.split("#SPLIT#");
var total=ret[0];var lista=ret[1];jQuery("#chat-curr-total-label").html(total);jQuery("#chat-lista-upd").html(lista)}})}
function backChat(cid){var token=Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*1E5)+Math.ceil(Math.random()*0xc09ee9a0e1fc100);var start=jQuery("#chat-msg-"+cid).find("p").size();var param="token="+token+"&id="+cid+"&start="+start;jQuery.ajax({type:"POST",async:false,cache:false,url:"./?m=chat&a=back",data:param,success:function(xhr){if(xhr.indexOf("Sem perm")>=0){alert("Sua sess\u00e3o de usu\u00e1rio expirou. Por favor fa\u00e7a login novamente.");expirado=1;return}if(xhr)console.log("enviado ultimas conversas!");
else console.log("erro ultimas conversas!");var texto=xhr;jQuery("#chat-msg-"+cid).append(texto);var chars=jQuery("#chat-msg-"+cid).html().length;var to=chars*chars;jQuery("#chat-msg-"+cid).scrollTo(to,300,{queue:false})}})}
function novasMsg(){var token=Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*1E5)+Math.ceil(Math.random()*0xc09ee9a0e1fc100);var param="token="+token;jQuery.ajax({type:"POST",async:false,cache:false,url:"./?m=chat&a=novas",data:param,success:function(xhr){if(xhr.indexOf("Sem perm")>=0){alert("Sua sess\u00e3o de usu\u00e1rio expirou. Por favor fa\u00e7a login novamente.");expirado=1;return}if(xhr)console.log("enviado novas!");else{console.log("erro novas!");return false}var mensagens=xhr.split("#SMG#");
var num_mensagens=mensagens.length;for(i=0;i<num_mensagens;i++){if(mensagens[i]=="")continue;var quebra=mensagens[i].split("#SN#");var texto=quebra[1];var quebra2=quebra[0].split("#SI#");var nome=quebra2[1];var id=quebra2[0];if(jQuery("#curr-user-"+id).length<=0)openChat(id);jQuery("#curr-user-"+id).find("li").css("background-color","orange");jQuery("#user-"+id).find("li").css("background-color","orange");jQuery("#chat-msg-"+id).append(texto);var chars=jQuery("#chat-msg-"+id).html().length;var to=
chars*chars;jQuery("#chat-msg-"+id).scrollTo(to,300,{queue:false});jQuery.gritter.add({title:"Nova Mensagem!",text:texto,image:"estilos/azul/media/1372472140_spechbubble.png",sticky:true,time:"",class_name:"my-sticky-class"});if(jQuery("#chat-msg-curr").val()=="0")jQuery("#chat-msg-curr").val(id)}}})}count=1E3;function timerFired(){if(count==1E3)atualizaLista();if(count%2E3==0)novasMsg();if(count%5E3==0){atualizaLista();count=0}count=count+1E3}
jQuery(document).ready(function(){var refreshHeader=setInterval("timerFired()",1E3)});//MINIFIED: http://www.jsmini.com/