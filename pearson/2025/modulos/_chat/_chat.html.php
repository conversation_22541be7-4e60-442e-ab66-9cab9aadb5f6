<? if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido'); ?>

<div id="chat-label" style="height: 30px; padding:0 10px;cursor: pointer;">
<p style="margin:10px;">
<?php
  $onn = '';
  $occ = '';
  $off = '';
  $status = Core::registro('usuario')->verificaLogadoChat();

  if($status == 'onn'){
    $onn = ' selected="selected"';
    $occ = '';
    $off = '';
    echo '<img id="chat-curr-img-label" style="height: 12px; margin-right: 10px;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png">';
  }
  elseif($status == 'off'){
    $onn = '';
    $occ = '';
    $off = ' selected="selected"';
    echo '<img id="chat-curr-img-label" style="height: 12px; margin-right: 10px;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png">';
  }
  elseif($status == 'occ'){
    $onn = '';
    $occ = ' selected="selected"';
    $off = '';
    echo '<img id="chat-curr-img-label" style="height: 12px; margin-right: 10px;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png">';
  }
  else{
    $onn = '';
    $occ = '';
    $off = ' selected="selected"';
    echo '<img id="chat-curr-img-label" style="height: 12px; margin-right: 10px;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png">';
  }
?>
  <span style="font-size: 12px;">Chat: On-line (<span id='chat-curr-total-label'>0</span>)</span>
</p>
</div>

<div id="chat-list">
<div> 
  <div style="float: left; display: block;">
    <p>
      <?php
        if($status == 'onn'){
          $onn = ' selected="selected"';
          $occ = '';
          $off = '';
          echo '<img id="chat-curr-img" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 15px; margin-right: 10px;">';
        }
        elseif($status == 'off'){
          $onn = '';
          $occ = '';
          $off = ' selected="selected"';
          echo '<img id="chat-curr-img" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 15px; margin-right: 10px;">';
        }
        elseif($status == 'occ'){
          $onn = '';
          $occ = ' selected="selected"';
          $off = '';
          echo '<img id="chat-curr-img" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 15px; margin-right: 10px;">';
        }
        else{
          $onn = '';
          $occ = '';
          $off = ' selected="selected"';
          echo '<img id="chat-curr-img" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 15px; margin-right: 10px;">';
        }
      ?>
      <span style="font-size: 15px;">
        Status: 
        <select onchange="javascript:trocaStatus();" size="1" id="status">
          <option value='onn' <?= $onn; ?>>Disponível</option>
          <option value='occ' <?= $occ; ?>>Ocupado</option>
          <option value='off' <?= $off; ?>>Off-line</option>  
        </select>
      </span>
    </p>
    <p>
      <a href="javascript:void(0);" onclick="javascript:openChatGrupo();" title="">
        <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
plus.png" style="height: 10px; margin-right: 5px;cursor: pointer;">
        <span>Abrir um Chat em Grupo</span>
      </a>
    </p>
  </div>
  <div style="float: right; display: block;">
    <p>
      <img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472136_delete.png" style="height: 15px; margin-right: 10px;cursor: pointer;">
    </p>
  </div>
</div>  
<hr style="background-color: #FFFFFF;
    border: 1px solid #FFFFFF;
    clear: both;
    color: #FFFFFF;
    width: 100%;">
<div style="width: 250px; clear: both; display: block; height: 29px;">
  <a style="text-align: center; float: left; margin: 5px 5px 0px; border-bottom: medium none; border-radius: 5px 5px 0px 0px; background: none repeat scroll 0px 0px rgb(238, 238, 238); height: 15px;" href="javascript:void(0);" class="button" id="chat-btn-user">
    <span style="float: right; overflow: hidden; text-align: center; margin: 0px; font-size: 11px; line-height: 15px;">Usuários</span>
  </a>
  <a style="text-align: center; float: right; margin: 5px 5px 0px; border-bottom: medium none; border-radius: 5px 5px 0px 0px; height: 15px;" href="javascript:void(0);" class="button" id="chat-btn-group">
    <span style="float: right; overflow: hidden; text-align: center; margin: 0px; font-size: 11px; line-height: 15px;">Grupos de Chat</span>
  </a>
</div>

<div style="background: none repeat scroll 0 0 #EEEEEE;color: #555555;float: left;height: 400px;overflow: auto;width: 250px;">
<ul id="chat-lista-upd" style="padding: 0;"></ul>
<ul id="chat-lista-grupo" style="display:none;padding: 5px;"></ul>
</div>
</div>

<div style="left: 35px; width: 700px; display:none;" class="chat-list" id="chat-gump">
<div style="height: auto;">
  <div style="float: left; display: block;">
    <p>
      <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472140_spechbubble_2.png" style="height: 15px; margin-right: 10px;">
      <span style="font-size: 15px;">Chat</span>
    </p>
  </div>  
  <div style="float: right; display: block;">
    <p>
      <img id="chat-gump-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472136_delete.png" style="height: 15px; margin-right: 10px;cursor: pointer;">
    </p>
  </div>  
</div>  
<hr style="background-color: #FFFFFF;
    border: 1px solid #FFFFFF;
    clear: both;
    color: #FFFFFF;
    width: 100%;">
<div style="overflow: auto; background: none repeat scroll 0px 0px rgb(238, 238, 238); color: rgb(85, 85, 85); width: 200px; float: left; height: 400px;">
<input type="hidden" id="chat-msg-curr" value="0"/>
<input type="hidden" id="chat-msg-curr-nome" value="<?php echo Core::registro('usuario')->obterNome(); ?>"/>
<input type="hidden" id="chat-msg-curr-nome-2" value=""/>
<ul id="curr-chats" style="padding: 0;">
  <li style="margin-left: 8px;">Conversas Ativas:</li></ul>
</div>
<div id="chat-box-all" style="float: right; background: none repeat scroll 0% 0% #EEEEEE; width: 490px; height: 400px;box-shadow:3px 3px 6px #bbb inset;">
<div class="chat-msg-all" id="chat-msg-all" style="color: #333333;
    height: 325px;
    padding: 5px;overflow: auto; ">
    <p style="font-size:18px;text-align: center;color:#888;font-weight: normal;border-bottom: 1px solid #CCCCCC;">Não há conversas ativas.</p>
</div>
<div style="background: none repeat scroll 0 0 #FFFFFF;
    height: 60px;
    padding-top: 5px;">
  <textarea id="chat-msg" style="width: 395px;" disabled='disabled'></textarea>
  <a id="chat-msg-btn" class="button" href="javascript:void(0);" style="float: right;
    font-size: 12px;
    margin: 5px 5px 0 0;
    text-align: center;
    width: 35px;height: 40px;"><span style="height: 38px;float: left;
    font-size: 12px;
    overflow: hidden;
    text-align: center;line-height: 20px;
    margin: 0;">Enviar<br>(Enter)</span></a>
</div>
</div>
</div>

<!-- <object id="beep" type="application/x-shockwave-flash" data="modulos/_chat/flashbeep_general.swf" width="1" height="1">
  <param name="movie" value="modulos/_chat/flashbeep_general.swf" />
  <param name="FlashVars" value="onLoad=isReady" />
</object> -->

<script type="text/javascript" src="modulos/_chat/engine.js"></script>