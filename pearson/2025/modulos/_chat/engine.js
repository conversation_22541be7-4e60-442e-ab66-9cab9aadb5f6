/* EVENTOS */

jQuery('#chat-label').click(function() {
	jQuery(this).hide();
	jQuery('#chat-list').show();

	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token;
	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=abrelista',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}
		}
	});
});

jQuery('#chat-btn-group').click(function() {
	jQuery('#chat-btn-user').css('background','');
	jQuery('#chat-btn-group').css('background','none repeat scroll 0 0 #EEEEEE');

	jQuery('#chat-lista-upd').hide();
	jQuery('#chat-lista-grupo').show();
});

jQuery('#chat-btn-user').click(function() {
	jQuery('#chat-btn-user').css('background','none repeat scroll 0 0 #EEEEEE');
	jQuery('#chat-btn-group').css('background','');

	jQuery('#chat-lista-grupo').hide();
	jQuery('#chat-lista-upd').show();
});

jQuery('#chat-list-close').click(function() {
	jQuery('#chat-list').hide();
	jQuery('#chat-label').show();
});

jQuery('#chat-gump-close').click(function() {
	jQuery('#chat-gump').hide();
});

jQuery("#chat-msg").keypress(function(evt) {
	evt = (evt) ? evt : event;
		var charCode = evt.keyCode;

  	if(charCode == 13){
  		if(jQuery('#chat-msg').is(':disabled')){
			return false;
		}
  		
		var time = getTimeCurr();
		var nome = jQuery("#chat-msg-curr-nome").val();
		var name = '<b>'+nome+'</b> ['+time+']<b>:</b><br>';
		var txt_start = '<p style="font-weight: normal;word-wrap: break-word;">';
		var txt_end = '</p>';
		var msg = jQuery(this).val();
		var id = jQuery("#chat-msg-curr").val();

		if(msg == ''){
			return false;
		}

		sendChat(id,msg);

		jQuery("#chat-msg").val('');

		if(id.indexOf('g') >= 0){
			return false;
		}

		jQuery('#chat-msg-'+id).append(txt_start+name+msg+txt_end);
		var chars = jQuery('#chat-msg-all').html().length;
		var to = chars*chars;
		jQuery('#chat-msg-'+id).scrollTo( to, 300, {queue:false} );

		return false;
	}
});

jQuery("#chat-msg-btn").click(function() {
	if(jQuery('#chat-msg').is(':disabled')){
		return false;
	}

	var time = getTimeCurr();
	var nome = jQuery("#chat-msg-curr-nome").val();
	var name = '<b>'+nome+'</b> ['+time+']<b>:</b><br>';
	var txt_start = '<p style="font-weight: normal;word-wrap: break-word;">';
	var txt_end = '</p>';
	var msg = jQuery("#chat-msg").val();
	var id = jQuery("#chat-msg-curr").val();

	if(msg == ''){
		return false;
	}

	sendChat(id,msg);

	jQuery("#chat-msg").val('');

	if(id.indexOf('g') >= 0){
		return false;
	}

	jQuery('#chat-msg-'+id).append(txt_start+name+msg+txt_end);
	var chars = jQuery('#chat-msg-'+id).html().length;
	var to = chars*chars;
	jQuery('#chat-msg-'+id).scrollTo( to, 300, {queue:false} );

	return false;
});

jQuery("#chat-msg").click(function() {
	var pid = jQuery("#chat-msg-curr").val();
	currentChat({idg:pid});
});


/* GLOBAIS */

var gNum = 0;


/* CHAT */

function getTimeCurr(){
	var data = new Date();

	var dia     = data.getDate();           // 1-31
	var dia_sem = data.getDay();            // 0-6 (zero=domingo)
	var mes     = data.getMonth();          // 0-11 (zero=janeiro)
	var ano2    = data.getYear();           // 2 dígitos
	var ano4    = data.getFullYear();       // 4 dígitos
	var hora    = data.getHours();          // 0-23
	var min     = data.getMinutes();        // 0-59
	var seg     = data.getSeconds();        // 0-59
	var mseg    = data.getMilliseconds();   // 0-999
	var tz      = data.getTimezoneOffset(); // em minutos

	var str_data = dia + '/' + (mes+1) + '/' + ano4;
	var str_hora = hora + ':' + min;

	return str_data+' '+str_hora;
}

function currentChat(cid){
	var cid2 = cid.idg;

	if (cid.constructor === String) {
		cid = cid2;
	}
	else{
		cid = cid2.toString();
	}

	if (jQuery("#curr-user-"+cid).length <= 0) {
	    return false;
	}

	jQuery('.chat-msg-all[id!="chat-msg-'+cid+'"]').hide();
	jQuery('#chat-msg-'+cid).show();
	var nome = jQuery('#nome-'+cid).html();
	jQuery("#chat-msg-curr-nome-2").val(nome);
	jQuery("#chat-msg-curr").val(cid);

	if(cid != 0){
		jQuery('#chat-msg').removeAttr('disabled');
		jQuery('#chat-msg').val('');
		jQuery('#chat-msg').focus();
	}
	else{
		jQuery('#chat-msg').attr('disabled','disabled');
		jQuery('#chat-msg').val('Esta � uma mensagem autom�tica do sistema e voc� n�o pode responder.');
	}

	jQuery('#curr-chats').find('a').find('li').each(function(index,element){
	    if(jQuery(this).css("background-color") != 'rgb(255, 165, 0)'){
	        jQuery(this).css("background-color",'#ddd');
	    }
	});

	//jQuery('#curr-user-'+cid).find('li').delay('800').css("background-color",'#ccc');
	//jQuery('#curr-user-'+cid).find('li').delay('1600').css("background",'#cccccc');
	jQuery('#curr-user-'+cid+' li').css("background",'#cccccc');

	var chars = jQuery('#chat-msg-'+cid).html().length;
	var to = chars*chars;
	jQuery('#chat-msg-'+cid).scrollTo( to, 300, {queue:false} );

	if(cid.indexOf('g') >= 0){
		backChat(cid);
		
		jQuery('#chat-btn-user').css('background','');
		jQuery('#chat-btn-group').css('background','none repeat scroll 0 0 #EEEEEE');
		jQuery('#chat-lista-upd').hide();
		jQuery('#chat-lista-grupo').show();

		jQuery('#chat-label').hide();
		jQuery('#chat-list').show();
	}

	jQuery('#chat-gump').show();
}

function openChat(cid){
	cid = cid.toString();
  	if (jQuery("#curr-user-"+cid).length <= 0) {
		var close_button = '<img onclick="javascript:closeChat('+cid+');" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472136_delete2.png" style="margin: 2px 10px;position:fixed;cursor: pointer; height: 12px; float: right;">';

	  	var click = '<a id="curr-user-'+cid+'" href="javascript:void(0);" onclick="javascript:currentChat({idg:\''+cid+'\'});">';

		var item = jQuery('#user-'+cid).html();
		var item1 = item.replace('</li>',close_button+'</li>');

		jQuery('#curr-chats').append(click+item1+'</a>');

		jQuery('#curr-user-'+cid+' li img:first').attr('src','estilos/azul/media/balloon4.png');

		var box = '<div class="chat-msg-all" id="chat-msg-'+cid+'" style="overflow-x:hidden;display:none;color: #333333;height: 325px;padding: 5px;overflow-y: auto; "><p style="font-size: 9px;margin: 1px;text-align: center;"><a href="javascript:void(0);" onclick="javascript:backChat('+cid+');" >Clique aqui para carregar mensagens anteriores (+20)</a></p></div>';

		jQuery('#chat-box-all').prepend(box);
		jQuery('.chat-msg-all[id!="chat-msg-'+cid+'"]').hide();
		jQuery('#chat-msg').removeAttr('disabled');
	}

	jQuery('#chat-msg-'+cid).show();
	jQuery('#chat-gump').show();

	backChat(cid);
	currentChat({idg:cid});

	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token+'&id='+cid;
	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=abrechat',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}
		}
	});
}

function msgHelp(cid,mid){
	cid = cid.toString();
	mid = mid.toString();

	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token+'&id='+cid+'&mid='+mid;
	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=regmatch',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}
		}
	});

	openChat(cid);
}

function closeChat(cid){
	cid = cid.toString();
	
	jQuery('#chat-msg-'+cid).remove();
	jQuery('#curr-user-'+cid).remove();

	if(jQuery(".chat-msg-all").size() <= 1){
		jQuery('.chat-msg-all').hide();
		jQuery('#chat-gump').hide();
		jQuery('#chat-msg').attr('disabled','disabled');
	}
	else{
		var iid = jQuery('#curr-chats').find('a:first').attr('id');
		iid = iid.replace("curr-user-", "");
		currentChat({idg:iid});
	}
}

function sendChat(id,msg){
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token+'&id='+id+'&msg="'+msg+'"';

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=send',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			/*if(xhr){
				console.log('enviado chat!');
			}
			else{
				console.log('erro envio chat!');
			}*/
		}
	});
}

function trocaStatus(){
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var status = jQuery('#status').find(":selected").val();
	var param = 'token='+token+'&status='+status;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=status',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			if(status == 'occ'){
				jQuery('#chat-curr-img').attr('src','estilos/azul/media/offline_2.png');
				jQuery('#chat-curr-img-label').attr('src','estilos/azul/media/offline_2.png');
			}
			else if(status == 'off'){
				jQuery('#chat-curr-img').attr('src','estilos/azul/media/sphere.png');
				jQuery('#chat-curr-img-label').attr('src','estilos/azul/media/sphere.png');
			}
			else{
				jQuery('#chat-curr-img').attr('src','estilos/azul/media/online.png');
				jQuery('#chat-curr-img-label').attr('src','estilos/azul/media/online.png');
			}

			/*if(xhr){
				console.log('enviado status!');
			}
			else{
				console.log('erro status!');
			}*/
		}
	});
}

function atualizaLista(){
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=lista',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			/*if(xhr){
				console.log('enviado lista!');
			}
			else{
				console.log('erro lista!');
			}*/

			var ret = xhr.split('#SPLIT#');
			var total = ret[0];
			var lista = ret[1];

			jQuery('#chat-curr-total-label').html(total);
			jQuery('#chat-lista-upd').html(lista);
		}
	});
}

function backChat(cid){
	cid = cid.toString();
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var start = jQuery('#chat-msg-'+cid).find('p').size();
	var param = 'token='+token+'&id='+cid+'&start='+start;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=back',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			/*if(xhr){
				console.log('enviado ultimas conversas!');
			}
			else{
				console.log('erro ultimas conversas!');
			}*/

			var texto = xhr;

			if(cid.indexOf('g') >= 0){
				jQuery('#chat-msg-'+cid).html(texto);
			}
			else{
				jQuery('#chat-msg-'+cid).find('p:first').after(texto);
			}

			var chars = jQuery('#chat-msg-'+cid).html().length;
			var to = chars*chars;
			jQuery('#chat-msg-'+cid).scrollTo( to, 300, {queue:false} );
		}
	});
}

function novasMsg(){
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=novas',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			if(xhr){
				//console.log('tem novas!');
			}
			else{
				//console.log('nenhuma novas!');
				return false;
			}

			var mensagens = xhr.split('#SMG#');
			var num_mensagens = mensagens.length;
			for(i=0;i<num_mensagens;i++){
				if(mensagens[i] == ''){
					continue;
				}

				var quebra = mensagens[i].split("#SN#");
				var texto = quebra[1];

				var quebra2 = quebra[0].split("#SI#");
				var nome = quebra2[1];
				var id = quebra2[0];

				if (jQuery("#curr-user-"+id).length <= 0) { 
					var cid = id;

					var close_button = '<img onclick="javascript:closeChat('+cid+');" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472136_delete2.png" style="margin: 2px 10px;position:fixed;cursor: pointer; height: 12px; float: right;">';

				  	var click = '<a id="curr-user-'+cid+'" href="javascript:void(0);" onclick="javascript:currentChat({idg:\''+cid+'\'});">';

					var item = jQuery('#user-'+cid).html();
					var item1 = item.replace('</li>',close_button+'</li>');

					jQuery('#curr-chats').append(click+item1+'</a>');

					jQuery('#curr-user-'+cid+' li img:first').attr('src','estilos/azul/media/balloon4.png');

					var box = '<div class="chat-msg-all" id="chat-msg-'+cid+'" style="overflow-x:hidden;display:none;color: #333333;height: 325px;padding: 5px;overflow-y: auto; "><p style="font-size: 9px;margin: 1px;text-align: center;"><a href="javascript:void(0);" onclick="javascript:backChat('+cid+');" >Clique aqui para carregar mensagens anteriores (+20)</a></p></div>';

					jQuery('#chat-box-all').prepend(box);
					jQuery('.chat-msg-all[id!="chat-msg-'+cid+'"]').hide();
					jQuery('#chat-msg').removeAttr('disabled');

					jQuery('#chat-msg-'+cid).show();

					backChat(cid);
				}  

				jQuery('#curr-user-'+id).find('li').css("background-color",'orange');
				jQuery('#user-'+id).find('li').css("background-color",'orange');

				jQuery('#chat-msg-'+id).append(texto);
				var chars = jQuery('#chat-msg-'+id).html().length;
				var to = chars*chars;
				jQuery('#chat-msg-'+id).scrollTo( to, 300, {queue:false} );

				var textoGump = '';
				if(!jQuery('#chat-gump').is(':visible')){ 
					textoGump1 = texto.substring(0, 1200);
					numGump = textoGump1.indexOf('<br>', 1000);
					textoGump2 = textoGump1.substring(0, numGump);

					if(textoGump2 == ''){
						textoGump2 = textoGump1;
					}

				    var clickID = jQuery.gritter.add({
						title: 'Nova Mensagem!',
						text: textoGump2+'<p>...</p>',
						image: 'estilos/azul/media/1372472140_spechbubble.png',
						sticky: true,
						time: '',
						class_name: 'my-sticky-class'
					});

				    jQuery('#gritter-item-'+clickID).css({"cursor": "pointer"});

					jQuery('#gritter-item-'+clickID).append('<input id="gritter-item-chat-'+clickID+'" value="'+id+'" type="hidden" />');

					jQuery('#gritter-item-'+clickID).live('click', function() {
						openChat(jQuery('#gritter-item-chat-'+clickID).val());
						jQuery.gritter.remove(clickID);
					});
				}

				//var beep = document.getElementById("beep");
				//beep.play(10);

				if(jQuery("#chat-msg-curr").val() == '0'){
					jQuery("#chat-msg-curr").val(id);
				}
			}
		}
	});
}


/* GRUPO */

function openChatGrupo(){
	var genID = Math.ceil(Math.random()*456)+Math.ceil(Math.random()*100)+Math.ceil(Math.random()*789);
	var cid = genID+'g';
	gNum = gNum + 1;
	var nomeG = 'Chat em Grupo: '+gNum;

	var close_button = '';//'<img onclick="javascript:closeChat(\''+cid+'\');" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472136_delete2.png" style="margin: 2px 10px;position:fixed;cursor: pointer; height: 12px; float: right;">';
  	var click = '<a id="curr-user-'+cid+'" href="javascript:void(0);" onclick="javascript:currentChat({idg:\''+cid+'\'});">';
	var item = '<li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;"><img style="height: 12px; margin-right: 10px;cursor: pointer;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
group2.png" id="chat-list-close"><span id="nome-'+cid+'">'+nomeG+'</span>';

	jQuery('#curr-chats').append(click+item+close_button+'</li></a>');

	var box = '<div class="chat-msg-all" id="chat-msg-'+cid+'" style="overflow-x:hidden;display:none;color: #333333;height: 325px;padding: 5px;overflow-y: auto; "></div>';

	jQuery('#chat-box-all').prepend(box);
	jQuery('.chat-msg-all[id!="chat-msg-'+cid+'"]').hide();
	jQuery('#chat-msg').removeAttr('disabled');

	jQuery('#chat-msg-'+cid).show();
	jQuery('#chat-gump').show();

	currentChat({idg:cid});

	createRoom(cid,gNum);

	jQuery('#chat-btn-user').css('background','');
	jQuery('#chat-btn-group').css('background','none repeat scroll 0 0 #EEEEEE');
	jQuery('#chat-lista-upd').hide();
	jQuery('#chat-lista-grupo').show();

	jQuery('#chat-label').hide();
	jQuery('#chat-list').show();
}

function createRoom(cid,num){
	cid = cid.toString();
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token+'&id='+cid+'&num='+num;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=room',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			/*if(xhr){
				console.log('enviado ultimas conversas!');
			}
			else{
				console.log('erro ultimas conversas!');
			}*/

			var texto = xhr;

			jQuery('#chat-lista-grupo').append(texto);
		}
	});
}

function sairDoGrupo(cid,sal){
	cid = cid.toString();
	sal = sal.toString();

	jQuery('#chat-msg-'+cid).remove();
	jQuery('#curr-user-'+cid).remove();

	if(jQuery(".chat-msg-all").size() <= 1){
		jQuery('.chat-msg-all').hide();
		jQuery('#chat-gump').hide();
		jQuery('#chat-msg').attr('disabled','disabled');
	}
	else{
		var iid = jQuery('#curr-chats').find('a:first').attr('id');
		iid = iid.replace("curr-user-", "");
		currentChat({idg:iid});
	}

	jQuery('#grul-'+cid).remove();
	jQuery('#curr-user-'+cid).remove();
	gNum = gNum - 1;

	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token+'&sla='+sal;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=grout',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			/*if(xhr){
				console.log('ok saiu grupo!');
			}
			else{
				console.log('erro saiu grupo!');
			}*/
		}
	});
}

function openChatGrupoMsg(sal,cid){
	sal = sal.toString();
	cid = cid.toString();

	var type = jQuery('#cburg-'+cid).is(':checked');
	if(type){
		type = 'invite';
	}
	else{
		type = 'quit';
	}
	
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token+'&id='+cid+'&sla='+sal+'&typ='+type;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=grinvite',
		data: param,
		success: function(xhr){
			if(xhr.indexOf('Sem perm') >= 0){
				window.location='./?m=principal&a=visaogeral';
				return;
			}

			if(xhr){
				var ret = xhr.split('#SPLIT#');
				var sala = ret[0];
				var msg = ret[1];

				jQuery('#chat-msg-'+sala).append(msg);
				var chars = jQuery('#chat-msg-all').html().length;
				var to = chars*chars;
				jQuery('#chat-msg-'+sala).scrollTo( to, 300, {queue:false} );
			}
			else{
				//console.log('erro invite/remove conversas!');
			}
		}
	});
}

function updGroup(){
	var token = Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475);
	var param = 'token='+token;

	jQuery.ajax({
		type: "POST",
		async: true,
		cache: false,
		url: './?m=chat&a=updgroup',
		data: param,
		success: function(xhr){
			//if(xhr.indexOf('Sem perm') >= 0){
				//window.location='./?m=principal&a=visaogeral';
				//return;
			//}

			var ret = jQuery.parseJSON(xhr);//parseXML(xhr);

			for (var i=0; i<ret.nums; i++) {
				var curr_i = ret[i];

				//cria a caixa de conversa quando n�o estiver ativa.
				var cid = curr_i.hash;
				cid = cid.toString();
				var escLista = curr_i.lista;

				if(!cid){
					continue;
				}

  				if (jQuery("#curr-user-"+cid).length <= 0) {
					gNum = gNum + 1;
					var nomeG = 'Chat em Grupo: '+gNum;

					var close_button = '';
				  	var click = '<a id="curr-user-'+cid+'" href="javascript:void(0);" onclick="javascript:currentChat({idg:\''+cid+'\'});">';
					var item = '<li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;"><img style="height: 12px; margin-right: 10px;cursor: pointer;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
group2.png" id="chat-list-close"><span id="nome-'+cid+'">'+nomeG+'</span>';

					jQuery('#curr-chats').append(click+item+close_button+'</li></a>');

					var box = '<div class="chat-msg-all" id="chat-msg-'+cid+'" style="overflow-x:hidden;display:none;color: #333333;height: 325px;padding: 5px;overflow-y: auto; "></div>';

					jQuery('#chat-box-all').prepend(box);
					jQuery('.chat-msg-all[id!="chat-msg-'+cid+'"]').hide();
					jQuery('#chat-msg').removeAttr('disabled');

					jQuery('#chat-btn-user').css('background','');
					jQuery('#chat-btn-group').css('background','none repeat scroll 0 0 #EEEEEE');
					jQuery('#chat-lista-upd').hide();
					jQuery('#chat-lista-grupo').show();

					jQuery('#chat-label').hide();
					jQuery('#chat-list').show();

					currentChat(cid);
				}

				//jQuery('#chat-msg-'+cid).show();
				//jQuery('#chat-gump').show();

				//cria a lista e adiciona na aba de grupos.
				escLista = escLista.replace("#NUM#",gNum);
				if (jQuery("#grul-"+cid).length <= 0) {
					jQuery('#chat-lista-grupo').append('<div id="grul-'+cid+'">'+escLista+'</li></div>');
				}
				else{
					jQuery("#grul-"+cid).html(escLista);
				}

				//pega as mensagens e adiciona.
				var msgs = curr_i.msg;
				for (var i=0; i<msgs.nums; i++) {
					var curr_m = msgs[i];
					var texto = curr_m.msg;				

					jQuery('#chat-msg-'+cid).append(texto);
					var chars = jQuery('#chat-msg-'+cid).html().length;
					var to = chars*chars;
					jQuery('#chat-msg-'+cid).scrollTo( to, 300, {queue:false} );

					check_i = parseInt(i+1);
					check_t = parseInt(msgs.nums);

					var textoGump = '';
					if(check_i == check_t){
						if(!jQuery('#chat-gump').is(':visible')){ 
							textoGump1 = texto.substring(0, 1200);
							numGump = textoGump1.indexOf('<br>', 1000);
							textoGump2 = textoGump1.substring(0, numGump);

						    var clickID = jQuery.gritter.add({
								title: 'Mensagem no Grupo:'+gNum,
								text: textoGump2+'<p>...</p>',
								image: 'estilos/azul/media/1372472140_spechbubble.png',
								sticky: true,
								time: '',
								class_name: 'my-sticky-class'
							});

						    jQuery('#gritter-item-'+clickID).css({"cursor": "pointer"});
							jQuery('#gritter-item-'+clickID).append('<input id="gritter-item-chat-'+clickID+'" value="'+cid+'" type="hidden" />');
							jQuery('#gritter-item-'+clickID).live('click', function() {
								openChat(jQuery('#gritter-item-chat-'+clickID).val());
								jQuery.gritter.remove(clickID);
							});
						}
					}
				}

				if(jQuery("#chat-msg-curr").val() == '0'){
					jQuery("#chat-msg-curr").val(cid);
				}
			}
		}
	});
}


/* TIMER */
groupStart = 1;
count = 2000; 
function timerFired () {
	if(count == 2000){
	    if(groupStart){
	        updGroup();	
	    }
	    novasMsg();
  	}

  	if(count == 4000){
	    novasMsg();
  	    atualizaLista();
        count = 0;
  	}

	count = count + 2000;
}

jQuery(document).ready(function() {
	atualizaLista();
	novasMsg();

	var refreshHeader = setInterval('timerFired()',10000);

	jQuery(window).unload(function(){
		clearInterval(refreshHeader);
	});

	window.onbeforeunload = function(){ 
		clearInterval(refreshHeader);
	}
});

//MINIFIED: http://www.jsmini.com/