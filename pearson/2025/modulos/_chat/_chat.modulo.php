<?php
ini_set('display_errors', 1);
error_reporting(0);

if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('UsuarioInstituido', null, true);
Core::incluir('Simulado', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);

class MChat extends Modulo
{
	public function carregarChat ()
	{
		if ( !Core::registro('usuario')->estaLogado() ){
			return false;
		}

		if(Core::diretiva('CHAT:Habilitado') != 1){
			return false;
		}

		//Bloqueia IE7
		preg_match('/MSIE (.*?);/', $_SERVER['HTTP_USER_AGENT'], $matches);
		if (count($matches)>1){
		  $version = $matches[1];
		  if($version<=7){
		  	$this->_iniciarRenderizacao(Modulo::HTML);
				include('_chatIE7.html.php');
			$this->_finalizarRenderizacao();
			return true;
		  }
		}

		Core::modulo('js')->incluirArquivo('includes/JavaScript/scrollTo/jquery.scrollTo-*******-min.js');
		Core::modulo('index')->anexarAoHeader('includes/JavaScript/Gritter-master/css/jquery.gritter.css', MIndex::ANEXO_CSS);
		Core::modulo('js')->incluirArquivo('includes/JavaScript/Gritter-master/js/jquery.gritter.min.js');

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('_chat.html.php');
		$this->_finalizarRenderizacao();

		if(Core::diretiva('CHAT:MATCH:Habilitado') == 1){
			$this->_matchChat();
		}

		return true;
	}

	public function carregarChatLista ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$g = Core::registro('usuario')->obterGrupos();

		if($g[0] == 1){
			$grupo = 'adm';
		}
		elseif($g[0] == 2){
			$grupo = 'gest';
		}
		elseif($g[0] == 3){
			$grupo = 'profs';
		}
		elseif($g[0] == 4){
			$grupo = 'alun';
		}
		elseif($g[0] == 5){
			$grupo = 'sec';
		}
		elseif($g[0] == 6){
			$grupo = 'reg';
		}
		else{
			$grupo = '';
		}

		$grupos = array();

		$grupos['admXadm'] = '';
		$grupos['admXsec'] = '';
		$grupos['admXreg'] = '';
		$grupos['admXgest'] = '';
		$grupos['admXprofs'] = '';
		$grupos['admXalun'] = '';
		$grupos['secXadm'] = '';
		$grupos['secXsec'] = '';
		$grupos['secXreg'] = '';
		$grupos['secXgest'] = '';
		$grupos['secXprofs'] = '';
		$grupos['secXalun'] = '';
		$grupos['regXadm'] = '';
		$grupos['regXsec'] = '';
		$grupos['regXreg'] = '';
		$grupos['regXgest'] = '';
		$grupos['regXprofs'] = '';
		$grupos['regXalun'] = '';
		$grupos['gestXadm'] = '';
		$grupos['gestXsec'] = '';
		$grupos['gestXreg'] = '';
		$grupos['gestXgest'] = '';
		$grupos['gestXprofs'] = '';
		$grupos['gestXalun'] = '';
		$grupos['profsXadm'] = '';
		$grupos['profsXsec'] = '';
		$grupos['profsXreg'] = '';
		$grupos['profsXgest'] = '';
		$grupos['profsXprofs'] = '';
		$grupos['profsXalun'] = '';
		$grupos['alunXadm'] = '';
		$grupos['alunXsec'] = '';
		$grupos['alunXreg'] = '';
		$grupos['alunXgest'] = '';
		$grupos['alunXprofs'] = '';
		$grupos['alunXalun'] = '';

		$rs0 = Core::registro('db')->query('SELECT c_permissao FROM chat_permissoes WHERE c_permissao LIKE "'.$grupo.'X%"');
		if ($rs0->num_rows) {
			while ($row0 = $rs0->fetch_assoc()) {
				$grupos[$row0['c_permissao']] = '1';
			}
		}
		$rs0->free();

		$rs = Core::registro('db')->query('SELECT u_id, u_grupos, u_email FROM usuarios WHERE u_email IS NOT NULL');
		
		$lista[1] = array();
		$lista[2] = array();
		$lista[3] = array();
		$lista[4] = array();
		$lista[5] = array();
		$lista[6] = array();

		if ($rs->num_rows) {
			$counter = 0;
			$onlines = 0;
			while ($row = $rs->fetch_assoc())
			{
				$usuario = new UsuarioInstituido($row['u_id']);
				$usuario->carregar();

				if($row['u_id'] == Core::registro('usuario')->obterID()){
					continue;
				}

				$lista[$row['u_grupos']][$counter]['id'] = $usuario->obterID();
				$lista[$row['u_grupos']][$counter]['nome'] = utf8_encode($usuario->obterNome());
				$lista[$row['u_grupos']][$counter]['email'] = $usuario->obterEmail()->obterEndereco();
				$lista[$row['u_grupos']][$counter]['logado'] = $usuario->estaLogado();
				$status = $usuario->verificaLogadoChat();
				$lista[$row['u_grupos']][$counter]['status'] = $status;

				if($status == 'onn'){
			    	$onlines++;
			    }
			    elseif($status == 'occ'){
			    	$onlines++;
			    }

				$counter++;
			}

			//$onlines--;
		}
		$rs->free();

		$retorno = "";

		$retorno .= '<a id="user-0" href="javascript:void(0);" onclick="javascript:openChat(0);" title="Avaliativa" style="display:none;">';
		$retorno .= '<li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';
		$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
		$retorno .= '<span id="nome-0">Avaliativa</span></li></a>';

		if($grupos[$grupo.'Xadm'] == '1'){
			$retorno .= '<li style="margin-left: 8px; color: #053963; font-size: 15px;">Administradores:</li>';
			if(count($lista[1])>0){
			  	foreach($lista[1] as $admk => $admv){
			  		$retorno .= '<a id="user-'.$admv['id'].'" href="javascript:void(0);" onclick="javascript:openChat('.$admv['id'].');" title="'.$admv['email'].'"><li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';
				    
				    if($admv['status'] == 'onn'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($admv['status'] == 'off'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($admv['status'] == 'occ'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    else{
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }

			    	$retorno .= '<span id="nome-'.$admv['id'].'">'.$admv['nome'].'</span>';
			  		$retorno .= '</li></a>';
			    }
			}
		    else{
		   		$retorno .= '<li style="font-weight: normal;margin-left: 20px; margin-bottom: 20px;">Nenhum usuário on-line.</li>';
		    }
		}

		if($grupos[$grupo.'Xsec'] == '1'){
			$retorno .= '<li style="margin-left: 8px; color: #053963; font-size: 15px;">Secretaria de Educação:</li>';
			if(count($lista[5])>0){
			  	foreach($lista[5] as $sek => $sev){
			  		$retorno .= '<a id="user-'.$sev['id'].'" href="javascript:void(0);" onclick="javascript:openChat('.$sev['id'].');" title="'.$sev['email'].'"><li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';
				    
				    if($sev['status'] == 'onn'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($sev['status'] == 'off'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($sev['status'] == 'occ'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    else{
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }

			    	$retorno .= '<span id="nome-'.$sev['id'].'">'.$sev['nome'].'</span>';
			  		$retorno .= '</li></a>';
			    }
			}
		    else{
		   		$retorno .= '<li style="font-weight: normal;margin-left: 20px; margin-bottom: 20px;">Nenhum usuário on-line.</li>';
		    }
		}

		if($grupos[$grupo.'Xreg'] == '1'){
		    $retorno .= '<li style="margin-left: 8px; color: #053963; font-size: 15px;">Regionais:</li>';
			if(count($lista[6])>0){
			  	foreach($lista[6] as $rek => $rev){
			  		$retorno .= '<a id="user-'.$rev['id'].'" href="javascript:void(0);" onclick="javascript:openChat('.$rev['id'].');" title="'.$rev['email'].'"><li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';
				    
				    if($rev['status'] == 'onn'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($rev['status'] == 'off'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($rev['status'] == 'occ'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    else{
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }

			    	$retorno .= '<span id="nome-'.$rev['id'].'">'.$rev['nome'].'</span>';
			  		$retorno .= '</li></a>';
			    }
			}
		    else{
		   		$retorno .= '<li style="font-weight: normal;margin-left: 20px; margin-bottom: 20px;">Nenhum usuário on-line.</li>';
		    }
		}
	  
	  	if($grupos[$grupo.'Xgest'] == '1'){
		  	$retorno .= '<li style="margin-left: 8px; color: #053963; font-size: 15px;">Gestores das Escolas:</li>';
		    if(count($lista[2])>0){
		    	foreach($lista[2] as $gek => $gev){
			 		$retorno .= '<a id="user-'.$gev['id'].'" href="javascript:void(0);" onclick="javascript:openChat('.$gev['id'].');" title="'.$gev['email'].'"><li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';
			  
				    if($gev['status'] == 'onn'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($gev['status'] == 'off'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($gev['status'] == 'occ'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    else{
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }

		    		$retorno .= '<span id="nome-'.$gev['id'].'">'.$gev['nome'].'</span>';
		 			$retorno .= '</li></a>';
		    	}
		    }
		    else{
		    	$retorno .= '<li style="font-weight: normal;margin-left: 20px; margin-bottom: 20px;">Nenhum usuário on-line.</li>';
		    }
		}
	  
	  	if($grupos[$grupo.'Xprofs'] == '1'){
			$retorno .= '<li style="margin-left: 8px; color: #053963; font-size: 15px;">Professores:</li>';
			if(count($lista[3])>0){
				foreach($lista[3] as $pek => $pev){
					$retorno .= '<a id="user-'.$pev['id'].'" href="javascript:void(0);" onclick="javascript:openChat('.$pev['id'].');" title="'.$pev['email'].'"><li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';

					if($pev['status'] == 'onn'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($pev['status'] == 'off'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($pev['status'] == 'occ'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    else{
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }

					$retorno .= '<span id="nome-'.$pev['id'].'">'.$pev['nome'].'</span>';
					$retorno .= '</li></a>';
				}
			}
			else{
				$retorno .= '<li style="font-weight: normal;margin-left: 20px; margin-bottom: 20px;">Nenhum usuário on-line.</li>';
			}
		}

		if($grupos[$grupo.'Xalun'] == '1'){
			$retorno .= '<li style="margin-left: 8px; color: #053963; font-size: 15px;">Alunos:</li>';
			if(count($lista[4])>0){
				foreach($lista[4] as $aek => $aev){
					$retorno .= '<a id="user-'.$aev['id'].'" href="javascript:void(0);" onclick="javascript:openChat('.$aev['id'].');" title="'.$aev['email'].'"><li style="background: #DDDDDD;border-radius: 2px 2px 2px 2px;box-shadow: 1px 1px 3px #999999;list-style: none outside none;margin: 10px;padding: 5px;">';

					if($aev['status'] == 'onn'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
online.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($aev['status'] == 'off'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    elseif($aev['status'] == 'occ'){
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
offline_2.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }
				    else{
				    	$retorno .= '<img id="chat-list-close" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sphere.png" style="height: 12px; margin-right: 10px;cursor: pointer;">';
				    }

					$retorno .= '<span id="nome-'.$aev['id'].'">'.$aev['nome'].'</span>';
					$retorno .= '</li></a>';
				}
			}
			else{
				$retorno .= '<li style="font-weight: normal;margin-left: 20px; margin-bottom: 20px;">Nenhum usuário on-line.</li>';
			}
		}

		$onlines = str_replace('#SPLIT#', '', $onlines);
		$retorno = str_replace('#SPLIT#', '', $retorno);
		exit($onlines.'#SPLIT#'.$retorno);
	}

	public function sendChat ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$cid = Core::registro('usuario')->obterID();
		$nome = utf8_encode(Core::registro('usuario')->obterNome());
		$para = $_POST['id'];
		$msg = strip_tags($_POST['msg']);
		$msg = str_replace('\"', '"', $msg);
		$msg = str_replace('"', '', $msg);
		$time = date('d/m/Y H:i:s');

		if (!$cid || !$para /*|| !$time*/ || empty($msg)){
			exit('0');
		}

		$msg = utf8_decode('<b>'.$nome.'</b> ['.$time.']<b>:</b><br><p style=font-weight:normal;>'.$msg.'</p>');

		$hg = strpos($para, 'g');
		if ($hg === false) {
			Core::registro('db')->query('INSERT INTO chat_mensagens (c_uid_e, c_uid_r, c_msg) VALUES ('.$cid.','.$para.',"'.$msg.'")');
		}
		else{
			$rs0 = Core::registro('db')->query('SELECT * FROM chat_group_room WHERE hash = "'.$para.'"');
			if ($rs0->num_rows) {
				while ($row0 = $rs0->fetch_assoc()) {
					$gid = $row0['id'];
				}
			}
			$rs0->free();

			$visualizaram = ';'.$cid.';,';

			Core::registro('db')->query('INSERT INTO chat_group_msgs (sala, usuario, msg, visualizaram) VALUES ("'.$gid.'",'.$cid.',"'.$msg.'", "'.$visualizaram.'")');	
		}

		//$g = Core::registro('usuario')->obterGrupos();
		//if($g[0] != 1){	
			//$usuario_recebedor_nome = UsuarioInstituido::obterNomeUsuarioPeloID($para);
			//$log = 'Usuario '.$nome.' ('.$cid.') enviou mensagem para usuario '.$usuario_recebedor_nome.' ('.$para.'). ';
			//Core::registro('db')->query('INSERT INTO chat_logs (usuario, log) VALUES ('.$cid.',"'.$log.'")');
		//}

		exit('1');
	}

	public function trocaStatus ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$campo1 = '';
		$valor1 = '';
		$campo2 = '';
		$valor2 = '';
		$status = $_POST['status'];
		if($status == 'occ'){
			$campo1 = 'u_ocupado';
			$valor1 = 'S';
			$campo2 = 'u_offline';
			$valor2 = 'N';
		}
		elseif($status == 'off'){
			$campo1 = 'u_offline';
			$valor1 = 'S';
			$campo2 = 'u_ocupado';
			$valor2 = 'N';
		}

		if($status != 'occ' && $status != 'off'){
			$campo1 = 'u_offline';
			$valor1 = 'N';
			$campo2 = 'u_ocupado';
			$valor2 = 'N';
		}

		$cid = Core::registro('usuario')->obterID();
		Core::registro('db')->query('UPDATE chat_usuarios SET '.$campo1.' = "'.$valor1.'", '.$campo2.' = "'.$valor2.'" WHERE u_id = '.$cid);
			
		$retorno = Core::registro('db')->errno == 0;

		exit('1');
	}

	public function carregaAnteriores ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$msg = '';
		$start = $_POST['start'];
		$cid = $_POST['id'];
		
		$hg = strpos($cid, 'g');
		if ($hg === false) {
			$rs = Core::registro('db')->query('SELECT * FROM chat_mensagens WHERE ((c_uid_r = '.$cid.' AND c_uid_e = '.Core::registro('usuario')->obterID().') OR (c_uid_r = '.Core::registro('usuario')->obterID().' AND c_uid_e = '.$cid.')) AND c_lido = "1" ORDER BY c_id DESC LIMIT '.$start.',20');
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){
					$id = $row['c_id'];
					$ide = $row['c_uid_e'];
					$idr = $row['c_uid_r'];
					$msgs = $row['c_msg'];
					$time = $row['c_time'];

					$msg = $msgs.$msg;
				}
			}
			$rs->free();
		}
		else{
			$sid = 0;
			$rs0 = Core::registro('db')->query('SELECT * FROM chat_group_room WHERE hash = "'.$cid.'"');
			if ($rs0->num_rows) {
				while ($row0 = $rs0->fetch_assoc()) {
					$sid = $row0['id'];
				}
			}
			$rs0->free();

			$rs = Core::registro('db')->query('SELECT * FROM chat_group_msgs WHERE sala = '.$sid.' ORDER BY id DESC');
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){
					$id = $row['id'];
					$ide = $row['sala'];
					$idr = $row['usuario'];
					$msgs = $row['msg'];
					$time = $row['tempo'];
					$view = $row['visualizaram'];

					$msg = $msgs.$msg;
				}
			}
			$rs->free();
		}

		$msg = utf8_encode($msg);

		exit($msg);
	}

	public function novasMsgs ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$msg = "";

		$rs = Core::registro('db')->query('SELECT * FROM chat_mensagens WHERE c_uid_r = "'.Core::registro('usuario')->obterID().'" AND c_lido = "0"');
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				$id = $row['c_id'];
				$ide = $row['c_uid_e'];
				$idr = $row['c_uid_r'];
				$msgs = $row['c_msg'];
				//$msgs = htmlentities($msgs, ENT_QUOTES, "UTF-8",true);
				$time = $row['c_time'];

				Core::registro('db')->query('UPDATE chat_mensagens SET c_lido = "1" WHERE c_id = '.$id);
				//$retorno = Core::registro('db')->errno == 0;

				$nome = 'Avaliativa';
				$rs2 = Core::registro('db')->query('SELECT u_nome FROM usuarios WHERE u_id = '.$ide.' LIMIT 1');
				if ($rs2->num_rows) {
					$row2 = $rs2->fetch_assoc();
					$nome = utf8_encode($row2['u_nome']);
				}
				$rs2->free();

				$ide  = str_replace('#SI#', '', $ide);
				$nome = str_replace('#SI#', '', $nome);
				$msgs = str_replace('#SI#', '', $msgs);

				$ide  = str_replace('#SN#', '', $ide);
				$nome = str_replace('#SN#', '', $nome);
				$msgs = str_replace('#SN#', '', $msgs);

				$ide  = str_replace('#SMG#', '', $ide);
				$nome = str_replace('#SMG#', '', $nome);
				$msgs = str_replace('#SMG#', '', $msgs);

				$msg .= $ide.'#SI#'.$nome.'#SN#'.$msgs.'#SMG#';
			}
		}
		$rs->free();

		$msg = utf8_encode($msg);

		exit($msg);
	}

	public function abreLista ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$cid = Core::registro('usuario')->obterID();
		$nome = Core::registro('usuario')->obterNome();
		
		$g = Core::registro('usuario')->obterGrupos();
		if($g[0] != 1){	
			$log = 'Usuario '.$nome.' ('.$cid.') abriu a label do chat. ';
			Core::registro('db')->query('INSERT INTO chat_logs (usuario, log) VALUES ('.$cid.',"'.$log.'")');
		}

		exit('1');
	}

	public function abreChat ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$cid = Core::registro('usuario')->obterID();
		$nome = Core::registro('usuario')->obterNome();
		$para = $_POST['id'];

		$g = Core::registro('usuario')->obterGrupos();
		if($g[0] != 1){	
			$usuario_recebedor_nome = UsuarioInstituido::obterNomeUsuarioPeloID($para);
			$log = 'Usuario '.$nome.' ('.$cid.') abriu uma conversa com o usuario '.$usuario_recebedor_nome.' ('.$para.'). ';
			Core::registro('db')->query('INSERT INTO chat_logs (usuario, log) VALUES ('.$cid.',"'.$log.'")');
		}

		exit('1');
	}

	public function _matchChat(){
		if ( !Core::registro('usuario')->estaLogado() ){
			return false;
		}

		$recebe = false;
		$ajuda = array();
		$time = date('d/m/Y H:i:s');
		$g = Core::registro('usuario')->obterGrupos();
		$idUsuario = Core::registro('usuario')->obterID();
		$usuario = new UsuarioInstituido($idUsuario);
		$usuario->carregar();

		$ss = Simulado::obterSimuladosParaFormulario();
		$rendimentos = $this->carregaRendimentos();
		$matchs = $this->carregaMatchs();

		$matchid = 0;
		foreach ($matchs as $mk => $mv) {
			$matchid = $mk;

			$rsCMS = Core::registro('db')->query('SELECT * FROM chat_match_stats WHERE id = '.$mk.' AND usuario = '.$idUsuario);
			if ($rsCMS->num_rows) {
				return false;
			}

			if($g[0] == 6 && $mv['tipo'] == 'regionais' && $mv['atividade'] == '1'){
				return false;
			}

			if($g[0] == 4 && $mv['tipo'] == 'alunos' && $mv['atividade'] == '1'){
				return false;
			}

			if($g[0] == 3 && $mv['tipo'] == 'professores' && $mv['atividade'] == '1'){
				return false;
			}

			if($g[0] == 2 && $mv['tipo'] == 'escolas' && $mv['atividade'] == '1'){
				$escola = $usuario->obterInstituicao()->obterID();
				if(!$escola){
					return false;
				}

				//verifica se eh todas avaliações.
				if($mv['avaliacao']){
					if(isset($rendimentos[$mv['avaliacao']]->rendimentoPorInstituicao[$escola])){
						$valor = round($rendimentos[$mv['avaliacao']]->rendimentoPorInstituicao[$escola]['total_pontos']);
					}

					if($mv['nivel'] == 0){
						if($valor >= $mv['de'] && $valor <= $mv['para']){
							if($mv['tipo2'] == 'escolas'){
								if($mv['avaliacao2']){
									if(isset($rendimentos[$mv['avaliacao2']]->rendimentoPorInstituicao)){
										$valor2 = $rendimentos[$mv['avaliacao2']]->rendimentoPorInstituicao;
									}

									foreach ($valor2 as $key2 => $value2) {
										if(!$key2){
											continue;
										}

										$valor22[$key2] = round($value2['total_pontos']);
										if($mv['nivel2'] == 0){
											return false;
										}
										else{					
											$simulados22 = new Simulado($mv['avaliacao2']);
											$simulados22->carregar();

											$id22 = $simulados22->obterID();
											$teste22 = $simulados22->obterEdicaoPB();
											$tipo22 = $simulados22->obterDisciplinaPB();
											$ano22 = $simulados22->obterAnoPB();

											$nivelSimulado22 = Core::diretiva('NIVEL_'.($mv['nivel2']-1).'_'.$teste22.'_'.$tipo22.'_'.$ano22.'_'.$id22);

											if($valor22[$key2] >= $nivelSimulado22){
												$rs = Core::registro('db')->query('SELECT * FROM usuarios WHERE u_grupos = '.$g[0].' AND u_instituicao = '.$key2);
												if ($rs->num_rows) {
													while ($row = $rs->fetch_assoc()) {
														if($row['u_id'] != $idUsuario){
															$ajuda[$row['u_id']] = $row['u_nome'];
														}
													}
												}
												$rs->free();

												$recebe = $idUsuario;
											}
										}
									}
								}
							}
						}
					}
				}
			}

			$msg = '';
			if($recebe && count($ajuda)>0){
				Core::registro('db')->query('INSERT INTO chat_match_stats (matchid, usuario, mensagem, clicou, indicacoes_clicadas) 
					VALUES ('.$matchid.','.$recebe.',"0","0","")');
				$matchID = Core::registro('db')->insert_id;

				$msg = $mv['msg'];//utf8_encode($mv['msg']);
				foreach($ajuda as $ak => $av){
					$id = $ak; $nome = $av;
					$msg .= '<br><a href="javascript:void(0);" onclick="javascript:msgHelp('.$id.','.$matchID.');">'.$nome.'</a>';
				}

				$ajudas = implode(',', array_reverse($ajuda));

				$msg = '<b>Avaliativa</b> ['.$time.']<b>:</b><br><p style=font-weight:normal;>'.$msg.'</p>';
				$msg = str_replace("'", "", $msg);
				$msg = str_replace('"', "", $msg);

				Core::registro('db')->query('INSERT INTO chat_mensagens (c_uid_e, c_uid_r, c_msg, c_lido) 
					VALUES (0,'.$recebe.',"'.$msg.'", "0")');
				$msgID = Core::registro('db')->insert_id;

				Core::registro('db')->query('UPDATE chat_match_stats
					SET mensagem = "'.$msgID.'" WHERE id = "'.$matchID.'"');
			}
		}

		return true;
	}

	public function registraMatch()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$uid = Core::registro('usuario')->obterID();
		$cid = $_POST['id'];
		$mid = $_POST['mid'];

		$indicacoes = '';
		$rs0 = Core::registro('db')->query('SELECT indicacoes_clicadas FROM chat_match_stats WHERE id = '.$mid);
		if ($rs0->num_rows) {
			while ($row0 = $rs0->fetch_assoc()) {
				$indicacoes = $row0['indicacoes_clicadas'];
			}
		}
		$rs0->free();
		
		$indicacoes = str_replace($cid, '', $indicacoes);
		$indicacoes = $indicacoes.','.$cid;

		Core::registro('db')->query('UPDATE chat_match_stats
					SET clicou = "1", indicacoes_clicadas = "'.$indicacoes.'" WHERE id = "'.$mid.'"');

		exit('1');
	}

	public function carregaRendimentos(){
		$rendimento = array();
		Core::carregarModulo( array('nome' => '_perfil_falso', 'classe' => 'MPerfilFalso', 'guardar_como' => '_perfil_falso') );
		Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::SECRETARIO);
		$ss = Simulado::obterSimuladosParaFormulario();
		if (count($ss)>0) {
			foreach ( $ss as $id => $snome){
				$simulado = new Simulado($id);
     			$simulado->carregar();

				$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();
				$this->_analizadorSimulado->fixarSimulado($simulado);
				$this->_analizadorSimulado->carregarInscritos(true);
				$this->_analizadorSimulado->carregarRespostasDosInscritos();
				$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
				$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
				$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
				$this->_analizadorSimulado->calcularRendimentoPorTurmaPorQuestao();
				$this->_analizadorSimulado->calcularRendimentoPorInstituicaoPorQuestao();
				$this->_analizadorSimulado->calcularRendimentoPorTurma();
				$this->_analizadorSimulado->calcularRendimentoPorInstituicao();
				$this->_analizadorSimulado->calcularRendimentoPorSerie();

				$rendimento[$id] = $this->_analizadorSimulado;
			}
		}

		return $rendimento;
	}

	public function carregaMatchs(){
		$resultados = array();
		$rs = Core::registro('db')->query('SELECT * FROM chat_match');
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$resultados[$row['id']]['tipo'] = $row['tipo'];
				$resultados[$row['id']]['de'] = $row['de'];
				$resultados[$row['id']]['para'] = $row['para'];
				$resultados[$row['id']]['nivel'] = $row['nivel'];
				$resultados[$row['id']]['avaliacao'] = $row['avaliacao'];
				$resultados[$row['id']]['msg'] = $row['msg'];
				$resultados[$row['id']]['tipo2'] = $row['tipo2'];
				$resultados[$row['id']]['de2'] = $row['de2'];	
				$resultados[$row['id']]['para2'] = $row['para2'];
				$resultados[$row['id']]['nivel2'] = $row['nivel2'];
				$resultados[$row['id']]['avaliacao2'] = $row['avaliacao2'];
				$resultados[$row['id']]['atividade'] = $row['atividade'];
			}
		}

		return $resultados;
	}

/* GRUPO */

	public function carregarChatListaGrupo ($sala = 0)
	{
		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$g = Core::registro('usuario')->obterGrupos();

		if($g[0] == 1){
			$grupo = 'adm';
		}
		elseif($g[0] == 2){
			$grupo = 'gest';
		}
		elseif($g[0] == 3){
			$grupo = 'profs';
		}
		elseif($g[0] == 4){
			$grupo = 'alun';
		}
		elseif($g[0] == 5){
			$grupo = 'sec';
		}
		elseif($g[0] == 6){
			$grupo = 'reg';
		}
		else{
			$grupo = '';
		}

		$grupos = array();

		$grupos['admXadm'] = '';
		$grupos['admXsec'] = '';
		$grupos['admXreg'] = '';
		$grupos['admXgest'] = '';
		$grupos['admXprofs'] = '';
		$grupos['admXalun'] = '';
		$grupos['secXadm'] = '';
		$grupos['secXsec'] = '';
		$grupos['secXreg'] = '';
		$grupos['secXgest'] = '';
		$grupos['secXprofs'] = '';
		$grupos['secXalun'] = '';
		$grupos['regXadm'] = '';
		$grupos['regXsec'] = '';
		$grupos['regXreg'] = '';
		$grupos['regXgest'] = '';
		$grupos['regXprofs'] = '';
		$grupos['regXalun'] = '';
		$grupos['gestXadm'] = '';
		$grupos['gestXsec'] = '';
		$grupos['gestXreg'] = '';
		$grupos['gestXgest'] = '';
		$grupos['gestXprofs'] = '';
		$grupos['gestXalun'] = '';
		$grupos['profsXadm'] = '';
		$grupos['profsXsec'] = '';
		$grupos['profsXreg'] = '';
		$grupos['profsXgest'] = '';
		$grupos['profsXprofs'] = '';
		$grupos['profsXalun'] = '';
		$grupos['alunXadm'] = '';
		$grupos['alunXsec'] = '';
		$grupos['alunXreg'] = '';
		$grupos['alunXgest'] = '';
		$grupos['alunXprofs'] = '';
		$grupos['alunXalun'] = '';

		$rs0 = Core::registro('db')->query('SELECT c_permissao FROM chat_permissoes WHERE c_permissao LIKE "'.$grupo.'X%"');
		if ($rs0->num_rows) {
			while ($row0 = $rs0->fetch_assoc()) {
				$grupos[$row0['c_permissao']] = '1';
			}
		}
		$rs0->free();

		$rs = Core::registro('db')->query('SELECT u_id, u_grupos, u_email FROM usuarios WHERE u_email IS NOT NULL');
		
		$lista[1] = array();
		$lista[2] = array();
		$lista[3] = array();
		$lista[4] = array();
		$lista[5] = array();
		$lista[6] = array();

		if ($rs->num_rows) {
			$counter = 0;
			while ($row = $rs->fetch_assoc())
			{
				$usuario = new UsuarioInstituido($row['u_id']);
				$usuario->carregar();

				if($row['u_id'] == Core::registro('usuario')->obterID()){
					continue;
				}

				/*$usuario->verificaLogadoChat();
				if($status != 'onn' && $status != 'occ'){
					continue;
				}*/

				$status = '';
				$rs3 = Core::registro('db')->query('SELECT * FROM chat_group_user WHERE usuario = '.$row['u_id'].' AND sala = '.$sala.' LIMIT 1');
				if ($rs3->num_rows) {
					$status = ' checked="checked"';
				}
				$rs3->free();

				$lista[$row['u_grupos']][$counter]['id'] = $usuario->obterID();
				$lista[$row['u_grupos']][$counter]['nome'] = utf8_encode($usuario->obterNome());
				$lista[$row['u_grupos']][$counter]['email'] = $usuario->obterEmail()->obterEndereco();
				$lista[$row['u_grupos']][$counter]['logado'] = $usuario->estaLogado();
				$lista[$row['u_grupos']][$counter]['status'] = $status;

				$counter++;
			}
		}
		$rs->free();

		$retorno = "";
		if($grupos[$grupo.'Xadm'] == '1'){
			if(count($lista[1])>0){
				$retorno .= '<p>Administradores:</p>';
			  	foreach($lista[1] as $admk => $admv){
			  		$retorno .= '<p><input id="cburg-'.$admv['id'].'" '.$admv['status'].' type="checkbox" onclick="javascript:openChatGrupoMsg('.$sala.','.$admv['id'].');">';
				    $retorno .= '<a href="javascript:void(0);" onclick="javascript:openChatGrupoMsg('.$sala.','.$admv['id'].');" title="'.$admv['email'].'">';
			    	$retorno .= '<span>'.$admv['nome'].'</span></a></p>';
			    }
			    $retorno .= '<p></p>';
			}
		}

		if($grupos[$grupo.'Xsec'] == '1'){
			if(count($lista[5])>0){
				$retorno .= '<p>Secretaria de Educação:</p>';
			  	foreach($lista[5] as $sek => $sev){
			  		$retorno .= '<p><input id="cburg-'.$sev['id'].'" '.$sev['status'].' type="checkbox" onclick="javascript:openChatGrupoMsg('.$sala.','.$sev['id'].');">';
				    $retorno .= '<a href="javascript:void(0);" onclick="javascript:openChatGrupoMsg('.$sala.','.$sev['id'].');" title="'.$sev['email'].'">';
			    	$retorno .= '<span>'.$sev['nome'].'</span></a></p>';
			    }
			    $retorno .= '<p></p>';
			}
		}

		if($grupos[$grupo.'Xreg'] == '1'){
		    if(count($lista[6])>0){
		    	$retorno .= '<p>Regionais:</p>';
			  	foreach($lista[6] as $rek => $rev){
			  		$retorno .= '<p><input id="cburg-'.$rev['id'].'" '.$rev['status'].' type="checkbox" onclick="javascript:openChatGrupoMsg('.$sala.','.$rev['id'].');">';
				    $retorno .= '<a href="javascript:void(0);" onclick="javascript:openChatGrupoMsg('.$sala.','.$rev['id'].');" title="'.$rev['email'].'">';
			    	$retorno .= '<span>'.$rev['nome'].'</span></a></p>';
			    }
			    $retorno .= '<p></p>';
			}
		}
	  
	  	if($grupos[$grupo.'Xgest'] == '1'){
		  	if(count($lista[2])>0){
		    	$retorno .= '<p>Gestores das Escolas:</p>';
		    	foreach($lista[2] as $gek => $gev){
			 		$retorno .= '<p><input id="cburg-'.$gev['id'].'" '.$gev['status'].' type="checkbox" onclick="javascript:openChatGrupoMsg('.$sala.','.$gev['id'].');">';
				    $retorno .= '<a href="javascript:void(0);" onclick="javascript:openChatGrupoMsg('.$sala.','.$gev['id'].');" title="'.$gev['email'].'">';
			    	$retorno .= '<span>'.$gev['nome'].'</span></a></p>';
		    	}
			    $retorno .= '<p></p>';
		    }
		}
	  
	  	if($grupos[$grupo.'Xprofs'] == '1'){
			if(count($lista[3])>0){
				$retorno .= '<p>Professores:</p>';
				foreach($lista[3] as $pek => $pev){
					$retorno .= '<p><input id="cburg-'.$pev['id'].'" '.$pev['status'].' type="checkbox" onclick="javascript:openChatGrupoMsg('.$sala.','.$pev['id'].');">';
				    $retorno .= '<a href="javascript:void(0);" onclick="javascript:openChatGrupoMsg('.$sala.','.$pev['id'].');" title="'.$pev['email'].'">';
			    	$retorno .= '<span>'.$pev['nome'].'</span></a></p>';
				}
			    $retorno .= '<p></p>';
			}
		}

		if($grupos[$grupo.'Xalun'] == '1'){
			if(count($lista[4])>0){
				$retorno .= '<p>Alunos:</p>';
				foreach($lista[4] as $aek => $aev){
					$retorno .= '<p><input id="cburg-'.$aev['id'].'" '.$aev['status'].' type="checkbox" onclick="javascript:openChatGrupoMsg('.$sala.','.$aev['id'].');">';
				    $retorno .= '<a href="javascript:void(0);" onclick="javascript:openChatGrupoMsg('.$sala.','.$aev['id'].');" title="'.$aev['email'].'">';
			    	$retorno .= '<span>'.$aev['nome'].'</span></a></p>';
				}
			    $retorno .= '<p></p>';
			}
		}

		if(strlen($retorno)<=0){
			$retorno = 'Nenhum usu&aacute;rio on-line.';
		}

		return $retorno;
	}

	public function criaSala ()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$cid = $_POST['id'];
		$num = $_POST['num'];

		Core::registro('db')->query('INSERT INTO chat_group_room (hash,criador) VALUES ("'.$cid.'",'.Core::registro('usuario')->obterID().')');
		$id_inserido = Core::registro('db')->insert_id;

		if (Core::registro('db')->affected_rows <= 0) {
			exit('0');
		}

		Core::registro('db')->query('INSERT INTO chat_group_user (sala,usuario) VALUES ("'.$id_inserido.'",'.Core::registro('usuario')->obterID().')');

		if (Core::registro('db')->affected_rows <= 0) {
			exit('0');
		}

		$lista = $this->carregarChatListaGrupo($id_inserido);

		$caixa = '<li style="background: none repeat scroll 0% 0% rgb(222, 222, 222); border-radius: 2px 2px 2px 2px; box-shadow: 1px 1px 3px rgb(153, 153, 153); list-style: none outside none; margin: 10px; padding: 5px;">';
		$caixa .= '<p style="color: #333333;font-size: 14px;margin: 0;text-decoration: underline;"><img style="cursor: pointer; height: 16px; margin-right: 10px; float: left;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sett.png" id="chat-list-close">';
		$caixa .= '<a id="curr-user-'.$cid.'" href="javascript:void(0);" onclick="javascript:currentChat({idg:\''.$cid.'\'});"><span style="line-height: 14px;">Chat em Grupo:'.$num.'</span></a></p>';
		$caixa .= '<p style="clear: both;margin: 0px; color: rgb(51, 51, 51); text-decoration: underline; font-size: 10px;">';
		$caixa .= '<a id="curr-user-close-'.$cid.'" href="javascript:void(0);" onclick="javascript:sairDoGrupo(\''.$cid.'\','.$id_inserido.');">';
		$caixa .= '<img style="cursor: pointer; height: 15px; margin-right: 5px; float: left;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
exit5.png" id="chat-list-close"><span style="line-height: 14px;">Sair desse Grupo </span></a></p>';
		
		$caixa .= '<p style="background: none repeat scroll 0 0 #F9F1B1; padding: 5px;">Marque abaixo os usuários para adicionar na conversa deste grupo.</p>';
		
		$g = Core::registro('usuario')->obterGrupos();
		$u = Core::registro('usuario')->obterID();
		$n2 = UsuarioInstituido::obterNomeUsuarioPeloID($u);
		$time = date('d/m/Y H:i:s');
		if($g[0] != 1){	
			$nome = UsuarioInstituido::obterNomeUsuarioPeloID($u);
			$log = 'Usuario '.$nome.' ('.$u.') criou um grupo de chat ('.$cid.'/'.$id_inserido.').';
			Core::registro('db')->query('INSERT INTO chat_logs (usuario, log) VALUES ('.$u.',"'.$log.'")');
		}

		//Notificação do CHAT.
		$msg = '<p style=font-weight:normal;color:#777777;><b>'.$n2.'</b> ['.$time.']<b>:</b> Criou este grupo.</p>';
		Core::registro('db')->query('INSERT INTO chat_group_msgs (sala, usuario, msg) VALUES ("'.$id_inserido.'",'.$u.',"'.$msg.'")');
		//Notificação do CHAT.

		exit('<div id="grul-'.$cid.'">'.$caixa.$lista.'</li></div>');
	}

	public function GRuserInvite()
	{
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$msg = '';
		$acao = 'NADA!';
		$cid = $_POST['id'];
		$sala = $_POST['sla'];
		$type = $_POST['typ'];

		$g = Core::registro('usuario')->obterGrupos();
		$u = Core::registro('usuario')->obterID();
		$n = UsuarioInstituido::obterNomeUsuarioPeloID($u);

		$u2 = $cid;
		$n2 = UsuarioInstituido::obterNomeUsuarioPeloID($u2);

		$time = date('d/m/Y H:i:s');

		if(empty($cid) || empty($sala) || empty($type)){
			exit('0');
		}

		if($type == 'invite'){
			Core::registro('db')->query('INSERT INTO chat_group_user (sala,usuario) VALUES ("'.$sala.'",'.$cid.')');
			$msg = '<p style=font-weight:normal;color:#777777;><b>'.$n2.'</b> ['.$time.']<b>:</b> Entrou no grupo.</p>';
			$acao = 'adicionou';
		}
		else{
			Core::registro('db')->query('DELETE FROM chat_group_user WHERE usuario = '.$cid.' AND sala = '.$sala);
			$msg = '<p style=font-weight:normal;color:#777777;><b>'.$n2.'</b> ['.$time.']<b>:</b> Saiu do grupo.</p>';
			$acao = 'removeu';
		}

		//Notificação do CHAT.
		Core::registro('db')->query('INSERT INTO chat_group_msgs (sala, usuario, msg) VALUES ("'.$sala.'",'.$u2.',"'.$msg.'")');
		//Notificação do CHAT.

		if (Core::registro('db')->affected_rows <= 0) {
			exit('0');
		}

		if($g[0] != 1){	
			$log = 'Usuario '.$n.' ('.$u.') '.$acao.' o usuário '.$n2.' ('.$u2.') no grupo de chat ('.$sala.').';
			Core::registro('db')->query('INSERT INTO chat_logs (usuario, log) VALUES ('.$u.',"'.$log.'")');
		}

		$rs0 = Core::registro('db')->query('SELECT * FROM chat_group_room WHERE id = '.$sala);
		if ($rs0->num_rows) {
			while ($row0 = $rs0->fetch_assoc()) {
				$hash = $row0['hash'];
			}
		}
		$rs0->free();

		$hash = str_replace('#SPLIT#', '', $hash);
		//$msg = str_replace('#SPLIT#', '', $msg);
		exit($hash.'#SPLIT#');//.$msg);
	}

	public function GRSaindoDoGrupo(){
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$sala = $_POST['sla'];
		if(empty($sala)){
			exit('0');
		}

		$u = Core::registro('usuario')->obterID();
		$n2 = UsuarioInstituido::obterNomeUsuarioPeloID($u);
		$time = date('d/m/Y H:i:s');

		Core::registro('db')->query('DELETE FROM chat_group_user WHERE usuario = '.$u.' AND sala = '.$sala);

		if (Core::registro('db')->affected_rows <= 0) {
			exit('0');
		}

		//Notificação do CHAT.
		$msg = '<p style=font-weight:normal;color:#777777;><b>'.$n2.'</b> ['.$time.']<b>:</b> Saiu do grupo.</p>';
		Core::registro('db')->query('INSERT INTO chat_group_msgs (sala, usuario, msg) VALUES ("'.$sala.'",'.$u.',"'.$msg.'")');
		//Notificação do CHAT.

		exit('1');
	}

	public function updGroup(){
		if (!array_key_exists('token', $_POST)) {
			$url = Gerenciador_URL::gerarLink('principal', 'visaogeral');	
			$url = substr($url, 1, strlen($url));
			header( 'Location: http://'.$_SERVER['HTTP_HOST'].$url);
		}

		if ( !Core::registro('usuario')->estaLogado() ){
			exit('0');
		}

		$msg = "";
		$salas = array();
		$grupos = array();
		$counter = 0;
		
		$xml = "<?xml version='1.0' encoding='utf-8'?>";

		$u = Core::registro('usuario')->obterID();
		$rs0 = Core::registro('db')->query('SELECT * FROM chat_group_user WHERE usuario = '.$u);
		if ($rs0->num_rows) {
			while ($row0 = $rs0->fetch_assoc()){
				$rs3 = Core::registro('db')->query('SELECT * FROM chat_group_room WHERE id = '.$row0['sala'].' LIMIT 1');
				if ($rs3->num_rows) {
					$row3 = $rs3->fetch_assoc();
					$grupos[$counter]['id'] = $row3['id'];
					$grupos[$counter]['hash'] = $row3['hash'];
					$grupos[$counter]['criador'] = $row3['criador'];
					$grupos[$counter]['data'] = $row3['data'];

					$xml .= "<cg id='".$row3['id']."' hash='".$row3['hash']."' criador='".$row3['criador']."' data='".$row3['data']."'>";
				}
				$rs3->free();

				$caixa = '';
				$final = '';
				$id_inserido = $row3['id'];
				$cid = $row3['hash'];

				$lista = $this->carregarChatListaGrupo($id_inserido);

				$caixa = '<li style="background: none repeat scroll 0% 0% rgb(222, 222, 222); border-radius: 2px 2px 2px 2px; box-shadow: 1px 1px 3px rgb(153, 153, 153); list-style: none outside none; margin: 10px; padding: 5px;">';
				$caixa .= '<p style="color: #333333;font-size: 14px;margin: 0;text-decoration: underline;"><img style="cursor: pointer; height: 16px; margin-right: 10px; float: left;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
sett.png" id="chat-list-close">';
				$caixa .= '<a id="curr-user-'.$cid.'" href="javascript:void(0);" onclick="javascript:currentChat({idg:\''.$cid.'\'});"><span style="line-height: 14px;">Chat em Grupo:#NUM#</span></a></p>';
				$caixa .= '<p style="clear: both;margin: 0px; color: rgb(51, 51, 51); text-decoration: underline; font-size: 10px;">';
				$caixa .= '<a id="curr-user-close-'.$cid.'" href="javascript:void(0);" onclick="javascript:sairDoGrupo(\''.$cid.'\','.$id_inserido.');">';
				$caixa .= '<img style="cursor: pointer; height: 15px; margin-right: 5px; float: left;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
exit5.png" id="chat-list-close"><span style="line-height: 14px;">Sair desse Grupo </span></a></p>';

				$caixa .= '<p style="background: none repeat scroll 0 0 #F9F1B1; padding: 5px;">Marque abaixo os usuários para adicionar na conversa deste grupo.</p>';

				$final .= $caixa.$lista;//'<div id="grul-'.$cid.'">'.$caixa.$lista.'</li></div>';

				$grupos[$counter]['lista'] = $final;
				$xml .= "<lista><![CDATA[".$final."]]></lista><msgs>";

				$rs1 = Core::registro('db')->query('SELECT * FROM chat_group_msgs WHERE sala = "'.$row0['sala'].'"');//.'" AND c_lido = "0"');
				if ($rs1->num_rows) {
					$counter_msg = 0;
					while ($row1 = $rs1->fetch_assoc()){
						$id = $row1['id'];
						$sala = $row1['sala'];
						$usuario = $row1['usuario'];

						$msg1 = $row1['msg'];
						$msgs = $msg1;
						$tempo = $row1['tempo'];
						$lidos = $row1['visualizaram'];

						if(strpos($lidos, ';'.$u.';')){				
							continue;
						}

						$rs2 = Core::registro('db')->query('SELECT u_nome FROM usuarios WHERE u_id = '.$usuario.' LIMIT 1');
						if ($rs2->num_rows) {
							$row2 = $rs2->fetch_assoc();
							$nome = $row2['u_nome'];
						}
						$rs2->free();

						$grupos[$counter]['msg'][$counter_msg]['id'] = $id;
						$grupos[$counter]['msg'][$counter_msg]['sala'] = $sala;
						$grupos[$counter]['msg'][$counter_msg]['usuario'] = $usuario;
						$grupos[$counter]['msg'][$counter_msg]['usuario_nome'] = utf8_encode($nome);
						$grupos[$counter]['msg'][$counter_msg]['msg'] = utf8_encode($msgs);
						$grupos[$counter]['msg'][$counter_msg]['tempo'] = $tempo;
						$grupos[$counter]['msg'][$counter_msg]['visualizaram'] = $lidos;
						
						$xml .= "<msg id='".$id."' sala='".$sala."' usuario='".$usuario."' nome='".$nome."' tempo='".$tempo."'><![CDATA[".$msgs."]]></msg>";

						$lidos = str_replace(';'.$u.';', '', $lidos);
						$lidos = $lidos.',;'.$u.';';

						Core::registro('db')->query('UPDATE chat_group_msgs 
									SET visualizaram = "'.$lidos.'" WHERE id = "'.$id.'"');

						$counter_msg ++;
					}

					$grupos[$counter]['msg']['nums'] = $counter_msg;
				}
				$rs1->free();

				$xml .= "</msgs>";

				$counter++;
			}
		}
		$rs0->free();

		//$xml .= "<nums>".$counter."</nums></cg>";
		//header ("content-type: text/xml");
		//exit($xml);

		$grupos['nums'] = $counter;
		$grupos = json_encode($grupos);
		exit($grupos);
	}
}
?>
