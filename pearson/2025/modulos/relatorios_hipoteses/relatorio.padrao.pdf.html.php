<? if (!defined('CORE_INCLUIDO')) exit(); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?= Core::modulo('index')->obterCharset(); ?>" />
<link href="<?= Core::diretiva('ESTILO:DIRETORIO:estilo') . Core::diretiva('ESTILO:ARQUIVO:css_pdf'); ?>" rel="stylesheet" type="text/css" />
</head>
<body>

<table width="100%" border="0" cellspacing="1" cellpadding="3" style="margin-bottom: 8px;">
      <tr>
        <td width="70%"><div class="gigante"><?= $this->_relatorio->obterNome(); ?></div>
			<div><?= $this->_textoFiltro; ?></div></td>
		<td valign="top" align="right" nowrap="nowrap"><?= $this->_obterData(); ?> <?= (!isset($esconderTotal) || !$esconderTotal ) ? '<div class="medio">Total: '. count($this->_dados) .'</div>' : '' ?></td>
      </tr>
  </table>
  
  <table width="100%" cellpadding="2" cellspacing="0" class="rltli">
		<?= $tabela['td']; ?>
		<?= @Core::modulo('_bloco_notas')->obterSaida(); ?>
  </table>

</body>
</html>