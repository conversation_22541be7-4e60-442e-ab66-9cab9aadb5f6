<?
if (!defined('CORE_INCLUIDO')) exit();

//A4 - Altura - 29,7 cm, metade é 14.85 cm - 297x210mm

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'footer' => '&nbsp;'
);

$logo_avaliativa = Core::diretiva('GABARITO_MPT:LogoAvaliativa');
$logo_municipio = Core::diretiva('GABARITO_MPT:LogoMunicipio');
$projeto = Core::diretiva('GABARITO_MPT:NomeProjeto');
$ano = Core::diretiva('GABARITO_MPT:ano');

$gppAusente = Core::diretiva('GABARITO_MPT:PreenchimentoProfessor:Ausente');
$gppAtestado = Core::diretiva('GABARITO_MPT:PreenchimentoProfessor:Atestado');
$gppRemanejado = Core::diretiva('GABARITO_MPT:PreenchimentoProfessor:Remanejado');
$gppTransferido = Core::diretiva('GABARITO_MPT:PreenchimentoProfessor:Transferido');

echo"<style type='text/css'>
	@font-face {
	  font-family:'OMRBubbles';
	  font-style: normal;
	  font-weight: normal;
	  src: local('OMRBubbles'), url('modulos/relatorios_hipoteses/rel_gabaritos/OMRBubbles.ttf') format('truetype');
	}
</style>";

$url = "http" . (($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://").$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
$url = substr($url, 0, strpos( $url,'?'));

foreach ($this->_dadosAlunos as $da) {
	echo'<div style="margin:0cm auto;padding:5mm;width:200mm;height:290mm; background-image:url('.$url.'modulos/relatorios_hipoteses/rel_gabaritos_mairipora_pt/2Ano1.png); background-size: 100%; background-repeat: no-repeat;">';//h280mm

	echo'<TABLE width="99%" align="center" border=0 style="height: 60mm;">
			<TR>
				<TD style="text-align: center; vertical-align: middle;">
					<TABLE width="100%" align="center" style="padding-top: 5pt; padding-bottom: 5pt;">
						<TR>
							<TD></TD>
						</TR>
					</TABLE>
				</TD>
				<TD style="width: 75mm;">';

				echo'<TABLE width="100%" align="center" style="background-color: #FFF; border:1px solid #000;text-align: center; height: 60mm;  padding: 2px 2px 2px 2px;">';
					echo'<TR>';
						echo'<TD colspan=10 style="border:1px solid #000;">';
							echo'<p style="width: 100%; font-weight: bold; font-size: 7px; text-align:center; margin: 2px 2px 4px 2px;">
									APLICADOR PREENCHA ABAIXO<br>O RA DO ALUNO
								</p>';
						echo'</TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
					echo'<TR>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">0</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">1</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">2</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">3</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">4</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">5</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">6</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">7</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">8</p></TD>';
						echo'<TD><p style="font-family:OMRextnd;font-size:11px;margin:0;">9</p></TD>';
					echo'</TR>';
				echo'</TABLE>';

			echo'</TD>';
	echo'	</TR>
	</TABLE>';

	//Linhas prod txt
	echo'<TABLE width="90%" align="center" style="height:100mm;width:200mm; margin-top: 10pt;">';
		echo'<TR>';
			echo'<TD style="vertical-align:top;padding: 0;">';

			echo'<p style="text-align:center; margin:0 0 5mm 0;"></p>';		

			echo'<p style="text-align:center; margin:0 0 5mm 0;"></p>';	

			echo'</TD>';

		echo'</TR>';
	echo'</TABLE>';

	echo'</div>';

	echo'<div style="page-break-after:always;"></div>';


















	echo'<div style="margin:0cm auto;padding:5mm;width:200mm;height:290mm; background-image:url('.$url.'modulos/relatorios_hipoteses/rel_gabaritos_mairipora_pt/2Ano2.png); background-size: 100%; background-repeat: no-repeat;">';//h280mm

	//Linhas prod txt
	echo'<TABLE width="90%" align="center" style="height:205mm;width:200mm;">';
		echo'<TR>';
			echo'<TD style="vertical-align:top;padding: 0;">';		

			echo'<p style="text-align:center; margin:0 0 5mm 0;"></p>';		

			echo'<p style="text-align:center; margin:0 0 5mm 0;"></p>';		

			echo'</TD>';

		echo'</TR>';
	echo'</TABLE>';

	//PREENCHIMENTO PROFESSOR LINHA - 28,25mm
	echo'<TABLE width="99%" align="center" cellspacing="0" cellpadding="0" style="height:62mm;border:1pt solid #000;padding:10pt 0pt 0pt 0pt;">
			<TR>
				<TD colspan=2 style="border-bottom: 1pt solid #000000;">
					<p style="text-align:center;font-size:10pt; margin: 0;">
						PREENCHIMENTO DO CORRETOR
					</p>
				</TD>
			</TR>				
			<TR>
				<TD style="text-align: left; width: 20%; border-right: 1pt solid #000000;">
						<TABLE width="100%" align="center" style="border:0pt solid #000;text-align: center; padding-top: 5pt; padding-bottom: 5pt;">';
						echo'<TR>';
							echo'<TD>';
								echo'<img style="margin-top:15%; height:26mm;" align="center" src="'.$da['QRCODE'].'" />';
							echo'</TD>';
						echo'</TR>';
					echo'</TABLE>
				</TD>

				<TD style="width: 80%;">
					<div style="border:0pt solid #000; padding-top: 15pt;">
					<TABLE width="100%" align="center" cellpadding="5">
						<TR>
							<TD style="text-align: center; width:33%;">
								<span style="font-size:9pt;">1. Nome</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
						<TR>
							<TD style="text-align: center;">
								<span style="font-size:9pt;">2. Apoio de imagem</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
						<TR>
							<TD style="text-align: center;">
								<span style="font-size:9pt;">3. Palavra ditada</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
						<TR>
							<TD style="text-align: center;">
								<span style="font-size:9pt;">4. Frase apoio cena</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
						<TR>
							<TD style="text-align: center;">
								<span style="font-size:9pt;">5. Segmenta&ccedil;&atilde;o</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
						<TR>
							<TD style="text-align: center;">
								<span style="font-size:9pt;">6. Narrativa</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
						<TR>
							<TD style="text-align: center;">
								<span style="font-size:9pt;">Global</span>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">A</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">B</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">C</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">D</span><br>
							</TD>
							<TD style="text-align: center;">
								<span style="font-family:OMRBubbles;font-size:12pt;">E</span><br>
							</TD>
						</TR>
					</TABLE>
					</div>
				</TD>
			</TR>
		</TABLE>';

	echo'</div>';

	echo'<div style="page-break-after:always;"></div>';

	BREAK;
}

$tabela['extra'] = ob_get_clean(); ob_start();
$relHTML[] = $tabela;
?>