<? if (!defined('CORE_INCLUIDO')) exit(); ?>

<? //= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<? /*<div class="vd_BlocoBotoes"><strong><?= $this->_obterLinkBotao(self::BT_VOLTAR); ?></strong></div>*/ ?>

<?
	$i = count($relHTML);
	foreach($relHTML as $relTabela) {
?>
		<? if (@$relTabela['descricao'] != null): ?>
			<div style="padding-bottom: 10px; line-height: 88%; width:100%;" class="medio">
				<table border=0 cellpadding=0 cellspacing=0 style="width:99%;">
					<tr>
						<td><?= @$relTabela['descricao']; ?></td>	
						<td class="normal" style="text-align: right;"><?= '<strong>Impresso: </strong>', $this->_obterData() ?></td>							
					</tr>
				</table>
			</div>
		<? endif; ?>
		
		<? if (@$relTabela['titulo'] != null): ?>
			<div style="padding-bottom: 10px; line-height: 88%; font-weight: bold; color:#888888; font-size: 12px;" align="center" class="rlt_painel_Titulo gigante">
				<?= @$relTabela['titulo'];?>
			</div>
		<? endif; ?>

<?
		if (isset($relTabela['grafico']) && !is_array($relTabela['grafico']) && !empty($relTabela['grafico']))
			echo '<div style="padding-bottom: 20px;" align="center">'. $relTabela['grafico'] .'</div>';
		elseif (isset($relTabela['grafico']) && is_array($relTabela['grafico'])) {
			foreach($relTabela['grafico'] as $grafico)
				echo '<div style="padding-bottom: 20px;" align="center">'. $grafico .'</div>';
		}
?>
<?
		if (isset($relTabela['td']) && $relTabela['td'] != null) {
?>
		<table <?= !isset($relTabela['usar_largura_maxima']) || $relTabela['usar_largura_maxima'] ? 'width="100%"' : ''; ?> align="center" cellpadding="5" cellspacing="0" class="rltli">
			<?= @$relTabela['td']; ?>
		</table>
<?
		}
?>

		<? if (@$relTabela['extra_antes_td_extra']) echo @$relTabela['extra']; ?>

<?
		if ( isset($relTabela['td_extra']) && $relTabela['td_extra'] != null ) {
?>
		<table style="margin-top: 20px;" <?= !isset($relTabela['usar_largura_maxima']) || $relTabela['usar_largura_maxima'] ? 'width="100%"' : ''; ?> align="center" cellpadding="5" cellspacing="0" class="rltli">
			<?= @$relTabela['td_extra']; ?>
		</table>
<?
		}
?>

		<? if (!isset($relTabela['extra_antes_td_extra']) || !@$relTabela['extra_antes_td_extra']) echo @$relTabela['extra']; ?>

		<? if (@$relTabela['nao_quebrar_pagina'] != true): ?>
			<div style="padding-top: 10px"><?php echo Core::diretiva('REL:FooterText'); ?></div>
		<? endif; ?>
<?
		if (--$i > 0) {
			if (@$relTabela['nao_quebrar_pagina'] != true) {
				echo '<div style="page-break-after: always;"></div>';
			} else {
				echo '<hr color="#ccc" noshade="noshade" size="1" style="margin-top: 20px; margin-bottom: 15px;" />';
			}
		}
	}
?>

<?
	if (false || @Core::modulo('_bloco_notas')->temDados()) {
?>
	<hr color="#ccc" noshade="noshade" size="1" style="margin-top: 20px; margin-bottom: 20px;" />

	<table width="100%" align="center" cellpadding="5" cellspacing="0" class="rltli">
		<?= @Core::modulo('_bloco_notas')->obterSaida(); ?>
	</table>
<?
	}
?>

<? //= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<? //= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>