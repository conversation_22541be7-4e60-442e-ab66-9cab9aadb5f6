<div id="teste_gravacao" class="modal-box" style="width: 70% !important;">
	<header>
		<!-- <a href="javascript:closeModalTesteRec();" class="js-modal-close close">&times;</a> -->
		<!-- <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/> -->
		<p style="width: 90%; text-align:left; font-size:18px; opacity:70%">TESTE DE GRAVA&Ccedil;&Atilde;O</p>
	</header>
	<div class="modal-body-novo-aluno">
		<STYLE type="text/css">
			/* * {box-sizing:border-box} */

			/* Slideshow container */
			.slideshow-container {
				border: solid #aaa 1px;
				max-width: 800px;
				position: relative;
				margin: auto;
			}

			/* Hide the images by default */
			.mySlides {
				display: none;
			}

			/* Next & previous buttons */
			.prev, .next {
				cursor: pointer;
				position: absolute;
				top: 50%;
				width: auto;
				margin-top: -22px;
				padding: 10px;
				color: white;
				font-weight: bold;
				font-size: 18px;
				transition: 0.6s ease;
				border-radius: 0 3px 3px 0;
				user-select: none;
				background-color: rgba(0,0,0,0.1);
			}

			/* Position the "next button" to the right */
			.prev {
				left: 0;
				border-radius: 3px 0 0 3px;
			}

			/* Position the "next button" to the right */
			.next {
				right: 0;
				border-radius: 3px 0 0 3px;
			}

			/* On hover, add a black background color with a little bit see-through */
			.prev:hover, .next:hover {
				text-decoration: none;
				background-color: rgba(0,0,0,0.5);
			}

			/* Caption text */
			.text {
				color: #f2f2f2;
				font-size: 15px;
				padding: 8px 12px;
				position: absolute;
				bottom: 8px;
				width: 100%;
				text-align: center;
			}

			/* Number text (1/3 etc) */
			.numbertext {
				color: #aaa;
				font-size: 12px;
				padding: 8px 12px;
				position: absolute;
				top: 0;
				right: 0;
			}

			/* The dots/bullets/indicators */
			.dot {
				cursor: pointer;
				height: 10px;
				width: 10px;
				margin: 0 2px;
				background-color: #bbb;
				border-radius: 50%;
				display: inline-block;
				transition: background-color 0.6s ease;
			}

			.active, .dot:hover {
				background-color: #717171;
			}

			/* Fading animation */
			.fade {
				-webkit-animation-name: fade;
				-webkit-animation-duration: 1.5s;
				animation-name: fade;
				animation-duration: 1.5s;
			}

			@-webkit-keyframes fade {
				from {opacity: .4}
				to {opacity: 1}
			}

			@keyframes fade {
				from {opacity: .4}
				to {opacity: 1}
			}
		</STYLE>

		<div class="slideshow-container">
			<table border=0 style="margin: 0 auto;">
				<tr>
					<td style="justify-content: center;display: grid;">
						<p style="font-size: 16px; font-weight: bolder;">Orientações:</p>
							<ul>
								<li style="font-size: 16px;">Faça os teste de gravações.</li>
								<li style="font-size: 16px;">Procure fazer silêncio durante as gravações.</li>
								<li style="font-size: 16px;">Se possível, procure um lugar silencioso.</li>
								<li style="font-size: 16px;">Não ajude o aluno.</li>
								<li style="font-size: 16px;">Não fale durante a gravação do aluno.</li>
								<li style="font-size: 16px;">Utilize o microfone da melhor forma para ficar um volume audível.</li>
								<li style="font-size: 16px;">Caso necessário, utilize o microfone de fone de ouvido.</li>
							</ul>
					</td>
				</tr>
				<tr>
					<td style="justify-content: center;display: grid;">
						<div id="timer_testerec" class="timer col-12">
							<div class="clock-wrapper">
								<input type="hidden" id="minutos_testerec" value="1"/>
								<span class="minutes">01</span>
								<span class="dots">:</span>
								<span class="seconds">00</span>
							</div>
						</div>
						<br>
						<button class='btnRec btnRecCorGRAVAR' id='btnRecGRAVAR_testerec'>&#127908; Gravar!</button>
						<button class='btnRec btnRecCorGRAVANDO' id='btnRecGRAVANDO_testerec'>&#127908; Parar e enviar!</button>
						<p id="fsalertT" class="fsalert fsalert2" style="display:none;"></p>
					</td>
				</tr>
				<tr>
					<td>
						<audio id='testerec_play' controls="true" style="border-radius: 10px; width: 100%; display:none;">
							<source src="" type="audio/ogg">
						</audio>
					</td>
				</tr>
				<tr>
					<td style="display: block; margin: 0 auto;">
						<p id="btnsCheck" style="font-size: 18px; display:none;">Voc&ecirc; consegue ouvir o que foi gravado?</p>
						<p style="text-align: center;">
							<a href="javascript:closeModalTesteRec();" id="btnsCheckSim" style="display:none;" class="btn-ok btn-small">&#10004; &nbsp; Sim</a>
							<a href="javascript:openModalTesteRecAjuda();" id="btnsCheckNao" style="display:none;" class="btn-notok btn-small js-modal-close-confirmation">&#x2716; &nbsp; N&atilde;o</a>
						</p>
					</td>
				</tr>
			</table>

		</div>
	</div>
</div>