<script>
	jQuery(function(){
		jQuery('#contactable').remove();
	});

	localStorage.setItem("testeRec", '0');

	var stats = null;
	var typeDevice = null;
	var typeBrowser = null;
	function checkBrowser(){
		const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

		if (isMobile) {
			typeDevice = 'mobile';
			//console.log("Usuário está em um dispositivo móvel");
		} else {
			typeDevice = 'desktop';
			//console.log("Usuário está em um desktop");
		}

		// Verificando se o usuário está no Chrome
		if (navigator.userAgent.indexOf("Chrome") !== -1 || navigator.vendor.indexOf("Google") !== -1) {
			typeBrowser = 'chrome';
			//console.log("Usuário está no Chrome");
		}

		// Verificando se o usuário está no Firefox
		if (navigator.userAgent.indexOf("Firefox") !== -1) {
			typeBrowser = 'firefox';
			//console.log("Usuário está no Firefox");
		}

		// Verificando se o usuário está no Safari
		if (navigator.userAgent.indexOf("Safari") !== -1 || navigator.vendor.indexOf("Apple") !== -1) {
			typeBrowser = 'safari';
			//console.log("Usuário está no Safari");
		}

		// Verifica se o navegador suporta a API de gravação de áudio
		if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
		// Solicita permissão para acessar o microfone
			navigator.mediaDevices.getUserMedia({ audio: true }).then(function(stream) {
				// Permissão concedida
				//console.log('Permissão concedida para acessar e gravar o microfone.');
				stats = stream.active;
				jQuery('#modal_load2').hide();
				jQuery('#modal_load2 img').hide();
				jQuery('#modal_load3').hide();
				jQuery('#modal_load3 img').hide();
			}).catch(function(error) {
				// Permissão negada ou erro ocorrido
				//console.log('Permissão negada para acessar e gravar o microfone.');
				stats = 'err2';
			})
			.finally(function() {
				//console.log('Promise finalizada');
			});
		} else {
			// Navegador não suporta a API de gravação de áudio
			//console.log('Este navegador não suporta a gravação de áudio.');
			stats = 'err1';
		}

		if(stats == null){
			jQuery('#modal_load').hide();
			jQuery('#modal_load2').show();
			jQuery('#modal_load2 img.'+typeDevice+'_'+typeBrowser+'_2').show();
			window.scroll(0,0);
			//checkBrowser();
		}
		else{
			return stats;
		}
	}

	document.onreadystatechange = function() { 
        if (document.readyState === "complete") { 
			stats = checkBrowser();

			if(stats == true){
            	jQuery('#modal_load').hide();

				const testeRec = localStorage.getItem("testeRec");

				if(testeRec == '0'){
					openModalTesteRec();
				}
			}
			else if(stats == 'err1'){
				jQuery('#fsalert').addClass('fsalert_erro');
				jQuery('#fsalert').html('O navegador não tem suporte para a gravação de áudio. Erro #01');
            	jQuery('#modal_load').hide();
            	jQuery('#modal_load2').show();
				jQuery('#modal_load2 img.'+typeDevice+'_'+typeBrowser+'_2').show();
            	window.scroll(0,0);
			}
			else if(stats == 'err2'){
				jQuery('#modal_load').hide();
				jQuery('#modal_load2').show();
				jQuery('#modal_load2 img.'+typeDevice+'_'+typeBrowser+'_2').show();
				window.scroll(0,0);
			}
        }
		else{
			setTimeout(document.onreadystatechange, 500);
		}
    }; 

	document.addEventListener('keydown', function (e) {
		if ((e.key === 'F5') || (e.ctrlKey && e.key === 'r')) {
			e.preventDefault();
			alert('A atualização da página foi desabilitada.');
		}
	});

	let entregou = false;
	window.onbeforeunload = function(){ 
		if(entregou == false){
			jQuery('#avisos').attr('style','border:3px solid #EE1111;');
			jQuery('.alert_box').html('Voc&#234; n&#227;o entregou a avalia&#231;&#227;o! Deseja sair dessa p&#225;gina?').fadeIn('fast');
			window.scroll(0,0);
			return "Você não entregou a avaliação! Deseja sair dessa página?";
		}
		else{
			localStorage.setItem("testeRec", '0');
		}
	};
</script>

<script src="https://kit.fontawesome.com/544f3b41ce.js" crossorigin="anonymous"></script>
<link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>

<script src="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/development-bundle/ui/jquery.ui.core.js" type="text/javascript"></script>
<script src="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/development-bundle/ui/jquery.ui.widget.js" type="text/javascript"></script>
<script src="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/development-bundle/ui/jquery.ui.accordion.js" type="text/javascript"></script>
<link rel="stylesheet" type="text/css" href="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/css/custom-theme/jquery-ui-1.8.22.custom.css" />

<style type='text/css'>
	.geral * {
		font-family: 'Roboto';
	}

	.fa{
		font-family:var(--fa-style-family,"Font Awesome 6 Free") !important;
	}

	.fa-solid{
		font-family: "Font Awesome 6 Free" !important;
	}

	div.geral{
		display: block;
		height: auto;
		width: 100%;
	}

	div.title{
		background-color: #eee;
		border-radius: 10px 10px 0px 0px;
		font-size: 20px;
		margin-bottom: 0;
		padding: 10px;
		font-weight: bold;
	}

	.crono{
		border-bottom: 1px solid #DEDEDE;
		border-left: 1px solid #DEDEDE;
		border-right: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		color: #555555;
		font-size: 13px;
		margin: 0px auto;
		padding: 5px 5px 2px;
		width: 125px;
		overflow: hidden;
		display:none;
	}

	img.crono_start, img.crono_stop{
		cursor: pointer;
		display:none;
	}

	#crono_time{
		display: block;
		float: right;
		margin-left: 5px;
	}

	.conteudo {
		border: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		margin-top: 0;
		background: #fff;
	}

	.num_questao{
		border-bottom: 1px solid #DEDEDE;
		border-radius: 5px 0 5px 0;
		border-right: 1px solid #DEDEDE;
		color: #333333;
		font-size: 14px;
		padding: 5px 10px;
		float:left;
	}

	.crono_questao{
		border-bottom: 1px solid #DEDEDE;
		border-left: 1px solid #DEDEDE;
		border-radius: 0 5px 0 5px;
		float: right;
		font-size: 14px;
		padding: 5px;
		color:#333333;
		display:none;
	}

	.tbl_conteudo{
		width: 100%;
		border-collapse: collapse; 
		border-spacing: 0;
	}

	.tbl_conteudo td{
		border-spacing: 0;
		padding: 0;
		line-height: 20px;
	}

	.botoes{
		padding: 5px;
		border-top: 1px solid #DEDEDE;
	}

	.resposta_questao {
		padding: 15px;
	}

	.questao{
		width: 100%;
	}

	.questionario_general_start{
		border: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		background: #fff;
	}

	.questionario_general_end{
		border: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		background: #fff;
	}

	#modal_load{
		display: block;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	#modal_load1{
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.3);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	#modal_load2{
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	#modal_load3{
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	h3{
		padding: 5px 5px 5px 25px !important;
	}

	.btnRec{
		padding: 15px;
		/*height: 75px;*/
		width: 270px;
		font-size: 27px !important;
		font-weight: bold;
		color: #fff;
		border-radius: 30px;
		cursor: pointer;
		margin: auto;
	}

	.btnRecCorGRAVAR{
		background: #f93737;
		border: 1px solid #ae0606;
	}

	.btnRecCorGRAVANDO{
		display: none;
		background: #f96e37;
		border: 1px solid #ae4d06;
	}

	.btnRecCorSALVANDO{
		display: none;
		background: #119fe6;
		border: 1px solid #066fae;
	}

	.btnRecCorERRO{
		display: none;
		background: #e3a40f;
		border: 1px solid #ae8406;
	}

	.btnRecCorGRAVADO{
		display: none;
		background: #20d905;
		border: 1px solid #129921;
	}

	.azul{
		color: #119fe6;
	}

	.verde{
		color: #129921;
	}

	.amarelo{
		color: #e3a40f;
	}

	.tblRec {
		border: solid 2px #aaa;
		border-radius: 10px;
		margin-bottom: 10px;
	}

	body .timer .endMessage {
		font-size: 50px;
		text-align: center;
		font-weight: bold;
		opacity: 0;
		transition: all .35s ease;
	}

	body .timer .clock-wrapper {
		display: flex;
		justify-content: center;
	}

	body .timer .clock-wrapper span {
		font-size: 60px;
		font-weight: bold;
		transition: all .2s ease;
	}

	body .timer .clock-wrapper span.red {
		transition: all .2s ease;
		color: rgb(233, 19, 19);
		font-size: 180px;
	}

	body .timer .clock-wrapper span.red.hours,
	body .timer .clock-wrapper span.red.minutes,
	body .timer .clock-wrapper span.red.dots {
		width: 0;
		opacity: 0;
		transition: all .3s ease;
	}

	body .timer .clock-wrapper span.dots {
		margin-top: -5px;
	}

	#fsalert, .fsalert{
		font-size: 18px !important; 
		background: none repeat scroll 0 0 #F7EECD;
		border: 1px dashed #8C7416;
		border-radius: 3px 3px 3px 3px;
		color: #896E00;
		font-size: 14px;
		padding: 10px;
		text-align: center;
		margin-top: 0px;
	}

	.fsalert_sucesso {
		border: 4px dashed #558E2F  !important;
	}

	.fsalert_erro {
		border: 4px dashed #EE1111  !important;
	}

	.fsalert2{
		width: 50%;
		display: block;
		margin: 5% auto;
		border-radius: 10px;
		padding: 20px;
		border: 3px solid #ae0606 !important;
		background: #f9d7d7;
		color: #333;
	}
</style>

<?php include_once('fluencia_leitora.tutorial.html.php'); ?>
<?php include_once('fluencia_leitora.testerec.html.php'); ?>

<div id="modal_load">
	<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
loader6.gif" style="width: 15%;display: block;margin: 15% auto;" />
</div>

<div id="modal_load1">
	&nbsp;
</div>

<div id="modal_load2">
	<p class="fsalert fsalert2" style="">
		Habilite o navegador para ter acesso ao microfone.
		<br>		
		<img class="mobile_safari_2" src="./modulos/fluencia_leitora/tutoriais/tuto_safari-mobile-gm1.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="desktop_firefox_2" src="./modulos/fluencia_leitora/tutoriais/tuto_firefox-pc-gm.gif" style="display:none; width: 100%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="desktop_chrome_2" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-pc2-gm2.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="desktop_chrome_2" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-pc-gm.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="mobile_chrome_2" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-mobile-gm1.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="mobile_chrome_2" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-mobile-gm2.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<br>
		Por favor, atualize a p&aacute;gina em seguida.
	</p>
</div>

<div id="modal_load3">
	<p class="fsalert fsalert2" style="">
		Seu microphone est&aacute; sem permiss&atilde;o para uso neste navegador. 
		<br>		
		<img class="mobile_safari_3" src="./modulos/fluencia_leitora/tutoriais/tuto_safari-mobile-gm1.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="desktop_firefox_3" src="./modulos/fluencia_leitora/tutoriais/tuto_firefox-pc-gm.gif" style="display:none; width: 100%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="desktop_chrome_3" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-pc2-gm2.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="desktop_chrome_3" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-pc-gm.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="mobile_chrome_2" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-mobile-gm1.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<img class="mobile_chrome_2" src="./modulos/fluencia_leitora/tutoriais/tuto_chrome-mobile-gm2.gif" style="display:none; width: 75%;border: 1px #333 solid;margin: 10px 0;" />
		<br>
		Por favor, atualize a p&aacute;gina em seguida.
	</p>
</div>

<div class="geral">
	<div class="title">
		<input type="hidden" id="id_simulado" value="<?php echo $_SESSION['fl_on']['simulado_id']; ?>" />
		<input type="hidden" id="id_turma" value="<?php echo $_SESSION['fl_on']['turma_id']; ?>" />
		<?php echo $iNome.' - '.$simulado->obterSerieAvaliacao()->obterNome().' - '.$tNome.' ('.$sNome.')'; ?>
	</div>

	<div id="div_btn_start" class="questionario_general_start" style="padding: 20px;">
		<p id="fsalert" class="">
			Bem vindos! Desejamos uma boa avalia&#231;&#227;o! &#128515;
		</p>

		<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp" style="margin-bottom: 10px; background-color: #F8FDB6;">
			<th colspan="4" class="descricao" style="text-align: left; background-color: #F8FDB6; color: #545454; font-weight: normal; font-size: 14px; padding: 10px;">
				<strong>Aplicador:</strong>
				<ul>
					<li style="font-size: 15px;">Lembre-se de escanear o qrcode da lista de presenças para registrar a chegada (checkin) na turma e o início das aplicações.</li>
					<li style="font-size: 15px;">Faça o teste de gravação para verificar se o microfone está funcionando e a internet também.</li>
					<li style="font-size: 15px;">Não se esqueça de preencher a lista de presenças.</li>
					<li style="font-size: 15px;">Ao final das gravações de cada alnuo, certifique-se que os audios do aluno foram enviador.</li>
					<li style="font-size: 15px;"><u>No final do período da aplicação, não se esqueça de escanear o qrcode de saída (checkout) da escola.</u></li>
				</ul>
			</th>
		</table>

		<table style='margin: 0 auto; margin-bottom: 15px;'>
			<tr>
				<td>
					<div id="inscrever_anular_aluno">
						<div class="grid-container-aluno">
							<div class="inscrever-aluno play_tutorial">
								<span>
									<i class="fa-solid fa-play" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											TUTORIAL
										</label>
									</i>
								</span>							
							</div>
							<div></div> <!-- espa&ccedil;o do grid entre os botes -->
							<div class="inscrever-aluno teste_gravacao">
								<span>
									<i class="fa-solid fa-wrench" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											TESTAR GRAVA&Ccedil;&Atilde;O
										</label>
									</i>
								</span>							
							</div>
							<div></div> <!-- espa&ccedil;o do grid entre os botes -->
							<div class="inscrever-aluno novo_aluno">
								<span>
									<i class="fa-solid fa-user-plus" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											INSCREVER ALUNO
										</label>
									</i>
								</span>							
							</div>
							<div></div> <!-- espa&ccedil;o do grid entre os botes -->
							<div class="inscrever-aluno delete_aluno">
								<span>
									<i class="fa-solid fa-ban" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											ANULAR INSCRI&Ccedil;&Atilde;O
										</label>
									</i>
								</span>
							</div>
						</div>
					</div>
				</td>
			</tr>
		</table>

		<div id="itens" class="itens">
		<?php 
			foreach($arrInscritos as $aNome => $aiv): 
				$feitoTMP = 1;
				foreach($arrQuestao as $qk => $qv){
					$qid = $qv['qid'];

					if(array_key_exists($qid, $aiv['feito']) === false){
						$feitoTMP = 0;
					}
				}
				$iid = $aiv['iid'];
				$feito = ($feitoTMP) ? '<span id="status_'.$iid.'" class="verde">(Gravado)</span>' : '<span id="status_'.$iid.'" class="azul">(Pendente)</span>';
		?>
			<h3 style="font-size: 20px !important;" id="acc_<?php echo $iid; ?>"> &#127891; <?php echo '<span class=nomeera>'.$aiv['nome'].' - '.$aiv['ra'].'</span>'.' '.$feito; ?></h3>
			<div>
				<?php 
					foreach($arrQuestao as $qk => $qv): 
						$qid = $qv['qid'];
						$qenun = $qv['qenun'];
						$qmin = $qv['qmin'];
				?>
				<table width="100%" border="0" cellpadding="0" cellspacing="0" class="tblRec">
					<tr>
						<td colspan=2 style="padding: 1px 15px;">
							<?php echo $qenun; ?>
						</td>
					</tr>
					<tr>
						<td style='border-top: solid 1px #aaa;'>
							<div id="timer_<?php echo $iid; ?>_<?php echo $qid; ?>" class="timer col-12">
								<div class="clock-wrapper">
									<input type="hidden" id="minutos_<?php echo $iid; ?>_<?php echo $qid; ?>" value="<?php echo $qmin; ?>"/>
									<span class="minutes"><?php echo str_pad($qmin,2, "0", STR_PAD_LEFT); ?></span>
									<span class="dots">:</span>
									<span class="seconds">00</span>
								</div>
							</div>
						</td>
						<td style='border-top: solid 1px #aaa;'>
							<p style='text-align: center;'>
								<?php
									if(array_key_exists($qid, $aiv['feito']) !== false):
								?>
								<button class='btnRec btnRecCorGRAVADO' style='display:block;' id='btnRecGRAVADO_<?php echo $iid; ?>_<?php echo $qid; ?>'>&#127908; Gravado!</button>
								<?php
									else:
								?>
								<button class='btnRec btnRecCorGRAVAR' id='btnRecGRAVAR_<?php echo $iid; ?>_<?php echo $qid; ?>'>&#127908; Gravar!</button>
								<button class='btnRec btnRecCorGRAVANDO' id='btnRecGRAVANDO_<?php echo $iid; ?>_<?php echo $qid; ?>'>&#127908; Parar e enviar!</button>
								<button class='btnRec btnRecCorSALVANDO' id='btnRecSALVANDO_<?php echo $iid; ?>_<?php echo $qid; ?>'>&#127908; Salvando...</button>
								<button class='btnRec btnRecCorERRO' id='btnRecERRO_<?php echo $iid; ?>_<?php echo $qid; ?>'>&#127908; Erro! Reenviar agora!</button>
								<button class='btnRec btnRecCorGRAVADO' id='btnRecGRAVADO_<?php echo $iid; ?>_<?php echo $qid; ?>'>&#127908; Gravado!</button>
								<?php
									endif;
								?>
							</p>
						</td>
					</tr>
				</table>
				<?php 
					endforeach; 
				?>
			</div>
		<?php 
			endforeach; 
		?>
		</div>
	</div>
</div>

<div id="incluir_aluno" class="modal-box">
	<header> 
		<a href="javascript:closeModalIncluirAluno();" class="js-modal-close close">&times;</a>
		<!-- <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/> -->
		<p style="text-align:left; font-size:18px; opacity:70%">INSCREVER ALUNO</p>
	</header>
	<div class="modal-body-novo-aluno">
		<label class="modal-font-novo-aluno" style="padding-bottom:15px"></label>
		<p style="margin-left: 30px;font-weight: bold;font-size: 14px">RA (Somente os 9 n&uacute;meros, sem o digito)</p>
		<p>
			<div class="mtse">
				<div class="zerosra" style="display: inline-block;">
					<div class="mtse2" style="width: 50px; display: inline-block; text-align: right">000</div>

					<input class="mtse2" id="matricula_incluir_aluno" type="text" maxlength="9" style="font-size: 14px; font-family: Verdana, Geneva, Tahoma, sans-serif; text-align:left;color:rgb(122, 122, 122)"/>
				</div>	    
			</div>
		</p>
		<label class="labelalunoincluir" style="font-weight: bold;font-size: 12px; display: none;">Nome do Aluno(a)</label>
		<p>
			<a class="nomealunoincluir" style="display: none;">
				<input id="nome_incluir_aluno" placeholder="Digite o nome do aluno" class="incluiraluno-input" maxlength="40" />
				<select id="nome_incluir_aluno_existente" class="selecione-input" style="width: 100%; background-color: #ddd; border: 1px solid #999;"></select>
			</a>
		</p>
	</div>
	<footer>
		<a href="javascript:incluirAluno();" class="btn-ok btn-small">&#10004; &nbsp; Incluir</a>
		<a href="javascript:closeModalIncluirAluno();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Cancelar</a>
	</footer>
</div>

<div id="anular_aluno" class="modal-box">
	<header> 
		<a href="javascript:closeModalAnularInscricaoAluno();" class="js-modal-close close">&times;</a>
		<!-- <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/> -->
		<p style="text-align:left; font-size:18px; opacity:70%">ANULAR INSCRI&Ccedil;&Atilde;O</p>
	</header>
	<div class="modal-body-anular-aluno">
		<p class="modal-font-anular-aluno"></p>
		<p>
			<label style="font-weight: bold;font-size: 14px;">Selecione o Aluno:</label>
			<p>
				<select id="anular_aluno_select" class="selecione-input" style="width: 100%; background-color: #ddd; border: 1px solid #999;"></select>
			</p>
		</p>
	</div>
	<footer>
		<a href="javascript:anularInscricao();" class="btn-ok btn-small">&#10004; &nbsp;Anular Inscri&ccedil;&atilde;o</a>
		<a href="javascript:closeModalAnularInscricaoAluno();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Cancelar</a>
	</footer>
</div>

<script src="modulos/fluencia_leitora/countdown.js"></script>
<script src="modulos/fluencia_leitora/engine.js"></script>

<script language="JavaScript">
	jQuery(document).ready(function(){
		jQuery("#itens").accordion({
			active:false,
			collapsible:true, 
			autoHeight: false,
			icons: { 
				"header": "ui-icon-plusthick", 
				"activeHeader": "ui-icon-minusthick" 
			}
		});

		jQuery("#itens h3").on("click", function() {
			jQuery('#modal_load').show();
			jQuery(this).get(0).scrollIntoView();			
			jQuery('#modal_load').hide();
		});
	});
</script>

<script type="text/javascript">
    var JAVA_URL_BASE = "<?= Core::diretiva('JAVA_URL_BASE'); ?>";     
</script>

<script src="modulos/fluencia_leitora/modal_box_novo_aluno.js"></script>
<script src="modulos/fluencia_leitora/modal_box_anular_aluno.js"></script>
<script src="modulos/fluencia_leitora/modal_box_tutorial.js"></script>
<script src="modulos/fluencia_leitora/modal_box_testerec.js"></script>

<script src="includes/JavaScript/iMask/jquery.mask.js"></script>
<script src="includes/JavaScript/iMask/jquery.maskMoney.js"></script>