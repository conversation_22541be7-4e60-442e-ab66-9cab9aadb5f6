<div id="play_tutorial" class="modal-box" style="width: 70% !important;">
	<header>
		<a href="javascript:closeModalTutorial();" class="js-modal-close close">&times;</a>
		<!-- <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/> -->
		<p style="width: 90%; text-align:left; font-size:18px; opacity:70%">TUTORIAL</p>
	</header>
	<div class="modal-body-novo-aluno">
		<STYLE type="text/css">
			/* * {box-sizing:border-box} */

			/* Slideshow container */
			.slideshow-container {
				border: solid #aaa 1px;
				max-width: 800px;
				position: relative;
				margin: auto;
			}

			/* Hide the images by default */
			.mySlides {
				display: none;
			}

			/* Next & previous buttons */
			.prev, .next {
				cursor: pointer;
				position: absolute;
				top: 50%;
				width: auto;
				margin-top: -22px;
				padding: 10px;
				color: white;
				font-weight: bold;
				font-size: 18px;
				transition: 0.6s ease;
				border-radius: 0 3px 3px 0;
				user-select: none;
				background-color: rgba(0,0,0,0.1);
			}

			/* Position the "next button" to the right */
			.prev {
				left: 0;
				border-radius: 3px 0 0 3px;
			}

			/* Position the "next button" to the right */
			.next {
				right: 0;
				border-radius: 3px 0 0 3px;
			}

			/* On hover, add a black background color with a little bit see-through */
			.prev:hover, .next:hover {
				text-decoration: none;
				background-color: rgba(0,0,0,0.5);
			}

			/* Caption text */
			.text {
				color: #f2f2f2;
				font-size: 15px;
				padding: 8px 12px;
				position: absolute;
				bottom: 8px;
				width: 100%;
				text-align: center;
			}

			/* Number text (1/3 etc) */
			.numbertext {
				color: #aaa;
				font-size: 12px;
				padding: 8px 12px;
				position: absolute;
				top: 0;
				right: 0;
			}

			/* The dots/bullets/indicators */
			.dot {
				cursor: pointer;
				height: 10px;
				width: 10px;
				margin: 0 2px;
				background-color: #bbb;
				border-radius: 50%;
				display: inline-block;
				transition: background-color 0.6s ease;
			}

			.active, .dot:hover {
				background-color: #717171;
			}

			/* Fading animation */
			.fade {
				-webkit-animation-name: fade;
				-webkit-animation-duration: 1.5s;
				animation-name: fade;
				animation-duration: 1.5s;
			}

			@-webkit-keyframes fade {
				from {opacity: .4}
				to {opacity: 1}
			}

			@keyframes fade {
				from {opacity: .4}
				to {opacity: 1}
			}
		</STYLE>

		<div class="slideshow-container">

			<div class="mySlides fade">
			<div class="numbertext">1 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia5.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">2 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia6.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">3 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia7.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">4 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia8.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">5 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia9.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">6 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia10.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">7 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia11.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">8 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia12.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">9 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia13.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">10 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia14.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">11 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia15.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<div class="mySlides fade">
			<div class="numbertext">12 / 12</div>
			<img src="./modulos/fluencia_leitora/tutoriais/tutorial_fluencia16.webp" style="width:100%; height:100%;">
			<!-- <div class="text">Caption Text</div> -->
			</div>

			<!-- Next and previous buttons -->
			<a class="prev" onclick="plusSlides(-1)">&#10094;</a>
			<a class="next" onclick="plusSlides(1)">&#10095;</a>
		</div>
		<br>

		<!-- The dots/circles -->
		<div style="text-align:center">
			<span class="dot" onclick="currentSlide(1)"></span>
			<span class="dot" onclick="currentSlide(2)"></span>
			<span class="dot" onclick="currentSlide(3)"></span>
			<span class="dot" onclick="currentSlide(4)"></span>
			<span class="dot" onclick="currentSlide(5)"></span>
			<span class="dot" onclick="currentSlide(6)"></span>
			<span class="dot" onclick="currentSlide(7)"></span>
			<span class="dot" onclick="currentSlide(8)"></span>
			<span class="dot" onclick="currentSlide(9)"></span>
			<span class="dot" onclick="currentSlide(10)"></span>
			<span class="dot" onclick="currentSlide(11)"></span>
			<span class="dot" onclick="currentSlide(12)"></span>
		</div> 

		<script type="text/javascript">
			var slideIndex = 1;
			showSlides(slideIndex);

			// Next/previous controls
			function plusSlides(n) {
				showSlides(slideIndex += n);
			}

			// Thumbnail image controls
			function currentSlide(n) {
				showSlides(slideIndex = n);
			}

			function showSlides(n) {
				var i;
				var slides = document.getElementsByClassName("mySlides");
				var dots = document.getElementsByClassName("dot");
				if (n > slides.length) {slideIndex = 1}
				if (n < 1) {slideIndex = slides.length}
				for (i = 0; i < slides.length; i++) {
					slides[i].style.display = "none";
				}
				for (i = 0; i < dots.length; i++) {
					dots[i].className = dots[i].className.replace(" active", "");
				}
				slides[slideIndex-1].style.display = "block";
				dots[slideIndex-1].className += " active";
			} 
		</script>
	</div>
	
	<footer>
		<a href="javascript:closeModalTutorial();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
	</footer>
</div>