<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('ord_simulados')->obterSaida(); ?>

<?= Core::modulo('procurar_simulados')->obterSaida(); ?>

<?
if ( $this->_paginacao->paginador->precisaDePaginacao() ) {
?>
	<div class="vd_BlocoBotoes" align="right"><?= $this->_paginacao->obterSaida(); ?></div>
<?
}
?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>  
<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
	  <td width="1%" class="lp_ColHeader"><?= $this->_formulario->obterHTML('selecionar_todos', Formulario::HTML_CAMPO, true); ?></td>
	  <td width="1%" class="lp_ColHeader">ID</td>
	  <td width="25%" class="lp_ColHeader"><?= $this->obterBotaoAlterador('s_nome'); ?></td>
	  <td class="lp_ColHeader" align="center">(ID) Pai Filho</td>
	  <td class="lp_ColHeader" align="center">Série</td>
	  <td class="lp_ColHeader" align="center">Aplicação</td>
	  <td class="lp_ColHeader" align="center">Ordem Lançamento</td>
	  <td class="lp_ColHeader" align="center">Período Aplicação</td>
	  <td class="lp_ColHeader" align="center">Período<br>"Atualiza&#231;&#227;o de Turmas"</td>
	  <td class="lp_ColHeader" align="center">Período Lançamento</td>
	  <td class="lp_ColHeader" align="center"><?= $this->obterBotaoAlterador('numero_inscritos', true); ?></td>
	  <td class="lp_ColHeader" align="center">Questões</td>
	  <td class="lp_ColHeader" align="center">Av. Especial</td>
	  <td class="lp_ColHeader" align="center">Rels. Seção</td>
	</tr>
<?
if ( count($this->_dados) ) {
	$camposIDs = $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true);
	$i = 0;

	foreach ( $this->_dados as &$d ) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
		<td align="center" ><?= $camposIDs[$i++][0]; ?></td>
		<td ><?= $d->obterID(); ?></td>
		<td ><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $d->obterID())); ?>"><?= $d->obterNome(); ?></a></td>
		<td align="center">(<?= $d->obterPaiFilho(); ?>) <?= $d->obterPaiFilho(); ?></td>
		<td align="center"><?php if(!is_null($d->obterSerieAvaliacao())){ echo $d->obterSerieAvaliacao()->obterNome(); } ?></td>
		<td align="center"><?= $d->obterBimestre(); ?></td>
		<td align="center"><?= $d->obterOrdem(); ?></td>
		<td align="center"><?= $d->obterDataRealizacao(true); ?> até <?= $d->obterDataRealizacaoFim(true); ?></td>
		<?php
			$datai = $d->obterDataInicioInscricoes();
			$datai = @strftime('%d/%m/%Y %H:%M', $datai);
			//$datai = substr($datai, 0, 10);
			$dataf = $d->obterDataFimInscricoes();
			$dataf = @strftime('%d/%m/%Y %H:%M', $dataf);
			//$dataf = substr($dataf, 0, 10);
		?>
		<td align="center"><?= $datai; ?> até <?= $dataf; ?></td>
		<td align="center"><?= $d->obterInicioLancamento(true); ?> até <?= $d->obterFimLancamento(true); ?></td>
		<td align="center"><?= $d->obterNumeroInscritosPelaInstituicao(Core::registro('instituicao'), true); ?></td>
		<td align="center"><?= $d->obterNumeroQuestoes(true, true); ?></td>	
		<td align="center"><? if($d->obterAvEspecial()){ echo"Sim"; }else{ echo"Não"; } ?></td>	
		<td align="center"><?= $d->obterSecaoRels(); ?></td>
    </tr>
<?
	}
} else {
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('Nenhuma avaliação encontrado!');
?>
	<tr>
		<td height="200" colspan="6" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
<?
}
?>
</table>
  
  <table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_BlocoBotoes">
    <tr>
      <td>
      	<?= $this->_formulario->obterHTML('remover', Formulario::HTML_CAMPO, true); ?>
      	<?= $this->_formulario->obterHTML('copiar', Formulario::HTML_CAMPO, true); ?>
      	<?= $this->_formulario->obterHTML('copiar_inscricoes', Formulario::HTML_CAMPO, true); ?>
      </td>
<?
	if ( $this->_paginacao->paginador->precisaDePaginacao() ) {
?>
      <td align="right"><?= $this->_paginacao->obterSaida(); ?></td>
<?
	}
?>
    </tr>
</table>
<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>