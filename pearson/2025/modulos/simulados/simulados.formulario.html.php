<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
	if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
	}
?>

	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?>
	<input type="button" onClick="window.location='<?= $this->_formulario->obterEstado() == Formulario::EDITANDO ? Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => $this->seletorVisao->visaoSelecionada()) ) : Gerenciador_URL::gerarLink('simulados', 'listar'); ?>'" value="Cancelar" class="botao cancelar">
	</div>

  <script type="text/javascript">
tinyMCE.init({
  // General options
  //mode : "textareas",
  mode : 'exact',
  elements : 'texto_inicio,texto_fim',
  theme : "advanced",
  plugins : "safari,spellchecker,pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,imagemanager,filemanager",

  // Theme options
  theme_advanced_buttons1 : "bold,italic,underline,|,justifyleft,justifycenter,justifyright,justifyfull,|,fontsizeselect",
  theme_advanced_buttons2 : "pastetext,pasteword,|,bullist,numlist,|,outdent,indent,|,undo,redo,|,link,unlink,image,cleanup,|,forecolor",
  theme_advanced_buttons3 : "tablecontrols,|,sub,sup,|,charmap,media",
  theme_advanced_toolbar_location : "top",
  theme_advanced_toolbar_align : "left",
  theme_advanced_statusbar_location : "bottom",
  theme_advanced_resizing : true
});
</script>

	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="4" class="titulo">Informa&ccedil;&otilde;es da avaliação </th>
        </tr>
         <tr>
          <th><?= $this->_formulario->obterHTML('nome', Formulario::HTML_LABEL, true); ?></th>
          <td width="25%"><?= $this->_formulario->obterHTML('nome', Formulario::HTML_CAMPO, true); ?> </td>
              <th><?= $this->_formulario->obterHTML('pai_filho', Formulario::HTML_LABEL, true); ?></th>
              <td><?= $this->_formulario->obterHTML('pai_filho', Formulario::HTML_CAMPO, true); ?> </td>
         </tr>
          <tr>
              <th><?= $this->_formulario->obterHTML('serie_avaliacao', Formulario::HTML_LABEL, true); ?></th>
              <td width="25%"><?= $this->_formulario->obterHTML('serie_avaliacao', Formulario::HTML_CAMPO, true); ?> </td>
            <th><?= $this->_formulario->obterHTML('ordem', Formulario::HTML_LABEL, true); ?></th>
            <td width="25%"><?= $this->_formulario->obterHTML('ordem', Formulario::HTML_CAMPO, true); ?> </td>
          </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('bimestre', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('bimestre', Formulario::HTML_CAMPO, true); ?> </td>
          <th><?= $this->_formulario->obterHTML('prod_txt', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('prod_txt', Formulario::HTML_CAMPO, true); ?> </td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('data_inicio_inscricoes', Formulario::HTML_LABEL, true); ?></th>
          <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td width="5%" class="sem_estilo"><?= $this->_formulario->obterHTML('data_inicio_inscricoes', Formulario::HTML_CAMPO, true); ?></td>
              <td class="sem_estilo"><?= $this->_formulario->seletoresDatas['data_inicio_inscricoes']['botao']; ?></td>
            </tr>
          </table></td>
          <th><?= $this->_formulario->obterHTML('data_fim_inscricoes', Formulario::HTML_LABEL, true); ?></th>
          <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td width="5%" class="sem_estilo"><?= $this->_formulario->obterHTML('data_fim_inscricoes', Formulario::HTML_CAMPO, true); ?></td>
              <td class="sem_estilo"><?= $this->_formulario->seletoresDatas['data_fim_inscricoes']['botao']; ?></td>
            </tr>
          </table></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('data_realizacao', Formulario::HTML_LABEL, true); ?></th>
          <td>
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td width="5%" class="sem_estilo"><?= $this->_formulario->obterHTML('data_realizacao', Formulario::HTML_CAMPO, true); ?></td>
                <td class="sem_estilo"><?= $this->_formulario->seletoresDatas['data_realizacao']['botao']; ?></td>
              </tr>
            </table>
          </td>
          <th><?= $this->_formulario->obterHTML('data_realizacao_fim', Formulario::HTML_LABEL, true); ?></th>
          <td>
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td width="5%" class="sem_estilo"><?= $this->_formulario->obterHTML('data_realizacao_fim', Formulario::HTML_CAMPO, true); ?></td>
                <td class="sem_estilo"><?= $this->_formulario->seletoresDatas['data_realizacao_fim']['botao']; ?></td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('data_inicio_lancamento', Formulario::HTML_LABEL, true); ?></th>
          <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td width="5%" class="sem_estilo"><?= $this->_formulario->obterHTML('data_inicio_lancamento', Formulario::HTML_CAMPO, true); ?></td>
              <td class="sem_estilo"><?= $this->_formulario->seletoresDatas['data_inicio_lancamento']['botao']; ?></td>
            </tr>
          </table></td>
          <th><?= $this->_formulario->obterHTML('data_fim_lancamento', Formulario::HTML_LABEL, true); ?></th>
          <td><table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td width="5%" class="sem_estilo"><?= $this->_formulario->obterHTML('data_fim_lancamento', Formulario::HTML_CAMPO, true); ?></td>
              <td class="sem_estilo"><?= $this->_formulario->seletoresDatas['data_fim_lancamento']['botao']; ?></td>
            </tr>
          </table></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('duracao', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('duracao', Formulario::HTML_CAMPO, true); ?> dia(s) </td>
          <th><?= $this->_formulario->obterHTML('tipos_provas', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('tipos_provas', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('realizacao_unica', Formulario::HTML_LABEL, true); ?></th>
          <td colspan="1">
            <?= $this->_formulario->obterHTML('realizacao_unica', Formulario::HTML_CAMPO, true); ?>
            <i>Marque essa opção para que a avaliação esteja disponível apenas para 1 realização pelo aluno.</i>
          </td>
          <th><?= $this->_formulario->obterHTML('av_especial', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('av_especial', Formulario::HTML_CAMPO, true); ?> </td>
        </tr>
        <tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('av_secao', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('av_secao', Formulario::HTML_CAMPO, true); ?> </td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('corretores', Formulario::HTML_LABEL, true); ?></th>
          <td>
            <?= $this->_formulario->obterHTML('corretores', Formulario::HTML_CAMPO, true); ?>
            <i id="corr2" style="display:none;">A nota final será resultado da média aritmética das notas dos dois corretores.</i>
            <i id="corr3" style="display:none;">O 3º corretor será considerado somente quando a diferença entre as notas dos 2 primeiros corretores for maior que 20% do total da avaliação.</i>
            <i id="corr4" style="display:none;">Caso a nota do terceiro corretor continue discrepante das notas dos dois primeiros corretores a nota final será atribuida por uma banca.</i>
          <script type="text/javascript">
          jQuery('#corretores').change(function(){
            jQuery('#corr2, #corr3, #corr4').hide();
            var curr = jQuery(this).val();
            jQuery('#corr'+curr).show();
          });
          </script>
          </td>
        </tr>
        <tr>
          <th>Corretores participantes</th>
          <td colspan="3" align="center"><table width="98%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('corr_disp', Formulario::HTML_LABEL, true); ?></th>
              <td>&nbsp;</td>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('corr_sele', Formulario::HTML_LABEL, true); ?></th>            </tr>
            <tr>
              <td><?= $this->_formulario->obterHTML('corr_disp', Formulario::HTML_CAMPO, true); ?></td>
              <td align="center"><div style="margin-bottom:4px;"><?= $this->_formulario->obterHTML('add_corr', Formulario::HTML_CAMPO, true); ?></div><div><?= $this->_formulario->obterHTML('del_corr', Formulario::HTML_CAMPO, true); ?></div></td>
              <td><?= $this->_formulario->obterHTML('corr_sele', Formulario::HTML_CAMPO, true); ?></td>
            </tr>
          </table></td>
        </tr>
<!-- ITENS -->

<tr>
  <th>Itens relacionados ao corretor<br>
    <select size="1" id="corretores_itens" name="corretores_itens">
      <option value="0">Selecione um corretor</option>
    </select>
  </th>

  <td colspan="3" align="center">
    <table id="cblock0" class="descricao" width="98%" border="0" cellpadding="0" cellspacing="0" style="display:none;">
    <tr>
      <td>Selecione algum corretor para relacionar os itens deste simulado.</td>
    </tr>
  </table>
<?php 
$simu_disp = $this->_formulario->_obterArrayProfessores();
foreach ($simu_disp as $spk => $spv)
{
?>
  <table id="cblock<?= $spk; ?>" width="98%" border="0" cellpadding="0" cellspacing="0" style="display:none;">
    <tr>
      <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('corr_disp_'.$spk, Formulario::HTML_LABEL, true); ?></th>
      <td>&nbsp;</td>
      <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('corr_sele_'.$spk, Formulario::HTML_LABEL, true); ?></th>            </tr>
    <tr>
      <td><?= $this->_formulario->obterHTML('corr_disp_'.$spk, Formulario::HTML_CAMPO, true); ?></td>
      <td align="center">
        <div style="margin-bottom:4px;">
          <?= $this->_formulario->obterHTML('add_corr_todos_'.$spk, Formulario::HTML_CAMPO, true); ?>
          <br><br><br><?= $this->_formulario->obterHTML('add_corr_'.$spk, Formulario::HTML_CAMPO, true); ?>
        </div>
        <div>
          <?= $this->_formulario->obterHTML('del_corr_'.$spk, Formulario::HTML_CAMPO, true); ?><br><br><br>
          <?= $this->_formulario->obterHTML('del_corr_todos_'.$spk, Formulario::HTML_CAMPO, true); ?>
        </div>
      </td>
      <td><?= $this->_formulario->obterHTML('corr_sele_'.$spk, Formulario::HTML_CAMPO, true); ?></td>
    </tr>
  </table>
<?php 
  echo JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('corr_disp_'.$spk.'[]', 'corr_sele_'.$spk.'[]'));
}
?>  
  </td>
</tr>

<!-- ITENS -->
        <tr>
          <th><?= $this->_formulario->obterHTML('linguas_compartilham', Formulario::HTML_LABEL, true); ?></th>
          <td colspan="3"><?= $this->_formulario->obterHTML('linguas_compartilham', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
<?
        /*<tr>
          <th>Turmas participantes</th>
          <td colspan="3" align="center"><table width="98%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('turmas_disp', Formulario::HTML_LABEL, true); ?></th>
              <td>&nbsp;</td>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('turmas_sele', Formulario::HTML_LABEL, true); ?></th>            </tr>
            <tr>
              <td><?= $this->_formulario->obterHTML('turmas_disp', Formulario::HTML_CAMPO, true); ?></td>
              <td align="center"><div style="margin-bottom:4px;"><?= $this->_formulario->obterHTML('add_turma', Formulario::HTML_CAMPO, true); ?></div><div><?= $this->_formulario->obterHTML('del_turma', Formulario::HTML_CAMPO, true); ?></div></td>
              <td><?= $this->_formulario->obterHTML('turmas_sele', Formulario::HTML_CAMPO, true); ?></td>
            </tr>
          </table></td>
        </tr>*/
?>
        <tr>
          <th><?= $this->_formulario->obterHTML('texto_inicio', Formulario::HTML_LABEL, true); ?></th>
          <td colspan="3" align="center">
            <?= $this->_formulario->obterHTML('texto_inicio', Formulario::HTML_CAMPO, true); ?>
          </td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('texto_fim', Formulario::HTML_LABEL, true); ?></th>
          <td colspan="3" align="center">
            <?= $this->_formulario->obterHTML('texto_fim', Formulario::HTML_CAMPO, true); ?>
          </td>
        </tr>
      </table>

	<?= $this->_formulario->seletoresDatas['data_realizacao']['html']; ?>
  <?= $this->_formulario->seletoresDatas['data_realizacao_fim']['html']; ?>
	<?= $this->_formulario->seletoresDatas['data_inicio_inscricoes']['html']; ?>
	<?= $this->_formulario->seletoresDatas['data_fim_inscricoes']['html']; ?>
  <?= $this->_formulario->seletoresDatas['data_inicio_lancamento']['html']; ?>
  <?= $this->_formulario->seletoresDatas['data_fim_lancamento']['html']; ?>

  <?= Core::modulo('_d_simulados')->obterSaida(); ?>

	<?= Core::modulo('_endereco')->obterSaida(); ?>

	</td>
  </tr>
</table>

<script type="text/javascript">
  jQuery('[id^="add_corr_todos_"]').click(function(){
    var id = jQuery(this).attr('id').replace("add_corr_todos_","");
    
    jQuery('[id="corr_disp_'+id+'[]"] option').each(function(){
      jQuery(this).attr('selected',true);
    });

    jQuery('#add_corr_'+id).click();    
  });

  jQuery('[id^="del_corr_todos_"]').click(function(){
    var id = jQuery(this).attr('id').replace("del_corr_todos_","");
    
    jQuery('[id="corr_sele_'+id+'[]"] option').each(function(){
      jQuery(this).attr('selected',true);
    });

    jQuery('#del_corr_'+id).click();  
  });

  jQuery('[id="corr_sele[]"], [id="corr_disp[]"]').change(function(){
    jQuery('[id^="cblock"]').hide(); jQuery('#cblock0').show();
    jQuery('#corretores_itens').html('<option value="0">Selecione um corretor</option>'+jQuery('[id="corr_sele[]"]').html());
  }).change();

  jQuery('#add_corr, #del_corr').click(function(){
    jQuery(this).mouseleave(function(){
      jQuery('[id^="cblock"]').hide(); jQuery('#cblock0').show();
      jQuery('#corretores_itens').html('<option value="0">Selecione um corretor</option>'+jQuery('[id="corr_sele[]"]').html());
    });
  }); 

  jQuery('#corretores_itens').change(function(){
    jQuery('[id^="cblock"]').hide();
    var idBS = jQuery('#corretores_itens :selected').val();
    jQuery('#cblock'+idBS).show();
  }).change();

  jQuery(document).ready(function() {
    jQuery('[id^="cblock"]').hide(); jQuery('#cblock0').show();
    jQuery('#corretores_itens').html('<option value="0">Selecione um corretor</option>'+jQuery('[id="corr_sele[]"]').html());
  });
</script>

<?= $this->_formulario->obterHTML('id', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>

<? //echo JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('turmas_disp[]', 'turmas_sele[]')); ?>
<?= JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('corr_disp[]', 'corr_sele[]')); ?>