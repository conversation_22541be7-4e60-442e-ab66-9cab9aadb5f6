<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class FImportarAddPdf_Exception extends Formulario_Exception { }

class FImportarAddPdf extends Formulario
{
	public function carregarFormulario ()
	{
		parent::carregarFormulario();
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo_1',
												'etiqueta' => 'Arquivo PDF #1',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::ARQUIVO,
												'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'pdf'),
												'html_tipo' => Campo::HTML_ARQUIVO,
												'html_tamanho' => 550,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_ordem' => 1
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo_2',
												'etiqueta' => 'Arquivo PDF #2 (MERGED #1 + #2)',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::ARQUIVO,
												'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'pdf'),
												'html_tipo' => Campo::HTML_ARQUIVO,
												'html_tamanho' => 550,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_ordem' => 2
							  )) );

		$simuladosPossiveis = Simulado::obterSimuladosParaFormulario(true);
		$simuladoSel = null;
		if (!$this->foiEnviado() && !isset($_POST['post_anterior']) && Core::diretiva('_importador_pdf.simulado.ultima_selecao') != false && array_key_exists(Core::diretiva('_importador_pdf.simulado.ultima_selecao'), $simuladosPossiveis))
			$simuladoSel = Core::diretiva('_importador_pdf.simulado.ultima_selecao');

		$this->adicionarCampo( new Campo(array( 'nome' => 'simulado',
												'etiqueta' => 'Prova',
												'valor' => $simuladoSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosPossiveis)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $simuladosPossiveis,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
						  	)) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Enviar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 3
							  )) );
							  
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e){		
			throw new FImportarAddPdf_Exception($e->getMessage());
		}
	}
	
	public function executar ()
	{
		if ( $this->_adicionar() ) {
			Core::modulo('redirecionador')->fixarMensagem('Pdf adicionado com sucesso!', 'Upload Pdf...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} 
		else {
			Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o Pdf!', 'Upload Pdf...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
		}
		
		return $this->_dados;
	}
	
	protected function _adicionar ()
	{
		$simulado = $this->_campos['simulado']->obter('valor');
		$path = str_replace('modulos/simulados/add_pdf', 'upload/simulados/'.$simulado.'/', realpath(dirname(__FILE__)));

		@mkdir($path, 0777, true); 
		@chmod($path, 0777);

		if (file_exists($path)){
			$arquivo1 = $this->_campos['arquivo_1']->obter('valor');
			$aext1 = $arquivo1[0]['tipo'];
			$tmp1 = $arquivo1[0]['nome_temporario'];
			$nome1 = str_replace(' ', '', $arquivo1[0]['nome']);
			$dir1 = $path.$nome1;

			$arquivo2 = $this->_campos['arquivo_2']->obter('valor');
			$aext2 = $arquivo2[0]['tipo'];
			$tmp2 = $arquivo2[0]['nome_temporario'];
			$nome2 = str_replace(' ', '', $arquivo2[0]['nome']);
			$dir2 = $path.$nome2;

			if($aext1 != 'application/pdf' || ($arquivo2[0]['erro'] == 0 && $aext2 != 'application/pdf')){
				$this->_adicionarErro('tipo', 'O arquivo precisa ser PDF;');
				throw new FQuestoes_Exception('O arquivo precisa ser PDF');
				return false;
			}

			$it1 = new RecursiveDirectoryIterator($path);
			foreach(new RecursiveIteratorIterator($it1) as $file) {
				@chmod(dirname($file), 0777);
				@chmod($file, 0777);
				@unlink($file);
			}

			$go = false;
			if($arquivo2[0]['erro'] == 0){
				if((is_uploaded_file($tmp1) && move_uploaded_file($tmp1, $dir1) !== false) && (is_uploaded_file($tmp2) && move_uploaded_file($tmp2, $dir2) !== false)){
					@chmod($dir2, 0777);

					$nome1 = str_replace('_', '', $nome1);
					$nome1 = str_replace('.pdf', '', $nome1);
					$nome2 = str_replace('_', '', $nome2);
					$nome2 = str_replace('.pdf', '', $nome2);

					$htmlMerged = $nome1.'_'.$nome2.'_MERGED.pdf';
					$cmdM = '/usr/bin/pdfunite '.$dir1.' '.$dir2.' '.$path.$htmlMerged.';';
					$PIDM = shell_exec($cmdM);

					$go = true;

					$nome1 = $htmlMerged;
					$dir1 = $path.$nome1;
				}
			}
			else{
				if(is_uploaded_file($tmp1) && move_uploaded_file($tmp1, $dir1) !== false){
					$go = true;
				}
			}

			if($go){
				@chmod($dir1, 0777);
				$htmlOut = str_replace('.pdf', '.html', $dir1);
				//$cmd = '/usr/bin/pdftohtml -s -enc Latin1 -c '.$dir1.' '.$htmlOut.';';
				$cmd = '/usr/bin/pdftohtml -s -enc UTF-8 -c '.$dir1.' '.$htmlOut.';';

				$PID = shell_exec($cmd);

				$check = @glob($path."*.html");
				if(count($check)>0){
					$dom = new DOMDocument;
					$dom->loadHTMLFile($check[0]);
					$xpath = new DOMXPath($dom);
					$imgs = $xpath->query('//img');
					foreach($imgs as $img) {
						$src = $img->getAttribute('src');
						$img->setAttribute('src', './upload/simulados/'.$simulado.'/'.$src);
					}
					$dom->saveHTMLFile($check[0]);

					return true;
				}
				return false;
			}
			else{
				$this->_adicionarErro('tipo', 'Erro ao fazer upload;');
				throw new FQuestoes_Exception('Erro ao fazer upload');
				return false;
			}
		}
		else{
			$this->_adicionarErro('tipo', 'Erro ao gerar path;');
			throw new FQuestoes_Exception('Erro ao gerar path');
			return false;
		}
	}
}

?>