<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio">
<?
if ($this->_formulario->obterEstado() == Formulario::ADICIONANDO) {
	echo '<strong>Upload Pdf de Avaliação</strong>';
} 
?>
</div>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th class="selecionada">Upload Pdf</th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>
	
	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	
	<input type="button" onClick="window.location='<?= $this->_formulario->obterEstado() == Formulario::EDITANDO ? Gerenciador_URL::gerarLink('downloads', 'detalhar', array('id' => $this->_formulario->obterDados()->obterID())) : Gerenciador_URL::gerarLink('downloads', 'listar'); ?>'" value="Cancelar" class="botao cancelar">
	</div>

  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
	<tr>
	  <th colspan="4" class="titulo">Formulário</th>
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('arquivo_1', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="75%" colspan="3"><?= $this->_formulario->obterHTML('arquivo_1', Formulario::HTML_CAMPO, true); ?> 
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('arquivo_2', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="75%" colspan="3"><?= $this->_formulario->obterHTML('arquivo_2', Formulario::HTML_CAMPO, true); ?> 
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('simulado', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="75%" colspan="3"><?= $this->_formulario->obterHTML('simulado', Formulario::HTML_CAMPO, true); ?> 
	</tr>
  </table>
 	</td>
  </tr>
</table>	
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>

<?= JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('grupos_disp[]', 'grupos_sele[]')); ?>