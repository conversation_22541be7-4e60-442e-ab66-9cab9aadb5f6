<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('ord_inscricoes')->obterSaida(); ?>

<?= Core::modulo('procurar_inscricoes')->obterSaida(); ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
	<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		<tr>
			<td><strong>Inscrições</strong></td>
			<td align="right"><?= Core::modulo('navegador_inscricoes')->obterSaida(); ?></td>
		</tr>
	</table>

<?
if ( count($this->_dados) ) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
			<td width="1%" class="lp_ColHeader"><?= $this->_formulario->obterHTML('selecionar_todos', Formulario::HTML_CAMPO, true); ?></td>
			<td width="25%" nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('usuarios.u_nome'); ?></td>
			<td width="15%" nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('alunos.a_matricula'); ?></td>
			<td width="10%" nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('instituicoes.i_nome'); ?></td>
			<td width="10%" nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('alunos.a_turma'); ?></td>
			<td nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('si_integral'); ?></td>
			<td nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('si_nao_alfabetico'); ?></td>
			<td nowrap="nowrap" class="lp_ColHeader">Ausente?</td>
			<? if ( $this->_simulado->obterNumeroDeTipos() > 1 ) { ?><td align="center" nowrap="nowrap" class="lp_ColHeader"><?= $this->obterBotaoAlterador('si_tipo', true); ?></td><? } ?>
		</tr>
<?
	$camposIDs = $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true);
	$i = 0;
	foreach ( $this->_dados as &$d ) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
		<td align="center" ><?= $camposIDs[$i++][0]; ?></td>
		<td ><a title="Editar inscrição &amp; respostas" href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $d->obterSimulado()->obterID(), 'id_inscricao' => $d->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)); ?>"><?= $d->obterAluno()->obterUsuario()->obterNome(); ?></a></td>
		<td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('alunos', 'detalhar', array('id' => $d->obterAluno()->obterID())); ?>"><?= $d->obterAluno()->obterMatricula(); ?></a></td>
		<td>
			<?php 
				$d->obterAluno()->obterUsuario()->carregar();
				$d->obterAluno()->obterUsuario()->obterInstituicao()->carregar();
				echo $d->obterAluno()->obterUsuario()->obterInstituicao()->obterNome(); 
			?>
		</td>
		<td>
			<a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $d->obterAluno()->obterTurma()->obterID())); ?>">
				<?= $d->obterAluno()->obterTurma()->obterNome(); ?>
			</a> 
			(<?php 
				$d->obterAluno()->obterTurma()->carregar();
				echo $d->obterAluno()->obterTurma()->obterSerie()->obterNome(); 
			?>)
		</td>
		<td><? if($d->obterEdIntegral()){echo"Sim";}else{echo"Não";} ?></td>
		<td><? if($d->obterNaoAlfabetico()){echo"Sim";}else{echo"Não";} ?></td>
		<td><? if($d->obterAusente()){echo"Sim";}else{echo"Não";} ?></td>
		<? if ( $this->_simulado->obterNumeroDeTipos() > 1 ) { ?><td align="center"><?= $d->obterTipo(); ?></td><? } ?>
    </tr>
<?
	}
?>
</table>

	<table width="100%" border="0" cellspacing="3" cellpadding="0">
		<tr>
			<td><?= $this->_formulario->obterHTML('remover', Formulario::HTML_CAMPO, true); ?> 
		  <?= $this->_formulario->obterHTML('editar_em_ordem', Formulario::HTML_CAMPO, true); ?> 
		  <?= $this->_formulario->obterHTML('cadastrar_respostas', Formulario::HTML_CAMPO, true); ?></td>
		  <td align="right"><?= $this->_paginacao->paginador->precisaDePaginacao() ? $this->_paginacao->obterSaida() : null; ?></td>
		</tr>
	</table>
	
<?
} else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
			<td><em>Nenhuma inscrição encontrada </em></td>
		</tr>
	</table>
<?
}
?>

<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>