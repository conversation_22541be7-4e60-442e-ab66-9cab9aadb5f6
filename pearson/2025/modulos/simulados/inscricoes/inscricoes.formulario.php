<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Inscricao', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('JDesabilitadorDeCampos', 'JavaScript/JDesabilitadorDeCampos/', true);
Core::incluir('JTransferidorDeItensDeMenu', 'JavaScript/JTransferidorDeItensDeMenu/', true);
Core::incluir('JMesclador', 'JavaScript/JMesclador/', true);
Core::incluir('JMascaradorDeCamposDeQuestao', 'JavaScript/JMascaradorDeCamposDeQuestao/', true);

class FInscricoes_Exception extends Formulario_Exception { }

class FInscricoes extends Formulario
{	
	const FALTA_NADA = 0;
	const FALTA_TIPO = 1;
	const FALTA_LINGUA = 2;
	const FALTA_TIPO_E_LINGUA = 3;

	public $podeEditarRespostas = self::FALTA_NADA;
	public $questoes = array();

	protected $_simulado;

	public function carregarFormulario (Simulado &$simu) {
		parent::carregarFormulario();

		$this->_simulado = $simu;

		$this->_carregar();


		if ($this->obterEstado() == self::ADICIONANDO) {
			$alunos_disp = $this->_obterArrayAlunos();			
			$alunos_sele = array();
			
			$this->adicionarCampo( new Campo(array( 'nome' => 'alunos_disp',
													'etiqueta' => 'Alunos disponíveis',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($alunos_disp)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => array(),
													'html_varias_selecoes' => true,
													'html_linhas' => 5,
													'html_array' => true
								  )) );
								  
			$this->adicionarCampo( new Campo(array( 'nome' => 'alunos_sele',
													'etiqueta' => 'Alunos selecionadas',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($alunos_disp)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => array(),
													'html_varias_selecoes' => true,
													'html_linhas' => 5,
													'html_array' => true
								  )) );

			$alunos_sele_IDs = $this->_campos['alunos_sele']->obter('valor');
			if (!is_array($alunos_sele_IDs)) { $alunos_sele_IDs = array(); }

			foreach ($alunos_sele_IDs as $k => $aID) {
				if ( array_key_exists($aID, $alunos_disp) ) {
					$alunos_sele[$aID] = $alunos_disp[$aID];
				} else {
					unset($alunos_sele_IDs[$k]);
				}
			}

			$alunos_disp_finais = array();

			foreach ($alunos_disp as $aID => $aNome) {
				if ( !array_key_exists($aID, $alunos_sele) )
					$alunos_disp_finais[$aID] = $aNome;
			}

			$this->_campos['alunos_sele']->fixar('html_valor', $alunos_sele);
			$this->_campos['alunos_disp']->fixar('html_valor', $alunos_disp_finais);

			$this->adicionarCampo( new Campo(array( 'nome' => 'add_aluno',
													'etiqueta' => 'Adicionar >>',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_tamanho' => 100,
													'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
													'html_classe' => 'botao salvar',
													'componente' => new JTransferidorDeItensDeMenu('alunos_disp[]', 'alunos_sele[]')
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'del_aluno',
													'etiqueta' => '<< Remover',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tamanho' => 100,
													'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao cancelar',
													'componente' => new JTransferidorDeItensDeMenu('alunos_sele[]', 'alunos_disp[]')
								  )) );

			if ( !count($alunos_disp) ) {
				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Core::modulo('redirecionador')->fixarMensagem('Não há mais alunos para serem inscritos!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_ALERTA);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)) );
				Core::modulo('redirecionador')->redirecionar();
			}
		} else {
			$aluno = array( $this->_dados->obterAluno()->obterID() => $this->_dados->obterAluno()->obterUsuario()->obterNome() );
			
			$this->adicionarCampo( new Campo(array( 'nome' => 'aluno',
													'etiqueta' => 'Aluno',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::INTEIRO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($aluno)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $aluno,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
													'html_ordem' => 1,
													'html_ativo' => false
								  )) );
		}
		
		$componenteTipoELingua = new JMesclador('onchange="%s"');
		$componenteTipoELingua->adicionarComponente( new JAlteradorDeFormulario($this->acoesPosEnvio->obterNomeRedirecionadorRapido(), 1, $this->_info['nome'], false) );
		$componenteTipoELingua->adicionarComponente( new JAlteradorDeFormulario($this->acoesPosEnvio->obterNomeSeletorAcoes(), 'editar', $this->_info['nome'], true) );

		$tipo = array( 'NULO' => '' );
		for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++) {
			$tipo[$i] = $i;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'tipo',
												'etiqueta' => 'Tipo de prova',
												'valor' => (!$this->foiEnviado() ? $this->_dados->obterTipo() : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($tipo)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $tipo,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO ,
												'html_ordem' => 2
							  )) );

		if ( $this->obterEstado() == Formulario::ADICIONANDO || $this->_simulado->obterNumeroDeTipos() <= 1 ) {
			$this->_campos['tipo']->fixar('html_ativo', false);
		} else {
			$this->_campos['tipo']->fixar('componente', $componenteTipoELingua );
		}

		// checa se pode editar respostas
		if ( $this->_simulado->obterNumeroDeTipos() > 1 && $this->_campos['tipo']->obter('valor') < 1 ) {
			$this->podeEditarRespostas = self::FALTA_TIPO;
		}
		
		$cursos = array( 'NULO' => '' );
		$cursoSel = null;

		foreach ( CursoVestibular::obterArrayCursosVestibularParaFormulario() as $cID => $cNome ) {
			$cursos[$cID] = $cNome;
		}

		if ( !$this->foiEnviado() && $this->_dados->obterCursoVestibular() != null && array_key_exists( $this->_dados->obterCursoVestibular()->obterID(), $cursos ) ) {
			$cursoSel = $this->_dados->obterCursoVestibular()->obterID();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'curso',
												'etiqueta' => 'Curso de vestibular',
												'valor' => $cursoSel,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($cursos)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $cursos,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 3
							  )) );

		$linguas = array( 'NULO' => '' );
		$linguaSel = null;

		foreach ( Disciplina::obterArrayDisciplinasParaFormulario(true) as $dID => $dNome ) {
			$linguas[$dID] = $dNome;
		}
		
		if ( !$this->foiEnviado() && $this->_dados->obterLingua() != null && array_key_exists( $this->_dados->obterLingua()->obterID(), $linguas ) ) {
			$linguaSel = $this->_dados->obterLingua()->obterID();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'lingua',
												'etiqueta' => 'Língua estrangeira',
												'valor' => $linguaSel,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($linguas)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $linguas,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 4
							  )) );

		if ( !$this->_simulado->temQuestoesLinguaEstrangeira() ) {
			$this->_campos['lingua']->fixar('html_ativo', false);
			$this->_campos['lingua']->fixar('valor', null);
		} else if ( $this->obterEstado() == Formulario::EDITANDO ) {
			$this->_campos['lingua']->fixar('componente', $componenteTipoELingua );
		}



		$optEI = array('0'=>'Não','1'=>'Sim');
		$optEISel = $this->_dados->obterEdIntegral();

		$this->adicionarCampo( new Campo(array( 'nome' => 'ed_integral',
										'etiqueta' => 'Ed. Integral',
										'valor' => $optEISel,
										'requerimento' => Campo::OPCIONAL,
										'tipo' => Campo::TEXTO,
										'argumentos' => array(Campo::POSSIBILIDADES => array_keys($optEI)),
										'html_tipo' => Campo::HTML_MENU,
										'html_valor' => $optEI,
										'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
										'html_ordem' => 4
					  )) );



		$optNA = array('0'=>'Não','1'=>'Sim');
		$optNASel = $this->_dados->obterNaoAlfabetico();

		$this->adicionarCampo( new Campo(array( 'nome' => 'nao_alfabetico',
										'etiqueta' => 'Não Alfabético',
										'valor' => $optNASel,
										'requerimento' => Campo::OPCIONAL,
										'tipo' => Campo::TEXTO,
										'argumentos' => array(Campo::POSSIBILIDADES => array_keys($optNA)),
										'html_tipo' => Campo::HTML_MENU,
										'html_valor' => $optNA,
										'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
										'html_ordem' => 4
					  )) );

		$optEI2 = array('0'=>'Não','1'=>'Sim');
		$optEISel2 = $this->_dados->obterAtestado();

		$this->adicionarCampo( new Campo(array( 'nome' => 'atestado',
										'etiqueta' => 'Atestado?',
										'valor' => $optEISel2,
										'requerimento' => Campo::OPCIONAL,
										'tipo' => Campo::TEXTO,
										'argumentos' => array(Campo::POSSIBILIDADES => array_keys($optEI2)),
										'html_tipo' => Campo::HTML_MENU,
										'html_valor' => $optEI2,
										'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
										'html_ordem' => 4
					  )) );

		$optEI3 = array('0'=>'Não','1'=>'Sim');
		$optEISel3 = $this->_dados->obterSimuladoFinalizado();

		$this->adicionarCampo( new Campo(array( 'nome' => 'rfinalizado',
										'etiqueta' => 'Finalizado?',
										'valor' => $optEISel3,
										'requerimento' => Campo::OPCIONAL,
										'tipo' => Campo::TEXTO,
										'argumentos' => array(Campo::POSSIBILIDADES => array_keys($optEI3)),
										'html_tipo' => Campo::HTML_MENU,
										'html_valor' => $optEI3,
										'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
										'html_ordem' => 4
					  )) );

        $this->adicionarCampo( new Campo(array( 'nome' => 'rdata',
            'etiqueta' => 'Data realização',
            'requerimento' => Campo::OPCIONAL,
            'tipo' => Campo::TEXTO,
			'html_ativo' => 0,
            'html_tamanho_maximo' => 50,
            'html_tipo' => Campo::HTML_TEXTO,
            'html_valor' => $this->_dados->obterSimuladoFinalizadoData(),
            'html_ordem' => 1
        )) );

        $this->adicionarCampo( new Campo(array( 'nome' => 'rtempo',
            'etiqueta' => 'Tempo realizado',
            'requerimento' => Campo::OPCIONAL,
            'tipo' => Campo::TEXTO,
			'html_ativo' => 0,
            'html_tamanho_maximo' => 50,
            'html_tipo' => Campo::HTML_TEXTO,
            'html_valor' => $this->_dados->obterTempoTotalDeRealizacaoDoAlunoNoSimulado(),
            'html_ordem' => 1
        )) );

		// checa se pode editar respostas
		if ( $this->_simulado->temQuestoesLinguaEstrangeira() && ($this->_campos['lingua']->obter('valor') == 'NULA' || !array_key_exists($this->_campos['lingua']->obter('valor'), $linguas)) ) {
			$this->podeEditarRespostas = $this->podeEditarRespostas != self::FALTA_TIPO ? self::FALTA_LINGUA : self::FALTA_TIPO_E_LINGUA;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'alterar_respostas',
												'etiqueta' => 'Alterar respostas',
												'valor' => (!$this->foiEnviado() ? 0 : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => 5
							  )) );		

		$ordem = 7;	  

		// RESPOSTAS MARCADAS
		if ( $this->obterEstado() == self::EDITANDO && $this->podeEditarRespostas == self::FALTA_NADA )	{
			$nomesCamposRespostas = array();
			$this->questoes = $this->_simulado->obterQuestoesPeloInscritoParaFormulario($this->_dados);

			// serve para questões discursivas
			$respostas_discursivas = array( 'NULO' => '' );
			for ($i = 0; $i <= 100; $i++) {
				$respostas_discursivas[$i] = $i;
			}
			
			$respostas_marcadas = array();			
			if ( $this->obterEstado() == self::EDITANDO ) {
				foreach ( $this->_dados->obterRespostasMarcadas() as $r ) {
					$respostas_marcadas[ (int) $r->obterQuestao()->obterID() ] = $r;
				}
			}

			foreach ($this->questoes as $fase => $questoes) {
				$this->_dados->verificarAusentePorFases();
				$ausencias = $this->_dados->obterAusentePorFases();

				$ausente = 0;
				if(!array_key_exists($fase, $ausencias)){
					$ausente = 1;
				}

				$this->adicionarCampo( new Campo(array( 'nome' => 'flag_ausente_'.$fase,
												'etiqueta' => 'Ausente em todas as fases',
												'valor' => (!$this->foiEnviado() ? $ausente : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => 6
							  )) );

				foreach ($questoes as $numero => &$q) {
					$nomesCamposRespostas[] = 'resposta_marcada_'. $fase .'_'. $numero;
					if ( $q->obterTipo() == Questao::MULTIPLAESCOLHA ) {
						$valorPadrao = null;
						if ( !$this->foiEnviado() && isset($respostas_marcadas[ $q->obterID() ]) )
							$valorPadrao = MultiplaEscolha::letra( $respostas_marcadas[ $q->obterID() ]->obterValor() );
							
						$this->adicionarCampo( new Campo(array( 'nome' => 'resposta_marcada_'. $fase .'_'. $numero,
																'etiqueta' => $q->obterIdentificador(),
																'requerimento' => Campo::OPCIONAL,
																'tipo' => Campo::TEXTO,
																'argumentos' => array(Campo::POSSIBILIDADES => MultiplaEscolha::$opcoes, Campo::DEPENDE => array('alterar_respostas')),
																'html_tipo' => Campo::HTML_TEXTO,
																'html_valor' => $valorPadrao,
																'html_tamanho_maximo' => 1,
																'html_tamanho' => 2,
																'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
																//'componente' => new JSComponente_Limitador_Digitacao(MultiplaEscolha::arrayParaLimitador()),
																'html_ativo' => (bool) @$this->_campos['alterar_respostas']->obter('valor'),
																'html_ordem' => $ordem++
											  )) );
					} else if ( $q->obterTipo() == Questao::DISCURSIVA || $q->obterTipo() == Questao::FLUENCIA) {
						$valorPadrao = null;
						if ( !$this->foiEnviado() && isset($respostas_marcadas[ $q->obterID() ]) && $respostas_marcadas[ $q->obterID() ]->obterValor() != null )
							$valorPadrao = sprintf('%0.02f', $respostas_marcadas[ $q->obterID() ]->obterValor());
							
						$this->adicionarCampo( new Campo(array( 'nome' => 'resposta_marcada_'. $fase .'_'. $numero,
																'etiqueta' => 'Resposta marcada '. $numero,
																'requerimento' => Campo::OPCIONAL,
																'tipo' => Campo::REAL,
																'html_tamanho_maximo' => 5,
																'html_tamanho' => 5,
																'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
																'argumentos' => array(Campo::DEPENDE => array('alterar_respostas'), Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
																'html_tipo' => Campo::HTML_TEXTO,
																'html_valor' => $valorPadrao,
																'html_ativo' => (bool) @$this->_campos['alterar_respostas']->obter('valor'),
																'html_ordem' => $ordem++
											  )) );
					} else {
						$valorPadrao = null;
						if ( !$this->foiEnviado() && isset($respostas_marcadas[ $q->obterID() ]) && $respostas_marcadas[ $q->obterID() ]->obterValor() != null )
							$valorPadrao = sprintf('%02d', $respostas_marcadas[ $q->obterID() ]->obterValor());

						$this->adicionarCampo( new Campo(array( 'nome' => 'resposta_marcada_'. $fase .'_'. $numero,
																'etiqueta' => 'Resposta marcada '. $numero,
																'requerimento' => Campo::OPCIONAL,
																'tipo' => Campo::REAL,
																'html_tamanho_maximo' => 2,
																'html_tamanho' => 2,
																'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
																'argumentos' => array(Campo::DEPENDE => array('alterar_respostas'), Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
																'html_tipo' => Campo::HTML_TEXTO,
																'html_valor' => $valorPadrao,
																'html_ativo' => (bool) @$this->_campos['alterar_respostas']->obter('valor'),
																'html_ordem' => $ordem++
											  )) );
					}

					$statusVALUE = '';
					$chuteVALUE = 0;
					$tempoVALUE = '';
					$nivelVALUE = 0;
					$nivelArr = array(1=>'Alto',2=>'Médio',3=>'Baixo');
					$comentarioVALUE = '';
					$origemVALUE = '';
					$origemUpdateVALUE = '';

					if(isset($respostas_marcadas[$q->obterID()])){
						$respostas_marcadas[$q->obterID()]->carregar();

						$statusVALUE = $respostas_marcadas[$q->obterID()]->obterStatus();
					
						$chuteVALUE = $respostas_marcadas[$q->obterID()]->obterChute();
						if($chuteVALUE){
							$chuteVALUE = 'Sim';
						}
						else{
							$chuteVALUE = 'Não';
						}

						$tempoVALUE = $respostas_marcadas[$q->obterID()]->obterTempo();

						$nivelVALUE = $respostas_marcadas[$q->obterID()]->obterNivelSeguranca();
						$nivelVALUE = $nivelArr[$nivelVALUE];

						$comentarioVALUE = $respostas_marcadas[$q->obterID()]->obterComentarioAluno();
						$origemVALUE =  $respostas_marcadas[$q->obterID()]->obterOrigem();
						$origemUpdateVALUE =  $respostas_marcadas[$q->obterID()]->obterOrigemUpdate();
					}

					$this->adicionarCampo( new Campo(array( 'nome' => 'status_'. $fase .'_'. $numero,
															'etiqueta' => 'Status '. $numero,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_TEXTO,
															'html_valor' => $statusVALUE,
															'html_ativo' => 0,
															'html_ordem' => $ordem++
										  )) );

					$this->adicionarCampo( new Campo(array( 'nome' => 'chute_'. $fase .'_'. $numero,
															'etiqueta' => 'Chute '. $numero,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_TEXTO,
															'html_valor' => $chuteVALUE,
															'html_ativo' => 0,
															'html_ordem' => $ordem++
										  )) );

					$this->adicionarCampo( new Campo(array( 'nome' => 'tempo_'. $fase .'_'. $numero,
															'etiqueta' => 'Tempo Realizado '. $numero,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_TEXTO,
															'html_valor' => $tempoVALUE,
															'html_ativo' => 0,
															'html_ordem' => $ordem++
										  )) );

					$this->adicionarCampo( new Campo(array( 'nome' => 'nivelseg_'. $fase .'_'. $numero,
															'etiqueta' => 'Nivel de Segurança '. $numero,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_TEXTO,
															'html_valor' => $nivelVALUE,
															'html_ativo' => 0,
															'html_ordem' => $ordem++
										  )) );

					$this->adicionarCampo( new Campo(array( 'nome' => 'comentario_'. $fase .'_'. $numero,
															'etiqueta' => 'Comentario '. $numero,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_TEXTO,
															'html_valor' => $comentarioVALUE,
															'html_ativo' => 0,
															'html_ordem' => $ordem++
										  )) );

						$this->adicionarCampo( new Campo(array( 'nome' => 'origem_'. $fase .'_'. $numero,
										  'etiqueta' => 'Origem '. $numero,
										  'requerimento' => Campo::OPCIONAL,
										  'tipo' => Campo::TEXTO,
										  'html_tipo' => Campo::HTML_TEXTO,
										  'html_valor' => $origemVALUE,
										  'html_ativo' => 0,
										  'html_ordem' => $ordem++
						)) );

						$this->adicionarCampo( new Campo(array( 'nome' => 'origem_update_'. $fase .'_'. $numero,
										  'etiqueta' => 'Origem Atualizada '. $numero,
										  'requerimento' => Campo::OPCIONAL,
										  'tipo' => Campo::TEXTO,
										  'html_tipo' => Campo::HTML_TEXTO,
										  'html_valor' => $origemUpdateVALUE,
										  'html_ativo' => 0,
										  'html_ordem' => $ordem++
						)) );

					/*
					// Configura as mascaras de input
					switch ( $q->obterTipo() ) {
						case Questao::SOMATORIO:
							$this->_campos['resposta_marcada_'. $fase .'_'. $numero]->fixar('componente', new JMascaradorDeCamposDeQuestao(array(
																								'type' => 'fixed',
																							    'mask' => '99',
																							    'stripMask' => false  )));
							break;
						case Questao::MULTIPLAESCOLHA:
							
							break;
					}
					*/
				}
			}
			
			$this->_campos['alterar_respostas']->fixar('componente', new JDesabilitadorDeCampos($nomesCamposRespostas, 'onclick="%s"'));
		}
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'id_inscricao',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
									  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem
							  )) );
							  
		$this->acoesPosEnvio->editarAcao('adicionar', 'Inscrever outro(s) alunos(s)');
		if ( $this->obterEstado() == self::EDITANDO )
			$this->acoesPosEnvio->adicionarAcao('editar', 'Continuar editando');
		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próxima inscrição');
		$this->acoesPosEnvio->prepararCampoRedirecionamentoRapido();
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			if ($this->obterEstado() == self::ADICIONANDO) {
				$alunos_sele = $this->_campos['alunos_sele']->obter('html_valor');
				if ( !is_array($alunos_sele) || !count($alunos_sele) ) {
					$this->_adicionarErro('alunos_sele', 'nenhum aluno selecionado;');
					throw new FQuestoes_Exception('Nenhum aluno foi selecionado!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FInscricoes_Exception($e->getMessage());
		}
	}
	
	public function &executar ()
	{
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Aluno(s) inscrito(s) com sucesso!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar a(s) inscrição(ões)!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Inscrição editada com sucesso!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar a inscrição!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		return $this->_dados;
	}

	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id_inscricao'])) {
			$id = $_GET['id_inscricao'];
		} else {
			$id = (isset($_POST['id_inscricao']) ? $_POST['id_inscricao'] : null );
		}
		
		$this->_dados = new Inscricao($id);

		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->obterSimulado()->obterID() == $this->_simulado->obterID()) {// && $this->_dados->validarInstituicao() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $this->_simulado->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)), 'Inscrição inválida!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$this->_dados->fixarSimulado($this->_simulado);
		}
	}
	
	protected function _adicionar ()
	{
		$retorno = true;
		
		$alunos_disp = $this->_obterArrayAlunos();
		foreach ($this->_campos['alunos_sele']->obter('html_valor') as $aID => $aNome) {
			if ( !array_key_exists($aID, $alunos_disp) )
				continue;
		
			$aluno = new Aluno($aID);
			$id = Inscricao::obterIDInscricaoPeloAluno($this->_simulado, $aluno);
			
			if ( !$id ) {
				$this->_dados = Inscricao::obterNovaInscricao($this->_simulado, $aluno);
				
				if ($this->_dados->obterID() != null)
					$this->_fixarDadosDaInscricao();
				
				if ( !$this->_dados->salvar() )
					$retorno = false;
			}
		}

		return $retorno;
	}

	protected function _editar ()
	{
		$this->_fixarDadosDaInscricao();
		
		return $this->_dados->salvar();
	}

	protected function _fixarDadosDaInscricao ()
	{
		$curso = $this->_campos['curso']->obter('valor');
		if ($curso == 'NULO') {	$curso = null; }
		$this->_dados->fixarCursoVestibular( new CursoVestibular($curso) );

		$tipo = null;
		if ( $this->obterEstado() == Formulario::EDITANDO && $this->_simulado->obterNumeroDeTipos() > 1 ) {
			$tipo = $this->_campos['tipo']->obter('valor');
			if ($tipo == 'NULO') { $tipo = null; }
		}
		$this->_dados->fixarTipo($tipo);
		
		$lingua = null;
		if ( $this->_simulado->temQuestoesLinguaEstrangeira() ) {
			$lingua = $this->_campos['lingua']->obter('valor');
			if ($lingua == 'NULO') { $lingua = null; }
		}
		$this->_dados->fixarLingua( new Disciplina($lingua) );

		$ed_integral = $this->_campos['ed_integral']->obter('valor');
		if ($ed_integral == 'NULO') { $ed_integral = null; }
		$this->_dados->fixarEdIntegral($ed_integral);

		$nao_alfabetico = $this->_campos['nao_alfabetico']->obter('valor');
		if ($nao_alfabetico == 'NULO') { $nao_alfabetico = null; }
		$this->_dados->fixarNaoAlfabetico($nao_alfabetico);
		
		$atestado = $this->_campos['atestado']->obter('valor');
		if ($atestado == 'NULO') { $atestado = null; }
		$this->_dados->fixarAtestado($atestado);
		
		$rfinalizado = $this->_campos['rfinalizado']->obter('valor');
		if ($rfinalizado == 'NULO') { $rfinalizado = null; }
		$this->_dados->fixarSimuladoFinalizado($rfinalizado);

		@$this->_fixarRespostasOpcoes();
		
		if ($this->obterEstado() == self::EDITANDO && $this->_campos['alterar_respostas']->obter('valor') == 1){
			$this->_fixarRespostas();
		}

		if ($this->obterEstado() == self::EDITANDO){
			$fases = $this->_simulado->obterDuracao();
			for ($i=1; $i <= $fases; $i++) { 
				$fase_ausente = $this->_campos['flag_ausente_'.$i]->obter('valor');
				if($fase_ausente == 1){
					$this->_removerRespostasFase($i);
				}
			}
		}
	}

	protected function _fixarRespostasOpcoes () {
		if ( $this->obterEstado() != Formulario::EDITANDO )
			return false;

		$idQuestao = $this->_simulado->obterIDQuestaoDoTipo(Questao::OPCAO_CURSO_VESTIBULAR);
		if ( $idQuestao !== false ) {
			$questao = new Questao($idQuestao);
			$questao->carregar();
			$resposta = null;
			foreach ( $this->_dados->obterRespostasMarcadas() as $r ) {
				if ( $r->obterQuestao()->obterID() == $idQuestao )
					$resposta = $r;
			}

			if ( $resposta == null ) {
				$resposta = Resposta::obterNovaResposta( $questao, $this->_dados, 'FIRESPOSTA1' );
				$d->adicionarRespostaMarcada($resposta);
			}
			
			$valor = null;
			foreach ( $questao->obterEquivalenciasOpcoes() as $eo ) {
				if ( (int) $eo->obterEquivale() == (int) @$this->_dados->obterCursoVestibular()->obterID() )
					$valor = $eo->obterValor();
			}
			
			$resposta->fixarValor( $valor );
		}
	}

	protected function _fixarRespostas ()
	{
		if ( $this->obterEstado() != Formulario::EDITANDO ) { return false; }
	
		$respostas_marcadas = array();
		foreach ( $this->_dados->obterRespostasMarcadas() as $r ) {
			// se for repetido remove...
			if ( array_key_exists((int) $r->obterQuestao()->obterID(), $respostas_marcadas) ) { @$r->remover(); continue; }
			
			$respostas_marcadas[ (int) $r->obterQuestao()->obterID() ] = $r;
		}
		
		foreach ($this->questoes as $fase => $questoes) {
			foreach ($questoes as $numero => &$q) {
				$resposta = null;
				$valor = null;
				
				if ( isset( $this->_campos[ 'resposta_marcada_'. $fase .'_'. $numero ] ) ) {
					$valor = $this->_campos[ 'resposta_marcada_'. $fase .'_'. $numero ]->obter('valor');
					if ( !strlen($valor) ) { $valor = null;	}
				}
				
				if ( $q->obterTipo() == Questao::MULTIPLAESCOLHA ) {
					$valor = MultiplaEscolha::inteiro($valor);
					if ($valor === false) { $valor = null; }
				} else if ( $q->obterTipo() == Questao::DISCURSIVA || $q->obterTipo() == Questao::FLUENCIA) {
					if ($valor == 'NULO') { $valor = null; }
				} else {
					if ( $valor != null ) { $valor = (int) $valor; }
				}
				
				if ( $this->obterEstado() == self::EDITANDO && array_key_exists($q->obterID(), $respostas_marcadas) ) {
					$resposta = $respostas_marcadas[ $q->obterID() ];
				} else {
					$resposta = Resposta::obterNovaResposta( $q, $this->_dados, 'FIRESPOSTA2' );
					$this->_dados->adicionarRespostaMarcada($resposta);
				}

				$resposta->fixarValor($valor);
			}
		}
	}

	protected function _removerRespostasFase ($fase)
	{
		if ($this->obterEstado() != Formulario::EDITANDO) { 
			return false; 
		}
	
		foreach ($this->_dados->obterRespostasMarcadas() as $index => $r) {
			if($r->obterQuestao()->obterFaseDuracao() == $fase){
				$this->_dados->removerRespostaMarcada($index);
				@$r->remover();
			}
		}
	}
	
	private function _obterArrayAlunos ()
	{
		$alunos = array();
		
		if(Core::registro('instituicao')->obterID() !== null){
			$rs = Core::registro('db')->query( sprintf('SELECT alunos.a_id, usuarios.u_nome, turmas.t_nome, instituicoes.i_nome FROM alunos 
														INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario AND usuarios.u_instituicao = %s 
														INNER JOIN instituicoes ON usuarios.u_instituicao = instituicoes.i_id 
														INNER JOIN turmas ON turmas.t_id = alunos.a_turma AND turmas.t_serie = %s
														LEFT JOIN simulados_inscricoes ON simulados_inscricoes.si_aluno = alunos.a_id AND simulados_inscricoes.si_simulado = %s 
														WHERE simulados_inscricoes.si_id IS NULL ORDER BY turmas.t_nome ASC, usuarios.u_nome ASC',
				Core::registro('db')->formatarValor( Core::registro('instituicao')->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterSerieAvaliacao()->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterID() )) );
			
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){			
					$alunos[$row['a_id']] = $row['u_nome'] .' ('. $row['t_nome'] .' - '.$row['i_nome'].')';
				}
			}
			$rs->free();
		}
		else{
			$rs = Core::registro('db')->query( sprintf('SELECT alunos.a_id, usuarios.u_nome, turmas.t_nome, instituicoes.i_nome FROM alunos 
														INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario  
														INNER JOIN instituicoes ON usuarios.u_instituicao = instituicoes.i_id 
														INNER JOIN turmas ON turmas.t_id = alunos.a_turma AND turmas.t_serie = %s
														LEFT JOIN simulados_inscricoes ON simulados_inscricoes.si_aluno = alunos.a_id AND simulados_inscricoes.si_simulado = %s 
														WHERE simulados_inscricoes.si_id IS NULL ORDER BY turmas.t_nome ASC, usuarios.u_nome ASC',
				Core::registro('db')->formatarValor( $this->_simulado->obterSerieAvaliacao()->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterID() )) );
			
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){			
					$alunos[$row['a_id']] = $row['u_nome'] .' ('. $row['t_nome'] .' - '.$row['i_nome'].')';
				}
			}
			$rs->free();
		}

		

		return $alunos;
	}

}

?>