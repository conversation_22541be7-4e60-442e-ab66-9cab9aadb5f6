<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>

<style type="text/css">
  #vd_Bloco th{
    width: 10% !important;
  }
</style>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>
	
	<div class="menor">
	<?
	if ( $this->_formulario->obterEstado() == Formulario::ADICIONANDO ) {
		echo '<strong>Adicionando inscrição</strong>';
	} else {
		echo '<strong>Editando inscrição</strong>';
	}
	?>
	</div>
<?
	if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
	}
?>

	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	
	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => $this->seletorVisao->visaoSelecionada()) ); ?>'" value="Cancelar" class="botao cancelar">
<?
if ( $this->_formulario->obterEstado() == Formulario::EDITANDO ) {
?>
	<input type="button" onClick="if (confirm('Tem certeza que deseja DESINSCREVER esse aluno?')) { window.location='<?= Gerenciador_URL::gerarLink('simulados', 'removerinscricao', array('id' => $this->_formulario->obterDados()->obterID())); ?>'; }" value="Desinscrever" class="botao remover">
<?
}
?>
	</div>
	
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="4" class="titulo">Informa&ccedil;&otilde;es da inscri&ccedil;&atilde;o  </th>
        </tr>
<?
if ( $this->_formulario->obterEstado() == Formulario::ADICIONANDO ) {
?>
        <tr>
          <th>Alunos</th>
          <td colspan="3" align="center"><table width="98%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th style="text-align:left; width: 45% !important"><?= $this->_formulario->obterHTML('alunos_disp', Formulario::HTML_LABEL, true); ?></th>
              <td>&nbsp;</td>
              <th style="text-align:left; width: 45% !important"><?= $this->_formulario->obterHTML('alunos_sele', Formulario::HTML_LABEL, true); ?></th>            </tr>
            <tr>
              <td><?= $this->_formulario->obterHTML('alunos_disp', Formulario::HTML_CAMPO, true); ?></td>
              <td align="center"><div style="margin-bottom:4px;"><?= $this->_formulario->obterHTML('add_aluno', Formulario::HTML_CAMPO, true); ?></div><div><?= $this->_formulario->obterHTML('del_aluno', Formulario::HTML_CAMPO, true); ?></div></td>
              <td><?= $this->_formulario->obterHTML('alunos_sele', Formulario::HTML_CAMPO, true); ?></td>
            </tr>
          </table></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('curso', Formulario::HTML_LABEL, true); ?></th>
          <td colspan="3"><?= $this->_formulario->obterHTML('curso', Formulario::HTML_CAMPO, true); ?></td>
        </tr>

<?
} else {
?>
        <tr>
          <th><?= $this->_formulario->obterHTML('aluno', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('aluno', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('curso', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('curso', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
<?
}
?>

        <tr>
          <th><?= $this->_formulario->obterHTML('tipo', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('tipo', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('lingua', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('lingua', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('ed_integral', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('ed_integral', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('atestado', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('atestado', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('nao_alfabetico', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('nao_alfabetico', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('rfinalizado', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('rfinalizado', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('rdata', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('rdata', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('rtempo', Formulario::HTML_LABEL, true); ?></th>
          <td ><?= $this->_formulario->obterHTML('rtempo', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
      </table>
	  
<?
if ( $this->_formulario->obterEstado() == Formulario::EDITANDO && count($this->_formulario->questoes) ) {
?>
	  <div class="vd_BlocoEspacador">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Respostas assinaladas</th>
        </tr>
<?
	if ( $this->_formulario->podeEditarRespostas != FInscricoes::FALTA_NADA ) {
?>
        <tr>
          <td colspan="2" class="descricao">
<?
		echo 'Você precisa selecionar ';
		if ( $this->_formulario->podeEditarRespostas == FInscricoes::FALTA_TIPO ) {
			echo 'o <strong>Tipo de prova</strong> ';
		}
		if ( $this->_formulario->podeEditarRespostas == FInscricoes::FALTA_LINGUA ) {
			echo 'a <strong>Língua estrangeira</strong> ';
		}
		if ( $this->_formulario->podeEditarRespostas == FInscricoes::FALTA_TIPO_E_LINGUA ) {
			echo 'o <strong>Tipo de prova</strong> e a <strong>Língua estrangeira</strong> ';
		}
		echo 'para poder editar as respostas assinaladas.';
?>
			</td>
        </tr>
<?
	} else {
?>
	<tr>
		<th style="width:10%;"><?= $this->_formulario->obterHTML('alterar_respostas', Formulario::HTML_LABEL, true); ?></th>
		<td style="width:90%;"><?= $this->_formulario->obterHTML('alterar_respostas', Formulario::HTML_CAMPO, true); ?></td>
	<tr>
<?
		foreach ($this->_formulario->questoes as $fase => $questoes) {
?>
        <tr>
          <th style="width:10%;">
            Fase: <strong><?= $fase; ?>&ordm; dia </strong><br>
            (Ausente: <?= $this->_formulario->obterHTML('flag_ausente_'.$fase, Formulario::HTML_CAMPO, true); ?>)
          </th>
          <td style="width:90%;"><table width="1%" border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
            <tr>
              <th nowrap="nowrap" style="text-align:center;width: 1%;">Identificador</th>
              <th nowrap="nowrap" style="text-align:center;width: 1%;">Resposta marcada</th>
              <th nowrap="nowrap" style="text-align:center;width: 10%;">Status</th>
              <th nowrap="nowrap" style="text-align:center;width: 1%;">Chute</th>
              <th nowrap="nowrap" style="text-align:center;width: 6%;">Tempo</th>
              <th nowrap="nowrap" style="text-align:center;width: 2%;">Nível de Segurança</th>
              <th nowrap="nowrap" style="text-align:center;width: 75%;">Comentário</th>
              <th nowrap="nowrap" style="text-align:center;width: 75%;">Origem</th>
              <th nowrap="nowrap" style="text-align:center;width: 75%;">Origem Atualizada</th>
            </tr>
<?
				foreach ($questoes as $numero => &$q) {
?>
				<tr>
				  <td align="center"><?= $q->obterIdentificador(); ?><?= $q->foiAnulada() ? ' (anulada)' : null; ?></td>
					<td nowrap="nowrap"><?= $this->_formulario->obterHTML('resposta_marcada_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('status_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('chute_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('tempo_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('nivelseg_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('comentario_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('origem_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
          <td nowrap="nowrap"><?= $this->_formulario->obterHTML('origem_update_'. $fase .'_'. $numero, Formulario::HTML_CAMPO, true); ?></td>
				</tr>
<?
				}
?>
          </table>
            </td>
        </tr>
<?
		}
	}
?>
      </table>
	</div>
<?
}
?>

	</td>
  </tr>
</table>
<?= $this->_formulario->obterHTML('id_inscricao', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoRedirecionamentoRapido(true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoEditarEmOrdem(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>

<?= JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('alunos_disp[]', 'alunos_sele[]')); ?>