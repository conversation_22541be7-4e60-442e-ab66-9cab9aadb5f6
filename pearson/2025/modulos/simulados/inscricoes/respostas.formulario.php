<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Inscricao', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('JDesabilitadorDeCampos', 'JavaScript/JDesabilitadorDeCampos/', true);
Core::incluir('JMesclador', 'JavaScript/JMesclador/', true);

class FRespostas_Exception extends Formulario_Exception { }

class FRespostas extends Formulario
{
	public $questoes = array();
	public $questoesOpcao = array();
	public $minimosPorFase = array();
	public $maximosPorFase = array();

	protected $_simulado;
	protected $_dadosIncompletos = array();

	public function carregarFormulario (Simulado &$simu) {
		parent::carregarFormulario();

		$this->_simulado = $simu;
		
		if ( $this->_simulado->temQuestoesLinguaEstrangeira() ) {
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Core::modulo('redirecionador')->fixarMensagem('Esse recurso só está ativo para avaliações sem questões de língua estrangeira!', 'Cadastrando respostas...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_ALERTA);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)) );
			Core::modulo('redirecionador')->redirecionar();
		}

		if (!$this->_carregar())
			return false;

		$ordem = 1;
		
		if ( count($this->_dadosIncompletos) ) {
			$numeroTipos = $this->_simulado->obterNumeroDeTipos();
			$temQuestoesLingua = $this->_simulado->temQuestoesLinguaEstrangeira();
			foreach ($this->_dadosIncompletos as &$d) {
				if ( $numeroTipos > 1 && $d->obterTipo() == null ) {
					$this->adicionarCampo( new Campo(array( 'nome' => 'r_tipo_'. $d->obterID(),
															'etiqueta' => 'Tipo de prova do aluno '. $d->obterAluno()->obterUsuario()->obterNome(),
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::NATURAL,
															'html_tamanho_maximo' => 2,
															'html_tamanho' => 2,
															'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
															'argumentos' => array(Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
															'html_tipo' => Campo::HTML_TEXTO,
															'html_ordem' => $ordem++
										  )) );
				}
				
				if ( $temQuestoesLingua && $d->obterLingua()->obterID() == null ) {			  
					$this->adicionarCampo( new Campo(array( 'nome' => 'r_lingua_'. $d->obterID(),
															'etiqueta' => 'Língua estrangeira do aluno '. $d->obterAluno()->obterUsuario()->obterNome(),
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::NATURAL,
															'html_tamanho_maximo' => 2,
															'html_tamanho' => 2,
															'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
															'argumentos' => array(Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
															'html_tipo' => Campo::HTML_TEXTO,
															'html_ordem' => $ordem++
										  )) );
				}
			}
			
			$componente = new JMesclador('onclick="%s"');
			$componente->adicionarComponente( new JAlteradorDeFormulario($this->acoesPosEnvio->obterNomeRedirecionadorRapido(), 1, $this->_info['nome'], false) );
			$componente->adicionarComponente( new JAlteradorDeFormulario($this->acoesPosEnvio->obterNomeSeletorAcoes(), 'editar', $this->_info['nome'], true) );
		
			$this->adicionarCampo( new Campo(array( 'nome' => 'enviar_incompletos',
													'etiqueta' => 'Atualizar dados incompletos',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao salvar',
													'componente' => $componente,
													'html_ordem' => $ordem++
								  )) );
		}

		foreach ( $this->questoes as $fase => $tipos ) {
			foreach ($this->_dados as &$d) {
				$d->verificarAusentePorFases();
				$ausencias = $d->obterAusentePorFases();

				$ausente = 0;
				if(!array_key_exists($fase, $ausencias)){
					$ausente = 1;
				}
				
				$this->adicionarCampo( new Campo(array( 'nome' => 'flag_ausente_'.$d->obterID().'_'.$fase,
												'etiqueta' => 'Ausente',
												'valor' => (!$this->foiEnviado() ? $ausente : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => $ordem++
							  )) );

				$respostas_marcadas = array();

				foreach ( $d->obterRespostasMarcadas() as $r ) {
					$respostas_marcadas[ (int) $r->obterQuestao()->obterID() ] = $r;
				}

				for ( $i = $this->minimosPorFase[$fase]; $i <= $this->maximosPorFase[$fase]; $i++ ) {
					if ( !isset($this->questoes[$fase][$d->obterTipo()][$i]) )
						continue;

					$q = &$this->questoes[$fase][$d->obterTipo()][$i];

					if ( $q->obterTipo() == Questao::OPCAO_TIPO_PROVA || $q->obterTipo() == Questao::OPCAO_LINGUA_ESTRANGEIRA )
						continue;

					if ( $q->obterTipo() == Questao::MULTIPLAESCOLHA ) {
						$valorPadrao = null;
						if ( !$this->foiEnviado() && isset($respostas_marcadas[ $q->obterID() ]) )
							$valorPadrao = MultiplaEscolha::letra( $respostas_marcadas[ $q->obterID() ]->obterValor() );

						$this->adicionarCampo( new Campo(array( 'nome' => 'r_'. $fase .'_'. $i .'_'. $d->obterID(),
																'etiqueta' => 'Resposta '. $i .' do aluno '. $d->obterAluno()->obterUsuario()->obterNome(),
																'requerimento' => Campo::OPCIONAL,
																'tipo' => Campo::TEXTO,
																'argumentos' => array(Campo::POSSIBILIDADES => MultiplaEscolha::$opcoes),
																'html_tipo' => Campo::HTML_TEXTO,
																'html_valor' => $valorPadrao,
																'html_tamanho_maximo' => 1,
																'html_tamanho' => 2,
																'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
																//'componente' => new JSComponente_Limitador_Digitacao(MultiplaEscolha::arrayParaLimitador()),
																'html_ordem' => $ordem++
											  )) );
					} else if ( $q->obterTipo() == Questao::DISCURSIVA || $q->obterTipo() == Questao::FLUENCIA) {
						$valorPadrao = null;
						if ( !$this->foiEnviado() && isset($respostas_marcadas[ $q->obterID() ]) && $respostas_marcadas[ $q->obterID() ]->obterValor() != null )
							$valorPadrao = sprintf('%0.02f', $respostas_marcadas[ $q->obterID() ]->obterValor());
							
						$this->adicionarCampo( new Campo(array( 'nome' => 'r_'. $fase .'_'. $i .'_'. $d->obterID(),
																'etiqueta' => 'Resposta '. $i .' do aluno '. $d->obterAluno()->obterUsuario()->obterNome(),
																'requerimento' => Campo::OPCIONAL,
																'tipo' => Campo::REAL,
																'html_tamanho_maximo' => 5,
																'html_tamanho' => 4,
																'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
																'argumentos' => array(Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
																'html_tipo' => Campo::HTML_TEXTO,
																'html_valor' => $valorPadrao,
																'html_ordem' => $ordem++
											  )) );
					} else {
						$valorPadrao = null;
						if ( !$this->foiEnviado() && isset($respostas_marcadas[ $q->obterID() ]) && $respostas_marcadas[ $q->obterID() ]->obterValor() != null )
							$valorPadrao = sprintf('%02d', $respostas_marcadas[ $q->obterID() ]->obterValor());
	
						$this->adicionarCampo( new Campo(array( 'nome' => 'r_'. $fase .'_'. $i .'_'. $d->obterID(),
																'etiqueta' => 'Resposta '. $i .' do aluno '. $d->obterAluno()->obterUsuario()->obterNome(),
																'requerimento' => Campo::OPCIONAL,
																'tipo' => Campo::REAL,
																'html_tamanho_maximo' => 2,
																'html_tamanho' => 2,
																'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
																'argumentos' => array(Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
																'html_tipo' => Campo::HTML_TEXTO,
																'html_valor' => $valorPadrao,
																'html_ordem' => $ordem++
											  )) );
					}
				}
			}
		}
		
		if ( !isset($_POST['ids']) )
			$_POST['ids'] = '';

		$this->adicionarCampo( new Campo(array( 'nome' => 'ids',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $_POST['ids']
							  )) );
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );


		$this->acoesPosEnvio->removerAcao('adicionar');
		$this->acoesPosEnvio->adicionarAcao('editar', 'Continuar editando');
		$this->acoesPosEnvio->prepararCampoRedirecionamentoRapido();
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e)
		{
			throw new FRespostas_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		$cadastradas = $this->_cadastrarRespostas();
		Core::modulo('redirecionador')->fixarMensagem( $cadastradas .' de '. count($this->_dados) .' séries de respostas cadastradas com sucesso!', 'Importando respostas...');
		Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
	}

	public function &obterDadosIncompletos () {
		return $this->_dadosIncompletos;
	}

	protected function _carregar ()
	{
		$this->fixarEstado(self::OUTRO);
		
		$this->_dados = array();
		$this->_dadosIncompletos = array();
		$this->_linguasDisponiveis = Disciplina::obterArrayDisciplinasParaFormulario(true);
		$this->_cursosDisponiveis = CursoVestibular::obterArrayCursosVestibularParaFormulario();
	
		$ids = array();
		if ( isset($_POST['ids']) )
			$ids = unserialize(base64_decode($_POST['ids']));
			
		if ( is_array($ids) )
			$ids = array_unique($ids);
		else
			$ids = array();
			
		if ( !count($ids) ) {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $this->_simulado->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)), 'Inscrições inválidas!');
		} else {
			$this->questoes = $this->_simulado->obterQuestoesParaFormulario(false, false, false);
			
			if ( !count($this->questoes) ) {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $this->_simulado->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)), 'Avaliação não tem questões!');
				return false;
			}
			
			$idQuestao = $this->_simulado->obterIDQuestaoDoTipo(Questao::OPCAO_TIPO_PROVA);
			if ( $idQuestao !== false ) {
				$this->questoesOpcao[Questao::OPCAO_TIPO_PROVA] = new Questao($idQuestao);
				$this->questoesOpcao[Questao::OPCAO_TIPO_PROVA]->carregar();
			}
			
			$idQuestao = $this->_simulado->obterIDQuestaoDoTipo(Questao::OPCAO_LINGUA_ESTRANGEIRA);
			if ( $idQuestao !== false ) {
				$this->questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA] = new Questao($idQuestao);
				$this->questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA]->carregar();
			}

			// descobre o número máximo de questões
			$this->minimosPorFase = array();
			$this->maximosPorFase = array();
			foreach ( $this->questoes as $fase => $tipos ) {
				$this->minimosPorFase[$fase] = 9999;
				$this->maximosPorFase[$fase] = 0;
				foreach ($tipos as $questoes) {
					$min = min(array_keys($questoes));
					$max = max(array_keys($questoes));

					if ( $min < $this->minimosPorFase[$fase] )
						$this->minimosPorFase[$fase] = $min;
						
					if ( $max > $this->maximosPorFase[$fase] )
						$this->maximosPorFase[$fase] = $max;
				}
				if ( $this->minimosPorFase[$fase] > $this->maximosPorFase[$fase] )
					$this->minimosPorFase[$fase] = $this->maximosPorFase[$fase];
			}

			foreach ($ids as $id) {
				if (!Filtrador::natural($id))
					continue;

				$idSimulado = Inscricao::obterIDSimuladoPeloIDInscricao( $id );

				if ( $idSimulado == $this->_simulado->obterID() ) {
					$incompleto = false;
					$inscricao = new Inscricao($id);
					$inscricao->carregar($this->_simulado);

					if ( $this->_simulado->obterNumeroDeTipos() <= 1 ) {
						$inscricao->fixarTipo(1);
					} else {
						if ( $inscricao->obterTipo() == null || $inscricao->obterTipo() < 1 || $inscricao->obterTipo() > $this->_simulado->obterNumeroDeTipos() ) {
							$inscricao->fixarTipo(null);
							$incompleto = true;
						}
					}
					
					if ( $this->_simulado->temQuestoesLinguaEstrangeira() ) {
						if ( $inscricao->obterLingua() == null || !isset($this->_linguasDisponiveis[$inscricao->obterLingua()->obterID()]) ) {
							$inscricao->fixarLingua( new Disciplina(null) );
							$incompleto = true;
						}
					}

					$inscricao->verificarAusente();
					$inscricao->verificarAusentePorFases();
					
					if ( !$incompleto )
						$this->_dados[] = $inscricao;
					else
						$this->_dadosIncompletos[] = $inscricao;
				}
			}
		}

		return true;
	}

	protected function _cadastrarIncompletos () {
		$numeroTipos = $this->_simulado->obterNumeroDeTipos();
		$temQuestoesLingua = $this->_simulado->temQuestoesLinguaEstrangeira();
		foreach ($this->_dadosIncompletos as &$d) {
			if ( $numeroTipos > 1 && $d->obterTipo() == null ) {
				$valor = null;

				if ( isset( $this->_campos[ 'r_tipo_'. $d->obterID() ] ) ) {
					$valor = $this->_campos[ 'r_tipo_'. $d->obterID() ]->obter('valor');
					if ( !strlen($valor) )
						$valor = null;
				}
				
				if ( $valor != null ) {
					if ( !isset( $this->questoesOpcao[Questao::OPCAO_TIPO_PROVA] ) ) {
						$valor = (int) $valor;
					} else {
						foreach ( $this->questoesOpcao[Questao::OPCAO_TIPO_PROVA]->obterEquivalenciasOpcoes() as $eo ) {
							if ( (int) $eo->obterValor() == (int) $valor )
								$valor = (int) $eo->obterEquivale();
						}
					}
					
					if ( $valor > 0 && $valor <= $numeroTipos )
						$d->fixarTipo( $valor );
					else
						$d->fixarTipo( 1 );
				}
			}

			if ( $temQuestoesLingua && $d->obterLingua()->obterID() == null ) {
				$valor = null;

				if ( isset( $this->_campos[ 'r_lingua_'. $d->obterID() ] ) ) {
					$valor = $this->_campos[ 'r_lingua_'. $d->obterID() ]->obter('valor');
					if ( !strlen($valor) )
						$valor = null;
				}
				
				if ( $valor != null ) {
					if ( !isset( $this->questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA] ) ) {
						$valor = (int) $valor;
					} else {
						foreach ( $this->questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA]->obterEquivalenciasOpcoes() as $eo ) {
							if ( (int) $eo->obterValor() == (int) $valor )
								$valor = (int) $eo->obterEquivale();
						}
					}
					
					if ( isset( $this->_linguasDisponiveis[$valor] ) )
						$d->fixarLingua( new Disciplina($valor) );
					else
						$d->fixarLingua( new Disciplina(null) );
				}
			}

			@$d->salvar();
		}
	}

	protected function _cadastrarRespostas ()
	{
		if ( count($this->_dadosIncompletos) )
			$this->_cadastrarIncompletos();

		$cadastrados = 0;
		foreach ($this->_dados as &$d) {
			$respostas_marcadas = array();

			foreach ( $d->obterRespostasMarcadas() as $r ) {
				// se for repetido remove...
				if ( array_key_exists((int) $r->obterQuestao()->obterID(), $respostas_marcadas) ) { @$r->remover(); continue; }
			
				$respostas_marcadas[ (int) $r->obterQuestao()->obterID() ] = $r;
			}
			
			foreach ( $this->questoes as $fase => $tipos ) {
				$ausente = $this->_campos['flag_ausente_'.$d->obterID().'_'.$fase]->obter('valor');
				if($ausente){
					foreach ($d->obterRespostasMarcadas() as $r) {
						if($r->obterQuestao()->obterFaseDuracao() == $fase){
							$d->removerRespostaMarcada($index);
							@$r->remover();
						}
					}
				}
				else{
					for ( $i = $this->minimosPorFase[$fase]; $i <= $this->maximosPorFase[$fase]; $i++ ) {
						if ( !isset($this->questoes[$fase][$d->obterTipo()][$i]) )
							continue;

						$q = &$this->questoes[$fase][$d->obterTipo()][$i];

						if ( $q->obterTipo() == Questao::OPCAO_TIPO_PROVA || $q->obterTipo() == Questao::OPCAO_LINGUA_ESTRANGEIRA )
							continue;

						$resposta = null;
						$valor = null;

						if ( isset( $this->_campos[ 'r_'. $fase .'_'. $i .'_'. $d->obterID() ] ) ) {
							$valor = $this->_campos[ 'r_'. $fase .'_'. $i .'_'. $d->obterID() ]->obter('valor');
							if ( !strlen($valor) )
								$valor = null;
						}

						if ( $q->obterTipo() == Questao::MULTIPLAESCOLHA ) {
							$valor = MultiplaEscolha::inteiro($valor);
							if ($valor === false)
								$valor = null;
						} else if ( $q->obterTipo() == Questao::DISCURSIVA || $q->obterTipo() == Questao::FLUENCIA) {
							// está aqui apenas para não cair no tipo de geral de questão
						} else if ( $q->obterTipo() == Questao::OPCAO_CURSO_VESTIBULAR ) {
							if ( $valor != null ) {
								$achouCurso = false;
								foreach ( $q->obterEquivalenciasOpcoes() as $eo ) {
									if ( (int) $eo->obterValor() == (int) $valor && isset($this->_cursosDisponiveis[(int) $eo->obterEquivale()]) ) {
										$d->fixarCursoVestibular( new CursoVestibular( (int) $eo->obterEquivale() ) );
										$achouCurso = true;
									}
								}

								if ( !$achouCurso )
									$d->fixarCursoVestibular( new CursoVestibular( null ) );
							} else {
								$d->fixarCursoVestibular( new CursoVestibular( null ) );
							}
						} else {
							if ( $valor != null ) { $valor = (int) $valor; }
						}

						if ( isset($respostas_marcadas[$q->obterID()]) ) {
							$resposta = $respostas_marcadas[ $q->obterID() ];
						} else {
							$resposta = Resposta::obterNovaResposta( $q, $d, 'FRESPOSTAS' );
							$d->adicionarRespostaMarcada($resposta);
						}

						$resposta->fixarValor($valor);
					}	
				}			
			}

			if ( $d->salvar() )
				$cadastrados++;
		}
	
		return $cadastrados;
	}
}

?>