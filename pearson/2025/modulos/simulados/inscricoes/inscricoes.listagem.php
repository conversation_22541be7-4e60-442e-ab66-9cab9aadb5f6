<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');
Core::incluir('Inscricao', null, true);

class LInscricoes extends Listagem
{
	protected $_procuraSQL = null;
	
	protected $_simulado = null;
	
	public function prepararListagem (Simulado &$simu)
	{
		$this->_simulado = $simu;
	
		$this->_prepararNavegador();
	
		$this->_prepararBusca();
	
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormularioAcoes();
		
		Core::modulo('navegador_inscricoes')->carregarNavegador();

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('inscricoes.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_inscricoes')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_inscricoes')->termoProcurado != null && Core::modulo('procurar_inscricoes')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_inscricoes')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_inscricoes')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}

			if ( Core::modulo('procurar_inscricoes')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(usuarios.u_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_inscricoes')->letraSelecionada);
			}
			
			if ( Filtrador::natural(Core::modulo('procurar_inscricoes')->obterResultado('turma_aluno')) ) {
				$this->_procuraSQL[] = 'a_turma = '. Core::registro('db')->formatarValor( Core::modulo('procurar_inscricoes')->obterResultado('turma_aluno') );
			}
			
			if ( is_array($this->_procuraSQL) ) {
				$this->_procuraSQL = ' AND '. implode(' AND ', $this->_procuraSQL);
			}
		}
	}
	
	protected function _preObterDados ()
	{	
		if(Core::registro('instituicao')->obterID() !== null){
			$rs = Core::registro('db')->query( sprintf(
						'SELECT COUNT(0) AS total FROM simulados_inscricoes
						LEFT JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno  
						LEFT JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
						LEFT JOIN turmas ON turmas.t_id = alunos.a_turma 
						LEFT JOIN disciplinas ON disciplinas.d_id = simulados_inscricoes.si_lingua  
						LEFT JOIN cursos_vestibular ON cursos_vestibular.c_id = simulados_inscricoes.si_curso_vestibular 
						LEFT JOIN simulados ON simulados.s_id = simulados_inscricoes.si_simulado 
						LEFT JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao
						WHERE t_instituicao = %s AND si_simulado = %s %s',
					Core::registro('db')->formatarValor( Core::registro('instituicao')->obterID() ),
					Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
					$this->_procuraSQL ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				$this->_total = $row['total'];
			}
			$rs->free();
		}
		else{
			$rs = Core::registro('db')->query( sprintf(
						'SELECT COUNT(0) AS total FROM simulados_inscricoes
						LEFT JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno  
						LEFT JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
						LEFT JOIN turmas ON turmas.t_id = alunos.a_turma 
						LEFT JOIN disciplinas ON disciplinas.d_id = simulados_inscricoes.si_lingua  
						LEFT JOIN cursos_vestibular ON cursos_vestibular.c_id = simulados_inscricoes.si_curso_vestibular 
						LEFT JOIN simulados ON simulados.s_id = simulados_inscricoes.si_simulado 
						LEFT JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao
						WHERE si_simulado = %s %s',
					Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
					$this->_procuraSQL ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				$this->_total = $row['total'];
			}
			$rs->free();
		}		
	}
	
	protected function _obterDados ()
	{
		if(Core::registro('instituicao')->obterID() !== null){		
			$rs = Core::registro('db')->query( sprintf(
						'SELECT * FROM simulados_inscricoes
						LEFT JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno  
						LEFT JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
						LEFT JOIN emails ON emails.e_id = usuarios.u_email 
						LEFT JOIN turmas ON turmas.t_id = alunos.a_turma 
						LEFT JOIN disciplinas ON disciplinas.d_id = simulados_inscricoes.si_lingua  
						LEFT JOIN cursos_vestibular ON cursos_vestibular.c_id = simulados_inscricoes.si_curso_vestibular 
						LEFT JOIN simulados ON simulados.s_id = simulados_inscricoes.si_simulado 
						LEFT JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao
						WHERE t_instituicao = %s AND si_simulado = %s %s 
						ORDER BY %s %s 
						LIMIT %s',
					Core::registro('db')->formatarValor( Core::registro('instituicao')->obterID() ),
					Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
					$this->_procuraSQL,			
					$this->_ordenacao->ordenarPor,
					$this->_ordenacao->tipoOrdem,
					$this->_paginacao->paginador->obterLimitesSQL() ) );
		}
		else{
			$rs = Core::registro('db')->query( sprintf(
						'SELECT * FROM simulados_inscricoes
						LEFT JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno  
						LEFT JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
						LEFT JOIN emails ON emails.e_id = usuarios.u_email 
						LEFT JOIN turmas ON turmas.t_id = alunos.a_turma 
						LEFT JOIN disciplinas ON disciplinas.d_id = simulados_inscricoes.si_lingua  
						LEFT JOIN cursos_vestibular ON cursos_vestibular.c_id = simulados_inscricoes.si_curso_vestibular 
						LEFT JOIN simulados ON simulados.s_id = simulados_inscricoes.si_simulado 
						LEFT JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao
						WHERE si_simulado = %s %s 
						ORDER BY %s %s 
						LIMIT %s',
					Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
					$this->_procuraSQL,			
					$this->_ordenacao->ordenarPor,
					$this->_ordenacao->tipoOrdem,
					$this->_paginacao->paginador->obterLimitesSQL() ) );
		}
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$inscricao = new Inscricao($row['si_id']);
				
				// ALUNO
				$aluno = new Aluno($row['si_aluno']);
				
				$usuario = new UsuarioInstituido($row['u_id']);
				$usuario->fixarNome($row['u_nome']);
				$usuario->fixarEndereco( new Endereco($row['u_endereco']) );
				$usuario->fixarInstituicao( new Instituicao($row['u_instituicao']) );
				
				$usuario->fixarEmail( new Email($row['e_id']) );				
				$usuario->obterEmail()->fixarEndereco($row['e_endereco']);
				$usuario->obterEmail()->fixarEstadoDaConfirmacao( (bool) $row['e_confirmado'] );
				$usuario->obterEmail()->fixarChaveConfirmacao( ( !empty($row['e_chave_confirmacao']) ? $row['e_chave_confirmacao'] : null ) );
				$usuario->obterEmail()->fixarDataUltimoEnvio($row['e_data_ultimo_envio']);
				
				$turma = new Turma($row['a_turma']);
				$turma->fixarNome($row['t_nome']);
				$aluno->fixarTurma( $turma );
				
				$aluno->fixarUsuario($usuario);
				$aluno->fixarMatricula($row['a_matricula']);
				$aluno->fixarNumeroChamada($row['a_numero_chamada']);

				// CURSO DE VESTIBULAR
				$curso = new CursoVestibular($row['si_curso_vestibular']);				
				$curso->fixarNome($row['c_nome']);
				
				// LINGUA
				$lingua = new Disciplina($row['si_lingua']);
				$lingua->fixarNome($row['d_nome']);

				if ( $this->_simulado->obterNumeroDeTipos() == 1 )
					$row['si_tipo'] = null;

				$inscricao->fixarTipo( $row['si_tipo'] );
				$inscricao->fixarAluno( $aluno );
				$inscricao->fixarCursoVestibular( $curso );
				$inscricao->fixarLingua( $lingua );
				$inscricao->fixarSimulado( $this->_simulado );
				$inscricao->fixarEdIntegral( $row['si_integral'] );
				$inscricao->fixarNaoAlfabetico( $row['si_nao_alfabetico'] );

				$inscricao->verificarAusente();
				
				$this->_dados[] = $inscricao;
			}
		}
		$rs->free();
	}
	
	protected function _prepararNavegador ()
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador_inscricoes'));
		
		Core::modulo('navegador_inscricoes')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		
		Core::modulo('navegador_inscricoes')->removerAtalhosRecarregarPadroes();
		
		Core::modulo('navegador_inscricoes')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)), 'Adicionar inscrição...');
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_inscricoes'));

		Core::modulo('procurar_inscricoes')->prepararProcura('form_procurar_inscricoes', 'caixa_procurar_inscricoes');
		Core::modulo('procurar_inscricoes')->configurarTermo();
		Core::modulo('procurar_inscricoes')->configurarOndeProcurar( array( 'usuarios.u_nome' => 'Nome do aluno',
																			'alunos.a_matricula' => 'Matrícula do aluno'   ) );
		Core::modulo('procurar_inscricoes')->configurarAlfabeto();
		
		$turmas = array('TODAS' => 'Todas as turmas');
		foreach (Turma::obterArrayTurmasParaFormulario(true) as $k => $v) {
			$turmas[$k] = $v;
		}
		Core::modulo('procurar_inscricoes')->configurarListaCustomizada('turma_aluno', 'Turma do inscrito', $turmas);
		
		Core::modulo('procurar_inscricoes')->carregarProcura();
		
		Core::modulo('procurar_inscricoes')->prepararAtalho( Core::modulo('navegador_inscricoes') );
	}
	
	protected function _prepararOrdenacao ()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_inscricoes'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_inscricoes');
		$this->_ordenacao->prepararOrdenacao();
		
		$itensOrdenacao = array( 'usuarios.u_nome' => 'Nome',
								 'emails.e_endereco' => 'Login (E-mail)',
								 'alunos.a_matricula' => 'Matrícula',
								 'instituicoes.i_nome' => 'Instituição',
								 'alunos.a_turma' => 'Turma',
								 'si_curso_vestibular' => 'Curso de vestibular',
								 'si_lingua' => 'Língua estrangeira',
								 'si_ausente' => 'Ausente?',
								 'si_integral' => 'Ed. Integral',
								 'si_nao_alfabetico' => 'Não Alfabético' );
															 
		if ( $this->_simulado->obterNumeroDeTipos() > 1 )
			$itensOrdenacao['si_tipo'] = 'Tipo de prova';
			
		$this->_ordenacao->configurarCampoOrdenarPor( $itensOrdenacao, 'u_nome' );
		
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();
		
		Core::modulo('ord_inscricoes')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_inscricoes')->carregarOrdenacao();
		Core::modulo('ord_inscricoes')->prepararAtalho( Core::modulo('navegador_inscricoes'), 'caixa_ord_inscricoes' );
		
		if ( Core::modulo('procurar_inscricoes')->procuraSolicitada() ) {
			Core::modulo('ord_inscricoes')->anexarHTML( Core::modulo('procurar_inscricoes')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao ()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_inscricoes'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormularioAcoes ()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_inscricoes', 'acao' => Gerenciador_URL::gerarLinkPelaEntrada()) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$v) {
			$ids[] = $v->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Desinscrever', 'Tem certeza que deseja DESINSCREVER todos os alunos selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		$this->_formulario->adicionarBotao('cadastrar_respostas', 'Cadastrar respostas', null, 'salvar');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();
				
				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {
					if (is_array($ids))
						Core::modulo('solicitado')->aRemoverInscricoes($ids);
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES, 'id_inscricao' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'cadastrar_respostas' ) {
					if (is_array($ids)) {
						ob_clean();
					
						$dados = array( 'ids' => chunk_split(base64_encode(serialize( $ids ))) );
				
						Gerenciador_URL::habilitarAtualiacaoReferencia(true);
						Gerenciador_URL::autoFixarIndexCompleto(true);
						Redirecionador::finalizarAoRedirecionar(true);
						Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_RESPOSTAS)), $dados );
					}
				}
			}
			catch (Formulario_Exception $e)	{ }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('inscrições', $totais);
	}
}

?>