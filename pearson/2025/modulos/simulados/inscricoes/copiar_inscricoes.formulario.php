<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class FCopiarInscricoes_Exception extends Formulario_Exception { }

class FCopiarInscricoes extends Formulario
{
	public $_simuladoDE = null;

	public function carregarFormulario ()
	{
		parent::carregarFormulario();
							 
		$serieDE = $this->_simuladoDE->obterSerieAvaliacao();

		$simuladosPossiveisII = array(); 
		$simuladosPossiveis = Simulado::obterSimuladosParaFormularioII(false);
		foreach ($simuladosPossiveis as $id => $nome) {
			$stmp = new Simulado($id);
			$stmp->carregar();

			$sstmp = $stmp->obterSerieAvaliacao();

			if($this->_simuladoDE->obterID() == $id){
				continue;
			}

			if($sstmp == $serieDE){
				$simuladosPossiveisII[$id] = '('.$id.') '.$nome;
			}
		}

		$simuladoSel = null;
		if (!$this->foiEnviado() && !isset($_POST['post_anterior']) && Core::diretiva('_copiar_inscricoes.simuladoDE.ultima_selecao') != false && array_key_exists(Core::diretiva('_copiar_inscricoes.simuladoDE.ultima_selecao'), $simuladosPossiveis))
			$simuladoSel = Core::diretiva('_copiar_inscricoes.simuladoDE.ultima_selecao');
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'simulado',
												'etiqueta' => '(ID) Avalia&ccedil;&atilde;o PARA:',
												'valor' => $simuladoSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosPossiveisII)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $simuladosPossiveisII,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
						  	)) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Enviar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 3
							  )) );
							  
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e){		
			throw new FCopiarInscricoes_Exception($e->getMessage());
		}
	}

	public function fixarSimuladoDE ($de){
		$this->_simuladoDE = $de;
	}
	
	public function executar ()
	{
		Core::modulo('redirecionador')->fixarMensagem(null, 'Copiando Inscritos...');

		$simuladoPARA = $this->_campos['simulado']->obter('valor');

		if (is_numeric($this->_simuladoDE->obterID()) && is_numeric($simuladoPARA)){
			if($this->_simuladoDE->obterID() == $simuladoPARA){
				Core::modulo('redirecionador')->adicionarSucesso(null, 'Avalia&ccedil;&atilde;o DE n&atilde;o pode ser igual a PARA.');
				return false;
			}

			$arrSkipDuplicateds = array();
			$qtdInscritosJaExistentes = 0;
			$idsInscricoesCopiadas = array();

			$rs = Core::registro('db')->query('SELECT si_simulado, si_aluno, a_matricula FROM simulados_inscricoes INNER JOIN alunos ON a_id = si_aluno WHERE si_simulado = '.$this->_simuladoDE->obterID());
			$qtdInscritos = $rs->num_rows;
			if ($qtdInscritos){
				while ($row = $rs->fetch_assoc()){
					if(in_array('s'.$simuladoPARA.'_a'.$row['si_aluno'], $arrSkipDuplicateds)){
						continue;
					}

					$rs1 = Core::registro('db')->query('SELECT si_id FROM simulados_inscricoes WHERE si_simulado = '.$simuladoPARA.' AND si_aluno = '.$row['si_aluno'].' LIMIT 1;');
					if ($rs1->num_rows <= 0){
						$iSql = "INSERT INTO simulados_inscricoes (si_simulado,si_aluno) VALUES (".$simuladoPARA." , ".$row['si_aluno'].");";				

						Core::registro('db')->query($iSql);
						$idsInscricoesCopiadas[] = Core::registro('db')->insert_id;
						$arrSkipDuplicateds[] = 's'.$simuladoPARA.'_a'.$row['si_aluno'];
					}
					else{
						$qtdInscritosJaExistentes++;
					}
					$rs1->free();
				}
			}
			$rs->free();

			Core::fixarDiretiva('_copiar_inscricoes.simuladoPARA.ultima_selecao', 0);

			Core::modulo('redirecionador')->adicionarSucesso('1. ', 'Avalia&ccedil;&atilde;o DE ('.$this->_simuladoDE->obterID().') possui '.$qtdInscritos.' inscritos.<br>');
			Core::modulo('redirecionador')->adicionarSucesso('2. ', 'Total de '.count($arrSkipDuplicateds).' inscritos DE estavam duplicados e foram ignorados.<br>');
			Core::modulo('redirecionador')->adicionarSucesso('3. ', 'Avalia&ccedil;&atilde;o PARA ('.$simuladoPARA.') teve '.count($idsInscricoesCopiadas).' inscritos copiados.<br>');
			Core::modulo('redirecionador')->adicionarSucesso('4. ', 'Total de '.$qtdInscritosJaExistentes.' inscritos DE j&aacute; estavam na avalia&ccedil;&atilde;o PARA.<br>');

			$arrSkipDupAulas = array();
			$qtdAulasJaExistentes = 0;
			$idsAulasCopiadas = array();

			$rs2 = Core::registro('db')->query('SELECT * FROM aulas WHERE a_simulado = '.$this->_simuladoDE->obterID()); 
			$qtdAulas = $rs2->num_rows;
			if ($qtdAulas){
				while ($row2 = $rs2->fetch_assoc()){
					if(in_array('d'.$row2['a_disciplina'].'_t'.$row2['a_turma'].'_p'.$row2['a_professor'].'_s'.$simuladoPARA, $arrSkipDupAulas)){
						continue;
					}

					$rs3 = Core::registro('db')->query('SELECT * FROM aulas WHERE a_simulado = '.$simuladoPARA.' AND a_disciplina = '.$row2['a_disciplina'].' AND a_turma = '.$row2['a_turma'].' AND a_professor = '.$row2['a_professor'].' LIMIT 1;');
					if ($rs3->num_rows <= 0){
						$iSql2 = "INSERT INTO aulas (a_simulado,a_disciplina,a_turma,a_professor) VALUES (".$simuladoPARA." , ".$row2['a_disciplina']." , ".$row2['a_turma']." , ".$row2['a_professor'].");";				

						Core::registro('db')->query($iSql2);
						$idsAulasCopiadas[] = Core::registro('db')->insert_id;
						$arrSkipDupAulas[] = 'd'.$row2['a_disciplina'].'_t'.$row2['a_turma'].'_p'.$row2['a_professor'].'_s'.$simuladoPARA;
					}
					else{
						$qtdAulasJaExistentes++;
					}
					$rs3->free();
				}
			}
			$rs2->free();

			Core::modulo('redirecionador')->adicionarSucesso('5. ', 'Aulas (rela&ccedil;&atilde;o de turmas) dos professores DE foram copiadas PARA.<br>');
		}
		else{
			Core::modulo('redirecionador')->adicionarSucesso(null, 'Falha ao processar as avalia&ccedil;&atilde;o DE e/ou PARA.');
			return false;
		}
	}
}

?>