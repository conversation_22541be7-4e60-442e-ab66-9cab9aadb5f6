<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>
	
	<div class="menor"><strong>Cadastrando respostas</strong></div>
<?
	if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
	}
?>

	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	 
	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => $this->seletorVisao->visaoSelecionada()) ); ?>'" value="Cancelar" class="botao cancelar">
	</div>

<?
if ( count($this->_formulario->obterDadosIncompletos()) ) {
	$numeroTipos = $simu->obterNumeroDeTipos();
	$temQuestoesLingua = $simu->temQuestoesLinguaEstrangeira();
?>
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th class="titulo">Inscritos com dados incompletos</th>
        </tr>
        <tr>
          <td>
			<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
			<tr>
			  <td width="1%" class="lp_ColHeader">Nome do inscrito </td>
<?
		if ( $numeroTipos > 1 ) {
?>
			  <td class="lp_ColHeader" nowrap="nowrap">Tipo de prova</td>
<?
		}
	
		if ( $temQuestoesLingua ) {
?>
			  <td class="lp_ColHeader" nowrap="nowrap">Língua estrangeira</td>
<?
		}
?>
			</tr>
<?
	foreach ($this->_formulario->obterDadosIncompletos() as $d) {
?>
			<tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
				<td nowrap="nowrap"><a title="Editar inscrição &amp; respostas" href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $d->obterSimulado()->obterID(), 'id_inscricao' => $d->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)); ?>"><?= $d->obterAluno()->obterUsuario()->obterNome(); ?></a></td>
<?
		if ( $numeroTipos > 1 ) {
?>
				<td><?= @$this->_formulario->obterHTML('r_tipo_'. $d->obterID(), Formulario::HTML_CAMPO, true); ?></td>
<?
		}
	
		if ( $temQuestoesLingua ) {
?>
				<td><?= @$this->_formulario->obterHTML('r_lingua_'. $d->obterID(), Formulario::HTML_CAMPO, true); ?></td>
<?
		}
?>
			</tr>
<?
	}
?>
			</table>
		  </td>
        </tr>
      </table>
	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar_incompletos', Formulario::HTML_CAMPO, true); ?>
	</div>
<?
}
?>

<?
foreach ( $this->_formulario->questoes as $fase => $tipos ) {
	if ( count($this->_formulario->obterDadosIncompletos()) || $fase > 1 )
		echo '<div class="vd_BlocoEspacador">';
?>
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th class="titulo"><?= $fase; ?>&deg; dia</th>
        </tr>
        <tr>
          <td>
			<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
			<tr>
			  <td class="lp_ColHeader">Nome do inscrito </td>
			  <td class="lp_ColHeader">Ausente </td>
<?
	for ( $i = $this->_formulario->minimosPorFase[$fase]; $i <= $this->_formulario->maximosPorFase[$fase]; $i++ ) {
?>
			  <td width="1%" class="lp_ColHeader" align="center" nowrap="nowrap">Qt. <?= $i; ?> </td>
              <?
	}
?>
			</tr>
<?
	foreach ($this->_formulario->obterDados() as $d) {
?>
			<tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
				<td nowrap="nowrap"><a title="Editar inscrição &amp; respostas" href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $d->obterSimulado()->obterID(), 'id_inscricao' => $d->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_INSCRICOES)); ?>"><?= $d->obterAluno()->obterUsuario()->obterNome(); ?></a></td>
				<td nowrap="nowrap"><?= $this->_formulario->obterHTML('flag_ausente_'.$d->obterID().'_'.$fase, Formulario::HTML_CAMPO, true); ?></td>
<?				
		for ( $i = $this->_formulario->minimosPorFase[$fase]; $i <= $this->_formulario->maximosPorFase[$fase]; $i++ ) {
?>
				<td align="center"><?= @$this->_formulario->obterHTML('r_'. $fase .'_'. $i .'_'. $d->obterID(), Formulario::HTML_CAMPO, true); ?></td>
<?
		}
?>
			</tr>
<?
	}
?>
			</table>
		  </td>
        </tr>
      </table>
<?
	if ( count($this->_formulario->obterDadosIncompletos()) || $fase > 1 )
		echo '</div>';
}
?>
	  
	</td>
  </tr>
</table>
<?= $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoRedirecionamentoRapido(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>