<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo">
<?
if ( $simu->obterID() == null ) {
	echo '<strong class="medio">Adicionando avaliação</strong>';
	echo '<div class="normal"><PERSON><PERSON><PERSON> as informações básicas você poderá adicionar questões e gerênciar as inscrições.</div>';
} else {
	echo '<span class="medio"><strong>'. $simu->obterNome() .'</strong>'. ($this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? ' - Editando avaliação' : '') .'</span>';
}
?>
</div>

<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
<?
if ( $simu->obterID() == null ) {
?>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>>Informações da avalição</th>
<?
} else {
?>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID())); ?>">Informações da avaliação</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_CONTEUDOS) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS)); ?>">Conteúdos</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_QUESTOES) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES)); ?>">Questões</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INSCRICOES) || $this->seletorVisao->visaoEstaSelecionada(self::VISAO_RESPOSTAS) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES)); ?>">Inscrições & Respostas</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::BI_MONTAR) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::BI_MONTAR)); ?>">Selecionar questões pelo Banco de Itens</a></th>
<?
}
?>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>