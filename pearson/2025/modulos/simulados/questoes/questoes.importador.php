<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Aluno', null, true);
Core::incluir('Turma', null, true);
Core::incluir('Serie', null, true);
Core::incluir('Instituicao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('Questao', null, true);
Core::incluir('Disciplina', null, true);
Core::incluir('Habilidade', null, true);

Core::incluir('ImportadorCVS', 'Importador/');
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);

class FImportarQuestoesGH_Exception extends Formulario_Exception { }

class FImportarQuestoesGH extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;
	const PASSO_4 = 3;

	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_campoParaTratar = array();
	protected $_dadosImportados = array();
	protected $_dadosNaoImportados = array();
	public  $_resultadoImportacao = array();

	protected $_simulado = null;
	protected $_fase = 1;

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_dados = array();
		$this->_correspondencias = array(
			'NULA' => '',
			'avaliacao' => 'Id Avaliação',
			'qnumero' => 'Num Questão',
			'qalternativas' => 'Qtd Alternativas',
			'pontuacao' => 'Pontuação',
			'gabarito' => 'Gabarito',
			'habilidades' => 'Habilidades',
			'identificador' => 'Identificador',
			'disciplina' => 'Disciplina ID',
			'fase' => 'Fase',
		);

		$this->_correspondenciasObrigatorias = array('avaliacao','qnumero','qalternativas','pontuacao','gabarito','habilidades','identificador','disciplina','fase');
		$this->_campoParaTratar = array('avaliacao','qnumero','qalternativas','gabarito');
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;

		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );

			$cabecalhoSel = null;
			if ( !$this->foiEnviado() && !isset($_POST['post_anterior'])) {
				$cabecalhoSel = 1;

				if (Core::diretiva('_importador_qgh.cabecalho.ultima_selecao') !== false &&
						Core::diretiva('_importador_qgh.cabecalho.ultima_selecao') == 0)
					$cabecalhoSel = 0;
			}

			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => $cabecalhoSel,
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ';',
													'html_ordem' => $ordem++
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );

			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2) {
			$questaoSel = 0;

			$ordensCorrespondencias = array('matricula');

			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'valor' => (!$this->foiEnviado() && $i > (count($ordensCorrespondencias) - 2) ? $questaoSel++ : null),
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}

			if (!$this->foiEnviado()) {
				foreach ($ordensCorrespondencias as $k => $v) {
					if (isset($this->_campos['correspondencia_' . $k]))
						$this->_campos['correspondencia_' . $k]->fixar('valor', $v);
				}

				if (isset($this->_campos['correspondencia_' . (count(@$this->_dados[0]) - 1)]))
					$this->_campos['correspondencia_' . (count(@$this->_dados[0]) - 1)]->fixar('valor', 'nota_anterior');
			}
		}

		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}

		$habilitado = true;
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$habilitado = false;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ativo' => $habilitado,
												'html_ordem' => $ordem++
							  )) );

		$csvTmp = '';
		$simuladoPossiveis = Simulado::obterSimuladosParaFormularioII();
		foreach ($simuladoPossiveis as $spkey => $spvalue) {
			$s = new Simulado($spkey);
			$s->carregar();
			$qtdQ = $s->obterNumeroQuestoes();

			$questoes = $s->obterQuestoesParaFormulario(false,false,true);
			foreach ($questoes as $qk => $qv) {
				foreach ($qv as $qk2 => $qv2) {
					foreach ($qv2 as $qk3 => $qv3) {
						//$qv3->carregarGabaritos();
						$gab = $qv3->obterGabaritos();
						$num = $gab[0]->obterNumero();

						$pontos = $qv3->obterPontosQuestao();
						$proposicoes = $qv3->obterNumeroProposicoes();

						$gabarito = $gab[0]->obterValor();
						$gabarito = MultiplaEscolha::letra($gabarito);

						$habilidades = array();
						foreach ($qv3->obterHabilidades() as $hk => $hv) {
							$hv->carregar();
		
							$habilidades[] = $hv->obterNome();
						}
						$habilidades = implode(',', $habilidades);

						$identificador = $qv3->obterIdentificador();

						$qv3->obterDisciplina()->carregar();
						$iddisciplina = $qv3->obterDisciplina()->obterNome();
						$fase = $qv3->obterFaseDuracao();

						$csvTmp .= $spvalue.';'.$spkey.';'.$num.';'.$proposicoes.';'.$gabarito.';'.$habilidades.';'.$identificador.';'.$iddisciplina.';'.$fase."\n";
					}
				}
			}
		}

		$this->csv_model = $csvTmp;
	}

	public function checarFormulario ()
	{
		try{
			parent::checarFormulario();

			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');

				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarQuestoesGH_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				$ultimaColuna = 0;
				for ($i = 0; $i < count(@$this->_dados[0]); $i++){
					$coluna = $this->_campos['correspondencia_'. $i]->obter('valor');

					$this->_correspondenciasSel[$i] = $coluna;

					if($coluna != 'NULA'){
						$ultimaColuna = $i;
					}
				}

				if(in_array('questoes', $this->_correspondenciasSel) == true){
					if($this->_correspondenciasSel[$ultimaColuna] != 'questoes'){
						$this->_adicionarErro(null, 'O campo de correspondêcia <i>'.@$this->_correspondencias['questoes'].'</i> deverá ser o último, pois as colunas seguintes representam as questões em sequências;');
						throw new FImportarQuestoesGH_Exception('Correspondência de ordem obrigatória!');
					}
				}

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarQuestoesGH_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarQuestoesGH_Exception('Correspondência obrigatória não selecionada!');
					}
				}

				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}

				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarQuestoesGH_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e){
			throw new FImportarQuestoesGH_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$this->_executarPasso_3();//$importados = $this->_executarPasso_3();
			//Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' séries de respostas importadas com sucesso!', 'Importando respostas...');
			//Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}

	public function &obterDadosImportados () 
	{
		return $this->_dadosImportados;
	}

	public function &obterDadosNaoImportados () 
	{
		return $this->_dadosNaoImportados;
	}

	public function &obterResultadoImportacaoErros () 
	{
		return $this->_resultadoImportacao['down'];
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));

			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;

			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();

				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}

				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}

		if ( $passo == null )
			$passo = self::PASSO_1;

		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;

				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}

		if ( $passo == self::PASSO_4 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));
		}

		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		Core::fixarDiretiva('_importador_qgh.cabecalho.ultima_selecao', $this->_campos['cabecalho']->obter('valor') == 1 ? 1 : 0);

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarqgh'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarQuestoesGH_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarqgh'), $dados );
	}

	protected function _executarPasso_3 () 
	{
		$imgSim = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sim.gif" border="0" />';
		$imgNao = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'nao.gif" border="0" />';

		$simuladoPossiveis = Simulado::obterSimuladosParaFormularioII();












































		$feedback = array('up'=>array(),'down'=>array());
		foreach ($this->_dadosImportados as $linha => $obj) {
			extract($obj);

			$rs = Core::registro('db')->query('
				SELECT * 
				FROM questoes 
				INNER JOIN gabaritos ON q_id = g_questao
				WHERE q_simulado = '.$avaliacao.' AND g_numero = '.$qnumero.';');
			if ($rs->num_rows){
				while ($row = $rs->fetch_assoc()){
					$qid = $row['q_id'];
					$gid = $row['g_id'];
					$qtipo = $row['q_tipo'];

					if($qtipo == 'MULTIPLAESCOLHA'){
						$gabarito = MultiplaEscolha::inteiro($gabarito);

						Core::registro('db')->query(sprintf('
							UPDATE gabaritos 
							SET g_valor = %s
							WHERE g_id = %s',
							Core::registro('db')->formatarValor($gabarito),
							Core::registro('db')->formatarValor($gid))
						);
					}
					else{
						$qalternativas = 0;
					}

					$habilidades_ids = array();
					$habilidades = explode(',', $habilidades);
					$habilidadesAvaliacao = Habilidade::obterArrayTdsHabilidadesParaFormulario();
					foreach ($habilidades as $hkey => $hvalue) {
						$habilidades_ids[] = array_search($hvalue, $habilidadesAvaliacao);						
					}
					$habilidades = implode(',', $habilidades_ids);

					Core::registro('db')->query(sprintf('
						UPDATE questoes 
						SET q_identificador = %s, q_disciplina = %s, q_pontos = %s, q_fase_duracao = %s, q_proposicoes = %s, q_habilidades = %s
						WHERE q_id = %s',
						Core::registro('db')->formatarValor($identificador),
						Core::registro('db')->formatarValor($disciplina),
						Core::registro('db')->formatarValor($pontuacao),
						Core::registro('db')->formatarValor($fase),
						Core::registro('db')->formatarValor($qalternativas),
						Core::registro('db')->formatarValor($habilidades),
						Core::registro('db')->formatarValor($qid))
					);

					if($qalternativas > 0){
						//Verificar quantidade existente de proposições
						$rs2 = Core::registro('db')->query('
							SELECT COUNT(*) AS qtd 
							FROM questoes_proposicoes 
							WHERE qp_questao = '.$qid.';');
						$row2 = $rs2->fetch_assoc();
						$qalternativas2 = $row2['qtd'];

						if($qalternativas != $qalternativas2){
							//remover questoes_proposicoes
							Core::registro('db')->query('
								DELETE FROM questoes_proposicoes 
								WHERE qp_questao = '.$qid.';');
							
							//atualizar questoes_proposicoes
							for($i = 1; $i <= $qalternativas; $i++){
								Core::registro('db')->query(sprintf('
									INSERT INTO questoes_proposicoes (qp_questao, qp_numero) 
									VALUES (%s, %s)',
									Core::registro('db')->formatarValor($qid),
									Core::registro('db')->formatarValor($i))
								);
							}
						}
					}
				}
				$rs->free();
			}
			// colocar aqui para criar
			else{
				$qalternativas = 1;//4;

				$simulado = new Simulado($avaliacao);
				$simulado->carregar();

				$questao = Questao::obterNovaQuestao($qnumero, $simulado);
				$questao->fixarSimulado($simulado);
				$questao->fixarIdentificador($identificador);
				$questao->fixarPontosQuestao($pontuacao);
				#$questao->fixarTipo('MULTIPLAESCOLHA');
				$questao->fixarTipo('DISCURSIVA');
				$questao->fixarDisciplina(new Disciplina($disciplina));
				$questao->fixarConteudo( new Conteudo(null) );
				$questao->fixarNivelDificuldade(1);
				$questao->fixarFaseDuracao($fase);
				//$questao->fixarNumeroProposicoes($qalternativas);
				$questao->fixarSimulado($simulado);
				$questao->salvar();

				//$gabarito = MultiplaEscolha::inteiro($gabarito);

				$gab = Gabarito::obterNovoGabarito($questao);
				$gab->fixarQuestao($questao);
				$gab->fixarNumero($qnumero);
				$gab->fixarValor($gabarito);
				$gab->fixarTipo(1);
				$gab->salvar();

				$habilidades_ids = array();
				$habilidades = explode(',', $habilidades);
				$habilidadesAvaliacao = Habilidade::obterArrayTdsHabilidadesParaFormulario();
				foreach ($habilidades as $hkey => $hvalue) {
					$habilidades_ids[] = array_search($hvalue, $habilidadesAvaliacao);						
				}
				$habilidades = implode(',', $habilidades_ids);

				Core::registro('db')->query(sprintf('
					UPDATE questoes 
					SET q_habilidades = %s
					WHERE q_id = %s',
					Core::registro('db')->formatarValor($habilidades),
					Core::registro('db')->formatarValor($questao->obterID()))
				);
					
				//atualizar questoes_proposicoes
				/* for($i = 1; $i <= $qalternativas; $i++){
					Core::registro('db')->query(sprintf('
						INSERT INTO questoes_proposicoes (qp_questao, qp_numero) 
						VALUES (%s, %s)',
						Core::registro('db')->formatarValor($qid),
						Core::registro('db')->formatarValor($i))
					);
				} */
			}

			$feedback['up'][] = "[$imgSim Sim][Linha $linha] Registro processado.";
		}
































		$this->_resultadoImportacao = $feedback;

		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array('passo' => self::PASSO_4,
					   'dados' => chunk_split(base64_encode(serialize($this->_resultadoImportacao))),
					   'post_anterior' => chunk_split(base64_encode(serialize($this->_entrada)))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post(Gerenciador_URL::gerarLink('simulados', 'importarqgh'), $dados);
	}

	protected function _gerarDadosImportados () 
	{
		$imgSim = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sim.gif" border="0" />';
		$imgNao = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'nao.gif" border="0" />';

		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;

		for ($i; $i < count($this->_dados); $i++){
			$obj = array(
				'feedback_up' => array(),
				'feedback_down' => array(),
					'avaliacao' => null,
					'qnumero' => null,
					'qalternativas' => null,
					'pontuacao' => null,
					'gabarito' => null,
					'habilidades' => null
			);

			foreach ($this->_correspondenciasSel as $j => $cID) {
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;
				
				if(in_array($cID, $this->_campoParaTratar)){
					$valor = ImportadorCVS::limparCharEspeciais($valor);
				}

				$obj[$cID] = $valor;
			}
			
			$importar = 1;
			extract($obj);

			//VALIDAR avaliacao
			if(!empty($avaliacao)){
				$simuladoPossiveis = Simulado::obterSimuladosParaFormularioII();

				if(!array_key_exists($avaliacao, $simuladoPossiveis)){
					$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] A avaliação ('.$avaliacao.') não existe.';
					$importar = 0;
				}
			}
			else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Avaliação - Sem preenchimento.';
			}

			//VALIDAR qnumero
			if(!empty($qnumero)){
				$aOBJ = new Simulado($avaliacao);
				$aOBJ->carregar();

				$questoesNumero = array();
				$questoesSimulado = $aOBJ->obterQuestoesPorID();
				foreach ($questoesSimulado as $qskey => $qsvalue) {
					$g = $qsvalue->obterGabaritos();
					$num = $g[0]->obterNumero();

					$questoesNumero[] = $num;
				}

				if(!in_array($qnumero, $questoesNumero)){
					/* $obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] O número ('.$qnumero.') não existe.';
					$importar = 0; */
					$obj['feedback_up'][] = '['.$imgSim.' Sim][Linha '.($i+1).'] O número ('.$qnumero.') não existe e será criado.';
				}	
			}
			else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Questão Numero - Sem preenchimento.';
			}

			//VALIDAR qalternativas
			if(!empty($qalternativas)){
				if($qalternativas < 0 || $qalternativas > 7){
					$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] A quantidade de alternativas ('.$qalternativas.') não é possível.';
					$importar = 0;
				}
			}
			/*else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Qtd Alternativas - Sem preenchimento.';
			}*/

			//VALIDAR pontuação
			if(!empty($pontuacao)){
				$pontuacao = (float) str_replace(',', '.', $pontuacao);
				if(!is_float($pontuacao)){
					$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] A pontuação ('.$pontuacao.') não é possível.';
					$importar = 0;
				}
			}
			else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Pontuação - Sem preenchimento.';
			}

			//VALIDAR gabarito
			if(!empty($gabarito)){
				$letras = array('A','B','C','D','E','F','G');
				if(!in_array($gabarito, $letras)){
					$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] O gabarito ('.$gabarito.') não é possível.';
					$importar = 0;
				}
			}
			/*else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Gabarito - Sem preenchimento.';
			}*/

			//VALIDAR habilidades
			if(!empty($habilidades)){
				//$habilidadesAvaliacao = Habilidade::obterArrayHabilidadesParaFormulario(new Simulado($avaliacao));
				$habilidadesAvaliacao = Habilidade::obterArrayTdsHabilidadesParaFormulario();

				$habilidades = explode(',', $habilidades);
				foreach ($habilidades as $hk => $hv) {
					if(!array_search($hv, $habilidadesAvaliacao)){
						$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] A habilidade ('.$hv.') não foi encontrada.';
						$importar = 0;
					}
				}
			}
			else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Habilidades - Sem preenchimento.';
			}

			if ($importar){
				$obj['feedback_up'][] = '['.$imgSim.' Sim][Linha '.($i+1).'] Registro será processado.';
				$this->_dadosImportados[($i+1)] = $obj;
			}
			else{
				$this->_dadosNaoImportados[] = $obj;
			}
		}
	}
}

?>