<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('ord_questoes')->obterSaida(); ?>

<?= Core::modulo('procurar_questoes')->obterSaida(); ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
	<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		<tr>
			<td><strong>Questões</strong></td>
			<td align="right"><?= Core::modulo('navegador_questoes')->obterSaida(); ?></td>
		</tr>
	</table>

<?
if ( count($this->_dados) ) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
			<td width="1%" class="lp_ColHeader"><?= $this->_formulario->obterHTML('selecionar_todos', Formulario::HTML_CAMPO, true); ?></td>
			<td width="5%" align="center" class="lp_ColHeader"><?= $this->obterBotaoAlterador('q_identificador'); ?></td>
			<td width="5%" align="center" class="lp_ColHeader">Número da Questão</td>
			<td width="5%" align="center" class="lp_ColHeader">Resposta Correta</td>
			<td width="5%" align="center" class="lp_ColHeader"><?= $this->obterBotaoAlterador('q_pontos', true); ?></td>
			<td width="10%" align="center" class="lp_ColHeader">Habilidades</td>
			<td width="10%" align="center" class="lp_ColHeader"><?= $this->obterBotaoAlterador('q_tipo', true); ?></td>
			<td width="10%" align="center" class="lp_ColHeader"><?= $this->obterBotaoAlterador('q_disciplina', true); ?></td>
			<td width="30%" align="center" class="lp_ColHeader">Sugestão</td>
			<td width="5%" align="center" class="lp_ColHeader">Fase</td>
		</tr>
<?
	$camposIDs = $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true);
	$i = 0;
	foreach ( $this->_dados as &$d ) {
		$gabarito = $d->obterGabaritos();

		$opcoes = array();
		$opcoes[1] = 'A';
		$opcoes[2] = 'B';
		$opcoes[4] = 'C';
		$opcoes[8] = 'D';
		$opcoes[16] = 'E';
		$opcoes[32] = 'F';
		$opcoes[64] = 'G';

		if($d->obterTipo(false) === Questao::FLUENCIA){
			$correta = $gabarito[0]->obterValor().' min';
		}else{
			$correta = isset($opcoes[$gabarito[0]->obterValor()]) ? $opcoes[$gabarito[0]->obterValor()] : '';
		}
		
		$hab_text = array();
		$habilidades = $d->obterHabilidades();
		foreach ($habilidades as $hk => $hv) {
			$hv->carregar();
			$hab_text[] = $hv->obterNome();
		}
		$hab_text = implode(', ', $hab_text);
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
		<td align="center" ><?= $camposIDs[$i++][0]; ?></td>
		<td align="center" <?= $d->foiAnulada() ? 'style="text-decoration: line-through;"' : null; ?>><a title="Editar questão" href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $d->obterSimulado()->obterID(), 'id_questao' => $d->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_QUESTOES)); ?>"><?= $d->obterIdentificador(); ?></a></td>
		<td align="center"><?= $gabarito[0]->obterNumero(); ?></td>
		<td align="center"><?= $correta; ?></td>
		<td align="center"><?= sprintf('%0.02f', $d->obterPontosQuestao()); ?></td>
		<td align="center"><?= $hab_text; ?></td>		
		<td align="center"><?= $d->obterTipo(true); ?></td>
		<td align="center"><? if (!$d->questaoDeOpcao()) { ?><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('disciplinas', 'detalhar', array('id' => $d->obterDisciplina()->obterID())); ?>"><?= $d->obterDisciplina()->obterNome(); ?></a><? } ?></td>
		<td align="center"><?= $d->obterSugestao(); ?></a></td>
		<td align="center"><?= $d->obterFaseDuracao(); ?></a></td>
	</tr>
<?
	}
?>
</table>

	<table width="100%" border="0" cellspacing="3" cellpadding="0">
		<tr>
			<td><?= $this->_formulario->obterHTML('remover', Formulario::HTML_CAMPO, true); ?> 
			<?= $this->_formulario->obterHTML('editar_em_ordem', Formulario::HTML_CAMPO, true); ?></td>
		  <td align="right"><?= $this->_paginacao->paginador->precisaDePaginacao() ? $this->_paginacao->obterSaida() : null; ?></td>
		</tr>
	</table>
	
<?
} else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
			<td><em>Nenhuma quest&atilde;o encontrada </em></td>
		</tr>
	</table>
<?
}
?>

<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>