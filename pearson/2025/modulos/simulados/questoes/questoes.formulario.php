<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Questao', null, true);
Core::incluir('JAlternadorDeVisibilidadePorMudancaComplexo', 'JavaScript/JAlternadorDeVisibilidadePorMudancaComplexo/', true);
Core::incluir('JAlternadorDeVisibilidadePorMudanca', 'JavaScript/JAlternadorDeVisibilidadePorMudanca/', true);
Core::incluir('JTransferidorDeItensDeMenu', 'JavaScript/JTransferidorDeItensDeMenu/', true);
Core::incluir('JMesclador', 'JavaScript/JMesclador/', true);
Core::incluir('JModificadorDeMultiplosItensPorMenu', 'JavaScript/JModificadorDeItensDeMenu/', true);
Core::incluir('JMascaradorDeCampos', 'JavaScript/JMascaradorDeCampos/', true);

class FQuestoes_Exception extends Formulario_Exception { }

class FQuestoes extends Formulario
{
	public $conteudos = array();
	public $disciplinaSelecionada = null;
	public $numeroProposicoesSelecionado = array();
	protected $_simulado;

	public function carregarFormulario (Simulado &$simu)
	{
		parent::carregarFormulario();
		
		Core::modulo('js')->incluirArquivo('includes/JavaScript/tiny_mce/tiny_mce.min.js');
        Core::modulo('js')->incluirArquivo('includes/JavaScript/custom/config-tinymce.js.php');	

		$this->_simulado = $simu;

		$this->_carregar();


		$this->adicionarCampo( new Campo(array( 'nome' => 'identificador',
												'etiqueta' => 'Identificador',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 50),
												'html_tamanho_maximo' => 50,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterIdentificador(),
												'html_ordem' => 1
							  )) );

		$fase = array();
		$faseSel = null;
		for ($i = 1; $i <= $this->_simulado->obterDuracao(); $i++)
			$fase[$i] = $i;

		if ( !$this->foiEnviado() ) {
			if ($this->_dados->obterFaseDuracao() != null && array_key_exists($this->_dados->obterFaseDuracao(), $fase)){
				$faseSel = $this->_dados->obterFaseDuracao();
			}
			elseif ( Core::diretiva('_questoes.fase.ultima_selecao') != false && array_key_exists(Core::diretiva('_questoes.fase.ultima_selecao'), $fase)){
				$faseSel = Core::diretiva('_questoes.fase.ultima_selecao');
			}
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'fase',
												'etiqueta' => 'Fase',
												'valor' => $faseSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($fase)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $fase,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
							  )) );

		$disciplinas = Disciplina::obterArrayDisciplinasParaFormulario();
		$disciplinaSel = null;

		if ( !$this->foiEnviado() ) {
			if ( $this->_dados->obterDisciplina() == null && Core::diretiva('_questoes.disciplina.ultima_selecao') != false &&
					array_key_exists(Core::diretiva('_questoes.disciplina.ultima_selecao'), $disciplinas))
				$disciplinaSel = Core::diretiva('_questoes.disciplina.ultima_selecao');
			elseif ( $this->_dados->obterDisciplina() != null && array_key_exists($this->_dados->obterDisciplina()->obterID(), $disciplinas))
				$disciplinaSel = $this->_dados->obterDisciplina()->obterID();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'disciplina',
												'etiqueta' => 'Disciplina',
												'valor' => $disciplinaSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::INTEIRO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($disciplinas)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $disciplinas,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 3
							  )) );

		$this->disciplinaSelecionada = $this->_campos['disciplina']->obter('valor');
		if ($this->disciplinaSelecionada == null) {
			foreach ( $disciplinas as $dID => $dNome ) {
				$this->disciplinaSelecionada = $dID;
				break;
			}
		}

		// CONTEÚDOS
		$conteudosDIVs = array();
		$conteudosDisponiveis = Conteudo::obterArrayConteudosParaFormulario($this->_simulado);
		foreach ($disciplinas as $dID => $dNome) {
			if ( !array_key_exists($dID, $conteudosDisponiveis) )
				$conteudosDisponiveis[$dID] = array();

			$conteudosDIVs[$dID] = 'div_conteudos_' . $dID;
			$conteudosDisciplina = array('NULO' => '');
			$conteudosDisciplinaSel = null;
			foreach ($conteudosDisponiveis[$dID] as $cID => $cNome) {
				$conteudosDisciplina[$cID] = $cNome;
			}

			if ( !$this->foiEnviado() && $this->_dados->obterConteudo() != null && array_key_exists($this->_dados->obterConteudo()->obterID(), $conteudosDisciplina) )
				$conteudosDisciplinaSel = $this->_dados->obterConteudo()->obterID();

			$this->adicionarCampo( new Campo(array( 'nome' => 'conteudos_'. $dID,
													'etiqueta' => 'Conteúdo',
													'valor' => $conteudosDisciplinaSel,
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($conteudosDisciplina)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $conteudosDisciplina,
													'html_tamanho_tipo' => Campo::HTML_TAM_PORCENTAGEM,
													'html_tamanho' => 99,
													'html_ordem' => 4
								  )) );
		}

		//$this->_campos['disciplina']->fixar('componente', new JAlternadorDeVisibilidadePorMudanca($this->disciplinaSelecionada, $conteudosDIVs, '%s') );


		$nivel = Questao::obterArrayNiveisDificuldade();
		$nivelSel = null;

		if ( array_key_exists( $this->_dados->obterNivelDificuldade(), $nivel ) ) {
			$nivelSel = $this->_dados->obterNivelDificuldade();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'nivel',
												'etiqueta' => 'Nível de dificuldade',
												'valor' => $nivelSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($nivel)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $nivel,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 5
							  )) );

		$pontosSel = null;
		if (!$this->foiEnviado()) {
			if ( $this->_dados->obterPontosQuestao() != null )
				$pontosSel = sprintf('%0.02f', $this->_dados->obterPontosQuestao());
			else
				$pontosSel = sprintf('%0.02f', 1);
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'pontos',
												'etiqueta' => 'Pontos da questão',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::REAL,
												'argumentos' => array(Campo::MAIOR_OU_IGUAL_QUE => 0, Campo::MENOR_OU_IGUAL_QUE => 100),
												'html_tamanho_maximo' => 5,
												'html_tamanho' => 4,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $pontosSel,
												'html_ordem' => 6,
												'componente' => new JMascaradorDeCampos(array(  'type' => 'number',
																							    'groupSymbol' => ',',
																							    'groupDigits' => 3,
																							    'decSymbol' => '.',
																							    'decDigits' => 2,
																							    'stripMask' => false  ))
							  )) );

		$tipos = Questao::obterArrayTiposQuestao();
		$tipoSel = null;

		if ( !$this->foiEnviado() && array_key_exists( $this->_dados->obterTipo(), $tipos ) ) {
			$tipoSel = $this->_dados->obterTipo();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'tipo',
												'etiqueta' => 'Tipo de questão',
												'valor' => $tipoSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($tipos)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $tipos,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 7
							  )) );

		$tipoSel = Questao::SOMATORIO;
		if ( array_key_exists($this->_campos['tipo']->obter('valor'), $tipos) ) {
			$tipoSel = $this->_campos['tipo']->obter('valor');
		}

		$tiposDIVs = array();
		foreach ($tipos as $t => $tv) {
			$tiposDIVs[$t] = 'resp_' . $t;
		}
		$this->_campos['tipo']->fixar('componente', new JAlternadorDeVisibilidadePorMudanca($tipoSel, $tiposDIVs) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'anulada',
												'etiqueta' => 'Questão anulada',
												'valor' => (!$this->foiEnviado() && $this->_dados->foiAnulada() ? 1 : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => 8
							  )) );




		$this->adicionarCampo( new Campo(array( 'nome' => 'sugestao',
			'etiqueta' => 'Sugestão',
			'requerimento' => Campo::OPCIONAL,
			'tipo' => Campo::TEXTO,
			'html_valor' => $this->_dados->obterSugestao(),
			'html_tipo' => Campo::HTML_AREA_TEXTO,
			'html_ordem' => 9
		)) );

		// PREPOSIÇÕES
		$this->adicionarCampo( new Campo(array( 'nome' => 'proposicoes_'. Questao::MULTIPLAESCOLHA,
												'etiqueta' => 'Número de proposições',
												'valor' => (!$this->foiEnviado() && $tipoSel == Questao::MULTIPLAESCOLHA ? $this->_dados->obterNumeroProposicoes() : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys(MultiplaEscolha::$numerosPreposicoes)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => MultiplaEscolha::$numerosPreposicoes,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'proposicoes_'. Questao::SOMATORIO,
												'etiqueta' => 'Número de proposições',
												'valor' => (!$this->foiEnviado() && $tipoSel == Questao::SOMATORIO ? $this->_dados->obterNumeroProposicoes() : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys(Somatoria::$numerosPreposicoes)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => Somatoria::$numerosPreposicoes,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO
							  )) );

		$this->numeroProposicoesSelecionado[Questao::MULTIPLAESCOLHA] = $this->_campos['proposicoes_'. Questao::MULTIPLAESCOLHA]->obter('valor');
		if ( !array_key_exists($this->numeroProposicoesSelecionado[Questao::MULTIPLAESCOLHA], MultiplaEscolha::$numerosPreposicoes) )
			$this->numeroProposicoesSelecionado[Questao::MULTIPLAESCOLHA] = 1;

		$this->numeroProposicoesSelecionado[Questao::SOMATORIO] = $this->_campos['proposicoes_'. Questao::SOMATORIO]->obter('valor');
		if ( !array_key_exists($this->numeroProposicoesSelecionado[Questao::SOMATORIO], Somatoria::$numerosPreposicoes) )
			$this->numeroProposicoesSelecionado[Questao::SOMATORIO] = 1;

		$proposicoes = array();
		if ( $this->obterEstado() == self::EDITANDO ) {
			foreach ( $this->_dados->obterProposicoes() as $p ) {
				$proposicoes[ $p->obterNumero() ] = $p;
			}
		}

		$i = 1;
		$proposicoesMultiplaEscolhaDIVs = array_fill(1, count(MultiplaEscolha::$numerosPreposicoes), array());
		$proposicoesMultiplaEscolhaNomesDIVs = array();
		foreach ( MultiplaEscolha::$opcoes as $j => $opcao ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'proposicoes_'. Questao::MULTIPLAESCOLHA .'_'. $j,
													'etiqueta' => 'Proposição '. $opcao,
													'valor' => (!$this->foiEnviado() && $tipoSel == Questao::MULTIPLAESCOLHA && isset($proposicoes[$j]) ? $proposicoes[$j]->obterTexto() : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_MAX => 255),
													'html_tamanho_maximo' => 255,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => null
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'proposicoes_analise_'. Questao::MULTIPLAESCOLHA .'_'. $j,
													'etiqueta' => 'Proposição '. $opcao,
													'valor' => (!$this->foiEnviado() && $tipoSel == Questao::MULTIPLAESCOLHA && isset($proposicoes[$j]) ? $proposicoes[$j]->obterAnalise() : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_AREA_TEXTO,
													'html_valor' => null
								  )) );

			$proposicoesMultiplaEscolhaNomesDIVs[] = 'div_proposicoes_'. Questao::MULTIPLAESCOLHA .'_'. $j;
			$proposicoesMultiplaEscolhaDIVs[$i++] = $proposicoesMultiplaEscolhaNomesDIVs;
		}

		$proposicoesMultiplaEscolhaSel = Questao::MULTIPLAESCOLHA;
		if ( array_key_exists($this->_campos['proposicoes_'. Questao::MULTIPLAESCOLHA]->obter('valor'), MultiplaEscolha::$numerosPreposicoes) ) {
			$proposicoesMultiplaEscolhaSel = $this->_campos['proposicoes_'. Questao::MULTIPLAESCOLHA]->obter('valor');
		}

		$this->_campos['proposicoes_'. Questao::MULTIPLAESCOLHA]->fixar('componente', new JAlternadorDeVisibilidadePorMudancaComplexo($proposicoesMultiplaEscolhaSel, $proposicoesMultiplaEscolhaDIVs, $proposicoesMultiplaEscolhaNomesDIVs) );


		$i = 1;
		$proposicoesSomatoriosDIVs = array_fill(1, count(Somatoria::$numerosPreposicoes), array());
		$proposicoesSomatoriosNomesDIVs = array();
		foreach ( Somatoria::$opcoes as $j => $opcao ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'proposicoes_'. Questao::SOMATORIO .'_'. $j,
													'etiqueta' => 'Proposição '. $opcao,
													'valor' => (!$this->foiEnviado() && $tipoSel == Questao::SOMATORIO && isset($proposicoes[$j]) ? $proposicoes[$j]->obterTexto() : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_MAX => 255),
													'html_tamanho_maximo' => 255,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => null
								  )) );

			$proposicoesSomatoriosNomesDIVs[] = 'div_proposicoes_'. Questao::SOMATORIO .'_'. $j;
			$proposicoesSomatoriosDIVs[$i++] = $proposicoesSomatoriosNomesDIVs;
		}

		$proposicoesSomatorioSel = Questao::SOMATORIO;
		if ( array_key_exists($this->_campos['proposicoes_'. Questao::SOMATORIO]->obter('valor'), Somatoria::$numerosPreposicoes) ) {
			$proposicoesSomatorioSel = $this->_campos['proposicoes_'. Questao::SOMATORIO]->obter('valor');
		}

		$this->_campos['proposicoes_'. Questao::SOMATORIO]->fixar('componente', new JAlternadorDeVisibilidadePorMudancaComplexo($proposicoesSomatorioSel, $proposicoesSomatoriosDIVs, $proposicoesSomatoriosNomesDIVs) );


		// RESPOSTAS
		$numeros_questao = array();
		for ($i = 1; $i <= 200; $i++) {
			$numeros_questao[$i] = $i;
		}

		// serve para abertas e equivalencias de opções
		$respostas_correta = array();
		for ($i = 0; $i <= 99; $i++) {
			$respostas_correta[$i] = sprintf('%02d', $i);
		}

		$respostas_somatoria_correta = array();
		for ($i = 1; $i <= 99; $i++) {
			$respostas_somatoria_correta[$i] = sprintf('%02d', $i);
		}

		$gabaritos = array();
		if ( $this->obterEstado() == self::EDITANDO ) {
			foreach ( $this->_dados->obterGabaritos() as $g ) {
				$gabaritos[ (int) $g->obterTipo() ] = $g;
			}
		}

		$equivalencias = array();
		if ( $this->obterEstado() == self::EDITANDO ) {
			foreach ( $this->_dados->obterEquivalenciasOpcoes() as $eo ) {
				$equivalencias[ (int) $eo->obterEquivale() ] = $eo;
			}
		}

		// opcão de equivalência para opção de tipo de prova
		$i = 0;
		for ($equivale = 1; $equivale <= $this->_simulado->obterNumeroDeTipos(); $equivale++)
		{
			$this->adicionarCampo( new Campo(array( 'nome' => 'opcao_equivalencia_'. Questao::OPCAO_TIPO_PROVA .'_'. $equivale,
													'etiqueta' => 'Opções de respostas',
													'valor' => (!$this->foiEnviado() ? (isset($equivalencias[$equivale]) ? $equivalencias[$equivale]->obterValor() : ++$i) : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($respostas_correta)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $respostas_correta,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO
								  )) );
		}

		// opcão de equivalência para opção de lingua estrangeira
		$i = 0;
		foreach (Disciplina::obterArrayDisciplinasParaFormulario(true) as $equivale => $dNome ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'opcao_equivalencia_'. Questao::OPCAO_LINGUA_ESTRANGEIRA .'_'. $equivale,
													'etiqueta' => 'Opções de respostas',
													'valor' => (!$this->foiEnviado() ? (isset($equivalencias[$equivale]) ? $equivalencias[$equivale]->obterValor() : $i++) : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($respostas_correta)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $respostas_correta,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO
								  )) );
		}

		// opcão de equivalência para opção de curso de vestibular
		$i = 0;
		foreach (CursoVestibular::obterArrayCursosVestibularParaFormulario() as $equivale => $cNome ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'opcao_equivalencia_'. Questao::OPCAO_CURSO_VESTIBULAR .'_'. $equivale,
													'etiqueta' => 'Opções de respostas',
													'valor' => (!$this->foiEnviado() ? (isset($equivalencias[$equivale]) ? $equivalencias[$equivale]->obterValor() : $i++) : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($respostas_correta)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $respostas_correta,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO
								  )) );
		}

		// números de questões e respostas
		for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++) {
			foreach ($tipos as $t => $tv) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'numero_questao_'. $t .'_' . $i,
														'etiqueta' => 'Número da questão',
														'valor' => (!$this->foiEnviado() && isset($gabaritos[$i]) ? $gabaritos[$i]->obterNumero() : null),
														'requerimento' => Campo::OPCIONAL,
														'tipo' => Campo::NATURAL,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($numeros_questao)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $numeros_questao,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO
									  )) );

				if ($t == Questao::OPCAO_TIPO_PROVA && $i != 1)
					$this->_campos['numero_questao_'. $t .'_' . $i]->fixar('valor', $this->_campos['numero_questao_'. $t .'_1']->obter('valor'));

				if ($t == Questao::FLUENCIA){	
					$this->adicionarCampo( new Campo(array( 'nome' => 'minutos_'. $t .'_' . $i,
									'etiqueta' => 'Número da questão',
									'valor' => (!$this->foiEnviado() && isset($gabaritos[$i]) ? $gabaritos[$i]->obterValor() : null),
									'requerimento' => Campo::OPCIONAL,
									'tipo' => Campo::NATURAL,
									'argumentos' => array(Campo::POSSIBILIDADES => array_keys($numeros_questao)),
									'html_tipo' => Campo::HTML_MENU,
									'html_valor' => $numeros_questao,
									'html_tamanho_tipo' => Campo::HTML_TAM_NULO
					)) );
					//$this->_campos['minutos_'. $t .'_' . $i]->fixar('valor', $this->_campos['minutos_'. $t .'_1']->obter('valor'));
				}
			}

			$this->adicionarCampo( new Campo(array( 'nome' => 'resposta_'. Questao::SOMATORIO .'_correta_' . $i,
													'etiqueta' => 'Resposta correta',
													'valor' => (!$this->foiEnviado() && $tipoSel == Questao::SOMATORIO && isset($gabaritos[$i]) ? $gabaritos[$i]->obterValor() : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($respostas_somatoria_correta)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $respostas_somatoria_correta,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'resposta_'. Questao::MULTIPLAESCOLHA .'_correta_' . $i,
													'etiqueta' => 'Resposta correta',
													'valor' => (!$this->foiEnviado() && $tipoSel == Questao::MULTIPLAESCOLHA && isset($gabaritos[$i]) ? $gabaritos[$i]->obterValor() : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys(MultiplaEscolha::$opcoes)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => MultiplaEscolha::$opcoes,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'resposta_'. Questao::ABERTA .'_correta_' . $i,
													'etiqueta' => 'Resposta correta',
													'valor' => (!$this->foiEnviado() && $tipoSel == Questao::ABERTA && isset($gabaritos[$i]) ? $gabaritos[$i]->obterValor() : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($respostas_correta)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $respostas_correta,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO
								  )) );
		}

		// HABILIDADES
		$habilidades_disp = array();
		$habilidades_sele = array();

		$opcoesPorValorNuloDisciplina = array();
		$opcoesPorValorDisciplina = array();
		foreach ($disciplinas as $dID => $dNome) {
			$habilidadesPorDisciplina = Habilidade::obterArrayHabilidadesParaFormulario( $this->_simulado);

			$opcoesPorValorDisciplina[$dID] = $habilidadesPorDisciplina;
			$opcoesPorValorNuloDisciplina[$dID] = array();

			if ($dID == $this->disciplinaSelecionada)
				$habilidades_disp = $habilidadesPorDisciplina;
		}

		//$componenteDisciplina = new JMesclador('onchange="%s"');
		//$componenteDisciplina->adicionarComponente( $this->_campos['disciplina']->obter('componente') );
		//$componenteDisciplina->adicionarComponente( new JModificadorDeMultiplosItensPorMenu('habilidades_disp[]', $opcoesPorValorDisciplina, '%s') );
		//$componenteDisciplina->adicionarComponente( new JModificadorDeMultiplosItensPorMenu('habilidades_sele[]', $opcoesPorValorNuloDisciplina, '%s') );
		//$this->_campos['disciplina']->fixar('componente', $componenteDisciplina );

		$this->adicionarCampo( new Campo(array( 'nome' => 'habilidades_disp',
												'etiqueta' => 'Habilidades disponíveis',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												//'argumentos' => array(Campo::POSSIBILIDADES => array_keys($habilidades_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'habilidades_sele',
												'etiqueta' => 'Habilidades selecionadas',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												//'argumentos' => array(Campo::POSSIBILIDADES => array_keys($habilidades_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$habilidades_sele_IDs = $this->_campos['habilidades_sele']->obter('valor');
		if (!is_array($habilidades_sele_IDs))
			$habilidades_sele_IDs = array();

		if ( !$this->foiEnviado() ) {
			$habilidades_existentes = array();
			foreach ($this->_dados->obterHabilidades() as $habilidade)
				$habilidades_existentes[] = $habilidade->obterID();

			$habilidades_sele_IDs = array_merge($habilidades_sele_IDs, $habilidades_existentes);
		}

		foreach ($habilidades_sele_IDs as $k => $hID) {
			if ( array_key_exists($hID, $habilidades_disp) )
				$habilidades_sele[$hID] = $habilidades_disp[$hID];
			else
				unset($habilidades_sele_IDs[$k]);
		}

		$habilidades_disp_finais = array();

		foreach ($habilidades_disp as $hID => $hNome) {
			if ( !array_key_exists($hID, $habilidades_sele) )
				$habilidades_disp_finais[$hID] = $hNome;
		}

		$this->_campos['habilidades_sele']->fixar('html_valor', $habilidades_sele);
		$this->_campos['habilidades_disp']->fixar('html_valor', $habilidades_disp_finais);

		$this->adicionarCampo( new Campo(array( 'nome' => 'add_habilidade',
												'etiqueta' => 'Adicionar >>',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_classe' => 'botao salvar',
												'componente' => new JTransferidorDeItensDeMenu('habilidades_disp[]', 'habilidades_sele[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'del_habilidade',
												'etiqueta' => '<< Remover',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao cancelar',
												'componente' => new JTransferidorDeItensDeMenu('habilidades_sele[]', 'habilidades_disp[]')
							  )) );



		$this->adicionarCampo( new Campo(array( 'nome' => 'enunciado',
												'etiqueta' => 'Enunciado',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 100000),
												'html_tipo' => Campo::HTML_AREA_TEXTO,
												'html_linhas' => 4,
												'html_valor' => $this->_dados->obterEnunciado(),
												'html_ordem' => 9
							)) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'resolucao',
												'etiqueta' => 'Resolução / Comentário',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 100000),
												'html_tipo' => Campo::HTML_AREA_TEXTO,
												'html_linhas' => 4,
												'html_valor' => $this->_dados->obterResolucao(),
												'html_ordem' => 10
							)) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'id_questao',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 11
							  )) );

		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outra questão');
		if ( $this->obterEstado() == self::EDITANDO )
			$this->acoesPosEnvio->adicionarAcao('editar', 'Continuar editando');

		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próxima questão');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();

			$this->_dados->fixarTipo($this->_campos['tipo']->obter('valor'));
			$this->_dados->fixarFaseDuracao($this->_campos['fase']->obter('valor'));
			$this->_dados->fixarDisciplina( new Disciplina($this->_campos['disciplina']->obter('valor')) );
			$this->_dados->obterDisciplina()->carregar();

			// checa se a questão é de opção e já tem uma questão desse tipo
			// @todo: se for questão de opção de tipo de prova precisa levar em conta a fase de duração selecionado
			if ( $this->_dados->questaoDeOpcao() ) {
				$questaoDeTipo = $this->_simulado->obterIDQuestaoDoTipo( $this->_dados->obterTipo() );
				if ( $questaoDeTipo !== false && $questaoDeTipo != $this->_dados->obterID() ) {
					$this->_adicionarErro('tipo', 'a avaliação já tem uma questão desse tipo;');
					throw new FQuestoes_Exception('Avaliação já tem esse tipo de questão!');
				}
			}

			// checa se opções de equivalência estão em conflito
			if ( $this->_dados->questaoDeOpcao() ) {
				$opcoes = array(); $total = array();
				if ( $this->_dados->obterTipo() == Questao::OPCAO_TIPO_PROVA ) {
					for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++) { $total[] = $i;	}
				} else if ( $this->_dados->obterTipo() == Questao::OPCAO_LINGUA_ESTRANGEIRA ) {
					$total = array_keys(Disciplina::obterArrayDisciplinasParaFormulario(true));
				} else if ( $this->_dados->obterTipo() == Questao::OPCAO_CURSO_VESTIBULAR ) {
					$total = array_keys(CursoVestibular::obterArrayCursosVestibularParaFormulario());
				}

				foreach ( $total as $equivale ) {
					$opcoes[] = (int) $this->_campos[ 'opcao_equivalencia_'. $this->_dados->obterTipo() .'_'. $equivale ]->obter('valor');
				}
				if ( count(array_unique($opcoes)) != count($opcoes) ) {
					$this->_adicionarErro('opcao_equivalencia_'. Questao::OPCAO_TIPO_PROVA .'_1', 'as opções não podem ser iguais;');
					throw new FQuestoes_Exception('Opções de respostas não podem ser iguais!');
				}
			}

			// checa se número da questão já está cadastrado
			$temQuestoesConflitantes = false;
			for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++)
			{
				$numero = (int) $this->_campos[ 'numero_questao_'. $this->_dados->obterTipo() .'_' . $i ]->obter('valor');

				$questaoConflitante = Gabarito::obterQuestaoDeNumeracaoConflitante($this->_dados, $numero, $i);
				if ( $questaoConflitante != null ) {
					$link = '<a title="Editar questão" href="'. Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), 'id_questao' => $questaoConflitante->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_QUESTOES)) .'">'. $questaoConflitante->obterIdentificador() .'</a>';

					$this->_adicionarErro('numero_questao_'. $this->_dados->obterTipo() .'_' . $i, 'número de questão '. $numero .' (tipo de prova '. $i .') já está cadastrado na questão '. $link .';');
					$temQuestoesConflitantes = true;
				}
			}

			if ($temQuestoesConflitantes) {
				throw new FQuestoes_Exception('Número da questão já cadastrado!');
			}

			// checar se a resposta é possível
			if ( $this->_dados->obterTipo() == Questao::SOMATORIO || $this->_dados->obterTipo() == Questao::MULTIPLAESCOLHA ) {
				$numeroProposicoes = @$this->_campos['proposicoes_'. $this->_dados->obterTipo()]->obter('valor');

				for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++) {
					$resposta = (int) @$this->_campos[ 'resposta_'. $this->_dados->obterTipo() .'_correta_' . $i ]->obter('valor');
					$respostaPossivel = true;
					if ($this->_dados->obterTipo() == Questao::SOMATORIO)
						$respostaPossivel = Somatoria::respostaPossivel($resposta, $numeroProposicoes);
					else
						$respostaPossivel = MultiplaEscolha::respostaPossivel($resposta, $numeroProposicoes);

					if ( !$respostaPossivel ) {
						$this->_adicionarErro('resposta_'. $this->_dados->obterTipo() .'_correta_' . $i, 'essa resposta não é possível;');
						throw new FQuestoes_Exception('A resposta selecionada não é possível!');
					}
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FQuestoes_Exception($e->getMessage());
		}
	}

	public function &executar ()
	{
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Questão adicionada com sucesso!', 'Editando avalição...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar a questão!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Questão editada com sucesso!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar a questão!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}

		Core::fixarDiretiva('_questoes.fase.ultima_selecao', $this->_campos['fase']->obter('valor'));
		Core::fixarDiretiva('_questoes.disciplina.ultima_selecao', $this->_campos['disciplina']->obter('valor'));

		return $this->_dados;
	}

	protected function _carregar ()
	{
		$id = null;

		if (!$this->foiEnviado() && isset($_GET['id_questao'])) {
			$id = $_GET['id_questao'];
		} else {
			$id = (isset($_POST['id_questao']) ? $_POST['id_questao'] : null );
		}

		$this->_dados = new Questao($id);

		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->obterSimulado()->obterID() == $this->_simulado->obterID() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $this->_simulado->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_QUESTOES)), 'Questão inválida!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$this->_dados->fixarSimulado($this->_simulado);
		}
	}

	protected function _adicionar ()
	{
		$this->_dados = Questao::obterNovaQuestao($this->_campos['identificador']->obter('valor'), $this->_simulado);

		if ($this->_dados->obterID() != null) {
			$this->_fixarDadosDaQuestao();
		}

		return $this->_dados->salvar();
	}

	protected function _editar ()
	{
		$this->_fixarDadosDaQuestao();

		return $this->_dados->salvar();
	}

	protected function _fixarDadosDaQuestao ()
	{
		$this->_dados->fixarIdentificador($this->_campos['identificador']->obter('valor'));
		
		// CORREÇÃO: Processar HTML do enunciado e resolução para preservar imagens
		$enunciado = $this->_campos['enunciado']->obter('valor');
		$resolucao = $this->_campos['resolucao']->obter('valor');
		
		// Processar URLs de imagens para garantir que sejam relativas ao sistema
		$enunciado = $this->_processarImagensHTML($enunciado);
		$resolucao = $this->_processarImagensHTML($resolucao);
		
		$this->_dados->fixarEnunciado($enunciado);
		$this->_dados->fixarResolucao($resolucao);
		
		$this->_dados->fixarTipo($this->_campos['tipo']->obter('valor'));
		$this->_dados->fixarFaseDuracao($this->_campos['fase']->obter('valor'));
		$this->_dados->fixarAnulada( ($this->_campos['anulada']->obter('valor') == 1) );
		$this->_dados->fixarSugestao($this->_campos['sugestao']->obter('valor'));

		if ( !$this->_dados->questaoDeOpcao() ) {
			$this->_dados->fixarPontosQuestao($this->_campos['pontos']->obter('valor'));
			$this->_dados->fixarNivelDificuldade($this->_campos['nivel']->obter('valor'));
			$this->_dados->fixarDisciplina( new Disciplina($this->_campos['disciplina']->obter('valor')) );
			$conteudo = null;
			if ( isset( $this->_campos['conteudos_'. $this->_campos['disciplina']->obter('valor')] ) )
				$conteudo = $this->_campos['conteudos_'. $this->_campos['disciplina']->obter('valor')]->obter('valor');
			if ($conteudo == 'NULO' || $this->_campos['disciplina']->obter('valor') == null)
				$conteudo = null;
			$this->_dados->fixarConteudo( new Conteudo($conteudo) );
		} else {
			$this->_dados->fixarPontosQuestao(0);
			$this->_dados->fixarNivelDificuldade(null);
			$this->_dados->fixarDisciplina( new Disciplina(null) );
			$this->_dados->fixarConteudo( new Conteudo(null) );
		}

		if ( $this->_campos['tipo']->obter('valor') == Questao::SOMATORIO ) {
			$this->_dados->fixarNumeroProposicoes($this->_campos['proposicoes_'. Questao::SOMATORIO]->obter('valor'));
		} else if ( $this->_campos['tipo']->obter('valor') == Questao::MULTIPLAESCOLHA ) {
			$this->_dados->fixarNumeroProposicoes($this->_campos['proposicoes_'. Questao::MULTIPLAESCOLHA]->obter('valor'));
		} else {
			$this->_dados->fixarNumeroProposicoes(1);
		}
		$this->_dados->fixarSugestao($this->_campos['sugestao']->obter('valor'));
		
		$this->_fixarHabilidades();
		$this->_fixarProposicoes();
		$this->_fixarGabaritos();
		$this->_fixarEquivalenciasOpcoes();
	}

	protected function _processarImagensHTML($html) {
		if (empty($html)) {
			return $html;
		}
		
		// Regex para encontrar tags img
		$pattern = '/<img[^>]*src=["\']([^"\']+)["\'][^>]*>/i';
		
		$html = preg_replace_callback($pattern, function($matches) {
			$fullTag = $matches[0];
			$srcUrl = $matches[1];
			
			// Se já é uma URL relativa válida do sistema, manter
			if (strpos($srcUrl, '/upload/') !== false) {
				return $fullTag;
			}
			
			// Se é uma URL absoluta do próprio sistema, converter para relativa
			$currentDomain = $_SERVER['HTTP_HOST'];
			if (strpos($srcUrl, $currentDomain) !== false) {
				// Extrair apenas a parte do caminho
				$parsedUrl = parse_url($srcUrl);
				if (isset($parsedUrl['path'])) {
					$newSrc = $parsedUrl['path'];
					return str_replace($srcUrl, $newSrc, $fullTag);
				}
			}
			
			return $fullTag;
		}, $html);
		
		return $html;
	}

	protected function _fixarHabilidades () {
		$this->_dados->fixarHabilidades(array());

		if ( !$this->_dados->questaoDeOpcao() ) {
			foreach ($this->_campos['habilidades_sele']->obter('html_valor') as $hID => $hNome) {
				if (Filtrador::natural($hID))
					$this->_dados->adicionarHabilidade(new Habilidade($hID));
			}
		}
	}

	protected function _fixarProposicoes () {
		if ( $this->_dados->obterTipo() != Questao::SOMATORIO && $this->_dados->obterTipo() != Questao::MULTIPLAESCOLHA )
			return @$this->_dados->removerProposicoes();

		$proposicoes = array();
		foreach ( $this->_dados->obterProposicoes() as $p ) {
			// se for repetido remove...
			if ( array_key_exists($p->obterNumero(), $proposicoes) ) { @$p->remover(); continue; }

			$proposicoes[ $p->obterNumero() ] = $p;
		}

		$opcoesRespostas = array();
		if ( $this->_dados->obterTipo() == Questao::SOMATORIO )
			$opcoesRespostas = Somatoria::$opcoes;
		else
			$opcoesRespostas = MultiplaEscolha::$opcoes;

		$i = 1;
		foreach ( $opcoesRespostas as $j => $opcao ) {
			if ($i++ > $this->_dados->obterNumeroProposicoes()) {
				if ( isset($proposicoes[$j]) )
					@$proposicoes[$j]->remover();

				continue;
			}

			$texto = null;
			if ( isset($this->_campos[ 'proposicoes_'. $this->_dados->obterTipo() .'_'. $j ]) )
				$texto = $this->_campos[ 'proposicoes_'. $this->_dados->obterTipo() .'_'. $j ]->obter('valor');

			if ( isset($this->_campos[ 'proposicoes_analise_'. $this->_dados->obterTipo() .'_'. $j ]) )
				$analise = $this->_campos[ 'proposicoes_analise_'. $this->_dados->obterTipo() .'_'. $j ]->obter('valor');

			if ( $this->obterEstado() == self::EDITANDO && array_key_exists($j, $proposicoes) ) {
				$proposicao = $proposicoes[$j];
			} else {
				$proposicao = Proposicao::obterNovaProposicao($this->_dados);
				$this->_dados->adicionarProposicao($proposicao);
			}

			$proposicao->fixarNumero($j);
			$proposicao->fixarTexto($texto);
			$proposicao->fixarAnalise($analise);
		}
	}

	protected function _fixarGabaritos ()
	{
		$gabaritos = array();
		foreach ( $this->_dados->obterGabaritos() as $g ) {
			// se for repetido remove...
			if ( array_key_exists((int) $g->obterTipo(), $gabaritos) ) { @$g->remover(); continue; }

			$gabaritos[ (int) $g->obterTipo() ] = $g;
		}

		for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++)
		{
			$gabarito = null;
			$tipo = $i;
			$numero = (int) $this->_campos[ 'numero_questao_'. $this->_dados->obterTipo() .'_' . $i ]->obter('valor');
			$valor = null;

			if ( isset( $this->_campos[ 'resposta_'. $this->_dados->obterTipo() .'_correta_' . $i ] ) && $this->_dados->obterTipo() != Questao::DISCURSIVA ) {
				$valor = (int) @$this->_campos[ 'resposta_'. $this->_dados->obterTipo() .'_correta_' . $i ]->obter('valor');
			}
			elseif ( isset( $this->_campos[ 'minutos_'. $this->_dados->obterTipo() .'_' . $i ] ) && $this->_dados->obterTipo() == Questao::FLUENCIA ) {
				$valor = (int) @$this->_campos[ 'minutos_'. $this->_dados->obterTipo() .'_' . $i ]->obter('valor');
			}

			if ( $this->obterEstado() == self::EDITANDO && array_key_exists($i, $gabaritos) ) {
				$gabarito = $gabaritos[$i];
			} else {
				$gabarito = Gabarito::obterNovoGabarito($this->_dados);
				$this->_dados->adicionarGabarito($gabarito);
			}

			$gabarito->fixarTipo($tipo);
			$gabarito->fixarValor($valor);
			$gabarito->fixarNumero($numero);
		}
	}

	protected function _fixarEquivalenciasOpcoes ()
	{
		if ( !$this->_dados->questaoDeOpcao() )
			return @$this->_dados->removerEquivalenciasOpcoes();


		$equivalencias = array();
		foreach ( $this->_dados->obterEquivalenciasOpcoes() as $eo ) {
			// se for repetido remove...
			if ( array_key_exists((int) $eo->obterEquivale(), $equivalencias) ) { @$eo->remover(); continue; }

			$equivalencias[ (int) $eo->obterEquivale() ] = $eo;
		}

		$total = array();
		if ( $this->_dados->obterTipo() == Questao::OPCAO_TIPO_PROVA ) {
			for ($i = 1; $i <= $this->_simulado->obterNumeroDeTipos(); $i++) { $total[] = $i; }
		} else if ( $this->_dados->obterTipo() == Questao::OPCAO_LINGUA_ESTRANGEIRA ) {
			$total = array_keys(Disciplina::obterArrayDisciplinasParaFormulario(true));
		} else if ( $this->_dados->obterTipo() == Questao::OPCAO_CURSO_VESTIBULAR ) {
			$total = array_keys(CursoVestibular::obterArrayCursosVestibularParaFormulario());
		}

		foreach ( $total as $equivale )
		{
			$equivalencia = null;
			$valor = null;

			if ( isset( $this->_campos[ 'opcao_equivalencia_'. $this->_dados->obterTipo() .'_' . $equivale ] ) ) {
				$valor = (int) @$this->_campos[ 'opcao_equivalencia_'. $this->_dados->obterTipo() .'_' . $equivale ]->obter('valor');
			}

			if ( $this->obterEstado() == self::EDITANDO && array_key_exists($equivale, $equivalencias) ) {
				$equivalencia = $equivalencias[$equivale];
			} else {
				$equivalencia = EquivalenciaOpcao::obterNovaEquivalenciaOpcao($this->_dados);
				$this->_dados->adicionarEquivalenciaOpcao($equivalencia);
			}

			$equivalencia->fixarValor($valor);
			$equivalencia->fixarEquivale($equivale);
		}
	}
}

?>
