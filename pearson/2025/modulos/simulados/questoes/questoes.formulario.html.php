<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>
	
	<div class="menor">
	<?
	if ( $this->_formulario->obterEstado() == Formulario::ADICIONANDO ) {
		echo '<strong>Adicionando questão</strong>';
	} else {
		echo '<strong>Editando questão</strong>';
	}
	?>
	</div>
<?
	if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
	}
?>

	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	
	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => $this->seletorVisao->visaoSelecionada()) ); ?>'" value="Cancelar" class="botao cancelar">
<?
if ( $this->_formulario->obterEstado() == Formulario::EDITANDO ) {
?>
	<input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('simulados', 'removerquestao', array('id' => $this->_formulario->obterDados()->obterID())); ?>'; }" value="Remover" class="botao remover">
<?
}
?>
	</div>
	
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="4" class="titulo">Informa&ccedil;&otilde;es da quest&atilde;o  </th>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('identificador', Formulario::HTML_LABEL, true); ?></th>
          <td width="25%"><?= $this->_formulario->obterHTML('identificador', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('fase', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('fase', Formulario::HTML_CAMPO, true); ?> &ordm; dia </td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('disciplina', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('disciplina', Formulario::HTML_CAMPO, true); ?></td>
          <th>Conteúdo </th>
          <td>
		  
<?
	foreach ( $this->_formulario->campo('disciplina')->obter('html_valor') as $dID => $dNome ) {
		echo '<div id="div_conteudos_'. $dID .'" style="'. ($this->_formulario->disciplinaSelecionada != $dID ? 'display: none;' : '') .'">';
		echo $this->_formulario->obterHTML('conteudos_'. $dID, Formulario::HTML_CAMPO, true);
		echo '</div>';
	}
?>
		  
		  </td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('nivel', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('nivel', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('pontos', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('pontos', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('tipo', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('tipo', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('anulada', Formulario::HTML_LABEL, true); ?></th>
          <td><?= $this->_formulario->obterHTML('anulada', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
          <tr>
              <th><?= $this->_formulario->obterHTML('sugestao', Formulario::HTML_LABEL, true); ?></th>
              <td><?= $this->_formulario->obterHTML('sugestao', Formulario::HTML_CAMPO, true); ?></td>
          </tr>
      </table>
	  
<?
$tipoSel = $this->_formulario->campo('tipo')->obter('valor');
?>
	  
	  <div id="resp_<?= Questao::OPCAO_TIPO_PROVA; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::OPCAO_TIPO_PROVA ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::OPCAO_TIPO_PROVA); ?></th>
        </tr>
        <tr>
          <th>Opções de respostas </th>
          <td width="75%"><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Op&ccedil;&atilde;o </th>
            </tr>
            <?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('opcao_equivalencia_'. Questao::OPCAO_TIPO_PROVA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
            <?
	}
?>
          </table></td>
        </tr>
        <tr>
          <th>Gabarito</th>
          <td colspan="3"><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $i == 1 ? $this->_formulario->obterHTML('numero_questao_'. Questao::OPCAO_TIPO_PROVA .'_'.$i, Formulario::HTML_CAMPO, true) : 'idem acima'; ?></td>
              </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::OPCAO_LINGUA_ESTRANGEIRA; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::OPCAO_LINGUA_ESTRANGEIRA ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::OPCAO_LINGUA_ESTRANGEIRA); ?></th>
        </tr>
        <tr>
          <th>Opções de respostas </th>
          <td width="75%">
<?
	$linguas = Disciplina::obterArrayDisciplinasParaFormulario(true);
	if ( count($linguas) ) {
?>
		  <table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">L&iacute;ngua estrangeira </th>
              <th nowrap="nowrap" style="text-align:left;">Op&ccedil;&atilde;o </th>
            </tr>
<?
		foreach ($linguas as $dID => $dNome ) {
?>
            <tr>
              <td nowrap="nowrap"><?= $dNome; ?></td>
              <td><?= $this->_formulario->obterHTML('opcao_equivalencia_'. Questao::OPCAO_LINGUA_ESTRANGEIRA .'_'. $dID, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
		}
?>
          </table>
<?
	} else {
		echo 'Nenhuma língua estrangeira encontrada.';
	}
?>
		  </td>
        </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::OPCAO_LINGUA_ESTRANGEIRA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
              </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::OPCAO_CURSO_VESTIBULAR; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::OPCAO_CURSO_VESTIBULAR ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::OPCAO_CURSO_VESTIBULAR); ?></th>
        </tr>
        <tr>
          <th>Opções de respostas </th>
          <td width="75%">
<?
	$cursos = array();
	foreach ( CursoVestibular::obterArrayCursosVestibularParaFormulario() as $cID => $cNome ) {
		$cursos[] = array( $cID, $cNome );
	}
	
	$total = count($cursos);
	$maximo_linhas = 20;
	
	if ( $total ) {
?>
		  <table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
<?
		$cursosOrd = array();
		$j = -1;
		for ($i = 0; $i < $total; $i++ ) {
			if ( $i % ($maximo_linhas) == 0 ) {
				$j++;
?>
				<th nowrap="nowrap" style="text-align:left;">Curso de vestibular </th>
				<th nowrap="nowrap" style="text-align:left;">Op&ccedil;&atilde;o </th>
<?
				if ( ($j + 1) * $maximo_linhas < $total ) { echo '<td>&nbsp;</td>'; }
			}
			$cursosOrd[ $j ][] = $cursos[$i];
		}
?>
            </tr>
			<tr>
<?		
		$maximo_linhas = $total < $maximo_linhas ? $total : $maximo_linhas;
		for ($i = 0; $i < $maximo_linhas; $i++ ) {
			for ($j = 0; $j <= count($cursosOrd); $j++ ) {
?>
				<td nowrap="nowrap"><?= isset($cursosOrd[$j][$i]) ? $cursosOrd[$j][$i][1] : null; ?></td>
				<td><?= isset($cursosOrd[$j][$i]) ? $this->_formulario->obterHTML('opcao_equivalencia_'. Questao::OPCAO_CURSO_VESTIBULAR .'_'. $cursosOrd[$j][$i][0], Formulario::HTML_CAMPO, true) : null; ?></td>
<?			
				if ( count($cursosOrd) && ($j + 1) < count($cursosOrd) ) { echo '<td>&nbsp;</td>'; }
			}
			echo '</tr><tr>';
		}
?>
			</tr>
          </table>
<?
	} else {
		echo 'Nenhum curso de vestibular encontrado.';
	}
?>
		  </td>
        </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::OPCAO_CURSO_VESTIBULAR .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
              </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::SOMATORIO; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::SOMATORIO ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::SOMATORIO); ?></th>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('proposicoes_'. Questao::SOMATORIO, Formulario::HTML_LABEL, true); ?></th>
          <td width="75%"><?= $this->_formulario->obterHTML('proposicoes_'. Questao::SOMATORIO, Formulario::HTML_CAMPO, true); ?></td>
        </tr>
       <tr>
          <th>Proposições</th>
          <td><table width="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;  width: 1%;">Proposição</th>
              <th nowrap="nowrap" style="text-align:left; width: 90%">Texto</th>
            </tr>
<?
	$i = 1;
	foreach ( Somatoria::$opcoes as $j => $opcao ) {
?>
            <tr id="<?= 'div_proposicoes_'. Questao::SOMATORIO .'_'. $j; ?>" <?= $i++ > @$this->_formulario->numeroProposicoesSelecionado[Questao::SOMATORIO] ? 'style="display: none"' : '';?>>
              <td align="center"><?= $opcao; ?></td>
              <td><?= $this->_formulario->obterHTML('proposicoes_'. Questao::SOMATORIO .'_'. $j, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              <th nowrap="nowrap" style="text-align:left;">Resposta correta</th>
            </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::SOMATORIO .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
              <td><?= $this->_formulario->obterHTML('resposta_'. Questao::SOMATORIO .'_correta_'.$i, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::MULTIPLAESCOLHA; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::MULTIPLAESCOLHA ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::MULTIPLAESCOLHA); ?></th>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('proposicoes_'. Questao::MULTIPLAESCOLHA, Formulario::HTML_LABEL, true); ?></th>
          <td width="75%"><?= $this->_formulario->obterHTML('proposicoes_'. Questao::MULTIPLAESCOLHA, Formulario::HTML_CAMPO, true); ?></td>
        </tr>
       <tr>
          <th>Proposições</th>
          <td><table width="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;  width: 1%;">Proposição</th>
              <th nowrap="nowrap" style="text-align:left; width: 90%">Texto</th>
            </tr>
<?
	$i = 1;
	foreach ( MultiplaEscolha::$opcoes as $j => $opcao ) {
?>
            <tr id="<?= 'div_proposicoes_'. Questao::MULTIPLAESCOLHA .'_'. $j; ?>" <?= $i++ > $this->_formulario->numeroProposicoesSelecionado[Questao::MULTIPLAESCOLHA] ? 'style="display: none"' : '';?>>
              <td align="center"><?= $opcao; ?></td>
              <td>
                <?= $this->_formulario->obterHTML('proposicoes_'. Questao::MULTIPLAESCOLHA .'_'. $j, Formulario::HTML_CAMPO, true); ?>
                <br>  
                <?= $this->_formulario->obterHTML('proposicoes_analise_'. Questao::MULTIPLAESCOLHA .'_'. $j, Formulario::HTML_CAMPO, true); ?>
              </td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              <th nowrap="nowrap" style="text-align:left;">Resposta correta</th>
            </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::MULTIPLAESCOLHA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
              <td><?= $this->_formulario->obterHTML('resposta_'. Questao::MULTIPLAESCOLHA .'_correta_'.$i, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::ABERTA; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::ABERTA ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::ABERTA); ?></th>
        </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              <th nowrap="nowrap" style="text-align:left;">Resposta correta</th>
            </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::ABERTA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
              <td><?= $this->_formulario->obterHTML('resposta_'. Questao::ABERTA .'_correta_'.$i, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::DISCURSIVA; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::DISCURSIVA ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::DISCURSIVA); ?></th>
        </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
            </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::DISCURSIVA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>
	  
	  <div id="resp_<?= Questao::FLUENCIA; ?>" class="vd_BlocoEspacador" style="display: <?= $tipoSel != Questao::FLUENCIA ? 'none' : ''; ?>">
      <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Resposta(s) - <?= Questao::nomeAmigavelTipo(Questao::FLUENCIA); ?></th>
        </tr>
        <tr>
          <th>Gabarito</th>
          <td><table width="1%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th nowrap="nowrap" style="text-align:left;">Tipo de prova</th>
              <th nowrap="nowrap" style="text-align:left;">Número da questão</th>
              <th nowrap="nowrap" style="text-align:left;">Minutos</th>
            </tr>
<?
	for ($i = 1; $i <= $simu->obterNumeroDeTipos(); $i++) {
?>
            <tr>
              <td align="center"><?= $i; ?></td>
              <td><?= $this->_formulario->obterHTML('numero_questao_'. Questao::FLUENCIA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
              <td><?= $this->_formulario->obterHTML('minutos_'. Questao::FLUENCIA .'_'.$i, Formulario::HTML_CAMPO, true); ?></td>
            </tr>
<?
	}
?>
          </table></td>
          </tr>
      </table>
	  </div>

	 <div class="vd_BlocoEspacador">
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Habilidades</th>
        </tr>
        <tr>
          <th>Seleção de habilidades</th>
          <td align="center">
          <table width="98%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('habilidades_disp', Formulario::HTML_LABEL, true); ?></th>
              <td>&nbsp;</td>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('habilidades_sele', Formulario::HTML_LABEL, true); ?></th>            </tr>
            <tr>
              <td><?= $this->_formulario->obterHTML('habilidades_disp', Formulario::HTML_CAMPO, true); ?></td>
              <td align="center"><div style="margin-bottom:4px;"><?= $this->_formulario->obterHTML('add_habilidade', Formulario::HTML_CAMPO, true); ?></div><div><?= $this->_formulario->obterHTML('del_habilidade', Formulario::HTML_CAMPO, true); ?></div></td>
              <td><?= $this->_formulario->obterHTML('habilidades_sele', Formulario::HTML_CAMPO, true); ?></td>
            </tr>
          </table>
          </td>
        </tr>
      </table>
	</div>

        <div class="vd_BlocoEspacador">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
                <tr>
                    <th colspan="2" class="titulo">Enunciado e Resolução</th>
                </tr>
                <tr>
                    <th><?= $this->_formulario->obterHTML('enunciado', Formulario::HTML_LABEL, true); ?></th>
                    <td><?= $this->_formulario->obterHTML('enunciado', Formulario::HTML_CAMPO, true); ?></td>
                </tr>
                <tr>
                    <th><?= $this->_formulario->obterHTML('resolucao', Formulario::HTML_LABEL, true); ?></th>
                    <td><?= $this->_formulario->obterHTML('resolucao', Formulario::HTML_CAMPO, true); ?></td>
                </tr>
            </table>
        </div>

    </td>
  </tr>
</table>
<?= $this->_formulario->obterHTML('id_questao', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoEditarEmOrdem(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>

<?= JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('habilidades_disp[]', 'habilidades_sele[]')); ?>


<script type="text/javascript">
  // Script de sincronização TinyMCE - versão final
  document.addEventListener('DOMContentLoaded', function() {
      var submitInProgress = false;
      var submitAttempts = 0;
      var maxSubmitAttempts = 3;
      
      function findTinyMCEEditors() {
          var editors = [];
          
          if (typeof tinymce !== 'undefined' && tinymce.editors) {
              for (var i = 0; i < tinymce.editors.length; i++) {
                  var editor = tinymce.editors[i];
                  if (editor && editor.initialized && !editor.removed) {
                      editors.push(editor);
                  }
              }
          }
          
          return editors;
      }
      
      function getEditorContentSafe(editor) {
          try {
              if (!editor || editor.removed || !editor.initialized) {
                  return '';
              }
              
              return editor.getContent({
                  format: 'html',
                  no_events: true,
                  skip_invisible: false
              }) || '';
              
          } catch (e) {
              try {
                  var iframe = document.getElementById(editor.id + '_ifr');
                  if (iframe && iframe.contentDocument && iframe.contentDocument.body) {
                      return iframe.contentDocument.body.innerHTML || '';
                  }
              } catch (e2) {
                  // Ignorar
              }
              
              return '';
          }
      }
      
      function syncTinyMCEContentSafe() {
          var editors = findTinyMCEEditors();
          var syncedCount = 0;
          var hasErrors = false;
          
          if (editors.length > 0) {
              for (var i = 0; i < editors.length; i++) {
                  var editor = editors[i];
                  var editorId = editor.id;
                  var textarea = document.getElementById(editorId);
                  
                  if (textarea) {
                      try {
                          var content = getEditorContentSafe(editor);
                          
                          // CORREÇÃO: Validar se conteúdo é válido mesmo sendo só mídia
                          if (content && (content.includes('<img') || content.includes('<audio') || content.includes('<video'))) {
                              var textOnly = content.replace(/<[^>]*>/g, '').trim();
                              // Se só tem mídia sem texto, ainda é válido
                              if (!textOnly || textOnly === '') {
                                  // Garantir que mídia está bem formatada
                                  if (!content.includes('<p>') || !content.includes('</p>')) {
                                      var mediaMatches = content.match(/<(img|audio|video)[^>]*>(<\/audio>|<\/video>)?/gi);
                                      if (mediaMatches && mediaMatches.length > 0) {
                                          var wrappedContent = '';
                                          for (var j = 0; j < mediaMatches.length; j++) {
                                              wrappedContent += '<p>' + mediaMatches[j] + '</p>';
                                          }
                                          content = wrappedContent;
                                      }
                                  }
                              }
                          }
                          
                          if (textarea.value !== content) {
                              textarea.value = content;
                              
                              if (typeof textarea.dispatchEvent === 'function') {
                                  try {
                                      var changeEvent = new Event('change', { bubbles: true });
                                      textarea.dispatchEvent(changeEvent);
                                  } catch (e) {
                                      if (textarea.onchange) {
                                          textarea.onchange();
                                      }
                                  }
                              }
                          }
                          
                          syncedCount++;
                          
                      } catch (e) {
                          //console.log('⚠️ Erro ao sincronizar editor ' + editorId + ':', e.message);
                          hasErrors = true;
                      }
                  }
              }
          }
          
          return {
              success: syncedCount > 0 || document.querySelectorAll('#enunciado, #resolucao').length > 0,
              syncedCount: syncedCount,
              hasErrors: hasErrors
          };
      }
      
      // Interceptar formulário
      var forms = document.querySelectorAll('form');
      
      for (var i = 0; i < forms.length; i++) {
          forms[i].addEventListener('submit', function(e) {
              if (submitInProgress) {
                  e.preventDefault();
                  return false;
              }
              
              submitAttempts++;
              if (submitAttempts > maxSubmitAttempts) {
                  return true;
              }
              
              e.preventDefault();
              submitInProgress = true;
              
              var currentForm = this;
              
              // CORREÇÃO: Verificar se campos têm apenas mídia
              var hasOnlyMedia = false;
              var textareas = document.querySelectorAll('#enunciado, #resolucao');
              for (var j = 0; j < textareas.length; j++) {
                  var content = textareas[j].value || '';
                  if (content.includes('<img') || content.includes('<audio') || content.includes('<video')) {
                      var textOnly = content.replace(/<[^>]*>/g, '').trim();
                      if (!textOnly || textOnly === '') {
                          hasOnlyMedia = true;
                          break;
                      }
                  }
              }
              
              //console.log('📤 Formulário sendo enviado, sincronizando editores...');
              var syncResult = syncTinyMCEContentSafe();
              //console.log('📊 Sincronização concluída:', syncResult);
              
              // Se tem apenas mídia, reduzir tempo de espera
              var waitTime = hasOnlyMedia ? 100 : 300;
              
              setTimeout(function() {
                  try {
                      currentForm.removeEventListener('submit', arguments.callee);
                      
                      submitInProgress = false;
                      
                      // Submit direto sem criar novo evento
                      if (typeof currentForm.submit === 'function') {
                          currentForm.submit();
                      } else {
                          var submitBtn = document.createElement('input');
                          submitBtn.type = 'submit';
                          submitBtn.style.display = 'none';
                          submitBtn.name = '_temp_submit_' + Date.now();
                          currentForm.appendChild(submitBtn);
                          submitBtn.click();
                          currentForm.removeChild(submitBtn);
                      }
                      
                  } catch (e) {
                      //console.log('⚠️ Erro no submit:', e.message);
                      submitInProgress = false;
                      
                      var submitButton = currentForm.querySelector('input[name="enviar"], input[type="submit"], button[type="submit"]');
                      if (submitButton) {
                          submitButton.click();
                      } else {
                          alert('Por favor, clique novamente no botão Salvar');
                      }
                  }
              }, waitTime);
          });
      }
      
      // Funções globais
      window.syncTinyMCE = syncTinyMCEContentSafe;
      
      window.forceSubmit = function() {
          submitInProgress = false;
          submitAttempts = 0;
          
          syncTinyMCEContentSafe();
          
          setTimeout(function() {
              var form = document.querySelector('form');
              if (form) {
                  var newForm = form.cloneNode(true);
                  form.parentNode.replaceChild(newForm, form);
                  newForm.submit();
              }
          }, 100);
      };
      
      window.checkEmptyFields = function() {
          var textareas = document.querySelectorAll('#enunciado, #resolucao');
          var emptyCount = 0;
          var mediaOnlyCount = 0;
          
          for (var i = 0; i < textareas.length; i++) {
              var content = textareas[i].value.trim();
              var textOnly = content.replace(/<[^>]*>/g, '').trim();
              
              // Verificar se é vazio
              if (!textOnly || textOnly === '' || content === '<p><br></p>' || content === '<p></p>') {
                  // Mas tem mídia?
                  if (content.includes('<img') || content.includes('<audio') || content.includes('<video')) {
                      mediaOnlyCount++;
                  } else {
                      emptyCount++;
                  }
              }
          }
          
          return {
              totalEmpty: emptyCount,
              mediaOnly: mediaOnlyCount,
              allEmpty: emptyCount === textareas.length && textareas.length > 0,
              hasMediaOnly: mediaOnlyCount > 0
          };
      };
      
      // Função adicional para forçar submit quando há problemas
      window.emergencySubmit = function() {
          submitInProgress = false;
          submitAttempts = 0;
          
          // Fazer sincronização rápida
          var editors = findTinyMCEEditors();
          for (var i = 0; i < editors.length; i++) {
              var editor = editors[i];
              var textarea = document.getElementById(editor.id);
              if (textarea && editor) {
                  try {
                      textarea.value = editor.getContent() || '';
                  } catch (e) {
                      // Ignorar erros
                  }
              }
          }
          
          // Submit direto
          var form = document.querySelector('form');
          if (form) {
              var submitBtn = form.querySelector('input[name="enviar"], input[type="submit"]');
              if (submitBtn) {
                  submitBtn.click();
              } else {
                  form.submit();
              }
          }
      };
      
      // CORREÇÃO: Função para resolver TinyMCE travado
      window.fixStuckTinyMCE = function() {
          // Procurar por editores travados (com spinner)
          var stuckEditors = document.querySelectorAll('.tox-throbber__busy-spinner');
          
          for (var i = 0; i < stuckEditors.length; i++) {
              var spinner = stuckEditors[i];
              var editorContainer = spinner.closest('[id$="_parent"]');
              
              if (editorContainer) {
                  var editorId = editorContainer.id.replace('_parent', '');
                  
                  // Remover spinner
                  spinner.remove();
                  
                  // Forçar reinicialização do editor
                  if (typeof tinymce !== 'undefined') {
                      try {
                          // Tentar remover editor existente
                          var existingEditor = tinymce.get(editorId);
                          if (existingEditor) {
                              existingEditor.remove();
                          }
                          
                          // Aguardar um pouco e reinicializar
                          setTimeout(function() {
                              // Mostrar textarea temporariamente
                              var textarea = document.getElementById(editorId);
                              if (textarea) {
                                  textarea.style.display = 'block';
                                  
                                  // Tentar reinicializar o TinyMCE
                                  tinymce.init({
                                      selector: '#' + editorId,
                                      // Usar configuração mais simples para recuperação
                                      plugins: 'link image code',
                                      toolbar: 'undo redo | bold italic | link image | code',
                                      height: 300,
                                      menubar: false,
                                      branding: false,
                                      setup: function(editor) {
                                          editor.on('init', function() {
                                              // Editor recuperado
                                              var successMsg = document.createElement('div');
                                              successMsg.style.cssText = 'color: green; font-size: 12px; margin-top: 5px;';
                                              successMsg.innerHTML = '✓ Editor recuperado com sucesso!';
                                              textarea.parentNode.insertBefore(successMsg, textarea.nextSibling);
                                              
                                              setTimeout(function() {
                                                  if (successMsg.parentNode) {
                                                      successMsg.parentNode.removeChild(successMsg);
                                                  }
                                              }, 3000);
                                          });
                                      }
                                  });
                              }
                          }, 1000);
                          
                      } catch (e) {
                          // Se reinicialização falhar, mostrar textarea
                          var textarea = document.getElementById(editorId);
                          if (textarea) {
                              textarea.style.display = 'block';
                              var errorDiv = document.createElement('div');
                              errorDiv.style.cssText = 'color: orange; font-size: 12px; margin-top: 5px;';
                              errorDiv.innerHTML = '⚠ Editor em modo texto. <button onclick="location.reload()" style="margin-left: 10px;">Recarregar Página</button>';
                              textarea.parentNode.insertBefore(errorDiv, textarea.nextSibling);
                          }
                      }
                  }
              }
          }
          
          if (stuckEditors.length === 0) {
              alert('Nenhum editor travado encontrado.');
          } else {
              alert('Tentando recuperar ' + stuckEditors.length + ' editor(es) travado(s)...');
          }
      };
      
      // CORREÇÃO: Detectar automaticamente editores travados após carregamento
      setTimeout(function() {
          var stuckEditors = document.querySelectorAll('.tox-throbber__busy-spinner');
          if (stuckEditors.length > 0) {
              // Aguardar mais um pouco para ver se resolve sozinho
              setTimeout(function() {
                  var stillStuck = document.querySelectorAll('.tox-throbber__busy-spinner');
                  if (stillStuck.length > 0) {
                      if (confirm('Editor TinyMCE parece estar travado. Tentar recuperar automaticamente?')) {
                          fixStuckTinyMCE();
                      }
                  }
              }, 3000);
          }
      }, 5000);
  });
</script>