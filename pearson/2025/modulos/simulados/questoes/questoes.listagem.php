<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');
Core::incluir('Questao', null, true);

class LQuestoes extends Listagem
{
	protected $_procuraSQL = null;
	
	protected $_simulado = null;
	
	public function prepararListagem (Simulado &$simu)
	{
		$this->_simulado = $simu;
	
		$this->_prepararNavegador();
	
		$this->_prepararBusca();
	
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormularioAcoes();
		
		
		Core::modulo('navegador_questoes')->carregarNavegador();

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('questoes.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_questoes')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_questoes')->termoProcurado != null && Core::modulo('procurar_questoes')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_questoes')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_questoes')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}
		
			if ( Core::modulo('procurar_questoes')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(q_identificador, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_questoes')->letraSelecionada);
			}
			
			if ( array_key_exists(Core::modulo('procurar_questoes')->obterResultado('tipo_questao'), Questao::obterArrayTiposQuestao()) ) {
				$this->_procuraSQL[] = 'q_tipo = '. Core::registro('db')->formatarValor( Core::modulo('procurar_questoes')->obterResultado('tipo_questao') );
			}
			
			if ( Filtrador::natural(Core::modulo('procurar_questoes')->obterResultado('disciplina_questao')) ) {
				$this->_procuraSQL[] = 'q_disciplina = '. Core::registro('db')->formatarValor( Core::modulo('procurar_questoes')->obterResultado('disciplina_questao') );
			}
			
			if ( is_array($this->_procuraSQL) ) {
				$this->_procuraSQL = ' AND '. implode(' AND ', $this->_procuraSQL);
			}
		}
	}
	
	protected function _preObterDados ()
	{	
		$rs = Core::registro('db')->query( sprintf(
					'SELECT COUNT(0) AS total FROM questoes
					LEFT JOIN simulados ON simulados.s_id = questoes.q_simulado 
					WHERE s_instituicao = %s AND q_simulado = %s %s',
				Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
				$this->_procuraSQL ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}
	
	protected function _obterDados ()
	{
		$rs = Core::registro('db')->query( sprintf(
					'SELECT questoes.*, disciplinas.* FROM questoes  
					LEFT JOIN simulados ON simulados.s_id = questoes.q_simulado 
					LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina 
					WHERE s_instituicao = %s AND q_simulado = %s %s 
					ORDER BY %s %s 
					LIMIT %s',
				Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
				$this->_procuraSQL,			
				$this->_ordenacao->ordenarPor,
				$this->_ordenacao->tipoOrdem,
				$this->_paginacao->paginador->obterLimitesSQL() ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$questao = new Questao($row['q_id']);
				$questao->carregar();
				
				$questao->fixarIdentificador($row['q_identificador']);
				$questao->fixarEnunciado($row['q_enunciado']);
				$questao->fixarResolucao($row['q_resolucao']);
				$questao->fixarPontosQuestao($row['q_pontos']);
				$questao->fixarTipo($row['q_tipo']);				
				$questao->fixarNivelDificuldade($row['q_nivel']);				
				$questao->fixarFaseDuracao($row['q_fase_duracao']);
				$questao->fixarAnulada($row['q_anulada']);
				
				$disciplina = new Disciplina( $row['d_id'] );
				$disciplina->fixarNome( $row['d_nome'] );
				//$disciplina->fixarInstituicao( Core::registro('instituicao') );
				$questao->fixarDisciplina( $disciplina );
				
				$questao->fixarConteudo( new Conteudo($row['q_conteudo']) );
							
				$questao->fixarSimulado( $this->_simulado );
				$questao->fixarLetraEquivalente($row['q_letra_equivalente']);
				$questao->fixarSugestao($row['q_sugestao'] );
				
				$this->_dados[] = $questao;
			}
		}
		$rs->free();
	}

	private function deParaLetra($posicao){

		$opcoes = array();
		$opcoes[1] = 'A';
		$opcoes[2] = 'B';
		$opcoes[4] = 'C';
		$opcoes[8] = 'D';
		$opcoes[16] = 'E';
		$opcoes[32] = 'F';
		$opcoes[64] = 'G';

		return $opcoes[$posicao];
	}
	
	protected function _prepararNavegador ()
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador_questoes'));
		
		Core::modulo('navegador_questoes')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		
		Core::modulo('navegador_questoes')->removerAtalhosRecarregarPadroes();
		
		Core::modulo('navegador_questoes')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_QUESTOES)), 'Adicionar questão...');
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_questoes'));

		Core::modulo('procurar_questoes')->prepararProcura('form_procurar_questoes', 'caixa_procurar_questoes');
		Core::modulo('procurar_questoes')->configurarTermo();
		Core::modulo('procurar_questoes')->configurarOndeProcurar( array('q_identificador' => 'Identificador da questão', 'q_enunciado' => 'Enunciado da questão') );
		Core::modulo('procurar_questoes')->configurarAlfabeto();
		
		$tipos = array('TODAS' => 'Todos os tipos');
		foreach (Questao::obterArrayTiposQuestao() as $k => $v) {
			$tipos[$k] = $v;
		}
		Core::modulo('procurar_questoes')->configurarListaCustomizada('tipo_questao', 'Tipo de questão', $tipos);

		$disciplinas = array('TODAS' => 'Todas as disciplinas');
		foreach (Disciplina::obterArrayDisciplinasParaFormulario() as $k => $v) {
			$disciplinas[$k] = $v;
		}
		Core::modulo('procurar_questoes')->configurarListaCustomizada('disciplina_questao', 'Disciplina da questão', $disciplinas);
		
		Core::modulo('procurar_questoes')->carregarProcura();
		
		Core::modulo('procurar_questoes')->prepararAtalho( Core::modulo('navegador_questoes') );
	}
	
	protected function _prepararOrdenacao ()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_questoes'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_questoes');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('q_identificador' => 'Identificador', 'q_pontos' => 'Pontos da questão', 'q_tipo' => 'Tipo', 'q_disciplina' => 'Disciplina'), 'q_identificador' );
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();
		
		Core::modulo('ord_questoes')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_questoes')->carregarOrdenacao();
		Core::modulo('ord_questoes')->prepararAtalho( Core::modulo('navegador_questoes'), 'caixa_ord_questoes' );
		
		if ( Core::modulo('procurar_questoes')->procuraSolicitada() ) {
			Core::modulo('ord_questoes')->anexarHTML( Core::modulo('procurar_questoes')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao ()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_questoes'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormularioAcoes ()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_questoes', 'acao' => Gerenciador_URL::gerarLinkPelaEntrada()) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$v) {
			$ids[] = $v->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Remover', 'Tem certeza que deseja REMOVER todos os itens selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();
				
				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {
					if (is_array($ids)) {
						Core::modulo('solicitado')->aRemoverQuestoes($ids);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_QUESTOES, 'id_questao' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				}
			}
			catch (Formulario_Exception $e)	{ }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('questões', $totais);
	}
}

?>