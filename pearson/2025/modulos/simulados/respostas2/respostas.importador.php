<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('ImportadorCVS', 'Importador/');
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);

class FImportarRespostas2_Exception extends Formulario_Exception { }

class FImportarRespostas2 extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;

	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_dadosImportados = array();
	protected $_dadosNaoImportados = array();

	protected $_simulado = null;
	protected $_fase = 1;

	protected $_escolasCriar = array();
	protected $_tumasCriar = array();
	protected $_professores<PERSON>riar = array();

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_dados = array();
		$this->_correspondencias = array(
			'NULA' => '',
			'municipio' => 'Município',
			'regiao' => 'Região',
			'cod_rede' => 'Código da Rede',
			'rede' => 'Rede (E ou M)',
			'escola' => 'Escola',
			'turma' => 'Turma do aluno',
			'professor' => 'Professor',
			'professor_email' => 'Professor Email',
			'professor_passw' => 'Professor Senha',
			'nome' => 'Nome do aluno',
			'numero' => 'Número/Chamada do aluno',
			'matricula' => 'Matrícula',
			'serie' => 'Série',
			'aluno_email' => 'Aluno Email',
			'aluno_passw' => 'Aluno Senha',
		);

		for ($i = 1; $i <= 200; $i++)
			$this->_correspondencias[$i] = 'Questão número ' . $i;

		$this->_correspondenciasObrigatorias = array('escola', 'nome', 'turma' );
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;


		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );

			$cabecalhoSel = null;
			if ( !$this->foiEnviado() && !isset($_POST['post_anterior'])) {
				$cabecalhoSel = 1;

				if (Core::diretiva('_importador_respostas.cabecalho.ultima_selecao') !== false &&
						Core::diretiva('_importador_respostas.cabecalho.ultima_selecao') == 0)
					$cabecalhoSel = 0;
			}


			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => $cabecalhoSel,
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ';',
													'html_ordem' => $ordem++
								  )) );

			$simuladosPossiveis = Simulado::obterSimuladosParaFormulario(true);
			$simuladoSel = null;

			if ( !$this->foiEnviado() && !isset($_POST['post_anterior']) && Core::diretiva('_importador_respostas.simulado.ultima_selecao') != false &&
					array_key_exists(Core::diretiva('_importador_respostas.simulado.ultima_selecao'), $simuladosPossiveis))
				$simuladoSel = Core::diretiva('_importador_respostas.simulado.ultima_selecao');

			$this->adicionarCampo( new Campo(array( 'nome' => 'simulado',
													'etiqueta' => 'Prova',
													'valor' => $simuladoSel,
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosPossiveis)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $simuladosPossiveis,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
													'html_ordem' => $ordem++
							  	)) );
		}

		$this->_fase = 1;
		$this->_simulado = new Simulado( (int) ($this->obterEstado() == self::PASSO_1 ? $this->_campos['simulado']->obter('valor') : $_POST['simulado']) );

		if (!$this->_simulado->carregar())
			$this->_simulado = null;

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );

			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2) {
			$questaoSel = 0;

			$ordensCorrespondencias = array('escola', 'turma', 'professor', 'portador_necessidade', 'nome', 'matricula');

			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'valor' => (!$this->foiEnviado() && $i > (count($ordensCorrespondencias) - 2) ? $questaoSel++ : null),
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}

			if (!$this->foiEnviado()) {
				foreach ($ordensCorrespondencias as $k => $v) {
					if (isset($this->_campos['correspondencia_' . $k]))
						$this->_campos['correspondencia_' . $k]->fixar('valor', $v);
				}

				if (isset($this->_campos['correspondencia_' . (count(@$this->_dados[0]) - 1)]))
					$this->_campos['correspondencia_' . (count(@$this->_dados[0]) - 1)]->fixar('valor', 'nota_anterior');
			}
		}

		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}

		$habilitado = true;
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$habilitado = false;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ativo' => $habilitado,
												'html_ordem' => $ordem++
							  )) );
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();


			if ($this->_simulado === null) {
				$this->_adicionarErro('simulado', 'simulado inválido;');
				throw new FImportarRespostas2_Exception('Simulado inválido!');
			}

			if ($this->_fase < 1 || $this->_fase > $this->_simulado->obterDuracao()) {
				$this->_adicionarErro('fase', 'o simulado selecionado só tem '. $this->_simulado->obterDuracao() . ' fase(s);');
				throw new FImportarRespostas2_Exception('Número de fases inválido!');
			}


			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');

				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarRespostas2_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				for ($i = 0; $i < count(@$this->_dados[0]); $i++)
					$this->_correspondenciasSel[$i] = $this->_campos['correspondencia_'. $i]->obter('valor');

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarRespostas2_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarRespostas2_Exception('Correspondência obrigatória não selecionada!');
					}
				}

				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}

				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarRespostas2_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FImportarRespostas2_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$importados = $this->_executarPasso_3();
			Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' séries de respostas importadas com sucesso!', 'Importando respostas...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}

	public function &obterDadosImportados () {
		return $this->_dadosImportados;
	}

	public function &obterDadosNaoImportados () {
		return $this->_dadosNaoImportados;
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));

			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;
			$_POST['simulado'] = isset($postAnterior['simulado']) ? $postAnterior['simulado'] : null;
			$_POST['fase'] = isset($postAnterior['fase']) ? $postAnterior['fase'] : 1;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;

			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();

				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}

				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}

		if ( $passo == null )
			$passo = self::PASSO_1;

		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;

				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}

		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		Core::fixarDiretiva('_importador_respostas.cabecalho.ultima_selecao', $this->_campos['cabecalho']->obter('valor') == 1 ? 1 : 0);
		Core::fixarDiretiva('_importador_respostas.simulado.ultima_selecao', $this->_campos['simulado']->obter('valor'));

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarrespostaspb'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarRespostas2_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];
		$this->_entrada['simulado'] = $_POST['simulado'];
		$this->_entrada['fase'] = $_POST['fase'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarrespostaspb'), $dados );
	}

	protected function _executarPasso_3 () 
	{
		$disciplinas_para_copiar = array();

		$temQuestoesDeLingua = $this->_simulado->temQuestoesLinguaEstrangeira();
		$linguasDisponiveis = Disciplina::obterArrayDisciplinasParaFormulario(true);
		$cursosDisponiveis = CursoVestibular::obterArrayCursosVestibularParaFormulario();

		$tempQuestoes = $this->_simulado->obterQuestoesParaFormulario(false, true, false);
		$questoes = array();
		$qDisciplinas = array();
		$questoesOpcao = array(Questao::OPCAO_TIPO_PROVA => array(), Questao::OPCAO_LINGUA_ESTRANGEIRA => array(), Questao::OPCAO_CURSO_VESTIBULAR => array());
		if ( isset($tempQuestoes[$this->_fase]) && is_array($tempQuestoes[$this->_fase]) ) {
			foreach ($tempQuestoes[$this->_fase] as $tipoProva => $questoesPorTipo) {
				foreach ($questoesPorTipo as $n => $questoesPorNumero) {
					foreach ($questoesPorNumero as &$q) {
						$qDisciplinas[] = $q->obterDisciplina()->obterID();

						if ($q->questaoDeOpcao())
							$questoesOpcao[$q->obterTipo()][$tipoProva][$n] = $q;
						else
							$questoes[$tipoProva][$n][] = $q;
					}
				}
			}
		}

		// ESCOLAS
		$escolasBuffer = array();
		$escolasPossiveis = Instituicao::obterArrayInstituicoesParaFormulario();

		foreach ($this->_dadosImportados as $k => $obj) {
			$instID = array_search($obj['escola'], $escolasPossiveis);

			if ($instID === false) {
				$endereco = new Endereco(NULL);

				$escola = Instituicao::obterNovaInstituicao($obj['escola'], $endereco);

				if ($escola->obterID()) {
					$escola->fixarNome($obj['escola']);
					$escola->fixarMunicipio($obj['municipio']);
					$escola->fixarRegiao($obj['regiao']);
					$escola->fixarCodRede($obj['cod_rede']);
					$escola->fixarTipoRede($obj['rede']);
					$escola->salvar();

					$instID = $escola->obterID();
					$escolasPossiveis[$instID] = $obj['escola'];

					//copiar disciplinas
					$rsD = Core::registro('db')->query('SELECT * FROM disciplinas WHERE d_instituicao = 0');//'.$instID);
					if ($rsD->num_rows) {
						while ($rowD = $rsD->fetch_assoc()) {
							$objDisciplinaLoad = new Disciplina($rowD['d_id']);
							$objDisciplinaLoad->carregar();

							$disciplinas_para_copiar[] = $objDisciplinaLoad->obterID();
						}
					}
					$rsD->free();
				}
			}

			if ($instID > 0) {
				if (!isset($escolasBuffer[$instID])) {
					$escolasBuffer[$instID] = new Instituicao($instID);
					$escolasBuffer[$instID]->carregar();
				}

				if ($this->_dadosImportados[$k]['aluno_para_criar'] != NULL)
					$this->_dadosImportados[$k]['aluno_para_criar']->obterUsuario()->fixarInstituicao($escolasBuffer[$instID]);
			} else {
				unset($this->_dadosImportados[$k]);
			}
		}

		// TURMAS
		$turmasBuffer = array();
		$turmasPossiveis = Turma::obterArrayTurmasPorInstituicaoParaFormulario();

		foreach ($this->_dadosImportados as $k => $obj) {
			$instID = $obj['aluno_para_criar']->obterUsuario()->obterInstituicao()->obterID();

			$tID = FALSE;

			$serieID = 0;
			$rsT = Core::registro('db')->query( 'SELECT * FROM series WHERE s_nome LIKE "%'.trim($obj['serie']).'%"');
			if ($rsT->num_rows) {
				while ($rowT = $rsT->fetch_assoc()) {
					$serieID = $rowT['s_id'];
				}
			}
			$rsT->free();

			if(empty($serieID)){
				$serieAdd = Serie::obterNovaSerie($obj['serie'], new Instituicao($instID));
				$serieID = $serieAdd->obterID();
			}

			if (isset($turmasPossiveis[$instID])){		
				//$rs = Core::registro('db')->query( 'SELECT * FROM turmas WHERE t_nome LIKE "%'.$obj['turma'].'%" AND t_serie = '.$serieID.' AND t_instituicao = '.$instID);
				$rs = Core::registro('db')->query( 'SELECT * FROM turmas WHERE t_nome LIKE "'.$obj['turma'].'" AND t_serie = '.$serieID.' AND t_instituicao = '.$instID);

				if ($rs->num_rows) {
					while ($row = $rs->fetch_assoc()) {
						$tID = $row['t_id'];
					}
				}
				$rs->free();
			}

			//$tID = FALSE;
			if ($tID === FALSE) {
				$turma = Turma::obterNovaTurma($obj['turma'], new Serie($serieID), $escolasBuffer[$instID]);
				if ($turma->obterID()) {
					$tID = $turma->obterID();
					$turmasPossiveis[$instID][$tID] = $obj['turma'];
				}
			}

			if ($tID > 0) {
				if (!isset($turmasBuffer[$tID])) {
					$turmasBuffer[$tID] = new Turma($tID);
					$turmasBuffer[$tID]->carregar();
				}

				if ($this->_dadosImportados[$k]['aluno_para_criar'] != NULL)
					$this->_dadosImportados[$k]['aluno_para_criar']->fixarTurma($turmasBuffer[$tID]);
			} else {
				unset($this->_dadosImportados[$k]);
			}
		}

		// PROFESSORES
		$this->_professoresCriar = array();
		$this->_professoresCriarEmail = array();
		$this->_professoresCriarPassWord = array();
		foreach ($this->_dadosImportados as $k => $obj) {
			if ($obj['professor'] == NULL)
				continue;

			$instID = $obj['aluno_para_criar']->obterUsuario()->obterInstituicao()->obterID();
			$tID = $obj['aluno_para_criar']->obterTurma()->obterID();

			if (!isset($this->_professoresCriar[$instID]))
				$this->_professoresCriar[$instID] = array();

			if (!isset($this->_professoresCriar[$instID][$obj['professor']]))
				$this->_professoresCriar[$instID][$obj['professor']] = array();

			$this->_professoresCriar[$instID][$obj['professor']][] = $tID;
			$this->_professoresCriarEmail[$instID][$obj['professor']] = $obj['professor_email'];
			$this->_professoresCriarPassWord[$instID][$obj['professor']] = $obj['professor_passw'];
		}

		foreach($this->_professoresCriar as $instID => $professores) {
			$professoresPossiveis = Professor::obterArrayProfessoresParaFormulario($escolasBuffer[$instID]);

			foreach($professores as $pNome => $turmas) {
				$pID = array_search($pNome, $professoresPossiveis);

				if ($pID === FALSE) {
					$professor_email = $this->_professoresCriarEmail[$instID][$pNome];
					$professor_passw = $this->_professoresCriarPassWord[$instID][$pNome];

					$email = Email::obterNovoEmail($professor_email);
					$email->fixarEstadoDaConfirmacao(1);
					$email->fixarChaveConfirmacao(null);
					$email->fixarDataUltimoEnvio(null);
					$email->salvar();

					$usuarioProf = UsuarioInstituido::obterNovoUsuarioInstituido($email, $professor_passw, array(3));
					$usuarioProf->fixarNome( $pNome );
					$usuarioProf->fixarEndereco( new Endereco(null) );
					$usuarioProf->fixarInstituicao( $escolasBuffer[$instID] );

					if ( $usuarioProf->obterID() == null || !$usuarioProf->salvar() )
						continue;

					PrimeiroAcesso::removerPrimeirosAcessosPeloUsuario( $usuarioProf );

					// cria professor
					$novoProfessor = Professor::obterNovoProfessor( $usuarioProf );

					if ( $novoProfessor->obterID() == null )
						continue;

					$professoresPossiveis[ $novoProfessor->obterID() ] = $pNome;

					$pID = $novoProfessor->obterID();
				}

				$qdns = null;
				$qDisciplinas = array_unique($qDisciplinas);
				foreach($turmas as $tID) {
					$professor = new Professor($pID);

					//foreach($qDisciplinas as $qdk => $qdv){
					foreach($disciplinas_para_copiar as $qdk => $qdv){
						$qdns = new Disciplina($qdv);
						$qdns->carregar();

						if ($pID !== FALSE && Aula::obterIDPelosDados($qdns, $turmasBuffer[$tID], $professor, $this->_simulado) == FALSE){
							Aula::obterNovaAula($qdns, $turmasBuffer[$tID], $professor, $this->_simulado);
						}
					}
				}
			}
		}

		// ALUNOS
		$importados = 0;
		foreach ($this->_dadosImportados as $obj) {
			// cadastra no sistema e inscreve caso ainda não esteja inscrito
			if ($obj['aluno_para_criar'] != null) {

				$aluno_email = $obj['aluno_email'];
				if(!empty($aluno_email)){
					$aluno_email = Email::obterNovoEmail($aluno_email);
					$aluno_email->fixarEstadoDaConfirmacao(1);
					$aluno_email->fixarChaveConfirmacao(null);
					$aluno_email->fixarDataUltimoEnvio(null);
					$aluno_email->salvar();

					$obj['aluno_para_criar']->obterUsuario()->fixarEmail($aluno_email);
				}

				$aluno_passw = $obj['aluno_passw'];
				if(empty($aluno_passw)){
					$aluno_passw = $obj['aluno_para_criar']->obterUsuario()->obterSenha();
				}
				$obj['aluno_para_criar']->obterUsuario()->fixarSenha($aluno_passw);

				$obj['inscricao'] = $this->_cadastrarAlunoEInscrever($obj['aluno_para_criar']);

				if ($obj['inscricao'] === false)
					continue;
			}

			// ajusta o tipo de prova do inscrito
			$tipoProva = 1;

			if ($this->_simulado->obterNumeroDeTipos() > 1) {
				$n = (count(@$questoesOpcao[Questao::OPCAO_TIPO_PROVA][1]) ? array_pop( array_keys($questoesOpcao[Questao::OPCAO_TIPO_PROVA][1]) ) : 0);

				if ($n > 0 && isset($obj['novas_respostas'][$n])) {
					$obj['novas_respostas'][$n] = (int) $obj['novas_respostas'][$n];

					foreach ( $questoesOpcao[Questao::OPCAO_TIPO_PROVA][1][$n]->obterEquivalenciasOpcoes() as $eo ) {
						if ( (int) $eo->obterValor() == $obj['novas_respostas'][$n] )
							$obj['inscricao']->fixarTipo( (int) $eo->obterEquivale() );
					}
				}

				$tipoProva = $obj['inscricao']->obterTipo();

				if ($tipoProva < 1 || $tipoProva > $this->_simulado->obterNumeroDeTipos())
					continue;
			}



			// ajusta a língua do inscrito
			if (isset($questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA][$tipoProva])) {
				$n = array_pop( array_keys($questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA][$tipoProva]));

				if ($n > 0 && isset($obj['novas_respostas'][$n])) {
					$obj['novas_respostas'][$n] = (int) $obj['novas_respostas'][$n];

					foreach ( $questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA][$tipoProva][$n]->obterEquivalenciasOpcoes() as $eo ) {
						if ( (int) $eo->obterValor() == $obj['novas_respostas'][$n] )
							$obj['inscricao']->fixarLingua( new Disciplina( (int) $eo->obterEquivale() ) );
					}
				}
			}

			if ($temQuestoesDeLingua && !array_key_exists(@$obj['inscricao']->obterLingua()->obterID(), $linguasDisponiveis) )
				continue;



			// ajusta o curso de vestibular do inscrito
			if (isset($questoesOpcao[Questao::OPCAO_CURSO_VESTIBULAR][$tipoProva])) {
				$n = array_pop( array_keys($questoesOpcao[Questao::OPCAO_CURSO_VESTIBULAR][$tipoProva]));

				if ($n > 0 && isset($obj['novas_respostas'][$n])) {
					$obj['novas_respostas'][$n] = (int) $obj['novas_respostas'][$n];

					foreach ( $questoesOpcao[Questao::OPCAO_CURSO_VESTIBULAR][$tipoProva][$n]->obterEquivalenciasOpcoes() as $eo ) {
						if ( (int) $eo->obterValor() == $obj['novas_respostas'][$n] )
							$obj['inscricao']->fixarCursoVestibular( new CursoVestibular( (int) $eo->obterEquivale() ) );
					}
				}
			}

			if (@$obj['inscricao']->obterCursoVestibular()->obterID() != null && !array_key_exists(@$obj['inscricao']->obterCursoVestibular()->obterID(), $cursosDisponiveis))
				$obj['inscricao']->fixarCursoVestibular( new CursoVestibular( null ) );



			// ajusta as respostas do inscrito
			$respostasMarcadas = array();
			foreach ( $obj['inscricao']->obterRespostasMarcadas() as $r )
				$respostasMarcadas[ (int) $r->obterQuestao()->obterID() ] = $r;

			if (isset($questoes[$tipoProva]) && is_array($questoes[$tipoProva])) {
				foreach ( $questoes[$tipoProva] as $n => $questoesPorNumero ) {
					foreach ( $questoesPorNumero as &$q ) {
						if ( $q->obterDisciplina()->linguaEstrangeira() && $obj['inscricao']->obterLingua()->obterID() != $q->obterDisciplina()->obterID() )
							continue;

						if (isset($obj['novas_respostas'][$n])) {
							$novaResposta = trim($obj['novas_respostas'][$n]);

							switch ($q->obterTipo()) {
								case Questao::MULTIPLAESCOLHA:
									if ( $novaResposta != null && !in_array( strtoupper($novaResposta), MultiplaEscolha::$opcoesParaImportar) &&
											isset( MultiplaEscolha::$opcoesParaImportar[ (int) $novaResposta ] ) )
											$novaResposta = MultiplaEscolha::$opcoesParaImportar[ (int) $novaResposta ];

									$novaResposta = MultiplaEscolha::inteiro($novaResposta);
									if ($novaResposta === false)
										$novaResposta = null;

									break;
								case Questao::DISCURSIVA:
									$novaResposta = str_replace(',', '.', $novaResposta);
									if ( !Filtrador::real($novaResposta) )
										$novaResposta = null;

									break;
								case Questao::ABERTA:
								case Questao::SOMATORIO:
									if ( empty($novaResposta) || !Filtrador::natural((int) $novaResposta) )
										$novaResposta = null;
									else
										$novaResposta = (int) $novaResposta;

									break;
								default:
									break;
							}

							if ($novaResposta !== null) {
								if (!isset($respostasMarcadas[(int) $q->obterID()])) {
									$respostasMarcadas[(int) $q->obterID()] = Resposta::obterNovaResposta( $q, $obj['inscricao'], 'IRESPOSTAS2' );
									$obj['inscricao']->adicionarRespostaMarcada($respostasMarcadas[(int) $q->obterID()]);
								}

								$respostasMarcadas[(int) $q->obterID()]->fixarValor($novaResposta);
							}
						}
					}
				}
			}

			if ( $obj['inscricao']->salvar() )
				$importados++;
		}

		return $importados;
	}

	protected function _cadastrarAlunoEInscrever (Aluno $obj) {
		$nome = $obj->obterUsuario()->obterNome();
		$numMatricula = $obj->obterMatricula();

		$existe = 0;
		$dalunos = Core::registro('db')->query('SELECT * FROM alunos 
									INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario WHERE alunos.a_matricula LIKE "'.$numMatricula.'" LIMIT 1;');
		if ($dalunos->num_rows) {
			while ($dar = $dalunos->fetch_assoc()){
				$aID = $dar['a_id'];
				$uID = $dar['u_id'];
				$currNome = $dar['u_nome'];
				$currNumM = $dar['a_matricula'];

				$existe = 1;
			}
		}

		if($existe){
			#$obj['_motivo'] = 'A matricula '.$matricula.' já existe! Não processado!';
			#$this->_dadosNaoImportados[] = $obj;
			
			return false;

			$u_existe = $uID;
			$a_existe = $aID;
		}
		else{
			$u_existe = 0;
			$a_existe = 0;
		}

		// usuario
		if(!$u_existe || !$a_existe){
			$usuario = UsuarioInstituido::obterNovoUsuarioInstituido($obj->obterUsuario()->obterEmail(), $obj->obterUsuario()->obterSenha(), array(4));

			if ( $usuario == null || $usuario->obterID() == null )
				return false;

			$usuario->fixarEndereco( new Endereco(null) );
			$usuario->fixarInstituicao( $obj->obterUsuario()->obterInstituicao() );
			$usuario->fixarNome( $nome );

			$obj->fixarUsuario($usuario);
		}

		if ( !$obj->obterUsuario()->salvar() )
			return false;

		PrimeiroAcesso::removerPrimeirosAcessosPeloUsuario($obj->obterUsuario());
		//if ( !PrimeiroAcesso::usuarioTemPrimeiroAcesso( $obj->obterUsuario() ) )
		//	PrimeiroAcesso::obterNovoPrimeiroAcesso( $obj->obterUsuario() );

		// aluno
		if(!$a_existe){
			$novoAluno = Aluno::obterNovoAluno( $obj->obterUsuario() );

			$a_existe = $novoAluno->obterID();
		}

		$obj->fixarID( $a_existe );

		if ( $obj->obterID() != null && $obj->salvar() ) {
			// inscreve aluno
			$inscricao = new Inscricao(null);
			$idInscricao = Inscricao::obterIDInscricaoPeloAluno($this->_simulado, $obj);

			if ( !$idInscricao ) {
				$inscricao = Inscricao::obterNovaInscricao($this->_simulado, $obj);

				if ( !$inscricao->salvar() )
					return false;
			} else {
				$inscricao->fixarID($idInscricao);
			}

			if ($inscricao->carregar($this->_simulado))
				return $inscricao;
		}

		return false;
	}

	protected function _gerarDadosImportados () {
		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;

		for ( $i; $i < count($this->_dados); $i++ )	{
			$obj = array(
				'inscricao' => new Inscricao(null),
				'novas_respostas' => array(),
				'precisa_cadastro' => true,
				'escola' => null,
				'turma' => null,
				'professor' => null,
				'aluno_para_criar' => null,
				'nota_anterior' => null,
				'serie' => null
			);

			$escola = $turma = $professor = $professor_email = $professor_passw = $nome = $portador_necessidade = $nota_anterior = $numero = $matricula = $aluno_email = $aluno_passw = $serie = null;

			foreach ($this->_correspondenciasSel as $j => $cID) {
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;

				switch ($cID) {
					case 'municipio':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$municipio = $valor;
						}

						break;
					case 'regiao':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$regiao = $valor;
						}

						break;
					case 'cod_rede':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$cod_rede = $valor;
						}
					case 'rede':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 && ($valor == 'M' || $valor == 'E') ) {
							$valor = str_replace("'", "`", $valor);
							$rede = $valor;
						}

						break;
					case 'nome':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$obj['precisa_cadastro'] = true;
							$nome = $valor;
						}

						break;
					case 'numero':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$numero = $valor;
						}

						break;
					case 'matricula':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$matricula = $valor;
						}

						break;
					case 'escola':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$escola = $valor;
						}

						break;
					case 'turma':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$turma = $valor;
						}

						break;
					case 'professor':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$professor = $valor;
						}
						break;
					case 'professor_email':
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$professor_email = $valor;
						}
						break;
					case 'professor_passw':
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$professor_passw = $valor;
						}
						break;
					case 'serie':	
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$serie = $valor;
						}
						break;
					case 'aluno_email':
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$aluno_email = $valor;
						}
						break;
					case 'aluno_passw':
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);
							$aluno_passw = $valor;
						}
						break;
					default:
						$obj['novas_respostas'][(int) $cID] = $valor;
						break;
				}
			}

			if ( $obj['precisa_cadastro'] ) {
				if ($nome != null && $escola != null && $turma != null) {
					$obj['aluno_para_criar'] = new Aluno(null);
					$obj['aluno_para_criar']->fixarPortadorNecessidade($portador_necessidade);
					$obj['aluno_para_criar']->fixarNumeroChamada($numero);
					$obj['aluno_para_criar']->fixarMatricula($matricula);
					$obj['aluno_para_criar']->fixarUsuario( new UsuarioInstituido(null) );
					$obj['aluno_para_criar']->obterUsuario()->fixarNome( $nome );
					$obj['aluno_para_criar']->obterUsuario()->fixarSenha( $aluno_passw );
					$obj['aluno_para_criar']->obterUsuario()->fixarGrupos( array(4) );
					$obj['aluno_para_criar']->obterUsuario()->fixarEndereco( new Endereco(null) );
					$obj['aluno_para_criar']->obterUsuario()->fixarInstituicao( new Instituicao(NULL) );
					$obj['aluno_para_criar']->obterUsuario()->fixarEmail( new Email(null) );
					$obj['aluno_para_criar']->fixarTurma( new Turma(null) );

					$obj['escola'] = $escola;
					$obj['turma'] = $turma;
					$obj['professor'] = $professor;
					$obj['professor_email'] = $professor_email;
					$obj['professor_passw'] = $professor_passw;
					$obj['serie'] = $serie;
					$obj['aluno_email'] = $aluno_email;
					$obj['aluno_passw'] = $aluno_passw;
					$obj['municipio'] = $municipio;
					$obj['regiao'] = $regiao;
					$obj['cod_rede'] = $cod_rede;
					$obj['rede'] = $rede;
					
					$passou = 1;
					$dalunos = Core::registro('db')->query('SELECT * FROM alunos 
												INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario WHERE alunos.a_matricula LIKE "'.$matricula.'"');
					if ($dalunos->num_rows) {
						while ($dar = $dalunos->fetch_assoc()){
							$aID = $dar['a_id'];
							$uID = $dar['u_id'];
							$currNome = $dar['u_nome'];
							$currNumM = $dar['a_matricula'];

							if($currNumM == $matricula && !empty($matricula)){
								if($currNome != $nome){
									$obj['_valor_nome'] = $nome;
									$obj['_motivo'] = 'Aluno com a matricula '.$matricula.' já existe no sistema, porem com o nome '.$currNome.'. Verifique! <br> {id aluno:'.$aID.',id usuario:'.$uID.',nome importado:'.$nome.',matricula importada:'.$matricula.',nome no sistema:'.$currNome.',matricula no sistema:'.$currNumM.'}';
									$this->_dadosNaoImportados[] = $obj;
									$passou = 0;
								}
							}
						}
					}

					if ( count($obj['novas_respostas']) && $passou)
						$this->_dadosImportados[] = $obj;
				}
			}
		}
	}
}

?>