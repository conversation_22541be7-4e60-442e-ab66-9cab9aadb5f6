<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio"><strong><?= $simu->obterNome(); ?></strong></div>

<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID())); ?>">Informações da avaliação</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_CONTEUDOS) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS)); ?>">Conteúdos</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_QUESTOES) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES)); ?>">Questões</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INSCRICOES) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES)); ?>">Inscrições & Respostas</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_MAIS_INFO)); ?>">Mais informa&ccedil;&otilde;es</a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::BI_MONTAR) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::BI_MONTAR)); ?>">Selecionar quest&otilde;es pelo Banco de Itens</a></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<?
if ( $this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO) ) {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

    <table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
      <tr>
      <td><strong>Hist&oacute;rico de registro </strong></td>
      <td align="right"><input type="button" value="Limpar hist&oacute;rico" class="botao novo"></td>
      </tr>
    </table>

  <table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
    <tr>
      <td width="10%" align="center" class="lp_ColHeader">Data</td>
      <td nowrap="nowrap" class="lp_ColHeader">Grupo</td>
      <td nowrap="nowrap" class="lp_ColHeader">Tipo</td>
      <td nowrap="nowrap" class="lp_ColHeader">Descri&ccedil;&atilde;o</td>
      <td nowrap="nowrap" class="lp_ColHeader">Refer&ecirc;ncia</td>
    </tr>

    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
      <td align="center" nowrap="nowrap" >---</td>
    <td>AVALIAÇÕES</td>
    <td>cria&ccedil;&atilde;o</td>
    <td>Avaliação cadastrada </td>
    <td><a href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID())); ?>"><?= $simu->obterNome(); ?></a></td>
    </tr>
  </table>

  </td>
  </tr>
</table>
<?
} else if ( $this->seletorVisao->visaoEstaSelecionada(self::VISAO_QUESTOES) ) {

    $listagemQuestoes = new LQuestoes();

    $listagemQuestoes->prepararListagem($simu);
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td><?= $listagemQuestoes->obterSaida(); ?></td>
  </tr>
</table>
<?
} else if ( $this->seletorVisao->visaoEstaSelecionada(self::VISAO_CONTEUDOS) ) {

    $listagemConteudos = new LConteudos();

    $listagemConteudos->prepararListagem($simu);
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td><?= $listagemConteudos->obterSaida(); ?></td>
  </tr>
</table>
<?
} else if ( $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INSCRICOES) ) {

    $listagemInscricoes = new LInscricoes();

    $listagemInscricoes->prepararListagem($simu);
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td><?= $listagemInscricoes->obterSaida(); ?></td>
  </tr>
</table>
<?
} else {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

  <div class="vd_BlocoBotoes">
  <table width="100%" border="0" cellspacing="0" cellpadding="0">
    <tr>
    <td><input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID())); ?>'" value="Editar" class="botao editar"></td>
    <td align="right"><input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('simulados', 'remover', array('id' => $simu->obterID())); ?>'; }" value="Remover" class="botao remover"></td>
    </tr>
  </table>
  </div>

    <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="4" class="titulo">Informa&ccedil;&otilde;es da avaliação </th>
        </tr>
          <tr>
          <th>Nome</th>
          <td width="25%"><?= $simu->obterNome(); ?></td>
            <th>Bimestre</th>
            <td width="25%"><?= $simu->obterBimestre(); ?></td>
         </tr>
          <tr>
              <th>Ordem na lista de lançamento</th>
              <td width="25%"><?= $simu->obterOrdem(); ?></td>
              <th>Simulado pai/filho</th>
              <td width="25%"><?= $simu->obterPaiFilho(); ?></td>
          </tr>
          <tr>
              <th>Per&iacute;odo de realiza&ccedil;&atilde;o </th>
              <td><strong><?= strftime('%d/%m/%Y %H:%M', $simu->obterDataRealizacao()); ?> até <?= strftime('%d/%m/%Y %H:%M', $simu->obterDataRealizacaoFim()); ?></strong></td>
              <th>Per&iacute;odo de "Atualiza&#231;&#227;o de Turmas"</th>
              <td><?= strftime('%d/%m/%Y %H:%M', $simu->obterDataInicioInscricoes()); ?> até <?= strftime('%d/%m/%Y %H:%M', $simu->obterDataFimInscricoes()); ?></td>

          </tr>
        <tr>
          <th>Duração</th>
          <td><?= $simu->obterDuracao(true); ?></td>
          <th>Tipos de provas</th>
          <td><?= $simu->obterNumeroDeTipos(); ?></td>
        </tr>
        <tr>
          <th>Inscritos</th>
          <td><?= $simu->obterNumeroInscritosPelaInstituicao(Core::registro('instituicao'), true); ?></td>
          <th>L&iacute;nguas estrangeiras compartilham n&uacute;mero da quest&atilde;o</th>
          <td><img src="<?= Core::diretiva('ESTILO:DIRETORIO:media') . ($simu->linguasCompartilham() ? 'sim' : 'nao') .'.gif'; ?>" border="0" /></td>
        </tr>
        <tr>
          <th>Questões</th>
          <td><?= $simu->obterNumeroQuestoes(true, true, '<br />'); ?></td>
          <th>Pontuta&ccedil;&atilde;o total</th>
          <td><?= $simu->obterPontuacaoTotal(true, true, '<br />'); ?></td>
        </tr>
        <tr>
          <th>Período de lançamento</th>
          <td>
            <?= strftime('%d/%m/%Y %H:%M', $simu->obterInicioLancamento()); ?>
             até 
            <?= strftime('%d/%m/%Y %H:%M', $simu->obterFimLancamento()); ?>
          </td>
          <th>Inscrições abertas</th>
          <td><img src="<?= Core::diretiva('ESTILO:DIRETORIO:media') . ($simu->inscricoesEstaoAbertas() ? 'sim' : 'nao') .'.gif'; ?>" border="0" /></td>
        </tr>
        <tr>
          <th>Série</th>
          <td><?php if(!is_null($simu->obterSerieAvaliacao())){ echo $simu->obterSerieAvaliacao()->obterNome(); } ?></td>
          <th>Aplicação</th>
          <td><?= $simu->obterBimestre(); ?></td>
        </tr>
        <tr>
          <th>Produção Textual</th>
          <td>
            <?php 
              if($simu->obterProdTxt()){
                echo"Sim";
              }
              else{
                echo"Não";
              }
            ?>
          </td>
          <th>Avaliação Especial</th>
          <td>
            <?php 
              if($simu->obterAvEspecial()){
                echo"Sim";
              }
              else{
                echo"Não";
              }
            ?>
          </td>
        </tr>
        <tr>
          <th>Seção Relatórios</th>
          <td>
            <?= $simu->obterSecaoRels(); ?>
          </td>
        </tr>
      </table>

  <div class="vd_BlocoEspacador">
<?
if ($simu->obterEndereco() != null) {
  Core::carregarModulo(array('nome' => '_endereco', 'classe' => 'MEndereco', 'guardar_como' => '_endereco'));
  Core::modulo('_endereco')->fixarTituloEndereco('Endereço de realização');
  Core::modulo('_endereco')->mostrarDetalhes($simu->obterEndereco());

  echo Core::modulo('_endereco')->obterSaida();
} else {
?>
  <div align="right" class="vd_BlocoBotoes">
    <input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID())); ?>'" value="Cadastrar endereço" class="botao editar">
  </div>
    <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th class="titulo">Endereço de realiza&ccedil;&atilde;o </th>
        </tr>
        <tr>
          <td>Endereço não cadastrado!</td>
        </tr>
      </table>
<?
}
?>
  </div>

  </td>
  </tr>
</table>
<?
}
?>