<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('ord_conteudos')->obterSaida(); ?>

<?= Core::modulo('procurar_conteudos')->obterSaida(); ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
	<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		<tr>
			<td><strong>Conteúdos</strong></td>
			<td align="right"><?= Core::modulo('navegador_conteudos')->obterSaida(); ?></td>
		</tr>
	</table>

<?
if ( count($this->_dados) ) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
			<td width="1%" class="lp_ColHeader"><?= $this->_formulario->obterHTML('selecionar_todos', Formulario::HTML_CAMPO, true); ?></td>
			<td class="lp_ColHeader"><?= $this->obterBotaoAlterador('sc_nome'); ?></td>
			<td width="20%" align="center" class="lp_ColHeader"><?= $this->obterBotaoAlterador('sc_disciplina', true); ?></td>
		</tr>
<?
	$camposIDs = $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true);
	$i = 0;
	foreach ( $this->_dados as &$d ) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
		<td align="center" ><?= $camposIDs[$i++][0]; ?></td>
		<td><a title="Editar conteúdo" href="<?= Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $d->obterSimulado()->obterID(), 'id_conteudo' => $d->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_CONTEUDOS)); ?>"><?= $d->obterNome(); ?></a></td>
		<td align="center"><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('disciplinas', 'detalhar', array('id' => $d->obterDisciplina()->obterID())); ?>"><?= $d->obterDisciplina()->obterNome(); ?></a></td>	
    </tr>
<?
	}
?>
</table>

	<table width="100%" border="0" cellspacing="3" cellpadding="0">
		<tr>
			<td><?= $this->_formulario->obterHTML('remover', Formulario::HTML_CAMPO, true); ?> 
			<?= $this->_formulario->obterHTML('editar_em_ordem', Formulario::HTML_CAMPO, true); ?></td>
		  <td align="right"><?= $this->_paginacao->paginador->precisaDePaginacao() ? $this->_paginacao->obterSaida() : null; ?></td>
		</tr>
	</table>
	
<?
} else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
			<td><em>Nenhum conteúdo encontrado </em></td>
		</tr>
	</table>
<?
}
?>

<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>