<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Conteudo', null, true);

class FConteudos_Exception extends Formulario_Exception { }

class FConteudos extends Formulario
{
	protected $_simulado;

	public function carregarFormulario (Simulado &$simu)
	{
		parent::carregarFormulario();
		
		$this->_simulado = $simu;

		$this->_carregar();
		

		$this->adicionarCampo( new Campo(array( 'nome' => 'nome',
												'etiqueta' => 'Nome',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 50),
												'html_tamanho_maximo' => 50,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterNome(),
												'html_ordem' => 1
							  )) );

		$disciplinas = Disciplina::obterArrayDisciplinasParaFormulario();
		$disciplinaSel = null;
		
		if ( !$this->foiEnviado() ) {
			if ( Core::diretiva('_conteudos.disciplina.ultima_selecao') != false && 
					array_key_exists(Core::diretiva('_conteudos.disciplina.ultima_selecao'), $disciplinas))
				$disciplinaSel = Core::diretiva('_conteudos.disciplina.ultima_selecao');
			else if ($this->_dados->obterDisciplina() != null && array_key_exists($this->_dados->obterDisciplina()->obterID(), $disciplinas))
				$disciplinaSel = $this->_dados->obterDisciplina()->obterID();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'disciplina',
												'etiqueta' => 'Disciplina',
												'valor' => $disciplinaSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::INTEIRO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($disciplinas)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $disciplinas,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
							  )) );
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'id_conteudo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
									  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 3
							  )) );
							  
		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outro conteúdo');
		if ( $this->obterEstado() == self::EDITANDO )
			$this->acoesPosEnvio->adicionarAcao('editar', 'Continuar editando');
		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próximo conteúdo');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e)
		{
			throw new FConteudos_Exception($e->getMessage());
		}
	}
	
	public function &executar ()
	{
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Conteúdo adicionado com sucesso!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o conteúdo!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Conteúdo editado com sucesso!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar o conteúdo!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		Core::fixarDiretiva('_conteudos.disciplina.ultima_selecao', $this->_campos['disciplina']->obter('valor'));
		
		return $this->_dados;
	}
	
	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id_conteudo'])) {
			$id = $_GET['id_conteudo'];
		} else {
			$id = (isset($_POST['id_conteudo']) ? $_POST['id_conteudo'] : null );
		}
		
		$this->_dados = new Conteudo($id);
		
		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->obterSimulado()->obterID() == $this->_simulado->obterID() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $this->_simulado->obterID(), @Core::modulo('solicitado')->seletorVisao->nomeSeletor() => MSimulados::VISAO_CONTEUDOS)), 'Conteúdo inválido!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$this->_dados->fixarSimulado($this->_simulado);
		}
	}
	
	protected function _adicionar ()
	{
		$this->_dados = Conteudo::obterNovoConteudo($this->_campos['nome']->obter('valor'), $this->_simulado);
		
		if ($this->_dados->obterID() != null) {
			$this->_fixarDadosDoConteudo();
		}
		
		return $this->_dados->salvar();
	}
	
	protected function _editar ()
	{
		$this->_fixarDadosDoConteudo();
		
		return $this->_dados->salvar();
	}
	
	protected function _fixarDadosDoConteudo ()
	{
		$this->_dados->fixarNome($this->_campos['nome']->obter('valor'));
		
		// se mudar a disciplina precisa atualizar as questões
		if ( $this->obterEstado() == self::EDITANDO && $this->_dados->obterDisciplina()->obterID() != $this->_campos['disciplina']->obter('valor') ) {
			$this->_dados->atualizarConteudosQuestoes();
		}
		$this->_dados->fixarDisciplina( new Disciplina($this->_campos['disciplina']->obter('valor')) );
	}

}

?>