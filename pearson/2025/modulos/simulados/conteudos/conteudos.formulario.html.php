<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>
	
	<div class="menor">
	<?
	if ( $this->_formulario->obterEstado() == Formulario::ADICIONANDO ) {
		echo '<strong>Adicionando conteúdo</strong>';
	} else {
		echo '<strong>Editando conteúdo</strong>';
	}
	?>
	</div>
<?
	if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
	}
?>

	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	
	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => $this->seletorVisao->visaoSelecionada()) ); ?>'" value="Cancelar" class="botao cancelar">
<?
if ( $this->_formulario->obterEstado() == Formulario::EDITANDO ) {
?>
	<input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('simulados', 'removerconteudo', array('id' => $this->_formulario->obterDados()->obterID())); ?>'; }" value="Remover" class="botao remover">
<?
}
?>
	</div>
	
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="4" class="titulo">Informa&ccedil;&otilde;es do conteúdo  </th>
        </tr>
        <tr>
          <th><?= $this->_formulario->obterHTML('nome', Formulario::HTML_LABEL, true); ?></th>
          <td width="25%"><?= $this->_formulario->obterHTML('nome', Formulario::HTML_CAMPO, true); ?></td>
          <th><?= $this->_formulario->obterHTML('disciplina', Formulario::HTML_LABEL, true); ?></th>
          <td colspan="3"><?= $this->_formulario->obterHTML('disciplina', Formulario::HTML_CAMPO, true); ?></td>
        </tr>
      </table>

	</td>
  </tr>
</table>
<?= $this->_formulario->obterHTML('id_conteudo', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoEditarEmOrdem(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>