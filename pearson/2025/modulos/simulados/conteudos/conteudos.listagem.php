<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');
Core::incluir('Conteudo', null, true);

class LConteudos extends Listagem
{
	protected $_procuraSQL = null;	
	protected $_simulado = null;
	
	public function prepararListagem (Simulado &$simu)
	{
		$this->_simulado = $simu;
	
		$this->_prepararNavegador();
	
		$this->_prepararBusca();
	
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormularioAcoes();
		
		
		Core::modulo('navegador_conteudos')->carregarNavegador();

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('conteudos.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_conteudos')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_conteudos')->termoProcurado != null && Core::modulo('procurar_conteudos')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_conteudos')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_conteudos')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}
		
			if ( Core::modulo('procurar_conteudos')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(sc_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_conteudos')->letraSelecionada);
			}
			
			if ( Filtrador::natural(Core::modulo('procurar_conteudos')->obterResultado('disciplina_conteudo')) ) {
				$this->_procuraSQL[] = 'sc_disciplina = '. Core::registro('db')->formatarValor( Core::modulo('procurar_conteudos')->obterResultado('disciplina_conteudo') );
			}
			
			if ( is_array($this->_procuraSQL) ) {
				$this->_procuraSQL = ' AND '. implode(' AND ', $this->_procuraSQL);
			}
		}
	}
	
	protected function _preObterDados ()
	{	
		$rs = Core::registro('db')->query( sprintf(
					'SELECT COUNT(0) AS total FROM simulados_conteudos 
					LEFT JOIN simulados ON simulados.s_id = simulados_conteudos.sc_simulado 
					WHERE s_instituicao = %s AND sc_simulado = %s %s',
				Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
				$this->_procuraSQL ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}
	
	protected function _obterDados ()
	{
		$rs = Core::registro('db')->query( sprintf(
					'SELECT simulados_conteudos.*, disciplinas.* FROM simulados_conteudos  
					LEFT JOIN simulados ON simulados.s_id = simulados_conteudos.sc_simulado 
					LEFT JOIN disciplinas ON disciplinas.d_id = simulados_conteudos.sc_disciplina 
					WHERE s_instituicao = %s AND sc_simulado = %s %s 
					ORDER BY %s %s 
					LIMIT %s',
				Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ),
				Core::registro('db')->formatarValor( $this->_simulado->obterID() ),
				$this->_procuraSQL,			
				$this->_ordenacao->ordenarPor,
				$this->_ordenacao->tipoOrdem,
				$this->_paginacao->paginador->obterLimitesSQL() ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$conteudo = new Conteudo($row['sc_id']);
				
				$conteudo->fixarNome($row['sc_nome']);
				
				$disciplina = new Disciplina( $row['d_id'] );
				$disciplina->fixarNome( $row['d_nome'] );
				//$disciplina->fixarInstituicao( ProvaFloripa::$instituicao );
				$conteudo->fixarDisciplina( $disciplina );
							
				$conteudo->fixarSimulado( $this->_simulado );
				
				$this->_dados[] = $conteudo;
			}
		}
		$rs->free();
	}
	
	protected function _prepararNavegador ()
	{
		Core::carregarModulo(array('nome' => '_navegador', 'classe' => 'MNavegador', 'guardar_como' => 'navegador_conteudos'));
		
		Core::modulo('navegador_conteudos')->fixarTipoNavegador( MNavegador::NAVEGADOR_MINI_ATALHOS );
		
		Core::modulo('navegador_conteudos')->removerAtalhosRecarregarPadroes();
		
		Core::modulo('navegador_conteudos')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_CONTEUDOS)), 'Adicionar conteúdo...');
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_conteudos'));

		Core::modulo('procurar_conteudos')->prepararProcura('form_procurar_conteudos', 'caixa_procurar_conteudos');
		Core::modulo('procurar_conteudos')->configurarTermo();
		Core::modulo('procurar_conteudos')->configurarOndeProcurar( array('sc_nome' => 'Nome do conteúdo') );
		Core::modulo('procurar_conteudos')->configurarAlfabeto();

		$disciplinas = array('TODAS' => 'Todas as disciplinas');
		foreach (Disciplina::obterArrayDisciplinasParaFormulario() as $k => $v) {
			$disciplinas[$k] = $v;
		}
		Core::modulo('procurar_conteudos')->configurarListaCustomizada('disciplina_conteudo', 'Disciplina do conteúdo', $disciplinas);
		
		Core::modulo('procurar_conteudos')->carregarProcura();
		
		Core::modulo('procurar_conteudos')->prepararAtalho( Core::modulo('navegador_conteudos') );
	}
	
	protected function _prepararOrdenacao ()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_conteudos'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_conteudos');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('sc_nome' => 'Nome', 'sc_disciplina' => 'Disciplina'), 'sc_nome' );
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();
		
		Core::modulo('ord_conteudos')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_conteudos')->carregarOrdenacao();
		Core::modulo('ord_conteudos')->prepararAtalho( Core::modulo('navegador_conteudos'), 'caixa_ord_conteudos' );
		
		if ( Core::modulo('procurar_conteudos')->procuraSolicitada() ) {
			Core::modulo('ord_conteudos')->anexarHTML( Core::modulo('procurar_conteudos')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao ()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_conteudos'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormularioAcoes ()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_conteudos', 'acao' => Gerenciador_URL::gerarLinkPelaEntrada()) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$v) {
			$ids[] = $v->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Remover', 'Tem certeza que deseja REMOVER todos os itens selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();
				
				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {
					if (is_array($ids)) {
						Core::modulo('solicitado')->aRemoverConteudos($ids);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $this->_simulado->obterID(), $this->seletorVisao->nomeSeletor() => MSimulados::VISAO_CONTEUDOS, 'id_conteudo' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				}
			}
			catch (Formulario_Exception $e)	{ }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('conteúdos', $totais);
	}
}

?>