<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('ImportadorCVS', 'Importador/');
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);

class FImportarRespostas_Exception extends Formulario_Exception { }

class FImportarRespostas extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;

	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_dadosImportados = array();

	protected $_simulado = null;
	protected $_fase = 1;

	protected $_tumasCriar = array();
	protected $_professoresCriar = array();

	public function __construct ($info = array())
	{
		parent::__construct($info);

		Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		$this->_dados = array();
		$this->_correspondencias = array(
			'NULA' => '',
			'turma' => 'Turma do aluno',
			'portador_necessidade' => 'Portador de necessidades especiais',
			'nome' => 'Nome do aluno',
			'matricula' => 'Matrícula do aluno'
		);

		for ($i = 1; $i <= 200; $i++)
			$this->_correspondencias[$i] = 'Questão número ' . $i;

		$this->_correspondenciasObrigatorias = array( 'nome', 'turma' );
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;


		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );

			$cabecalhoSel = null;
			if ( !$this->foiEnviado() && !isset($_POST['post_anterior'])) {
				$cabecalhoSel = 1;

				if (Core::diretiva('_importador_respostas.cabecalho.ultima_selecao') !== false &&
						Core::diretiva('_importador_respostas.cabecalho.ultima_selecao') == 0)
					$cabecalhoSel = 0;
			}


			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => $cabecalhoSel,
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ';',
													'html_ordem' => $ordem++
								  )) );

			$simuladosPossiveis = Simulado::obterSimuladosParaFormulario(true);
			$simuladoSel = null;

			if ( !$this->foiEnviado() && !isset($_POST['post_anterior']) && Core::diretiva('_importador_respostas.simulado.ultima_selecao') != false &&
					array_key_exists(Core::diretiva('_importador_respostas.simulado.ultima_selecao'), $simuladosPossiveis))
				$simuladoSel = Core::diretiva('_importador_respostas.simulado.ultima_selecao');

			$this->adicionarCampo( new Campo(array( 'nome' => 'simulado',
													'etiqueta' => 'Avaliação',
													'valor' => $simuladoSel,
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosPossiveis)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $simuladosPossiveis,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
													'html_ordem' => $ordem++
							  	)) );

			$fasesPossiveis = array();
			for ($i = 1; $i <= 3; $i++)
				$fasesPossiveis[$i] = $i;

			$this->adicionarCampo( new Campo(array( 'nome' => 'fase',
													'etiqueta' => 'Fase',
													'valor' => (!$this->foiEnviado() && !isset($_POST['post_anterior']) ? 1 : null),
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($fasesPossiveis)),
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $fasesPossiveis,
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
													'html_ordem' => $ordem++
								  )) );
		}

		$this->_fase = (int) ($this->obterEstado() == self::PASSO_1 ? $this->_campos['fase']->obter('valor') : $_POST['fase']);
		$this->_simulado = new Simulado( (int) ($this->obterEstado() == self::PASSO_1 ? $this->_campos['simulado']->obter('valor') : $_POST['simulado']) );

		if (!$this->_simulado->carregar())
			$this->_simulado = null;

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );

			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2) {
			$questaoSel = 0;

			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'valor' => (!$this->foiEnviado() && $i > 2 ? $questaoSel++ : null),
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}

			if (!$this->foiEnviado()) {
				$ordensCorrespondencias = array('turma', 'portador_necessidade', 'nome', 'matricula');

				foreach ($ordensCorrespondencias as $k => $v) {
					if (isset($this->_campos['correspondencia_' . $k]))
						$this->_campos['correspondencia_' . $k]->fixar('valor', $v);
				}
			}
		}

		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();


			if ($this->_simulado === null) {
				$this->_adicionarErro('simulado', 'avaliação inválida;');
				throw new FImportarRespostas_Exception('Avaliação inválida!');
			}

			if ($this->_fase < 1 || $this->_fase > $this->_simulado->obterDuracao()) {
				$this->_adicionarErro('fase', 'a avaliação selecionada só tem '. $this->_simulado->obterDuracao() . ' fase(s);');
				throw new FImportarRespostas_Exception('Número de fases inválido!');
			}


			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');

				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarRespostas_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				for ($i = 0; $i < count(@$this->_dados[0]); $i++)
					$this->_correspondenciasSel[$i] = $this->_campos['correspondencia_'. $i]->obter('valor');

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarRespostas_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarRespostas_Exception('Correspondência obrigatória não selecionada!');
					}
				}

				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}

				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarRespostas_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FImportarRespostas_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$importados = $this->_executarPasso_3();
			Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' séries de respostas importadas com sucesso!', 'Importando respostas...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}

	public function &obterDadosImportados () {
		return $this->_dadosImportados;
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));

			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;
			$_POST['simulado'] = isset($postAnterior['simulado']) ? $postAnterior['simulado'] : null;
			$_POST['fase'] = isset($postAnterior['fase']) ? $postAnterior['fase'] : 1;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;

			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();

				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}

				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}

		if ( $passo == null )
			$passo = self::PASSO_1;

		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;

				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}

		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		Core::fixarDiretiva('_importador_respostas.cabecalho.ultima_selecao', $this->_campos['cabecalho']->obter('valor') == 1 ? 1 : 0);
		Core::fixarDiretiva('_importador_respostas.simulado.ultima_selecao', $this->_campos['simulado']->obter('valor'));

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarrespostas'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarRespostas_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];
		$this->_entrada['simulado'] = $_POST['simulado'];
		$this->_entrada['fase'] = $_POST['fase'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarrespostas'), $dados );
	}

	protected function _executarPasso_3 () {
		$temQuestoesDeLingua = $this->_simulado->temQuestoesLinguaEstrangeira();
		$linguasDisponiveis = Disciplina::obterArrayDisciplinasParaFormulario(true);
		$cursosDisponiveis = CursoVestibular::obterArrayCursosVestibularParaFormulario();

		$tempQuestoes = $this->_simulado->obterQuestoesParaFormulario(false, true, false);
		$questoes = array();
		$questoesOpcao = array(Questao::OPCAO_TIPO_PROVA => array(), Questao::OPCAO_LINGUA_ESTRANGEIRA => array(), Questao::OPCAO_CURSO_VESTIBULAR => array());
		if ( isset($tempQuestoes[$this->_fase]) && is_array($tempQuestoes[$this->_fase]) ) {
			foreach ($tempQuestoes[$this->_fase] as $tipoProva => $questoesPorTipo) {
				foreach ($questoesPorTipo as $n => $questoesPorNumero) {
					foreach ($questoesPorNumero as &$q) {
						if ($q->questaoDeOpcao())
							$questoesOpcao[$q->obterTipo()][$tipoProva][$n] = $q;
						else
							$questoes[$tipoProva][$n][] = $q;
					}
				}
			}
		}

		// cadastra turmas
		$turmasNovas = array();
		foreach($this->_tumasCriar as $tNome) {
			$turma = Turma::obterNovaTurma($tNome, $this->_simulado->obterSerieAvaliacao(), Core::registro('instituicao'));

			if ($turma->obterID())
				$turmasNovas[$tNome] = $turma;
		}

		// cadastra professor
		$turmasPossiveis = Turma::obterArrayTurmasParaFormulario();
		$professoresPossiveis = Professor::obterArrayProfessoresParaFormulario();
		foreach ($this->_professoresCriar as $turma => $professoresCriar) {
			$tID = array_search($turma, $turmasPossiveis);
			if ($tID === false)
				continue;

			foreach ($professoresCriar as $professorCriar) {
				if (!in_array($professorCriar, $professoresPossiveis)) {
					// cria usuário
					$usuarioProf = UsuarioInstituido::obterNovoUsuarioInstituido(new Email(null), null, array(3));
					$usuarioProf->fixarNome( $professorCriar );
					$usuarioProf->fixarEndereco( new Endereco(null) );
					$usuarioProf->fixarInstituicao( Core::registro('instituicao') );

					if ( $usuarioProf->obterID() == null || !$usuarioProf->salvar() )
						continue;

					PrimeiroAcesso::obterNovoPrimeiroAcesso( $usuarioProf );

					// cria professor
					$novoProfessor = Professor::obterNovoProfessor( $usuarioProf );

					if ( $novoProfessor->obterID() == null )
						continue;

					$professoresPossiveis[ $novoProfessor->obterID() ] = $professorCriar;
				}

				$pID = array_search($professorCriar, $professoresPossiveis);
				$professor = new Professor($pID);

				// adiciona aulas ao prof
				if ( $pID !== false && Aula::obterIDPelosDados(ProvaFloripa::$alfabetizacao, new Turma($tID), $professor) == false )
					Aula::obterNovaAula(ProvaFloripa::$alfabetizacao, new Turma($tID), $professor);
			}
		}

		$importados = 0;
		foreach ($this->_dadosImportados as $obj) {
			// cadastra no sistema e inscreve caso ainda não esteja inscrito
			if ($obj['aluno_para_criar'] != null) {
				if ($obj['nova_turma'] != null && isset($turmasNovas[$obj['nova_turma']]))
					$obj['aluno_para_criar']->fixarTurma( $turmasNovas[$obj['nova_turma']] );

				$obj['inscricao'] = $this->_cadastrarAlunoEInscrever($obj['aluno_para_criar']);

				if ($obj['inscricao'] === false)
					continue;
			}

			// ajusta o tipo de prova do inscrito
			$tipoProva = 1;

			if ($this->_simulado->obterNumeroDeTipos() > 1) {
				$n = (count(@$questoesOpcao[Questao::OPCAO_TIPO_PROVA][1]) ? array_pop( array_keys($questoesOpcao[Questao::OPCAO_TIPO_PROVA][1]) ) : 0);

				if ($n > 0 && isset($obj['novas_respostas'][$n])) {
					$obj['novas_respostas'][$n] = (int) $obj['novas_respostas'][$n];

					foreach ( $questoesOpcao[Questao::OPCAO_TIPO_PROVA][1][$n]->obterEquivalenciasOpcoes() as $eo ) {
						if ( (int) $eo->obterValor() == $obj['novas_respostas'][$n] )
							$obj['inscricao']->fixarTipo( (int) $eo->obterEquivale() );
					}
				}

				$tipoProva = $obj['inscricao']->obterTipo();

				if ($tipoProva < 1 || $tipoProva > $this->_simulado->obterNumeroDeTipos())
					continue;
			}



			// ajusta a língua do inscrito
			if (isset($questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA][$tipoProva])) {
				$n = array_pop( array_keys($questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA][$tipoProva]));

				if ($n > 0 && isset($obj['novas_respostas'][$n])) {
					$obj['novas_respostas'][$n] = (int) $obj['novas_respostas'][$n];

					foreach ( $questoesOpcao[Questao::OPCAO_LINGUA_ESTRANGEIRA][$tipoProva][$n]->obterEquivalenciasOpcoes() as $eo ) {
						if ( (int) $eo->obterValor() == $obj['novas_respostas'][$n] )
							$obj['inscricao']->fixarLingua( new Disciplina( (int) $eo->obterEquivale() ) );
					}
				}
			}

			if ($temQuestoesDeLingua && !array_key_exists(@$obj['inscricao']->obterLingua()->obterID(), $linguasDisponiveis) )
				continue;



			// ajusta o curso de vestibular do inscrito
			if (isset($questoesOpcao[Questao::OPCAO_CURSO_VESTIBULAR][$tipoProva])) {
				$n = array_pop( array_keys($questoesOpcao[Questao::OPCAO_CURSO_VESTIBULAR][$tipoProva]));

				if ($n > 0 && isset($obj['novas_respostas'][$n])) {
					$obj['novas_respostas'][$n] = (int) $obj['novas_respostas'][$n];

					foreach ( $questoesOpcao[Questao::OPCAO_CURSO_VESTIBULAR][$tipoProva][$n]->obterEquivalenciasOpcoes() as $eo ) {
						if ( (int) $eo->obterValor() == $obj['novas_respostas'][$n] )
							$obj['inscricao']->fixarCursoVestibular( new CursoVestibular( (int) $eo->obterEquivale() ) );
					}
				}
			}

			if (@$obj['inscricao']->obterCursoVestibular()->obterID() != null && !array_key_exists(@$obj['inscricao']->obterCursoVestibular()->obterID(), $cursosDisponiveis))
				$obj['inscricao']->fixarCursoVestibular( new CursoVestibular( null ) );



			// ajusta as respostas do inscrito
			$respostasMarcadas = array();
			foreach ( $obj['inscricao']->obterRespostasMarcadas() as $r )
				$respostasMarcadas[ (int) $r->obterQuestao()->obterID() ] = $r;

			if (isset($questoes[$tipoProva]) && is_array($questoes[$tipoProva])) {
				foreach ( $questoes[$tipoProva] as $n => $questoesPorNumero ) {
					foreach ( $questoesPorNumero as &$q ) {
						if ( $q->obterDisciplina()->linguaEstrangeira() && $obj['inscricao']->obterLingua()->obterID() != $q->obterDisciplina()->obterID() )
							continue;

						if (isset($obj['novas_respostas'][$n])) {
							$novaResposta = trim($obj['novas_respostas'][$n]);

							switch ($q->obterTipo()) {
								case Questao::MULTIPLAESCOLHA:
									if ( $novaResposta != null && !in_array( strtoupper($novaResposta), MultiplaEscolha::$opcoesParaImportar) &&
											isset( MultiplaEscolha::$opcoesParaImportar[ (int) $novaResposta ] ) )
											$novaResposta = MultiplaEscolha::$opcoesParaImportar[ (int) $novaResposta ];

									$novaResposta = MultiplaEscolha::inteiro($novaResposta);
									if ($novaResposta === false)
										$novaResposta = null;

									break;
								case Questao::DISCURSIVA:
									$novaResposta = str_replace(',', '.', $novaResposta);
									if ( !Filtrador::real($novaResposta) )
										$novaResposta = null;

									break;
								case Questao::ABERTA:
								case Questao::SOMATORIO:
									if ( empty($novaResposta) || !Filtrador::natural((int) $novaResposta) )
										$novaResposta = null;
									else
										$novaResposta = (int) $novaResposta;

									break;
								default:
									break;
							}

							if ($novaResposta !== null) {
								if (!isset($respostasMarcadas[(int) $q->obterID()])) {
									$respostasMarcadas[(int) $q->obterID()] = Resposta::obterNovaResposta( $q, $obj['inscricao'], 'IRESPOSTAS' );
									$obj['inscricao']->adicionarRespostaMarcada($respostasMarcadas[(int) $q->obterID()]);
								}

								$respostasMarcadas[(int) $q->obterID()]->fixarValor($novaResposta);
							}
						}
					}
				}
			}



			if ( $obj['inscricao']->salvar() )
				$importados++;
		}

		return $importados;
	}

	protected function _cadastrarAlunoEInscrever (Aluno $obj) {
		// usuario
		$usuario = UsuarioInstituido::obterNovoUsuarioInstituido($obj->obterUsuario()->obterEmail(), $obj->obterUsuario()->obterSenha(), array(4));

		if ( $usuario == null || $usuario->obterID() == null )
			return false;

		$obj->obterUsuario()->fixarID( $usuario->obterID() );

		if ( !$obj->obterUsuario()->salvar() )
			return false;

		if ( !PrimeiroAcesso::usuarioTemPrimeiroAcesso( $obj->obterUsuario() ) )
			PrimeiroAcesso::obterNovoPrimeiroAcesso( $obj->obterUsuario() );

		// aluno
		$novoAluno = Aluno::obterNovoAluno( $obj->obterUsuario() );

		$obj->fixarID( $novoAluno->obterID() );

		if ( $obj->obterID() != null && $obj->salvar() ) {
			// inscreve aluno
			$inscricao = new Inscricao(null);
			$idInscricao = Inscricao::obterIDInscricaoPeloAluno($this->_simulado, $obj);

			if ( !$idInscricao ) {
				$inscricao = Inscricao::obterNovaInscricao($this->_simulado, $obj);

				if ( !$inscricao->salvar() )
					return false;
			} else {
				$inscricao->fixarID($idInscricao);
			}

			if ($inscricao->carregar($this->_simulado))
				return $inscricao;
		}

		return false;
	}

	protected function _gerarDadosImportados () {
		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;

		$turmasBuffer = array();
		$turmasPossiveis = Turma::obterArrayTurmasParaFormulario();
		$this->_tumasCriar = array();

		for ( $i; $i < count($this->_dados); $i++ )	{
			$obj = array( 'inscricao' => new Inscricao(null),
						  'novas_respostas' => array(),
						  'precisa_cadastro' => true,
						  'nova_turma' => null,
						  'aluno_para_criar' => null );

			$turma = $professor = $nome = $portador_necessidade = $matricula = null;

			foreach ($this->_correspondenciasSel as $j => $cID) {
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;

				switch ($cID) {
					case 'nome':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 1 ) {
							$valor = str_replace("'", "`", $valor);

							//$idInscricao = Inscricao::obterIDInscricaoPeloNomeAluno($valor, $this->_simulado);

							//if ($idInscricao !== false) {
							//	$obj['inscricao']->fixarID( $idInscricao );
							//	$obj['precisa_cadastro'] = false;
							//} else {
								$obj['precisa_cadastro'] = true;
								$nome = $valor;
							//}
						}

						break;
					case 'matricula':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$matricula = $valor;
						}
						break;
					case 'turma':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$turma = $valor;
							
							if (!in_array($valor, $turmasPossiveis) && !in_array($valor, $this->_tumasCriar))
								$this->_tumasCriar[] = $turma;
						}

						break;
					case 'professor':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 ) {
							$valor = str_replace("'", "`", $valor);

							$professor = $valor;
						}
						break;
					case 'portador_necessidade':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) > 0 && ($valor == 1 || strtolower($valor) == 'sim' || strtolower($valor) == 'x') )
							$portador_necessidade = true;
						break;
					default:
						$obj['novas_respostas'][(int) $cID] = $valor;
						break;
				}
			}

			if ( $obj['precisa_cadastro'] ) {
				if ($nome != null && $turma != null) {
					$obj['aluno_para_criar'] = new Aluno(null);
					$obj['aluno_para_criar']->fixarPortadorNecessidade($portador_necessidade);
					$obj['aluno_para_criar']->fixarMatricula($matricula);
					$obj['aluno_para_criar']->fixarUsuario( new UsuarioInstituido(null) );
					$obj['aluno_para_criar']->obterUsuario()->fixarNome( $nome );
					$obj['aluno_para_criar']->obterUsuario()->fixarSenha( randomico(true, 16) );
					$obj['aluno_para_criar']->obterUsuario()->fixarGrupos( array(4) );
					$obj['aluno_para_criar']->obterUsuario()->fixarEndereco( new Endereco(null) );
					$obj['aluno_para_criar']->obterUsuario()->fixarInstituicao( Core::registro('instituicao') );
					$obj['aluno_para_criar']->obterUsuario()->fixarEmail( new Email(null) );
					$obj['aluno_para_criar']->fixarTurma( new Turma(null) );

					$turmaID = array_search($turma, $turmasPossiveis);
					if ($turmaID === false) {
						$turmaID = $turma;

						if (!isset($turmasBuffer[$turmaID]))
							$turmasBuffer[$turmaID] = new Turma(null);

						$obj['nova_turma'] = $turma;
					} elseif ( !isset($turmasBuffer[$turmaID]) ) {
						$turmasBuffer[$turmaID] = new Turma($turmaID);
						@$turmasBuffer[$turmaID]->carregar();
					}

					$obj['aluno_para_criar']->fixarTurma( $turmasBuffer[$turmaID] );

					// professor
					/*if ( $professor != null && $turma != null ) {
						if (!isset($this->_professoresCriar[$turma]))
							$this->_professoresCriar[$turma] = array();

						if (!in_array($professor, $this->_professoresCriar[$turma]))
							$this->_professoresCriar[$turma][] = $professor;
					}*/

					if ( count($obj['novas_respostas']) )
						$this->_dadosImportados[] = $obj;
				}
			} else {
				//if ($obj['inscricao']->obterID() != null) {
				//	if ( count($obj['novas_respostas']) && $obj['inscricao']->carregar($this->_simulado) )
				//		$this->_dadosImportados[] = $obj;
				//}
			}
		}
	}
}

?>