<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);

include_once('simulados.listagem.php');
include_once('simulados.formulario.php');

include_once('questoes/questoes.formulario.php');
include_once('questoes/questoes.listagem.php');
include_once('questoes/questoes.importador.php');

include_once('inscricoes/inscricoes.formulario.php');
include_once('inscricoes/respostas.formulario.php');
include_once('inscricoes/inscricoes.listagem.php');

include_once('conteudos/conteudos.formulario.php');
include_once('conteudos/conteudos.listagem.php');

include_once('respostas/respostas.importador.php');
include_once('respostas2/respostas.importador.php');
include_once('respostasV2/respostas.importador.php');
include_once('respostasV3/respostas.importador.php');

include_once('add_pdf/add_pdf.importador.php');

include_once('inscricoes/copiar_inscricoes.formulario.php');

include_once('banco_itens/banco_itens.montar.php');

class MSimulados extends Modulo
{
	const VISAO_QUESTOES = 'questoes';
	const VISAO_INSCRICOES = 'inscricoes';
	const VISAO_RESPOSTAS = 'respostas';
	const VISAO_CONTEUDOS = 'conteudos';
	const BI_MONTAR = 'bi_montar';

	protected $_listagem;
	protected $_formulario;
	
	public function __construct ()
	{
		parent::__construct();
		
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		// habilita botão de adicionar nos atalhos do navegador
		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('simulados', 'novo'), 'Adicionar avaliação...');
		}
		
		$this->seletorVisao->adicionarVisao(self::VISAO_QUESTOES);
		$this->seletorVisao->adicionarVisao(self::VISAO_INSCRICOES);
		$this->seletorVisao->adicionarVisao(self::VISAO_RESPOSTAS);
		$this->seletorVisao->adicionarVisao(self::VISAO_CONTEUDOS);
		$this->seletorVisao->adicionarVisao(self::BI_MONTAR);
		$this->seletorVisao->verificarVisaoSolicitada();
	}

	public function aListarSimulados () {
		return $this->_carregarListagem();
	}
	
	public function aDetalharSimulado ()
	{
		$simu = new Simulado( (int) @$_GET['id'] );
		
		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Avaliação inválida!');	
		} else {
			switch ( $this->seletorVisao->visaoSelecionada() ) {
				case self::BI_MONTAR:
					if ( Core::moduloCarregado('navegador') )
						Core::modulo('navegador')->habilitarAtalho('adicionar', false);
					break;
				case self::VISAO_QUESTOES:
					if ( Core::moduloCarregado('navegador') )
						Core::modulo('navegador')->habilitarAtalho('adicionar', false);
					break;
				case self::VISAO_RESPOSTAS:
					$this->seletorVisao->forcarVisao(self::VISAO_INSCRICOES);
				case self::VISAO_INSCRICOES:
					if ( Core::moduloCarregado('navegador') )
						Core::modulo('navegador')->habilitarAtalho('adicionar', false);
					break;
				case self::VISAO_CONTEUDOS:
					if ( Core::moduloCarregado('navegador') )
						Core::modulo('navegador')->habilitarAtalho('adicionar', false);
					break;
				default:
			}
		
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('simulados.detalhes.html.php');
			$this->_finalizarRenderizacao();
		}
		
		return true;
	}
	
	public function aImportarRespostas ()
	{
		if ( Core::moduloCarregado('navegador') )
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);

		$this->_formulario = new FImportarRespostas( array('nome' => 'form_importar_respostas', 'acao' => Gerenciador_URL::gerarLink('simulados', 'importarrespostas'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();
		
		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();
				
				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'importarrespostas') );
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e)	{ }
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('respostas/respostas.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aImportarRespostas2 ()
	{
		if ( Core::moduloCarregado('navegador') )
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);

		$this->_formulario = new FImportarRespostas2( array('nome' => 'form_importar_respostas', 'acao' => Gerenciador_URL::gerarLink('simulados', 'importarrespostaspb'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'importarrespostaspb') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('js')->incluirArquivo('includes/JavaScript/netCheck/engine.js');
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('respostas2/respostas.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}
	
	public function aNovoSimulado ()
	{
		switch ( $this->seletorVisao->visaoSelecionada() ) {
			case self::BI_MONTAR:
				if ( Core::moduloCarregado('navegador') )
					Core::modulo('navegador')->habilitarAtalho('adicionar', false);
				$this->_prepararFormularioBIMontar();
				break;
			case self::VISAO_INSCRICOES:
				if ( Core::moduloCarregado('navegador') )
					Core::modulo('navegador')->habilitarAtalho('adicionar', false);
				$this->_prepararFormularioInscricoes();
				break;
			case self::VISAO_RESPOSTAS:
				if ( Core::moduloCarregado('navegador') )
					Core::modulo('navegador')->habilitarAtalho('adicionar', false);
				$this->_prepararFormularioRespostas();
				break;
			case self::VISAO_QUESTOES:
				if ( Core::moduloCarregado('navegador') )
					Core::modulo('navegador')->habilitarAtalho('adicionar', false);
				$this->_prepararFormularioQuestoes();
				break;
			case self::VISAO_CONTEUDOS:
				if ( Core::moduloCarregado('navegador') )
					Core::modulo('navegador')->habilitarAtalho('adicionar', false);
				$this->_prepararFormularioConteudos();
				break;
			default:
				$this->_prepararFormularioInfo();
		}
	}
	
	public function aProcessarFormulario () {
		return $this->aNovoSimulado();
	}
	
	public function aRemoverSimulados ($ids = null)	{
		if ( $ids != null && is_array($ids) ) {
			foreach ($ids as $id) {
				$obj = new Simulado($id);
				
				if ( !$obj->carregar() || !$obj->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}
				
				if ( $obj->podeRemover() ) {
					if (!$obj->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'avaliação não foi removida;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($obj->obterNome(), 'avaliação removida com sucesso;');
					}
				} else {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'avaliação não pode ter questões para poder ser removida;');
				}
			}
			
			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo avaliação...');
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if (isset($_GET['id']) && (int) $_GET['id'] > 0) {
				return $this->aRemoverSimulados( array((int) $_GET['id']) );
			}

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}

	public function aCopiarSimulados ($ids = null)	{
		if ( $ids != null && is_array($ids) ) {
			foreach ($ids as $id) {
				$nome = "";
				$simuladoSql = ""; 

				$rs = Core::registro('db')->query('SELECT * FROM simulados WHERE s_id = '.$id);
				if ($rs->num_rows) 
				{
					while ($row = $rs->fetch_assoc()) 
					{
						$rslt = array();
						foreach($row as $rk => $rv){
							$rslt[$rk] = $rv;
						}

						$nome = $rslt['s_nome'];

						$rslt['s_id'] = '';
						$rslt['s_nome'] = $nome." (Copia)";
						$rslt['s_ordem'] = '';
						$rslt['s_bimestre'] = '';
						$rslt['s_pai_filho_id'] = '';

						$simuladoSql .= "INSERT INTO simulados VALUES ('";
						$simuladoSql .= implode("','",$rslt);
						$simuladoSql .= "');";
					}
					$rs->free();

					if($simuladoSql!=''){
						Core::registro('db')->query($simuladoSql);
						$nid = Core::registro('db')->insert_id;
					}
					else{
						Core::modulo('redirecionador')->adicionarFalha($nome, 'Erro ao copiar o simulado.');

						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());

						return;
					}
				}

				$rs = Core::registro('db')->query('SELECT * FROM questoes WHERE q_simulado = '.$id);
				if ($rs->num_rows) 
				{
					$lnum = 1;
					while ($row = $rs->fetch_assoc()) 
					{
						$rslt = array();
						foreach($row as $rk => $rv){
							$rslt[$rk] = $rv;
						}

						$qidDE = $rslt['q_id'];

						$rslt['q_id'] = '';
						$rslt['q_simulado'] = $nid;

						Core::registro('db')->query(
							sprintf('INSERT INTO questoes (q_identificador, q_simulado) VALUES (%s, %s)', 
								Core::registro('db')->formatarValor($rslt['q_identificador']),
								Core::registro('db')->formatarValor($rslt['q_simulado']) 
							) 
						);

						$qid = Core::registro('db')->insert_id;

						Core::registro('db')->query(sprintf('
							UPDATE questoes 
							SET q_identificador = %s, q_pontos = %s, q_tipo = %s, q_disciplina = %s, q_conteudo = %s, q_nivel = %s, q_fase_duracao = %s, q_proposicoes = %s, q_simulado = %s
							WHERE q_id = %s',
							Core::registro('db')->formatarValor($rslt['q_identificador']),
							Core::registro('db')->formatarValor($rslt['q_pontos']),
							Core::registro('db')->formatarValor($rslt['q_tipo']),
							Core::registro('db')->formatarValor($rslt['q_disciplina']),
							Core::registro('db')->formatarValor($rslt['q_conteudo']),
							Core::registro('db')->formatarValor($rslt['q_nivel']),
							Core::registro('db')->formatarValor($rslt['q_fase_duracao']),
							Core::registro('db')->formatarValor($rslt['q_proposicoes']),
							Core::registro('db')->formatarValor($rslt['q_simulado']),
							Core::registro('db')->formatarValor($qid))
						);

						Core::registro('db')->query( sprintf('INSERT INTO gabaritos (g_questao) VALUES (%s)',
							Core::registro('db')->formatarValor($qid) ) );

						$gid = Core::registro('db')->insert_id;

						$rsG = Core::registro('db')->query(sprintf('SELECT g_numero FROM gabaritos WHERE g_questao = %s',Core::registro('db')->formatarValor($qidDE)));
						if ($rsG->num_rows){
							$rowg = $rsG->fetch_assoc();
							$lnum = $rowg['g_numero'];
						}

						Core::registro('db')->query( sprintf('UPDATE gabaritos SET g_numero = %s, g_valor = %s, g_tipo = %s, g_questao = %s WHERE g_id = %s',
							Core::registro('db')->formatarValor($lnum),
							Core::registro('db')->formatarValor(NULL),
							Core::registro('db')->formatarValor('1'),
							Core::registro('db')->formatarValor($qid),
							Core::registro('db')->formatarValor($gid) ) );

                        $rsqp = Core::registro('db')->query('SELECT * FROM questoes_proposicoes WHERE qp_questao = '.$qidDE);
                        if ($rsqp->num_rows)
                        {
                        	while ($rowqp = $rsqp->fetch_assoc())
                            {
                                $rsltqp = array();
                                foreach($rowqp as $rkqp => $rvqp){
                                	$rsltqp[$rkqp] = $rvqp;
                                }

                                unset($rsltqp['qp_id']);
                                $rsltqp['qp_questao'] = $qid;

                                Core::registro('db')->query(
                                    sprintf('INSERT INTO questoes_proposicoes (qp_numero, qp_texto, qp_questao) VALUES (%s, %s, %s);',
                                        Core::registro('db')->formatarValor($rsltqp['qp_numero']),
                                        Core::registro('db')->formatarValor($rsltqp['qp_texto']),
                                        Core::registro('db')->formatarValor($rsltqp['qp_questao'])
                                    )
                                );
                            }
                        }
						$rsqp->free();

						$lnum++;
					}
				}
				$rs->free();

				$rsD = Core::registro('db')->query('SELECT * FROM diretivas WHERE d_nome LIKE "NIVEL_%%_'.$id.'" AND d_usuario IS NULL');
				if ($rsD->num_rows) {
					while ($rowD = $rsD->fetch_assoc()) {
                        $rsltD = array();
                        foreach($rowD as $rkD => $rvD){
                        	$rsltD[$rkD] = $rvD;
                        }

                        unset($rsltD['d_id']);
                        $rsltD['d_nome'] = str_replace($id, $nid, $rsltD['d_nome']);

                        Core::registro('db')->query(
                            sprintf('INSERT INTO diretivas (d_nome, d_valor, d_definicao) VALUES (%s, %s, %s);',
                                Core::registro('db')->formatarValor($rsltD['d_nome']),
                                Core::registro('db')->formatarValor($rsltD['d_valor']),
                                Core::registro('db')->formatarValor($rsltD['d_definicao'])
                            )
                        );
					}
				}
				$rsD->free();

				$arrSkipDupAulas = array();
				$qtdAulasJaExistentes = 0;
				$idsAulasCopiadas = array();
	
				$rs2 = Core::registro('db')->query('SELECT * FROM aulas WHERE a_simulado = '.$id); 
				$qtdAulas = $rs2->num_rows;
				if ($qtdAulas){
					while ($row2 = $rs2->fetch_assoc()){
						if(in_array('d'.$row2['a_disciplina'].'_t'.$row2['a_turma'].'_p'.$row2['a_professor'].'_s'.$nid, $arrSkipDupAulas)){
							continue;
						}
	
						$rs3 = Core::registro('db')->query('SELECT * FROM aulas WHERE a_simulado = '.$nid.' AND a_disciplina = '.$row2['a_disciplina'].' AND a_turma = '.$row2['a_turma'].' AND a_professor = '.$row2['a_professor'].' LIMIT 1;');
						if ($rs3->num_rows <= 0){
							$iSql2 = "INSERT INTO aulas (a_simulado,a_disciplina,a_turma,a_professor) VALUES (".$nid." , ".$row2['a_disciplina']." , ".$row2['a_turma']." , ".$row2['a_professor'].");";				
	
							Core::registro('db')->query($iSql2);
							$idsAulasCopiadas[] = Core::registro('db')->insert_id;
							$arrSkipDupAulas[] = 'd'.$row2['a_disciplina'].'_t'.$row2['a_turma'].'_p'.$row2['a_professor'].'_s'.$nid;
						}
						else{
							$qtdAulasJaExistentes++;
						}
						$rs3->free();
					}
				}
				$rs2->free();

				Core::modulo('redirecionador')->adicionarSucesso($nome, 'simulado copiado com sucesso;');
			}

			Core::modulo('redirecionador')->fixarMensagem(null, 'Copiando simulados...');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}
	
	public function aRemoverQuestoes ($ids = null) {
		if ( $ids != null && is_array($ids) ) {
			
			$simu = null;
			foreach ($ids as $id) {
				$obj = new Questao($id);
				
				if ( !$obj->carregar() || !$obj->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}
				
				if ( $simu == null ) {
					$simu = $obj->obterSimulado();
				}
				
				if ( $obj->podeRemover() ) {
					if (!$obj->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($obj->obterIdentificador(), 'questão não foi removida;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($obj->obterIdentificador(), 'questão removida com sucesso;');
					}
				} else {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterIdentificador(), 'questão (inserir critério aqui) para poder ser removida;');
				}
			}
			
			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo questões...');
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			if ( $simu != null ) {		
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES) ) );
			} else {
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'listar') );
			}
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if ( isset($_GET['id']) && (int) $_GET['id'] > 0 ) {
				return $this->aRemoverQuestoes( array((int) $_GET['id']) );
			}
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}
	
	public function aRemoverConteudos ($ids = null) {
		if ( $ids != null && is_array($ids) ) {
			
			$simu = null;
			foreach ($ids as $id) {
				$obj = new Conteudo($id);
				
				if ( !$obj->carregar() || !$obj->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}
				
				if ( $simu == null ) {
					$simu = $obj->obterSimulado();
				}
				
				if ( $obj->podeRemover() ) {
					if (!$obj->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'conteúdo não foi removido;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($obj->obterNome(), 'conteúdo removido com sucesso;');
					}
				} else {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'conteúdo (inserir critério aqui) para poder ser removido;');
				}
			}
			
			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo conteúdos...');
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			if ( $simu != null ) {		
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS) ) );
			} else {
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'listar') );
			}
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if ( isset($_GET['id']) && (int) $_GET['id'] > 0 ) {
				return $this->aRemoverConteudos( array((int) $_GET['id']) );
			}
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}
	
	public function aRemoverInscricoes ($ids = null) {
		if ( $ids != null && is_array($ids) ) {
		
			$simu = null;
			foreach ($ids as $id) {
				$obj = new Inscricao($id);
				
				if ( !$obj->carregar()) {// || !$obj->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}
				
				if ( $simu == null ) {
					$simu = $obj->obterSimulado();
				}
				
				$nome = @$obj->obterAluno()->obterUsuario()->obterNome();
				
				if ( $obj->podeRemover() ) {
					if (!$obj->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($nome, 'desinscrito não foi feita;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($nome, 'aluno desinscrito com sucesso;');
					}
				} else {
					Core::modulo('redirecionador')->adicionarFalha($nome, 'desinscrição (inserir critério aqui) para poder ser feita;');
				}
			}
			
			Core::modulo('redirecionador')->fixarMensagem(null, 'Desinscrevendo alunos...');
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			if ( $simu != null ) {		
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES) ) );
			} else {
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'listar') );
			}
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if ( isset($_GET['id']) && (int) $_GET['id'] > 0 ) {
				return $this->aRemoverInscricoes( array((int) $_GET['id']) );
			}
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}
	
	private function _carregarListagem () {
		$this->_listagem = new LSimulados();
		
		$this->_listagem->prepararListagem();
		
		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}
	
	private function _prepararFormularioQuestoes ()	{
		if (Core::registro('permissoes')->temPermissao('simulados.editar.somente_inscricoes')){	
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Você não tem permissão para editar questões!');
		}

		$simu = new Simulado( (int) @$_GET['id'] );
		
		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {				
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Avaliação inválida!');
		} else {
			$this->_formulario = new FQuestoes( array('nome' => 'form_questoes', 'acao' => Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES) )) );
	
			$this->_formulario->incluirChecadorDeEnvio();
			$this->_formulario->prepararEntrada();	
			$this->_formulario->carregarFormulario($simu);
			
			if ($this->_formulario->foiEnviado()) {
				try
				{
					$this->_formulario->checarFormulario();
					$id = $this->_formulario->executar()->obterID();
					
					switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
						case 'adicionar':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES, 'pos_envio' => 'adicionar')) ); break;
						case 'editar':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES, 'id_questao' => $id)) ); break;
						case 'editar_proximo':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES, 'id_questao' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
						default:
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES)) );
					}
					
					Gerenciador_URL::habilitarAtualiacaoReferencia(false);
					if ( $this->_formulario->acoesPosEnvio->redirecionarRapido() )
						Redirecionador::redirecionar(Redirecionador::HEADER);
					else
						Core::modulo('redirecionador')->redirecionar();
				}
				catch (Formulario_Exception $e)	{ }
			}
			
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('simulados.formulario.header.html.php');
				include('questoes/questoes.formulario.html.php');
			$this->_finalizarRenderizacao();
		}
	}		
			
	private function _prepararFormularioInscricoes () {
		$simu = new Simulado( (int) @$_GET['id'] );
		
		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {				
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Avaliação inválida!');
		} else {
			$this->_formulario = new FInscricoes( array('nome' => 'form_inscricoes', 'acao' => Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES) )) );
	
			$this->_formulario->incluirChecadorDeEnvio();
			$this->_formulario->prepararEntrada();	
			$this->_formulario->carregarFormulario($simu);
			
			if ($this->_formulario->foiEnviado()) {
				try
				{
					$this->_formulario->checarFormulario();
					$id = $this->_formulario->executar()->obterID();

					switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
						case 'adicionar':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES, 'adicionando' => 1, 'pos_envio' => 'adicionar')) ); break;
						case 'editar':
							if ( !$this->_formulario->acoesPosEnvio->redirecionarRapido() )
								Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES, 'id_inscricao' => $id)) );
							else
								Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES, 'id_inscricao' => $id, 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
							break;
						case 'editar_proximo':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES, 'id_inscricao' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
						default:
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES)) );
					}
					
					Gerenciador_URL::habilitarAtualiacaoReferencia(false);
					if ( $this->_formulario->acoesPosEnvio->redirecionarRapido() )
						Redirecionador::redirecionar(Redirecionador::HEADER);
					else
						Core::modulo('redirecionador')->redirecionar();
				}
				catch (Formulario_Exception $e)	{ }
			}
			
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('simulados.formulario.header.html.php');
				include('inscricoes/inscricoes.formulario.html.php');
			$this->_finalizarRenderizacao();
		}
	}
	
	private function _prepararFormularioRespostas () {
		$simu = new Simulado( (int) @$_GET['id'] );
		
		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {				
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Avaliação inválida!');
		} else {
			$this->_formulario = new FRespostas( array('nome' => 'form_respostas', 'acao' => Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_RESPOSTAS) )) );
	
			$this->_formulario->incluirChecadorDeEnvio();
			$this->_formulario->prepararEntrada();	
			$this->_formulario->carregarFormulario($simu);
			
			if ($this->_formulario->foiEnviado()) {
				try
				{
					$this->_formulario->checarFormulario();
					$this->_formulario->executar();
					
					if ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() == 'editar' ) {
						ob_clean();						
						$dados = array( 'ids' => $_POST['ids'] );					
						Gerenciador_URL::habilitarAtualiacaoReferencia(true);
						Gerenciador_URL::autoFixarIndexCompleto(true);
						Redirecionador::finalizarAoRedirecionar(true);
						Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_RESPOSTAS)), $dados );
					} else {
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_INSCRICOES)) );
					}

					Gerenciador_URL::habilitarAtualiacaoReferencia(false);
					if ( $this->_formulario->acoesPosEnvio->redirecionarRapido() )
						Redirecionador::redirecionar(Redirecionador::HEADER);
					else
						Core::modulo('redirecionador')->redirecionar();
				}
				catch (Formulario_Exception $e)	{ }
			}
			
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('simulados.formulario.header.html.php');
				include('inscricoes/respostas.formulario.html.php');
			$this->_finalizarRenderizacao();
		}
	}
	
	private function _prepararFormularioConteudos () {
		if (Core::registro('permissoes')->temPermissao('simulados.editar.somente_inscricoes')){			
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Você não tem permissão para editar conteúdos!');
		}

		$simu = new Simulado( (int) @$_GET['id'] );
		
		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {				
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Avaliação inválida!');
		} else {
			$this->_formulario = new FConteudos( array('nome' => 'form_conteudos', 'acao' => Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS) )) );
	
			$this->_formulario->incluirChecadorDeEnvio();
			$this->_formulario->prepararEntrada();	
			$this->_formulario->carregarFormulario($simu);
			
			if ($this->_formulario->foiEnviado()) {
				try
				{
					$this->_formulario->checarFormulario();
					$id = $this->_formulario->executar()->obterID();
					
					switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
						case 'adicionar':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS, 'pos_envio' => 'adicionar')) ); break;
						case 'editar':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS, 'id_conteudo' => $id)) ); break;
						case 'editar_proximo':
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS, 'id_conteudo' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
						default:
							Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_CONTEUDOS)) );
					}
					
					Gerenciador_URL::habilitarAtualiacaoReferencia(false);
					if ( $this->_formulario->acoesPosEnvio->redirecionarRapido() )
						Redirecionador::redirecionar(Redirecionador::HEADER);
					else
						Core::modulo('redirecionador')->redirecionar();
				}
				catch (Formulario_Exception $e)	{ }
			}
			
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('simulados.formulario.header.html.php');
				include('conteudos/conteudos.formulario.html.php');
			$this->_finalizarRenderizacao();
		}
	}
			
	private function _prepararFormularioInfo () {
		Core::carregarModulo(array('nome' => '_endereco', 'classe' => 'MEndereco', 'guardar_como' => '_endereco'));
		Core::carregarModulo(array('nome' => '_d_simulados', 'classe' => 'MDSimulados', 'guardar_como' => '_d_simulados'));
	
		$this->_formulario = new FSimulados( array('nome' => 'form_simulados', 'acao' => Gerenciador_URL::gerarLink('simulados', 'novo')) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();	
		$this->_formulario->carregarFormulario();
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				$id = $this->_formulario->executar()->obterID();
				
				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				if ( $this->_formulario->obterEstado() == Formulario::ADICIONANDO ) {
					Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $id, $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES)) );
				} else {
					Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $id)) );
				}
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e)	{ }
		}
		
		Core::modulo('_endereco')->fixarTituloEndereco('Endereço de realização');
		Core::modulo('_endereco')->prepararFormulario($this->_formulario);

		Core::modulo('_d_simulados')->fixarTituloDSimulados('Diretivas do Simulado');
		Core::modulo('_d_simulados')->prepararFormulario($this->_formulario);
		
		$simu = $this->_formulario->obterDados();
		
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('simulados.formulario.header.html.php');
			include('simulados.formulario.html.php');
		$this->_finalizarRenderizacao();
	}

	public function aImportarRespostasV2 ()
	{
		if ( Core::moduloCarregado('navegador') )
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);

		$this->_formulario = new FImportarRespostasV2( array('nome' => 'form_importar_respostas', 'acao' => Gerenciador_URL::gerarLink('simulados', 'importarrespostasv2'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'importarrespostasv2') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('js')->incluirArquivo('includes/JavaScript/netCheck/engine.js');
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('respostasV2/respostas.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aImportarRespostasV3 ()
	{
		if ( Core::moduloCarregado('navegador') )
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);

		$this->_formulario = new FImportarRespostasV3( array('nome' => 'form_importar_respostas', 'acao' => Gerenciador_URL::gerarLink('simulados', 'importarrespostasv3'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'importarrespostasv3') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('js')->incluirArquivo('includes/JavaScript/netCheck/engine.js');
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('respostasV3/respostas.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aSimuladoAddPdf ()
	{
		if ( Core::moduloCarregado('navegador') )
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);

		$this->_formulario = new FImportarAddPdf( array('nome' => 'form_importar_pdf', 'acao' => Gerenciador_URL::gerarLink('simulados', 'saddpdf'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'saddpdf') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('add_pdf/add_pdf.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aCopiarInscritos ($ids = null)	{
		$this->_formulario = new FCopiarInscricoes(array('nome' => 'form_copiar_inscricoes', 'acao' => Gerenciador_URL::gerarLink('simulados', 'copiar_inscricoes')));
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();

		if ($this->_formulario->foiEnviado()) {
			$simulado = new Simulado(Core::diretiva('_copiar_inscricoes.simuladoPARA.ultima_selecao'));
			if(!$simulado->carregar()){
				Core::modulo('redirecionador')->fixarMensagem(null, 'Copiando Inscritos...');
				Core::modulo('redirecionador')->adicionarFalha(null, 'Falha ao obter dados da avaliação selecionada.');

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('simulados', 'listar'));
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			$this->_formulario->fixarSimuladoDE($simulado);
			$this->_formulario->carregarFormulario();

			try{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('simulados', 'listar'));
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}
		elseif ($ids != null && is_array($ids)){
			if(count($ids)>1){
				Core::modulo('redirecionador')->fixarMensagem(null, 'Copiando Inscritos...');
				Core::modulo('redirecionador')->adicionarFalha(null, 'Só é permitido selecionar 1 avaliação.');

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('simulados', 'listar'));
				Core::modulo('redirecionador')->redirecionarNenhum();
			}

			$simulado = new Simulado($ids[0]);
			if(!$simulado->carregar()){
				Core::modulo('redirecionador')->fixarMensagem(null, 'Copiando Inscritos...');
				Core::modulo('redirecionador')->adicionarFalha(null, 'Falha ao obter dados da avaliação selecionada.');

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('simulados', 'listar'));
				Core::modulo('redirecionador')->redirecionarNenhum();
			}

			Core::fixarDiretiva('_copiar_inscricoes.simuladoPARA.ultima_selecao', $simulado->obterID());
			$this->_formulario->fixarSimuladoDE($simulado);
			$this->_formulario->carregarFormulario();

			$this->_iniciarRenderizacao(Modulo::HTML);
				include('inscricoes/copiar_inscricoes.formulario.html.php');
			$this->_finalizarRenderizacao();
		} 
		else {
			Core::modulo('redirecionador')->fixarMensagem(null, 'Copiando Inscritos...');
			Core::modulo('redirecionador')->adicionarFalha(null, 'É preciso selecionar a Avaliação que vão ter as inscrições copiadas.');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('simulados', 'listar'));
			Core::modulo('redirecionador')->redirecionarNenhum();
		}
	}

	public function aImportarQGH ()
	{
		if ( Core::moduloCarregado('navegador') )
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);

		$this->_formulario = new FImportarQuestoesGH( array('nome' => 'form_importar_qgh', 'acao' => Gerenciador_URL::gerarLink('simulados', 'importarqgh'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'importarqgh') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('js')->incluirArquivo('includes/JavaScript/netCheck/engine.js');
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('questoes/questoes.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	private function _prepararFormularioBIMontar ()	{
		if (Core::registro('permissoes')->temPermissao('simulados.editar.somente_inscricoes')){	
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Você não tem permissão para editar questões!');
		}

		$simu = new Simulado( (int) @$_GET['id'] );
		
		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {				
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Simulado inválido!');
		} else {
			$this->_formulario = new FBIMontar( array('nome' => 'form_bi_montar', 'acao' => Gerenciador_URL::gerarLink('simulados', 'editar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::BI_MONTAR) )) );
	
			$this->_formulario->incluirChecadorDeEnvio();
			$this->_formulario->prepararEntrada();	
			
			if ($this->_formulario->foiEnviado()) {
				try
				{
					$bitens = array();

					$regiao = explode('#$AVSEP$#',$_POST['selecoes-regiao']);
				    $uf = explode('#$AVSEP$#',$_POST['selecoes-uf']);
				    $fonte = explode('#$AVSEP$#',$_POST['selecoes-instituicao']);
				    $ano = explode('#$AVSEP$#',$_POST['selecoes-ano']); 
				    $fase = explode('#$AVSEP$#',$_POST['selecoes-fase']);
				    $discip = explode('#$AVSEP$#',$_POST['selecoes-disciplina']);
				    $tipo = explode('#$AVSEP$#',$_POST['selecoes-tipo']);
				    $dificul = explode('#$AVSEP$#',$_POST['selecoes-dificuldade']);

					$bit = BancoItens::carregarTodas();

					//strtoupper(ereg_replace("[^a-zA-Z0-9_]", "", strtr($var, "áàãâéêíóôõúüçÁÀÃÂÉÊÍÓÔÕÚÜÇ ", "aaaaeeiooouucAAAAEEIOOOUUC_")));

					foreach($bit as $bitk => $bitv){
						if(in_array($bitv->obterRegiao(), $regiao)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}

						if(in_array($bitv->obterUF(),$uf)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}

						$bitv->obterFonte()->carregar();

						if(in_array($bitv->obterFonte()->sigla, $fonte)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}

						if(in_array($bitv->obterAno(), $ano)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}

						foreach($fase as $fk => $fv){
							if($bitv->obterFaseDuracao() == preg_replace("/[^0-9\s]/", "", $fv)){
								$bitens[$bitv->obterID()] = $bitv;
								continue;
							}
						}

						$bitv->obterDisciplina()->carregar();
						if(in_array($bitv->obterDisciplina()->obterNome(), $discip)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}
						else if(in_array($bitv->obterDivisao(), $discip)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}
						else if(in_array($bitv->obterConteudo()->obterNome(), $discip)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}

						if(in_array($bitv->obterTipo(true), $tipo)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}	

						if(in_array($bitv->obterNivelDificuldade(true), $dificul)){
							$bitens[$bitv->obterID()] = $bitv;
							continue;
						}
					}

					if($_POST['passo'] == 'SELECIONAR'){
						$this->_formulario->carregarFormularioPassoDois($simu);

						$this->_iniciarRenderizacao(Modulo::HTML);
							include('simulados.formulario.header.html.php');
							include('banco_itens/banco_itens.selecionar.html.php');
						$this->_finalizarRenderizacao();
					}
					else{
						//$this->_formulario->checarFormulario();
						$id = $this->_formulario->executar();

						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $simu->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_QUESTOES)) );
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						if ( $this->_formulario->acoesPosEnvio->redirecionarRapido() )
							Redirecionador::redirecionar(Redirecionador::HEADER);
						else
							Core::modulo('redirecionador')->redirecionar();
					}
				}
				catch (Formulario_Exception $e)	{ }
			}
			else{
				$this->_formulario->carregarFormularioPassoUm($simu);

				$this->_iniciarRenderizacao(Modulo::HTML);
					include('simulados.formulario.header.html.php');
					include('banco_itens/banco_itens.montar.html.php');
				$this->_finalizarRenderizacao();
			}
		}
	}
}

?>