<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Turma', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Simulado', null, true);
Core::incluir('Serie', null, true);
Core::incluir('JTransferidorDeItensDeMenu', 'JavaScript/JTransferidorDeItensDeMenu/', true);

class FSimulados_Exception extends Formulario_Exception { }

class FSimulados extends Formulario
{
	public $seletoresDatas = array();

	protected $_formularioEndereco;

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_formularioEndereco = Core::modulo('_endereco')->obterFormulario();
		$this->_formularioDiretivas = Core::modulo('_d_simulados')->obterFormulario();

		Core::carregarModulo(array('nome' => '_seletor_data', 'classe' => 'MSeletorData', 'guardar_como' => '_seletor_data_simu'));
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		Core::modulo('js')->incluirArquivo('includes/JavaScript/tiny_mce/tiny_mce.js');

		$this->_carregar();

        $this->adicionarCampo( new Campo(array( 'nome' => 'nome',
            'etiqueta' => 'Nome',
            'requerimento' => Campo::REQUERIDO,
            'tipo' => Campo::TEXTO,
            'argumentos' => array(Campo::TAM_MIN => 2, Campo::TAM_MAX => 100),
            'html_tamanho_maximo' => 100,
            'html_tipo' => Campo::HTML_TEXTO,
            'html_valor' => $this->_dados->obterNome(),
            'html_ordem' => 1
        )) );


        $bimestre = array();
        for ($i = 1; $i <= 6; $i++)
            $bimestre[$i] = $i;

        $this->adicionarCampo( new Campo(array( 'nome' => 'bimestre',
            'etiqueta' => 'Aplicação',
            'valor' => $this->_dados->obterBimestre(),
            'requerimento' => Campo::REQUERIDO,
            'tipo' => Campo::NATURAL,
            'argumentos' => array(Campo::POSSIBILIDADES => array_keys($bimestre)),
            'html_tipo' => Campo::HTML_MENU,
            'html_valor' => $bimestre,
            'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
            'html_ordem' => 1
        )) );

        $prodTxt = array(0 => 'Não', 1 => 'Sim');
        $this->adicionarCampo( new Campo(array( 'nome' => 'prod_txt',
            'etiqueta' => 'Produção Textual',
            'valor' => $this->_dados->obterProdTxt(),
            'tipo' => Campo::NATURAL,
            'argumentos' => array(Campo::POSSIBILIDADES => array_keys($prodTxt)),
            'html_tipo' => Campo::HTML_MENU,
            'html_valor' => $prodTxt,
            'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
            'html_ordem' => 1
        )) );

        $avEspecial = array(0 => 'Não', 1 => 'Sim');
        $this->adicionarCampo( new Campo(array( 'nome' => 'av_especial',
            'etiqueta' => 'Avaliação Especial',
            'valor' => $this->_dados->obterAvEspecial(),
            'tipo' => Campo::NATURAL,
            'argumentos' => array(Campo::POSSIBILIDADES => array_keys($avEspecial)),
            'html_tipo' => Campo::HTML_MENU,
            'html_valor' => $avEspecial,
            'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
            'html_ordem' => 1
        )) );

        $rels = array(
        				'' => '',
        				'relatorios' => 'relatorios',
        				'relatorios_hipoteses' => 'relatorios_hipoteses',
        				'relatorios_campos' => 'relatorios_campos',
        				'relatorios_transicao' => 'relatorios_transicao',
        				'relatorios_conhecimento' => 'relatorios_conhecimento',
        				'relatorios_sondagem' => 'relatorios_sondagem',
        				'relatorios_fluencialeitora' => 'relatorios_fluencialeitora',
        			);
        $this->adicionarCampo( new Campo(array( 'nome' => 'av_secao',
        	'etiqueta' => 'Seção Relatórios',
            'requerimento' => Campo::OPCIONAL,
        	'valor' => $this->_dados->obterSecaoRels(),
            'tipo' => Campo::TEXTO,
            'argumentos' => array(Campo::POSSIBILIDADES => $rels),
            'html_tamanho_maximo' => 50,
            'html_tipo' => Campo::HTML_MENU,
            'html_valor' => $rels,
            'html_ordem' => 1
        )) );

        $this->adicionarCampo( new Campo(array( 'nome' => 'ordem',
            'etiqueta' => 'Ordem no lançamento',
            'requerimento' => Campo::REQUERIDO,
            'tipo' => Campo::TEXTO,
            'argumentos' => array(Campo::TAM_MIN => 1, Campo::TAM_MAX => 50),
            'html_tamanho_maximo' => 50,
            'html_tipo' => Campo::HTML_TEXTO,
            'html_tamanho' => 10,
            'html_valor' => $this->_dados->obterOrdem(),
            'html_ordem' => 4
        )) );

        $simulados = $this->_obterArraySimulados();
        foreach($simulados as $sid => $snome){
        	$sdata = new Simulado($sid);
        	if($sdata->carregar()){
	        	if($sdata->obterBimestre() != $this->_dados->obterBimestre()){
	        		unset($simulados[$sid]);
	        	}

	        	if($sdata->obterSerieAvaliacao() != $this->_dados->obterSerieAvaliacao()){
	        		unset($simulados[$sid]);
	        	}

	        	if($sdata->obterProdTxt() == $this->_dados->obterProdTxt()){
	        		unset($simulados[$sid]);
	        	}
        	}
        }

        $this->adicionarCampo( new Campo(array( 'nome' => 'pai_filho',
            'etiqueta' => 'Simulado Pai/Filho',
            'valor' => $this->_dados->obterPaiFilho(),
            'requerimento' => Campo::OPCIONAL,
            'tipo' => Campo::NATURAL,
            'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simulados)),
            'html_tipo' => Campo::HTML_MENU,
            'html_valor' => $simulados,
            'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
            'html_ordem' => 2
        )) );


        $data = null;
		if ($this->_dados->obterDataRealizacao() != null) {
			$data = strftime('%d/%m/%Y %H:%M', $this->_dados->obterDataRealizacao());
		}

		$this->seletoresDatas['data_realizacao'] = array('html' => '', 'botao' => '');
		Core::modulo('_seletor_data_simu')->prepararSeletor('data_realizacao', 'bt_sel_data_realizacao', '%d/%m/%Y %H:%M', true);
		$this->seletoresDatas['data_realizacao']['botao'] = Core::modulo('_seletor_data_simu')->obterBotaoImagem(2);
		$this->seletoresDatas['data_realizacao']['html'] = Core::modulo('_seletor_data_simu')->obterSaida();

		$this->adicionarCampo( new Campo(array( 'nome' => 'data_realizacao',
												'etiqueta' => 'Data de realização',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::DATA,
												'argumentos' => array(Campo::PADRAO_DATA => '%d/%m/%Y %H:%M'),
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $data,
												'html_tamanho' => 15,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_somente_leitura' => true
							  )) );

		$data_fim = null;
		if ($this->_dados->obterDataRealizacaoFim() != null){
			$data_fim = strftime('%d/%m/%Y %H:%M', $this->_dados->obterDataRealizacaoFim());
		}

		$this->seletoresDatas['data_realizacao_fim'] = array('html' => '', 'botao' => '');
		Core::modulo('_seletor_data_simu')->prepararSeletor('data_realizacao_fim', 'bt_sel_data_realizacao_f', '%d/%m/%Y %H:%M', true);
		$this->seletoresDatas['data_realizacao_fim']['botao'] = Core::modulo('_seletor_data_simu')->obterBotaoImagem(3);
		$this->seletoresDatas['data_realizacao_fim']['html'] = Core::modulo('_seletor_data_simu')->obterSaida();

		$this->adicionarCampo( new Campo(array( 'nome' => 'data_realizacao_fim',
												'etiqueta' => 'Data final de realização',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::DATA,
												'argumentos' => array(Campo::PADRAO_DATA => '%d/%m/%Y %H:%M'),
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $data_fim,
												'html_tamanho' => 15,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_somente_leitura' => FALSE
							  )) );

		$data = null;
		if ($this->_dados->obterDataInicioInscricoes() != null) {
			$data = strftime('%d/%m/%Y %H:%M', $this->_dados->obterDataInicioInscricoes());
		}

		$this->seletoresDatas['data_inicio_inscricoes'] = array('html' => '', 'botao' => '');
		Core::modulo('_seletor_data_simu')->prepararSeletor('data_inicio_inscricoes', 'bt_sel_data_inicio_inscricoes', '%d/%m/%Y %H:%M', true);
		$this->seletoresDatas['data_inicio_inscricoes']['botao'] = Core::modulo('_seletor_data_simu')->obterBotaoImagem(4);
		$this->seletoresDatas['data_inicio_inscricoes']['html'] = Core::modulo('_seletor_data_simu')->obterSaida();

		$this->adicionarCampo( new Campo(array( 'nome' => 'data_inicio_inscricoes',
												'etiqueta' => 'Início do período de "Atualização de Turmas"',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::DATA,
												'argumentos' => array(Campo::PADRAO_DATA => '%d/%m/%Y %H:%M'),
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $data,
												'html_tamanho' => 15,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_somente_leitura' => true
							  )) );

		$data = null;
		if ($this->_dados->obterDataFimInscricoes() != null) {
			$data = strftime('%d/%m/%Y %H:%M', $this->_dados->obterDataFimInscricoes());
		}

		$this->seletoresDatas['data_fim_inscricoes'] = array('html' => '', 'botao' => '');
		Core::modulo('_seletor_data_simu')->prepararSeletor('data_fim_inscricoes', 'bt_sel_data_fim_inscricoes', '%d/%m/%Y %H:%M', true);
		$this->seletoresDatas['data_fim_inscricoes']['botao'] = Core::modulo('_seletor_data_simu')->obterBotaoImagem(5);
		$this->seletoresDatas['data_fim_inscricoes']['html'] = Core::modulo('_seletor_data_simu')->obterSaida();

		$this->adicionarCampo( new Campo(array( 'nome' => 'data_fim_inscricoes',
												'etiqueta' => 'Fim do período de "Atualização de Turmas"',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::DATA,
												'argumentos' => array(Campo::PADRAO_DATA => '%d/%m/%Y %H:%M'),
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $data,
												'html_tamanho' => 15,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_somente_leitura' => true
							  )) );

		$data = null;
		if ($this->_dados->obterInicioLancamento() != null) {
			$data = strftime('%d/%m/%Y %H:%M', $this->_dados->obterInicioLancamento());
		}				  		  
		$this->seletoresDatas['data_inicio_lancamento'] = array('html' => '', 'botao' => '');
		Core::modulo('_seletor_data_simu')->prepararSeletor('data_inicio_lancamento', 'bt_sel_data_inicio_lancamento', '%d/%m/%Y %H:%M', true);
		$this->seletoresDatas['data_inicio_lancamento']['botao'] = Core::modulo('_seletor_data_simu')->obterBotaoImagem(4);
		$this->seletoresDatas['data_inicio_lancamento']['html'] = Core::modulo('_seletor_data_simu')->obterSaida();
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'data_inicio_lancamento',
												'etiqueta' => 'Início do lançamento',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::DATA,
												'argumentos' => array(Campo::PADRAO_DATA => '%d/%m/%Y %H:%M'),//, Campo::MAIOR_OU_IGUAL_QUE_CAMPO => 'data_realizacao'),
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $data,
												'html_tamanho' => 15,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_somente_leitura' => true
							  )) );
						
		$data = null;
		if ($this->_dados->obterFimLancamento() != null) {
			$data = strftime('%d/%m/%Y %H:%M', $this->_dados->obterFimLancamento());
		}	
		$this->seletoresDatas['data_fim_lancamento'] = array('html' => '', 'botao' => '');
		Core::modulo('_seletor_data_simu')->prepararSeletor('data_fim_lancamento', 'bt_sel_data_fim_lancamento', '%d/%m/%Y %H:%M', true);
		$this->seletoresDatas['data_fim_lancamento']['botao'] = Core::modulo('_seletor_data_simu')->obterBotaoImagem(4);
		$this->seletoresDatas['data_fim_lancamento']['html'] = Core::modulo('_seletor_data_simu')->obterSaida();
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'data_fim_lancamento',
												'etiqueta' => 'Fim do lançamento',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::DATA,
												'argumentos' => array(Campo::PADRAO_DATA => '%d/%m/%Y %H:%M'),//, Campo::MAIOR_QUE_CAMPO => 'data_inicio_lancamento'),
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $data,
												'html_tamanho' => 15,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_somente_leitura' => true
							  )) );	

		$duracao = array();
		for ($i = 1; $i <= 5; $i++)
			$duracao[$i] = $i;

		$this->adicionarCampo( new Campo(array( 'nome' => 'duracao',
												'etiqueta' => 'Duração',
												'valor' => (!$this->foiEnviado() ? $this->_dados->obterDuracao() : null),
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($duracao)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $duracao,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 6
							  )) );

		$tipos_provas = array();
		for ($i = 1; $i <= 10; $i++) {
			$tipos_provas[$i] = $i;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'tipos_provas',
												'etiqueta' => 'Tipos de provas',
												'valor' => (!$this->foiEnviado() ? $this->_dados->obterNumeroDeTipos() : null),
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($tipos_provas)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $tipos_provas,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 7
							  )) );

		$linguaSel = null;
		if ( !$this->foiEnviado() ) {
			if ( $this->obterEstado() == self::EDITANDO ) {
				$linguaSel = (int) $this->_dados->linguasCompartilham();
			} else {
				$linguaSel = 1;
			}
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'linguas_compartilham',
												'etiqueta' => 'Línguas estrangeiras compartilham número da questão',
												'valor' => $linguaSel,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => 8
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'realizacao_unica',
												'etiqueta' => 'Realização Única',
												'valor' => (!$this->foiEnviado() ? $this->_dados->obterRealizacaoUnica() : null),
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => 9
							  )) );

		$ncorretores[1] = 1;
		$ncorretores[2] = 2;
		$ncorretores[3] = 3;
		$ncorretores[4] = 'Banca';
		$this->adicionarCampo( new Campo(array( 'nome' => 'corretores',
												'etiqueta' => 'Número de Corretores',
												'valor' => (!$this->foiEnviado() ? $this->_dados->obterCorretores() : 1),
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($ncorretores)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $ncorretores,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO
							  )) );

		$corr_disp = $this->_obterArrayProfessores();
		$corr_dispf = $this->_obterArrayProfessores();
		$corr_sele = $this->_obterArrayCorretores($this->_dados->obterID());

		foreach ($corr_dispf as $pID => $pNome) {
			if ( array_key_exists($pID, $corr_sele) ) {
				$corr_sele[$pID] = $corr_dispf[$pID];
				unset($corr_dispf[$pID]);
			}
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'corr_disp',
												'etiqueta' => 'Corretores disponíveis',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($corr_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'corr_sele',
												'etiqueta' => 'Corretores selecionados',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($corr_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->_campos['corr_sele']->fixar('html_valor', $corr_sele);
		$this->_campos['corr_disp']->fixar('html_valor', $corr_dispf);

		$this->adicionarCampo( new Campo(array( 'nome' => 'add_corr',
												'etiqueta' => 'Adicionar >>',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_classe' => 'botao salvar',
												'componente' => new JTransferidorDeItensDeMenu('corr_disp[]', 'corr_sele[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'del_corr',
												'etiqueta' => '<< Remover',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao cancelar',
												'componente' => new JTransferidorDeItensDeMenu('corr_sele[]', 'corr_disp[]')
							  )) );

		$serie_null = array(0 => ' -- Selecione -- ');
		$series = Serie::obterArraySeriesParaFormulario();
		$series = $serie_null+$series;
		$seriesSelecionada = null;

		foreach ($series as $sk => $sv) {
			if(!is_null($this->_dados->obterSerieAvaliacao())){ 
				if($this->_dados->obterSerieAvaliacao()->obterID() == $sk){
					$seriesSelecionada = $sk;
				}
			}
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'serie_avaliacao',
            'etiqueta' => 'Série',
            'valor' => $seriesSelecionada,
            'requerimento' => Campo::REQUERIDO,
            'tipo' => Campo::TEXTO,
            'argumentos' => array(Campo::POSSIBILIDADES => array_keys($series)),
            'html_tipo' => Campo::HTML_MENU,
            'html_valor' => $series,
            'html_ordem' => 3
        )) );

//-------------------------->

foreach ($corr_disp as $spk => $spv) {
	$preItens = $this->_dados->obterQuestoesParaFormulario();

	$itens = array();
	$itensSelec = array();
	$itensDispo = array();
	foreach ($preItens as $pik => $piv) {
		foreach ($piv as $ppik => $ppiv) {
			foreach ($ppiv as $pppik => $pppiv) {
				$itens[$pppiv->obterID()] = $pppiv->obterIdentificador()." (".$pppiv->obterDisciplina()->obterNome().")";
			}
		}
	}

	$itensDispo = $itens;

	$itensSelecPre = $this->_obterArrayItensSimulados($this->_dados->obterID());

	if(array_key_exists($spk, $itensSelecPre)){
		foreach ($itensDispo as $ID => $Nome) {
			if ( in_array($ID, $itensSelecPre[$spk]) ) {
				$itensSelec[$ID] = $itensDispo[$ID];
				unset($itensDispo[$ID]);
			}
		}
	}

	$this->adicionarCampo( new Campo(array( 'nome' => 'corr_disp_'.$spk,
												'etiqueta' => 'Itens disponíveis',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($itens)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'corr_sele_'.$spk,
												'etiqueta' => 'Itens selecionadas',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($itens)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->_campos['corr_sele_'.$spk]->fixar('html_valor', $itensSelec);
		$this->_campos['corr_disp_'.$spk]->fixar('html_valor', $itensDispo);

		$this->adicionarCampo( new Campo(array( 'nome' => 'add_corr_'.$spk,
												'etiqueta' => 'Adicionar >',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_classe' => 'botao salvar',
												'componente' => new JTransferidorDeItensDeMenu('corr_disp_'.$spk.'[]', 'corr_sele_'.$spk.'[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'del_corr_'.$spk,
												'etiqueta' => '< Remover',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao cancelar',
												'componente' => new JTransferidorDeItensDeMenu('corr_sele_'.$spk.'[]', 'corr_disp_'.$spk.'[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'add_corr_todos_'.$spk,
												'etiqueta' => 'Adicionar Todos >>',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_tamanho' => 120,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_classe' => 'botao salvar'
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'del_corr_todos_'.$spk,
												'etiqueta' => '<< Remover Todos',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tamanho' => 120,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao cancelar'
							  )) );
}

//-------------------------->

		/*$turmas_disp = $this->_obterArrayTurmas();
		$turmas_sele = array();

		$this->adicionarCampo( new Campo(array( 'nome' => 'turmas_disp',
												'etiqueta' => 'Turmas disponíveis',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($turmas_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'turmas_sele',
												'etiqueta' => 'Turmas selecionadas',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($turmas_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$turmas_sele_IDs = $this->_campos['turmas_sele']->obter('valor');
		if (!is_array($turmas_sele_IDs)) { $turmas_sele_IDs = array(); }

		if ( !$this->foiEnviado() ) {
			$turmas_sele_IDs = array_merge($turmas_sele_IDs, $this->_dados->obterTurmas());
		}

		foreach ($turmas_sele_IDs as $k => $tID) {
			if ( array_key_exists($tID, $turmas_disp) ) {
				$turmas_sele[$tID] = $turmas_disp[$tID];
			} else {
				unset($turmas_sele_IDs[$k]);
			}
		}

		$turmas_disp_finais = array();

		foreach ($turmas_disp as $tID => $tNome) {
			if ( !array_key_exists($tID, $turmas_sele) ) {
				$turmas_disp_finais[$tID] = $tNome;
			}
		}

		$this->_campos['turmas_sele']->fixar('html_valor', $turmas_sele);
		$this->_campos['turmas_disp']->fixar('html_valor', $turmas_disp_finais);

		$this->adicionarCampo( new Campo(array( 'nome' => 'add_turma',
												'etiqueta' => 'Adicionar >>',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_classe' => 'botao salvar',
												'componente' => new JTransferidorDeItensDeMenu('turmas_disp[]', 'turmas_sele[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'del_turma',
												'etiqueta' => '<< Remover',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao cancelar',
												'componente' => new JTransferidorDeItensDeMenu('turmas_sele[]', 'turmas_disp[]')
							  )) );
		*/

		$this->adicionarCampo( new Campo(array( 'nome' => 'texto_inicio',
												'etiqueta' => 'Texto de introdução da avaliação on-line',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 10000),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_AREA_TEXTO,
												//'html_linhas' => 6,
												'html_valor' => $this->_dados->obterTextoInicio()
								)) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'texto_fim',
												'etiqueta' => 'Texto de finalização da avaliação on-line',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 10000),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_AREA_TEXTO,
												//'html_linhas' => 6,
												'html_valor' => $this->_dados->obterTextoFim()
								)) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar'
							  )) );

		$ordem = $this->_formularioDiretivas->carregarFormulario($this->_dados, 8);
		foreach ($this->_formularioDiretivas->obterCampos() as $k => $v) {
			$this->_campos[$k] = $v;
		}

		$endereco = $this->_dados->obterEndereco() != null ? $this->_dados->obterEndereco() : new Endereco(null);

		$this->_formularioEndereco->prepararEntrada($this->_entrada);
		$ordem = $this->_formularioEndereco->carregarFormulario($endereco, 8);

		foreach ($this->_formularioEndereco->obterCampos() as $k => $v) {
			$this->_campos[$k] = $v;
		}

		$this->_campos['enviar']->fixar('html_ordem', $ordem);
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();

			if ( $this->obterEstado() == self::EDITANDO && $this->_dados->linguasCompartilham() != ($this->_campos['linguas_compartilham']->obter('valor') == 1) && $this->_dados->temQuestoesLinguaEstrangeira() ) {
				$this->_adicionarErro('linguas_compartilham', 'você precisa remover as questões de língua estrangeira antes de alterar essa opção;');
				throw new FQuestoes_Exception('Questões de língua estrangeira precisam ser removidas!');
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FSimulados_Exception($e->getMessage());
		}
	}

	public function &executar ()
	{
		$endereco = $this->_formularioEndereco->executar();

		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar($endereco) ) {
				$diretivas = $this->_formularioDiretivas->executar($this->_dados->obterID());

				if(!$diretivas){
					Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar as diretivas da avaliação!', 'Adicionando avaliação...');
					Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
				}
				else{
					Core::modulo('redirecionador')->fixarMensagem('Avaliação adicionada com sucesso!', 'Adicionando avaliação...');
					Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
				}
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar a avaliação!', 'Adicionando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar($endereco) ) {
				$diretivas = $this->_formularioDiretivas->executar($this->_dados->obterID());

				if(!$diretivas){
					Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar as diretivas da avaliação!', 'Editando avaliação...');
					Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
				}
				else{
					Core::modulo('redirecionador')->fixarMensagem('Avaliação editada com sucesso!', 'Editando avaliação...');
					Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
				}
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar a avaliação!', 'Editando avaliação...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}

		return $this->_dados;
	}

	public function foiEnviado ()
	{
		$retorno = parent::foiEnviado();

		$this->_formularioEndereco->fixarFoiEnviado($retorno);
		$this->_formularioDiretivas->fixarFoiEnviado($retorno);

		return $retorno;
	}

	protected function _carregar ()
	{
		$id = null;

		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}

		$this->_dados = new Simulado($id);

		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->validarInstituicao() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados', 'listar'), 'Avaliação inválida!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);

			// prepara endereço default
			if ( !$this->foiEnviado() && Core::registro('instituicao')->obterEndereco() != null ) {
				$this->_dados->fixarEndereco( Core::registro('instituicao')->obterEndereco() );
			}
		}
	}

	protected function _adicionar (Endereco &$endereco)
	{
		$this->_dados = Simulado::obterNovoSimulado($this->_campos['nome']->obter('valor'));//, Core::registro('instituicao'));

		if ($this->_dados->obterID() != null) {
			$this->_fixarDadosDoSimulado($endereco);
		}

		return $this->_dados->salvar();
	}

	protected function _editar (Endereco &$endereco)
	{
		$this->_fixarDadosDoSimulado($endereco);

		return $this->_dados->salvar();
	}

	protected function _fixarDadosDoSimulado (Endereco &$endereco)
	{
		$this->_dados->fixarEndereco($endereco);

		$this->_dados->fixarNome($this->_campos['nome']->obter('valor'));
        $this->_dados->fixarBimestre($this->_campos['bimestre']->obter('valor'));
        $this->_dados->fixarOrdem($this->_campos['ordem']->obter('valor'));
        $this->_dados->fixarPaiFilho($this->_campos['pai_filho']->obter('valor'));
		$this->_dados->fixarTextoInicio($this->_campos['texto_inicio']->obter('valor'));
		$this->_dados->fixarTextoFim($this->_campos['texto_fim']->obter('valor'));

		$this->_dados->fixarDataRealizacao( strptime_real('%d/%m/%Y %H:%M', $this->_campos['data_realizacao']->obter('valor')) );

		if($this->_campos['data_realizacao_fim']->obter('valor')){
			$this->_dados->fixarDataRealizacaoFim( strptime_real('%d/%m/%Y %H:%M', $this->_campos['data_realizacao_fim']->obter('valor')) );
		}
		else{
			$this->_dados->fixarDataRealizacaoFim( null );
		}

		$this->_dados->fixarDataInicioInscricoes( strptime_real('%d/%m/%Y %H:%M', $this->_campos['data_inicio_inscricoes']->obter('valor')) );
		$this->_dados->fixarDataFimInscricoes( strptime_real('%d/%m/%Y %H:%M', $this->_campos['data_fim_inscricoes']->obter('valor')) );

		$this->_dados->fixarInicioLancamento( strptime_real('%d/%m/%Y %H:%M', $this->_campos['data_inicio_lancamento']->obter('valor')) );
		$this->_dados->fixarFimLancamento( strptime_real('%d/%m/%Y %H:%M', $this->_campos['data_fim_lancamento']->obter('valor')) );
		
		$this->_dados->fixarLinguasCompartilham( ($this->_campos['linguas_compartilham']->obter('valor') == 1) );

		// remove questões defasadas pelo número de tipos ou duração do simulado e atualiza inscritos
		if ( $this->obterEstado() == self::EDITANDO ) {
			Questao::removerQuestoesDefasadas($this->_dados, $this->_campos['tipos_provas']->obter('valor'), $this->_campos['duracao']->obter('valor'));
			Inscricao::atualizarInscricoesDefasadas($this->_dados, $this->_campos['tipos_provas']->obter('valor'));
		}

		$this->_dados->fixarNumeroDeTipos($this->_campos['tipos_provas']->obter('valor'));
		$this->_dados->fixarDuracao($this->_campos['duracao']->obter('valor'));

		$this->_dados->fixarRealizacaoUnica($this->_campos['realizacao_unica']->obter('valor'));

		if($this->_campos['corretores']->obter('valor')){
			$this->_dados->fixarCorretores( $this->_campos['corretores']->obter('valor') );
		}
		else{
			$this->_dados->fixarCorretores(1);
		}

		$itensSelecionados = array();
		$corrSelecionados = $this->_campos['corr_sele']->obter('valor');
		foreach ($corrSelecionados as $sk => $sv) {
			$itensSelecionados[$sv] = array();
			if ( isset($this->_campos['corr_sele_'.$sv]) ) {
				$itensSelecionados[$sv] = $this->_campos['corr_sele_'.$sv]->obter('valor');
			}
		}

		$this->_dados->fixarCorretoresSelecionados($corrSelecionados, $itensSelecionados);

		$serie = new Serie($this->_campos['serie_avaliacao']->obter('valor'));
		$serie->carregar();
		$this->_dados->fixarSerieAvaliacao($serie);

		$this->_dados->fixarProdTxt($this->_campos['prod_txt']->obter('valor'));
		$this->_dados->fixarAvEspecial($this->_campos['av_especial']->obter('valor'));
		$this->_dados->fixarSecaoRels($this->_campos['av_secao']->obter('valor'));

		//if ( $this->obterEstado() == self::ADICIONANDO ) {
			$this->_dados->gerarAulas();
		//}

		// turmas
		/*$turmas_sele = array();
		foreach ($this->_campos['turmas_sele']->obter('html_valor') as $tID => $tNome) {
			$turmas_sele[] = $tID;
		}

		// remove alunos inscritos de turmas que foram deselecionadas
		if ( $this->obterEstado() == self::EDITANDO ) {
			foreach ( $this->_dados->obterTurmas() as $tID ) {
				if ( !in_array($tID, $turmas_sele) ) {
					Inscricao::removerInscritosPelaTurma( $this->_dados, new Turma($tID) );
				}
			}
		}

		$this->_dados->fixarTurmas($turmas_sele);*/
	}

  private function _obterArraySimulados()
    {
        $simulados = array();
        $simulados[null] = "";
        $rs = Core::registro('db')->query( sprintf('SELECT * FROM simulados ORDER BY s_nome') );

        if ($rs->num_rows) {
            while ($row = $rs->fetch_assoc())
            {
                $simulados[$row['s_id']] = $row['s_nome'];
            }
        }
        $rs->free();

        return $simulados;
    }

	private function _obterArrayTurmas ()
	{
		$turmas = array();

		$rs = Core::registro('db')->query( sprintf('
			SELECT * FROM turmas
			INNER JOIN series ON series.s_id = turmas.t_serie AND s_instituicao = %s
			INNER JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao
			ORDER BY i_nome ASC, s_nome ASC, t_nome ASC',
			Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$turmas[$row['t_id']] = sprintf('%s - %s - %s', $row['i_nome'], $row['s_nome'], $row['t_nome']);
			}
		}
		$rs->free();

		return $turmas;
	}

	public function _obterArrayProfessores ()
	{
		$sql_prof = "";
		$prof = array();

		$rs = Core::registro('db')->query('
			SELECT p_id, u_nome FROM usuarios 
			INNER JOIN professores ON professores.p_usuario = usuarios.u_id
			WHERE p_corretor = "1" OR p_somentecorretor = "1" 
			ORDER BY u_nome ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				$prof[$row['p_id']] = $row['u_nome'];
			}
		}
		$rs->free();

		return $prof;
	}

	private function _obterArrayCorretores ($simu)
	{
		$sql_prof = "";
		$prof = array();

		$rs = Core::registro('db')->query( sprintf('
			SELECT p_id, u_nome FROM usuarios 
			INNER JOIN professores ON professores.p_usuario = usuarios.u_id
			INNER JOIN simulados_corretores ON professores.p_id = simulados_corretores.sc_professor
			WHERE simulados_corretores.sc_simulado = %s %s
			ORDER BY u_nome ASC',
			Core::registro('db')->formatarValor( $simu ),
			$sql_prof ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				$prof[$row['p_id']] = $row['u_nome'];
			}
		}
		$rs->free();

		return $prof;
	}

	private function _obterArrayItensSimulados ($simu)
	{
		$sql_prof = "";
		$simus = array();

		$rs = Core::registro('db')->query( sprintf('
			SELECT * FROM simulados_corretores WHERE sc_simulado = %s %s ',
			Core::registro('db')->formatarValor( $simu ),
			$sql_prof ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$simus[$row['sc_professor']] = explode(',', $row['sc_itens']);
			}
		}
		$rs->free();

		return $simus;
	}
}

?>