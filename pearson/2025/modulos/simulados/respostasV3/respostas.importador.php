<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Aluno', null, true);
Core::incluir('Turma', null, true);
Core::incluir('Serie', null, true);
Core::incluir('Instituicao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('Questao', null, true);
Core::incluir('Disciplina', null, true);

Core::incluir('ImportadorCVS', 'Importador/');
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);

class FImportarRespostasV3_Exception extends Formulario_Exception { }

class FImportarRespostasV3 extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;
	const PASSO_4 = 3;

	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_campoParaTratar = array();
	protected $_dadosImportados = array();
	protected $_dadosNaoImportados = array();
	public  $_resultadoImportacao = array();

	protected $_simulado = null;
	protected $_fase = 1;

	protected $_escolasCriar = array();
	protected $_tumasCriar = array();
	protected $_professoresCriar = array();

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_dados = array();
		$this->_correspondencias = array(
			'NULA' => '',
			'avaliacao' => 'ID AVALIAÇÃO *',
			'fase' => 'FASE *',
			'matricula' => 'Matrícula *',
			'atestado' => 'Atestado',
			'questoes' => 'Respostas: Primeira resposta das colunas seguintes',
		);

		$this->_correspondenciasObrigatorias = array('matricula','avaliacao','fase');

		$this->_campoParaTratar = array(
			'avaliacao',
			'fase',
			'matricula',
		);
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;

		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );

			$cabecalhoSel = null;
			if ( !$this->foiEnviado() && !isset($_POST['post_anterior'])) {
				$cabecalhoSel = 1;

				if (Core::diretiva('_importador_respostas_v3.cabecalho.ultima_selecao') !== false &&
						Core::diretiva('_importador_respostas_v3.cabecalho.ultima_selecao') == 0)
					$cabecalhoSel = 0;
			}

			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => $cabecalhoSel,
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ';',
													'html_ordem' => $ordem++
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );

			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2) {
			$questaoSel = 0;

			$ordensCorrespondencias = array('matricula');

			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'valor' => (!$this->foiEnviado() && $i > (count($ordensCorrespondencias) - 2) ? $questaoSel++ : null),
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}

			if (!$this->foiEnviado()) {
				foreach ($ordensCorrespondencias as $k => $v) {
					if (isset($this->_campos['correspondencia_' . $k]))
						$this->_campos['correspondencia_' . $k]->fixar('valor', $v);
				}

				if (isset($this->_campos['correspondencia_' . (count(@$this->_dados[0]) - 1)]))
					$this->_campos['correspondencia_' . (count(@$this->_dados[0]) - 1)]->fixar('valor', 'nota_anterior');
			}
		}

		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}

		$habilitado = true;
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$habilitado = false;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ativo' => $habilitado,
												'html_ordem' => $ordem++
							  )) );
	}

	public function checarFormulario ()
	{
		try{
			parent::checarFormulario();

			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');

				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarRespostasV3_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				$ultimaColuna = 0;
				for ($i = 0; $i < count(@$this->_dados[0]); $i++){
					$coluna = $this->_campos['correspondencia_'. $i]->obter('valor');

					$this->_correspondenciasSel[$i] = $coluna;

					if($coluna != 'NULA'){
						$ultimaColuna = $i;
					}
				}

				if(in_array('questoes', $this->_correspondenciasSel) == true){
					if($this->_correspondenciasSel[$ultimaColuna] != 'questoes'){
						$this->_adicionarErro(null, 'O campo de correspondêcia <i>'.@$this->_correspondencias['questoes'].'</i> deverá ser o último, pois as colunas seguintes representam as questões em sequências;');
						throw new FImportarRespostasV3_Exception('Correspondência de ordem obrigatória!');
					}
				}

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarRespostasV3_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarRespostasV3_Exception('Correspondência obrigatória não selecionada!');
					}
				}

				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}

				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarRespostasV3_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e){
			throw new FImportarRespostasV3_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$this->_executarPasso_3();//$importados = $this->_executarPasso_3();
			//Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' séries de respostas importadas com sucesso!', 'Importando respostas...');
			//Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}

	public function &obterDadosImportados () 
	{
		return $this->_dadosImportados;
	}

	public function &obterDadosNaoImportados () 
	{
		return $this->_dadosNaoImportados;
	}

	public function &obterResultadoImportacaoErros () 
	{
		return $this->_resultadoImportacao['down'];
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));

			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;

			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();

				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}

				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}

		if ( $passo == null )
			$passo = self::PASSO_1;

		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;

				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}

		if ( $passo == self::PASSO_4 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));
		}

		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		Core::fixarDiretiva('_importador_respostas_v3.cabecalho.ultima_selecao', $this->_campos['cabecalho']->obter('valor') == 1 ? 1 : 0);

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarrespostasv3'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarRespostasV3_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('simulados', 'importarrespostasv3'), $dados );
	}

	protected function _executarPasso_3 () 
	{
		$imgSim = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sim.gif" border="0" />';
		$imgNao = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'nao.gif" border="0" />';

		$simuladoPossiveis = Simulado::obterSimuladosParaFormularioII();

		$feedback = array('down'=>array());//'up'=>array(),'down'=>array());
		foreach ($this->_dadosImportados as $linha => $obj) {
			extract($obj);

			//AVALIACAO
			if(!empty($avaliacao) && !empty($fase)){
				$aID = array_key_exists($avaliacao, $simuladoPossiveis);

				if($aID){
					$aOBJ = new Simulado($avaliacao);
					$aOBJ->carregar();
				}
				else{
					$feedback['down'][] = "[$imgNao Não][Linha $linha] A avaliação $avaliacao não encontrada.";
					continue;
				}
			}
			else{
				$feedback['down'][] = "[$imgNao Não][Linha $linha] A avaliação $avaliacao e/ou fase $fase não foram definidas.";
				continue;
			}

			//ALUNO
			if(!empty($matricula)){
				$alID = Aluno::obterAlunoPelaMatricula($matricula);
				if($alID !== false){
					$alOBJ = new Aluno($alID);
					$alOBJ->carregar();
				}
				else{
					$feedback['down'][] = "[$imgNao Não][Linha $linha] Matrícula não encontrada.";
					continue;
				}
			}
			else{
				$feedback['down'][] = "[$imgNao Não][Linha $linha] Matrícula não foi definida.";
				continue;
			}

			//INSCRICAO
			if(isset($alOBJ)){
				$inscID = Inscricao::obterIDInscricaoPeloAluno($aOBJ, $alOBJ);
				if($inscID !== false){
					$inscOBJ = new Inscricao($inscID);
					$inscOBJ->carregar();
				}
				else{
					//$feedback['down'][] = "[$imgNao Não][Linha $linha] A matrícula $matricula não está inscrita na avaliação $avaliacao.";
					//continue;

					$inscOBJ = Inscricao::obterNovaInscricao($aOBJ, $alOBJ);
					$inscOBJ->carregar();

					//$feedback['up'][] = "[$imgSim Sim][Linha ".($i+1)."] A matrícula $matricula não está inscrita na avaliação $avaliacao. Será inscrito.";
				}

				if(!empty($atestado)){
					$inscOBJ->fixarAtestado($atestado);
				}

				if (!$inscOBJ->salvar()){
					$feedback['down'][] = "[$imgNao Não][Linha $linha] Erro ao processar inscrição do aluno.";
					continue;
				}
			}
			else{
				$feedback['down'][] = "[$imgNao Não][Linha $linha] Erro ao processar inscrição do aluno.";
				continue;
			}

			//RESPOSTAS
			if(count($novas_respostas)>0){
				$questoes = Questao::carregarQuestoes($aID, $fase);
				if(count($questoes)>0){
					foreach ($questoes as $qk => $qv) {
						$iR = $qv['g_numero']-1;
						$novaResposta = $novas_respostas[$iR];

						switch ($qv['q_tipo']) {
							case Questao::MULTIPLAESCOLHA:
								if ($novaResposta != null && !in_array(strtoupper($novaResposta), MultiplaEscolha::$opcoesParaImportar) && isset(MultiplaEscolha::$opcoesParaImportar[(int)$novaResposta])){
									$novaResposta = MultiplaEscolha::$opcoesParaImportar[(int)$novaResposta];
								}

								$novaResposta = MultiplaEscolha::inteiro($novaResposta);
								if ($novaResposta === false){
									$novaResposta = null;
								}

								break;
							case Questao::DISCURSIVA:
								$novaResposta = str_replace(',', '.', $novaResposta);
								if (!Filtrador::real($novaResposta)){
									$novaResposta = null;
								}

								break;
							case Questao::ABERTA:
							case Questao::SOMATORIO:
								if (empty($novaResposta) || !Filtrador::natural((int)$novaResposta)){
									$novaResposta = null;
								}
								else{
									$novaResposta = (int) $novaResposta;
								}

								break;
							default:
								$novaResposta = null;

								break;
						}

						foreach ($inscOBJ->obterRespostasMarcadas() as $delR) {
							@$delR->remover();
						}

						if ($novaResposta !== null) {
							$qal = Resposta::obterNovaResposta(new Questao($qv['q_id']), $inscOBJ, 'RESPOSTASV3');
							$qal->fixarValor($novaResposta);
							if($qal->salvar() === false){
								$feedback['down'][] = "[$imgNao Não][Linha $linha] O aluno ($matricula) teve ERRO ao registrar a nova resposta $iR.";
							}
						}
					}
				}
				else{
					$feedback['down'][] = "[$imgNao Não][Linha $linha] A avaliacao $avaliacao não tem questões. Verifique a fase.";
				}
			}
			else{
				$feedback['down'][] = "[$imgNao Não][Linha $linha] O aluno ($matricula) não tem respostas.";
			}
		}

		$this->_resultadoImportacao = $feedback;

		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array('passo' => self::PASSO_4,
					   'dados' => chunk_split(base64_encode(serialize($this->_resultadoImportacao))),
					   'post_anterior' => chunk_split(base64_encode(serialize($this->_entrada)))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post(Gerenciador_URL::gerarLink('simulados', 'importarrespostasv3'), $dados);
	}

	protected function _gerarDadosImportados () 
	{
		$matriculas[] = array();

		$imgSim = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'sim.gif" border="0" />';
		$imgNao = '<img src="'.Core::diretiva('ESTILO:DIRETORIO:media').'nao.gif" border="0" />';

		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;

		for ($i; $i < count($this->_dados); $i++){
			$obj = array(
				'feedback_up' => array(),
				'feedback_down' => array(),
					'matricula' => null,
					'atestado' => null,
					'avaliacao' => null,
					'fase' => null,
					'novas_respostas' => array()
			);

			foreach ($this->_correspondenciasSel as $j => $cID) {
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;
				if($cID == 'questoes'){
					$arrStart = array_search('questoes', $this->_correspondenciasSel);
					$arrSize = count($this->_dados[$i]);
					for ($a=$arrStart; $a < $arrSize; $a++) { 
						$valor = preg_replace('/\s+/', ' ', trim($this->_dados[$i][$a]));
						$obj['novas_respostas'][$a-$arrStart] = $valor;
					}
				}
				else{
					if(in_array($cID, $this->_campoParaTratar)){
						$valor = ImportadorCVS::limparCharEspeciais($valor);
					}

					$obj[$cID] = $valor;
				}
			}
			
			$importar = 0;
			extract($obj);

			//VALIDAR MATRICULA
			if(!empty($matricula)){
				if(in_array(ImportadorCVS::limparCharEspeciais($matricula.$avaliacao.$fase), $matriculas)){
					$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] A matrícula ('.$matricula.') já está no arquivo e não será processada novamente.';
				}
				else{
					$matriculas[] = ImportadorCVS::limparCharEspeciais($matricula.$avaliacao.$fase);

					$sqlMatricula = Core::registro('db')->query('SELECT a_id, a_turma FROM alunos WHERE a_matricula = "'.$matricula.'" LIMIT 1;');
					if ($sqlMatricula->num_rows) {
						while ($sqlMatriculaArr = $sqlMatricula->fetch_assoc()){
							$aID = $sqlMatriculaArr['a_id'];
							$tID = $sqlMatriculaArr['a_turma'];
						}

						$serieID = 0;
						$sqlSerie = Core::registro('db')->query('SELECT s_id FROM turmas INNER JOIN series ON t_serie = s_id WHERE t_id = "'.$tID.'"');
						if ($sqlSerie->num_rows) {
							while ($sqlSerieArr = $sqlSerie->fetch_assoc()) {
								$serieID = $sqlSerieArr['s_id'];
							}
						}
						$sqlSerie->free();

						if(!empty($avaliacao) && !empty($fase)){
							$sqlSimulado = Core::registro('db')->query('SELECT s_id FROM simulados WHERE s_id = "'.$avaliacao.'" AND s_serie = "'.$serieID.'";');
							if ($sqlSimulado->num_rows) {
								$sqlInscricao = Core::registro('db')->query('SELECT si_id FROM simulados_inscricoes WHERE si_simulado = "'.$avaliacao.'" AND si_aluno = "'.$aID.'";');
								if ($sqlInscricao->num_rows) {
									$importar = 1;
								}
								else{
									$importar = 1;
									//$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Aluno não está inscrito na avaliação.';
									$obj['feedback_up'][] = '['.$imgSim.' Sim][Linha '.($i+1).'] Aluno não está inscrito na avaliação. Será inscrito.';
								}
								$sqlInscricao->free();
							}
							else{
								$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Não tem avaliação e/ou série válida. Verifique se a série do aluno é a mesma da avaliação.';
							}
							$sqlSimulado->free();
						}
						else{
							$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Não tem avaliação e/ou fase preenchida.';
						}
					}
					else{
						$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] A matrícula não foi encontrada.';
					}
					$sqlMatricula->free();
				}
			}
			else{
				$obj['feedback_down'][] = '['.$imgNao.' Não][Linha '.($i+1).'] Não tem matrícula preenchida.';
			}

			if ($importar){
				$obj['feedback_up'][] = '['.$imgSim.' Sim][Linha '.($i+1).'] Registro será processado.';
				$this->_dadosImportados[($i+1)] = $obj;
			}
			else{
				$this->_dadosNaoImportados[] = $obj;
			}
		}
	}
}

?>