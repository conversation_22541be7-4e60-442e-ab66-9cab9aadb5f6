<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio"><strong>Importando respostas V3</strong></div>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th class="selecionada">
<?
	if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_2 )
		echo '2º passo - Relacionando os dados';
	else if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_3 )
		echo '3º passo - Visualizando resultados';
	else
		echo '1º passo - Selecionando o arquivo';
?>
	</th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>
	<div class="vd_BlocoBotoes" align="center">
<?
	if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_3 || $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_2 )
		echo $this->_formulario->obterHTML('voltar', Formulario::HTML_CAMPO, true);
?>

<?
	if ( ($this->_formulario->obterEstado() != FImportarRespostasV3::PASSO_3 && $this->_formulario->obterEstado() != FImportarRespostasV3::PASSO_4) || ($this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_3 && count( $this->_formulario->obterDadosImportados() ) > 0) )
		echo $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true);
?>

	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('simulados', 'importarrespostasv3'); ?>'" value="Cancelar" class="botao cancelar">
	</div>

<?
	if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_1 ) {
?>
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
		<tr>
		  <th colspan="2" class="titulo">Informações do arquivo </th>
		</tr>
		<tr>
		  <th><?= $this->_formulario->obterHTML('arquivo', Formulario::HTML_LABEL, true); ?></th>
		  <td width="75%"><?= $this->_formulario->obterHTML('arquivo', Formulario::HTML_CAMPO, true); ?> </td>
		</tr>
		<tr>
		  <th><?= $this->_formulario->obterHTML('cabecalho', Formulario::HTML_LABEL, true); ?></th>
		  <td><?= $this->_formulario->obterHTML('cabecalho', Formulario::HTML_CAMPO, true); ?> </td>
		</tr>
		<tr>
		  <th><?= $this->_formulario->obterHTML('separador', Formulario::HTML_LABEL, true); ?></th>
		  <td><?= $this->_formulario->obterHTML('separador', Formulario::HTML_CAMPO, true); ?> </td>
		</tr>
	  </table>
<?
	}
?>

<?
	if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_2 ) {
		$titulos = array('Correspondência', 'Cabeçalho', '1º linha', '2º linha', '3º linha');
		if ( @$_POST['cabecalho'] != 1 )
			unset($titulos[1]);
?>
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
		<tr>
		  <th class="titulo">Relação dos dados </th>
		</tr>
        <tr>
          <td>
		  <table width="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th style="text-align: center;"><?= array_shift($titulos); ?></th>
              <th style="text-align: left;"><?= array_shift($titulos); ?></th>
			  <th style="text-align: left;"><?= array_shift($titulos); ?></th>
			  <th style="text-align: left;"><?= array_shift($titulos); ?></th>
            </tr>
<?
	$dados = $this->_formulario->obterDados();
	for ($i = 0; $i < count(@$dados[0]); $i++) {
?>
            <tr>
              <td align="center"><?= $this->_formulario->obterHTML('correspondencia_'. $i, Formulario::HTML_CAMPO, true); ?></td>
              <td><?= isset($dados[0][$i]) ? substr($dados[0][$i], 0, 75) : null; ?></td>
			  <td><?= isset($dados[1][$i]) ? substr($dados[1][$i], 0, 75) : null; ?></td>
			  <td><?= isset($dados[2][$i]) ? substr($dados[2][$i], 0, 75) : null; ?></td>
            </tr>
<?
	}
?>
          </table>
		  </td>
        </tr>
	  </table>
	  <?= $this->_formulario->obterHTML('dados', Formulario::HTML_CAMPO, true); ?>
<?
	}
?>

<?
	if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_3 ) {
?>
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
		<tr>
		  <th class="titulo">Resultados da importação </th>
		</tr>
		<tr>
		  <td class="descricao"><?= count($this->_formulario->obterDadosImportados()); ?> de <?= count($this->_formulario->obterDados()) - (@$_POST['cabecalho'] == 1 ? 1 : 0); ?> linhas de respostas  podem ser importadas. <?= count($this->_formulario->obterDadosNaoImportados()) ? '<a href="#dados_nao_importados">'.count($this->_formulario->obterDadosNaoImportados()).'</a> linhas de repostas apresentaram algum problema.' : ''; ?></td>
		</tr>
        <tr>
          <td>
<?
	if ( count($this->_formulario->obterDadosImportados()) ) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
		  <td class="lp_ColHeader">Matrícula</td>
		  <td class="lp_ColHeader">Feedback Positivo</td>
		</tr>
<?
		foreach ($this->_formulario->obterDadosImportados() as $d) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
        <td><?= $d['matricula']; ?></td>
        <td><?= implode('<br>',$d['feedback_up']); ?></td>
    </tr>
<?
		}
	}
?>
	</table>
<!-- nao -->

<?
	if ( count($this->_formulario->obterDadosNaoImportados()) ) {
?>
		<div style="font-weight: bold; padding-bottom: 10px; padding-top: 20px;"><a name="dados_nao_importados">Dados que não podem ser importados:</a></div>
		<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
			<tr>
			  <td class="lp_ColHeader">Matrícula</td>
		  <td class="lp_ColHeader">Feedback Negativo</td>
			</tr>
<?
			foreach ($this->_formulario->obterDadosNaoImportados() as $d) {
?>
	    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
	      	<td><?= $d['matricula']; ?></td>
        	<td><?= implode('<br>',$d['feedback_down']); ?></td>
	    </tr>
<?
			}
?>
		</table>
<?
		}
	else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
		  <td><em>Nenhuma linha de resposta importada</em></td>
	    </tr>
	</table>
<?
	}
?>

		  </td>
        </tr>
	  </table>
	  <?= $this->_formulario->obterHTML('dados', Formulario::HTML_CAMPO, true); ?>
<?
	}
?>

<?
	if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_4 ) {
?>
	  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
		<tr>
		  <th class="titulo">Resultados da importação </th>
		</tr>
        <tr>
          <td>
<?
	$dadosFalhas = $this->_formulario->obterDados();

	if ( count($dadosFalhas) ) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
		  <td class="lp_ColHeader">Feedback</td>
		</tr>
<?
		foreach ($dadosFalhas as $d) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
        <td><?= implode('<br>',$d); ?></td>
    </tr>
<?
		}
?>
	</table>

<?
	} else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
		  <td><em>Nenhuma linha de resposta importada</em></td>
	    </tr>
	</table>
<?
	}
?>

		  </td>
        </tr>
	  </table>
	  <?= $this->_formulario->obterHTML('dados', Formulario::HTML_CAMPO, true); ?>
<?
	}
?>

	</td>
  </tr>
</table>
<?
	//if ( $this->_formulario->obterEstado() == FImportarRespostasV3::PASSO_2 ) {
?>
<script type="text/javascript">
jQuery(document).ready(function() {
	//netCheckEng('<?php echo"http://".$_SERVER["SERVER_NAME"]; ?>');
	setTimeout(function(){
		jQuery('#enviar').removeAttr('disabled');
	},1000);
});
</script>
<?
	//}
?>
<?
if ( $this->_formulario->obterEstado() != FImportarRespostasV3::PASSO_1 )
		echo $this->_formulario->obterHTML('post_anterior', Formulario::HTML_CAMPO, true);
?>
<?= $this->_formulario->obterHTML('passo', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>