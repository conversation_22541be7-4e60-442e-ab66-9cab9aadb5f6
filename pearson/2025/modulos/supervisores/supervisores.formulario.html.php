<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio">
<?
if ($this->_formulario->obterEstado() == Formulario::ADICIONANDO) {
	echo '<strong>Adicionando supervisor</strong>';
} else {
	echo '<strong>'. $this->_formulario->obterDados()->obterUsuario()->obterNome() .'</strong> - Editando supervisor';
}
?>
</div>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th class="selecionada"><?= $this->_formulario->obterEstado() == Formulario::EDITANDO ? '<a href="'. Gerenciador_URL::gerarLink('supervisores', 'detalhar', array('id' => $this->_formulario->obterDados()->obterID())) .'">Informações do supervisor</a>' : 'Informações do supervisor' ; ?></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>
	
	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	
	<input type="button" onClick="window.location='<?= $this->_formulario->obterEstado() == Formulario::EDITANDO ? Gerenciador_URL::gerarLink('supervisores', 'detalhar', array('id' => $this->_formulario->obterDados()->obterID())) : Gerenciador_URL::gerarLink('supervisores', 'listar'); ?>'" value="Cancelar" class="botao cancelar">
	</div>

<div class="vd_BlocoEspacador">
	<?= Core::modulo('_componente_usuario')->obterSaida(); ?>
</div>

<?
	// @todo: hardcoded, vai sair: checa se é admin/diretor pra poder adicionar instituicoes...
	if ( in_array('1', Core::registro('usuario')->obterGrupos()) || in_array('2', Core::registro('usuario')->obterGrupos()) ) {
?>
	<div class="vd_BlocoEspacador">
	<table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Informa&ccedil;&otilde;es do supervisor </th>
        </tr>
        <tr>
          <th>Instituições disponíveis</th>
          <td colspan="3" align="center"><table width="98%" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('simu_disp', Formulario::HTML_LABEL, true); ?></th>
              <td>&nbsp;</td>
              <th style="text-align:left; width: 45%"><?= $this->_formulario->obterHTML('simu_sele', Formulario::HTML_LABEL, true); ?></th>           
            </tr>
            <tr>
              <td><?= $this->_formulario->obterHTML('simu_disp', Formulario::HTML_CAMPO, true); ?></td>
              <td align="center"><div style="margin-bottom:4px;"><?= $this->_formulario->obterHTML('add_simu', Formulario::HTML_CAMPO, true); ?></div><div><?= $this->_formulario->obterHTML('del_simu', Formulario::HTML_CAMPO, true); ?></div></td>
              <td><?= $this->_formulario->obterHTML('simu_sele', Formulario::HTML_CAMPO, true); ?></td>
            </tr>
          </table></td>
        </tr>
      </table>
      </div>
    <?php } ?>
	</td>
  </tr>
</table>

<?= $this->_formulario->obterHTML('id', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoEditarEmOrdem(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>

<?= JTransferidorDeItensDeMenu::obterHTMLParaPrepararFormulario($this->_formulario->info('nome'), array('simu_disp[]', 'simu_sele[]')); ?>