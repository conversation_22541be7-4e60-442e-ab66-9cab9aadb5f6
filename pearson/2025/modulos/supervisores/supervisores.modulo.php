<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Supervisor', null, true);
Core::incluir('Relatorio', null, true);
Core::incluir('Simulado', null, true);

include_once('supervisores.listagem.php');
include_once('supervisores.formulario.php');
include_once('supervisores.exportador.php');
include_once('supervisores.importador.php');

class MSupervisores extends Modulo
{
	protected $_listagem;
	protected $_formulario;

	public function __construct ()
	{
		parent::__construct();

		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		// habilita botão de adicionar nos atalhos do navegador
		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('supervisores', 'novo'), 'Adicionar supervisor...');
		}
	}

	public function aExportarSupervisores ()
	{
		try {
			$exportador = new ExportadorSupervisores();

			$exportador->exportar();

			$this->_iniciarRenderizacao( $exportador->obterTipoSaida() );
				$exportador->obterSaida('supervisores.csv');
			$this->_finalizarRenderizacao();
		} catch (Core_Exception $e) {
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		}
	}

	public function aImportarSupervisores ()
	{
		$this->_formulario = new FImportarSupervisores( array('nome' => 'form_importar_supervisores', 'acao' => Gerenciador_URL::gerarLink('supervisores', 'importar'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'listar') );
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ }
		}

		//Core::modulo('js')->incluirArquivo('includes/JavaScript/netCheck/engine.js');
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('supervisores.importador.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aListarSupervisores ()
	{
		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('importar', true, Gerenciador_URL::gerarLink('supervisores', 'importar'), 'Importar supervisores...');

			Core::modulo('navegador')->habilitarAtalho('exportar', true, Gerenciador_URL::gerarLink('supervisores', 'exportar'), 'Exportar supervisores...');
		}

		return $this->_carregarListagem();
	}

	public function aDetalharSupervisor ()
	{
		$supervisor = new Supervisor( (int) @$_GET['id'] );

		if ( $supervisor->carregar() ) {
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('supervisores.detalhes.html.php');
			$this->_finalizarRenderizacao();
		} else {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('supervisores', 'listar'), 'supervisor inválido!');
		}

		return true;
	}

	public function aNovoSupervisor ()
	{
		Core::carregarModulo(array('nome' => '_componente_usuario', 'classe' => 'MComponenteUsuario', 'guardar_como' => '_componente_usuario'));

		$this->_formulario = new FSupervisores( array('nome' => 'form_supervisores', 'acao' => Gerenciador_URL::gerarLink('supervisores', 'novo'), 'tipo_encriptacao' => Formulario::MULTIPART) );

		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->prepararEntradaArquivos();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$id = $this->_formulario->executar()->obterID();

				switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
					case 'adicionar':
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'novo', array('pos_envio' => 'adicionar')) ); break;
					case 'editar_proximo':
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
					default:
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'detalhar', array('id' => $id)) );
				}

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e)	{ }
		}

		Core::modulo('_componente_usuario')->prepararFormulario($this->_formulario);

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('supervisores.formulario.html.php');
		$this->_finalizarRenderizacao();

		return true;
	}

	public function aProcessarFormulario ()
	{
		return $this->aNovoSupervisor();
	}

	// @todo: critério de remoção não definido
	public function aRemoverSupervisores ($ids = null)
	{
		if ( $ids != null && is_array($ids) ) {
			foreach ($ids as $id) {
				$supervisor = new Supervisor($id);

				if ( !$supervisor->carregar() ) {
					Core::modulo('redirecionador')->adicionarFalha($supervisor->obterID(), 'id inválido;');
					continue;
				}

				if (!$supervisor->remover()) {
					Core::modulo('redirecionador')->adicionarFalha($supervisor->obterUsuario()->obterNome(), 'supervisor não foi removido;');
				} else {
					Core::modulo('redirecionador')->adicionarSucesso($supervisor->obterUsuario()->obterNome(), 'supervisor removido com sucesso;');
				}
			}

			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo supervisores...');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if (isset($_GET['id']) && (int) $_GET['id'] > 0) {
				return $this->aRemoverSupervisores( array((int) $_GET['id']) );
			}

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaReferencia() );
			Core::modulo('redirecionador')->redirecionar();
		}
	}

	private function _carregarListagem ()
	{
		$this->_listagem = new LSupervisores();

		$this->_listagem->prepararListagem();

		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}
}

?>