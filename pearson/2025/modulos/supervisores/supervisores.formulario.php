<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('JTransferidorDeItensDeMenu', 'JavaScript/JTransferidorDeItensDeMenu/', true);

include_once('supervisores.formulario.exception.php');

class FSupervisores extends Formulario
{
	protected $_formularioUsuario;
	
	public function __construct ($info = array())
	{
		parent::__construct($info);
		
		$this->_formularioUsuario = Core::modulo('_componente_usuario')->obterFormulario();
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$simu_sele_super = array();
		$instituicoes = $this->_dados->obterInstituicoes();
		if(!empty($instituicoes)){
			$simu_sele_super = json_decode($instituicoes,true);
		}

		$simu_sele = array();
		$simu_disp = $this->_obterArrayInstituicoes();
		$simu_dispf = $simu_disp;

		if(count($simu_sele_super)>0){
			foreach ($simu_dispf as $pID => $pNome) {
				if (in_array($pID, $simu_sele_super)) {
					$simu_sele[$pID] = $simu_dispf[$pID];
					unset($simu_dispf[$pID]);
				}
			}
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'simu_disp',
												'etiqueta' => 'Instituições disponíveis',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simu_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'simu_sele',
												'etiqueta' => 'Instituições selecionadas',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simu_disp)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => array(),
												'html_varias_selecoes' => true,
												'html_linhas' => 15,
												'html_array' => true
							  )) );

		$this->_campos['simu_sele']->fixar('html_valor', $simu_sele);
		$this->_campos['simu_disp']->fixar('html_valor', $simu_dispf);

		$this->adicionarCampo( new Campo(array( 'nome' => 'add_simu',
												'etiqueta' => 'Adicionar >>',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_classe' => 'botao salvar',
												'componente' => new JTransferidorDeItensDeMenu('simu_disp[]', 'simu_sele[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'del_simu',
												'etiqueta' => '<< Remover',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tamanho' => 100,
												'html_tamanho_tipo' => Campo::HTML_TAM_PIXEL,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao cancelar',
												'componente' => new JTransferidorDeItensDeMenu('simu_sele[]', 'simu_disp[]')
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar'
							  )) );


		$this->_formularioUsuario->fixarEstado($this->obterEstado());
		$this->_formularioUsuario->prepararEntrada($this->_entrada);
		$ordem = $this->_formularioUsuario->carregarFormulario($this->_dados->obterUsuario(), array('grupos' => array(6 => true)));

		foreach ($this->_formularioUsuario->obterCampos() as $campo)
			$this->adicionarCampo($campo);
		
		$this->_campos['enviar']->fixar('html_ordem', $ordem);
		
		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outro supervisor');		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próximo supervisor');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			$this->_formularioUsuario->checarFormulario();
		}
		catch (FComponenteUsuario_Exception $e)
		{
			$this->_erros = $this->_formularioUsuario->obterErros();
			throw new FSupervisores_Exception($e->getMessage());
		}
		catch (Formulario_Exception $e)
		{
			throw new FSupervisores_Exception($e->getMessage());
		}
	}
	
	public function &executar ()
	{	
		$usuario = $this->_formularioUsuario->executar();
	
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $usuario != false && $usuario->obterID() != null && $this->_adicionar($usuario) ) {
				Core::modulo('redirecionador')->fixarMensagem('Supervisor adicionado com sucesso!', 'Adicionando supervisor...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o supervisor!', 'Adicionando supervisor...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $usuario != false && $this->_editar($usuario) ) {
				Core::modulo('redirecionador')->fixarMensagem('Supervisor editado com sucesso!', 'Editando supervisor...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar o supervisor!', 'Editando supervisor...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		return $this->_dados;
	}
	
	public function foiEnviado ()
	{
		$retorno = parent::foiEnviado();
		
		$this->_formularioUsuario->fixarFoiEnviado($retorno);
		
		return $retorno;
	}
	
	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}
		
		$this->_dados = new Supervisor($id);
		
		if ( $id != null ) {
			if ( $this->_dados->carregar() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('supervisores', 'listar'), 'Supervisor inválido!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$usuario = new UsuarioInstituido(null);
			$usuario->fixarInstituicao( Core::registro('instituicao') );
			$usuario->fixarEndereco( new Endereco(null) );
			$usuario->fixarEmail( new Email(null) );
			$this->_dados->fixarUsuario( $usuario );
		}
	}
	
	protected function _adicionar (UsuarioInstituido &$usuario)
	{
		$this->_dados = Supervisor::obterNovoSupervisor($usuario);

		$instSelecionados = null;
		$instSelecionadosCampo = $this->_campos['simu_sele']->obter('valor');
		if(count($instSelecionadosCampo)>0){
			$instSelecionados = json_encode($instSelecionadosCampo);
		}
		$this->_dados->fixarInstituicoes($instSelecionados);

		$retorno = $this->_dados->salvar();	
		
		return true;
	}
	
	protected function _editar (UsuarioInstituido &$usuario)
	{
		$this->_dados->fixarUsuario($usuario);
		
		$instSelecionados = null;
		$instSelecionadosCampo = $this->_campos['simu_sele']->obter('valor');
		if(count($instSelecionadosCampo)>0){
			$instSelecionados = json_encode($instSelecionadosCampo);
		}
		$this->_dados->fixarInstituicoes($instSelecionados);

		$retorno = $this->_dados->salvar();

		return $retorno;
	}
	
	public function _obterArrayInstituicoes ()
	{
		$simus = array();
		$rs = Core::registro('db')->query('SELECT * FROM instituicoes ORDER BY i_nome ASC');
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				$simus[$row['i_id']] = $row['i_nome'];
			}
		}
		$rs->free();
		return $simus;
	}
}
?>