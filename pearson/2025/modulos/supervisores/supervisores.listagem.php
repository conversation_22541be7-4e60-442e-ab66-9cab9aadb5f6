<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');
Core::incluir('Instituicao', null, true);

class LSupervisores extends Listagem
{
	
	protected $_procuraSQL = null;
	
	public function prepararListagem ()
	{
		$this->_prepararBusca();
	
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormularioAcoes();


		$this->_iniciarRenderizacao(Modulo::HTML);
			include('supervisores.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_supervisores')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_supervisores')->termoProcurado != null && Core::modulo('procurar_supervisores')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_supervisores')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_supervisores')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}
		
			if ( Core::modulo('procurar_supervisores')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(u_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_supervisores')->letraSelecionada);
			}
		}
		
		// configura pra mostrar somente da instituição selecionada
		if(Core::registro('instituicao')->obterID()){
			$this->_procuraSQL[] = 'u_instituicao = '. Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID());
		}
		
		if ( is_array($this->_procuraSQL) ) {
			$this->_procuraSQL = ' WHERE '. implode(' AND ', $this->_procuraSQL);
		}
	}
	
	protected function _preObterDados ()
	{	
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT COUNT(0) AS total FROM supervisores 
			  LEFT JOIN usuarios ON usuarios.u_id = supervisores.s_usuario 
			  LEFT JOIN emails ON emails.e_id = usuarios.u_email  %s', $this->_procuraSQL) );
			  
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}
	
	protected function _obterDados ()
	{
		$rs = Core::registro('db')->query( sprintf(
			'SELECT * FROM supervisores 
			LEFT JOIN usuarios ON usuarios.u_id = supervisores.s_usuario 
			LEFT JOIN emails ON emails.e_id = usuarios.u_email 
			LEFT JOIN instituicoes ON usuarios.u_instituicao = instituicoes.i_id %s 
			ORDER BY %s %s 
			LIMIT %s',
			$this->_procuraSQL, $this->_ordenacao->ordenarPor, $this->_ordenacao->tipoOrdem, $this->_paginacao->paginador->obterLimitesSQL() ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$user = new UsuarioInstituido($row['u_id']);
				$user->carregar();

				$supervisor = new Supervisor($row['s_id']);
				$supervisor->carregar($user);
				
				$this->_dados[] = $supervisor;
			}
		}
		$rs->free();
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_supervisores'));

		Core::modulo('procurar_supervisores')->prepararProcura('form_procurar_supervisores', 'caixa_procurar_supervisores');
		Core::modulo('procurar_supervisores')->configurarTermo();
		Core::modulo('procurar_supervisores')->configurarOndeProcurar( array('u_nome' => 'Nome do supervisor',
																			'emails.e_endereco' => 'Login (E-mail) do supervisor') );
		Core::modulo('procurar_supervisores')->configurarAlfabeto();
		
		Core::modulo('procurar_supervisores')->carregarProcura();
		
		Core::modulo('procurar_supervisores')->prepararAtalho();
	}
	
	protected function _prepararOrdenacao ()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_supervisores'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_supervisores');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('u_nome' => 'Nome', 'emails.e_endereco' => 'Login (E-mail)'), 'u_nome' );
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();
		
		Core::modulo('ord_supervisores')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_supervisores')->carregarOrdenacao();
		Core::modulo('ord_supervisores')->prepararAtalho(null, 'caixa_ord_supervisores');
		
		if ( Core::modulo('procurar_supervisores')->procuraSolicitada() ) {
			Core::modulo('ord_supervisores')->anexarHTML( Core::modulo('procurar_supervisores')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao ()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_supervisores'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormularioAcoes ()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_supervisores','acao' => Gerenciador_URL::gerarLink('supervisores', 'listar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$v) {
			$ids[] = $v->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Remover', 'Tem certeza que deseja REMOVER todos os itens selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();

				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {
					if (is_array($ids)) {
						Core::modulo('solicitado')->aRemoverSupervisores($ids);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('supervisores', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				}
			}
			catch (Formulario_Exception $e)	{ }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('supervisores', $totais);
	}
		
}

?>