<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('ExportadorCVS', 'Exportador/');

class ExportadorSupervisores extends ExportadorCVS
{
	
	public function exportar ()
	{
		$this->_dados = array( array('Nome', 'E-mail','Instituicoes') );
	
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT * FROM supervisores 
			  LEFT JOIN usuarios ON usuarios.u_id = supervisores.s_usuario 
			  LEFT JOIN emails ON emails.e_id = usuarios.u_email 
			  WHERE u_instituicao = %s ORDER BY u_nome ASC',
				Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID()) ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$instituicoes = '';
				if(!empty($row['s_instituicoes'])){
					$json_instituicoes = json_decode($row['s_instituicoes'],true);
					foreach($json_instituicoes as &$iID){
						$inst = new Instituicao($iID);
						$inst->carregar();

						$instituicoes .= $inst->obterNome().'; ';
					}
				}

				$this->_dados[] = array($row['u_nome'], $row['e_endereco'], $instituicoes);
			}
		}
		$rs->free();
				
		if ( count($this->_dados) < 2 ) {
			Core::modulo('redirecionador')->fixarMensagem('Nenhum supervisor encontrado!', 'Exportando supervisores...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			throw new Core_Exception('Sem dados!');
		}
	}
		
}

?>