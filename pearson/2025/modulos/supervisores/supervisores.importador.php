<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('ImportadorCVS', 'Importador/');
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);

class FImportarSupervisores_Exception extends Formulario_Exception { }

class FImportarSupervisores extends Formulario
{
	const PASSO_1 = 0;
	const PASSO_2 = 1;
	const PASSO_3 = 2;
	const PASSO_4 = 3;
	
	protected $_correspondencias = array();
	protected $_correspondenciasSel = array();
	protected $_correspondenciasObrigatorias = array();
	protected $_dadosImportados = array();
	protected $feedback = array();

	public function __construct ($info = array())
	{
		parent::__construct($info);

		$this->_dados = array();
		$this->_correspondencias = array(   'NULA' => '',
											'nome' => 'Nome do supervisor',
											'email' => 'E-mail',
											'senha' => '<PERSON>ha de acesso'
									    );

		for ($i=1; $i < 100; $i++) { 
			$this->_correspondencias['escola'.$i] = "Escola ".$i;
		}
							
		$this->_correspondenciasObrigatorias = array('nome','email');
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;


		if ( $this->obterEstado() == self::PASSO_1 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'arquivo',
													'etiqueta' => 'Arquivo CSV (utf8)',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::ARQUIVO,
													'argumentos' => array(Campo::ARQUIVO_EXTENSAO => 'csv', Campo::TAM_MAX => 4194304),
													'html_tipo' => Campo::HTML_ARQUIVO,
													'html_tamanho' => 50,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_ordem' => $ordem++
								  )) );
								  
			$this->adicionarCampo( new Campo(array( 'nome' => 'cabecalho',
													'etiqueta' => 'Primeira linha é cabeçalho',
													'valor' => (!$this->foiEnviado() && !isset($_POST['post_anterior']) ? 1 : null),
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_valor' => '1',
													'html_tipo' => Campo::HTML_CAIXA_SELECAO,
													'html_ordem' => $ordem++
								  )) );

			$this->adicionarCampo( new Campo(array( 'nome' => 'separador',
													'etiqueta' => 'Separador de campos',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::TAM_IGUAL => 1),
													'html_tamanho_maximo' => 1,
													'html_tamanho' => 2,
													'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
													'html_tipo' => Campo::HTML_TEXTO,
													'html_valor' => ',',
													'html_ordem' => $ordem++
								  )) );
		}

		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'dados',
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['dados']
								  )) );
			
			if ( !isset($_POST['post_anterior']) )
				$_POST['post_anterior'] = '';

			$this->adicionarCampo( new Campo(array( 'nome' => 'post_anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => $_POST['post_anterior']
								  )) );
		}
		
		if ( $this->obterEstado() == self::PASSO_2) {
			for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
				$this->adicionarCampo( new Campo(array( 'nome' => 'correspondencia_'. $i,
														'etiqueta' => 'Correspondência',
														'requerimento' => Campo::REQUERIDO,
														'tipo' => Campo::TEXTO,
														'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_correspondencias)),
														'html_tipo' => Campo::HTML_MENU,
														'html_valor' => $this->_correspondencias,
														'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
														'html_ordem' => $ordem++
									  )) );
			}
		}
		
		if ( $this->obterEstado() == self::PASSO_3)
			$this->_gerarDadosImportados();

		$this->adicionarCampo( new Campo(array( 'nome' => 'passo',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->obterEstado()
							  )) );

		$textoEnviar = 'Próximo passo >';
		if ( $this->obterEstado() == self::PASSO_3)
			$textoEnviar = 'Confirmar importação';
			
		if ( $this->obterEstado() == self::PASSO_2 || $this->obterEstado() == self::PASSO_3 ) {		
			$this->adicionarCampo( new Campo(array( 'nome' => 'voltar',
													'etiqueta' => '< Passo anterior',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_BOTAO,
													'html_classe' => 'botao remover',
													'componente' => new JAlteradorDeFormulario('passo', ($this->obterEstado() == self::PASSO_3 ? self::PASSO_2 : self::PASSO_1), $this->_info['nome'], true, 'onclick="%s"')
								  )) );
		}
			
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $textoEnviar,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			if ( $this->obterEstado() == self::PASSO_1 ) {
				$arquivo = $this->_campos['arquivo']->obter('valor');
			
				if ( !count($arquivo) || !is_uploaded_file( $arquivo[0]['nome_temporario'] ) ) {
					$this->_adicionarErro('arquivo', 'arquivo inválido;');
					throw new FImportarSupervisores_Exception('Arquivo inválido!');
				}
			}

			if ( $this->obterEstado() == self::PASSO_2 ) {
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					$this->_correspondenciasSel[$i] = $this->_campos['correspondencia_'. $i]->obter('valor');
				}

				$correspondenciasDuplicadas = array();
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' ) {
						if ( !in_array($cID, $correspondenciasDuplicadas) ) {
							$correspondenciasDuplicadas[] = $cID;
						} else {
							$this->_adicionarErro('correspondencia_'. $i, 'só pode haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
							throw new FImportarSupervisores_Exception('Multiplas correspondências encontradas!');
						}
					}
				}

				foreach ($this->_correspondenciasObrigatorias as $cID) {
					if ( !in_array($cID, $this->_correspondenciasSel) ) {
						$this->_adicionarErro(null, 'Deve haver uma correspondêcia para o item <i>'. @$this->_correspondencias[$cID] .'</i>;');
						throw new FImportarSupervisores_Exception('Correspondência obrigatória não selecionada!');
					}
				}
				
				$numCorrespondenciasSel = 0;
				foreach ($this->_correspondenciasSel as $i => $cID) {
					if ( $cID != 'NULA' )
						$numCorrespondenciasSel++;
				}
				
				if ( $numCorrespondenciasSel == 0 ) {
					$this->_adicionarErro(null, 'Nenhuma correspondência foi feita;');
					throw new FImportarSupervisores_Exception('Sem correspondências!');
				}
			}
		}
		catch (Formulario_Exception $e)
		{
			throw new FImportarSupervisores_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ( $this->obterEstado() == self::PASSO_2 ) {
			$this->_executarPasso_2();
		} else if ( $this->obterEstado() == self::PASSO_3 ) {
			$importados = $this->_executarPasso_3();
			//Core::modulo('redirecionador')->fixarMensagem( $importados .' de '. count($this->_dadosImportados) .' supervisores importados com sucesso!', 'Importando supervisores...');
			//Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
		} else {
			$this->_executarPasso_1();
		}
	}
	
	public function &obterDadosImportados () {
		return $this->_dadosImportados;
	}
	
	public function &obterFeedback () {
		return $this->feedback;
	}

	protected function _carregar ()
	{
		$passo = null;

		if ( !$this->foiEnviado() && isset($_GET['passo']) )
			$passo = $_GET['passo'];
		else
			$passo = (isset($_POST['passo']) ? $_POST['passo'] : null );

		$postAnterior = array();
		if ( isset($_POST['post_anterior']) )
			$postAnterior = unserialize(base64_decode($_POST['post_anterior']));

		if ( !is_array($postAnterior) )
			$postAnterior = array();

		if ( ($passo == self::PASSO_2 || $passo == self::PASSO_3) && (!isset($_POST['dados'])) )
			$passo = null;

		if ( $passo == self::PASSO_2 || $passo == self::PASSO_3 ) {
			$this->_dados = unserialize(base64_decode($_POST['dados']));
			
			$_POST['cabecalho'] = isset($postAnterior['cabecalho']) ? $postAnterior['cabecalho'] : 0;

			if ( !is_array($this->_dados) || count($this->_dados) < 1 )
				$passo = null;
				
			if ( $passo == self::PASSO_3 ) {
				$this->_correspondenciasSel = array();
				
				for ($i = 0; $i < count(@$this->_dados[0]); $i++) {
					if ( isset($postAnterior['correspondencia_'. $i]) )
						$this->_correspondenciasSel[$i] = $postAnterior['correspondencia_'. $i];
				}
	
				if ( !count($this->_correspondenciasSel) )
					$passo = null;
			}
		}
		
		if ( $passo == null )
			$passo = self::PASSO_1;
			
		if ( ($passo == self::PASSO_1 || $passo == self::PASSO_2) && count($postAnterior) ) {
			if ( (@$postAnterior['passo'] == self::PASSO_1 && $passo == self::PASSO_1) || (@$postAnterior['passo'] == self::PASSO_2 && $passo == self::PASSO_2) ) {
				$this->_entrada = $postAnterior;
				
				if ( isset($postAnterior[$this->obterNomeChecadorDeEnvio()]) )
					$this->_campos[$this->obterNomeChecadorDeEnvio()]->fixar('valor', 0);
			}
		}
		
		$this->fixarEstado($passo);
	}

	protected function _executarPasso_1 ()
	{
		$importador = new ImportadorCVS();
		$importador->temCabecalho( $this->_campos['cabecalho']->obter('valor') == 1 );
		$importador->fixarSeparador( $this->_campos['separador']->obter('valor') );

		$arquivo = $this->_campos['arquivo']->obter('valor');

		if ( $importador->importar( $arquivo[0]['nome_temporario'] ) ) {
			$dados = array( 'passo' => self::PASSO_2,
							'dados' => chunk_split(base64_encode(serialize($importador->obterDados()))),
							'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
						  );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Gerenciador_URL::autoFixarIndexCompleto(true);
			Redirecionador::finalizarAoRedirecionar(true);
			Redirecionador::post( Gerenciador_URL::gerarLink('supervisores', 'importar'), $dados );
		} else {
			$this->_adicionarErro('arquivo', 'o arquivo não contém dados;');
			throw new FImportarSupervisores_Exception('Arquivo sem dados!');
		}
	}

	protected function _executarPasso_2 ()
	{
		unset($this->_entrada['dados']);
		$this->_entrada['cabecalho'] = $_POST['cabecalho'];

		$dados = array( 'passo' => self::PASSO_3,
						'dados' => $_POST['dados'],
						'post_anterior' => chunk_split(base64_encode(serialize( $this->_entrada )))
					  );

		Gerenciador_URL::habilitarAtualiacaoReferencia(false);
		Gerenciador_URL::autoFixarIndexCompleto(true);
		Redirecionador::finalizarAoRedirecionar(true);
		Redirecionador::post( Gerenciador_URL::gerarLink('supervisores', 'importar'), $dados );
	}

	protected function _executarPasso_3 ()
	{
		$return = array();
		$feedback = array();

		$importados = 0;	
		foreach ($this->_dadosImportados as $dados)	{	
			$primeiroAcesso = false;
			$senhaViaEmail = false;

			//checa nome
			$nomeExiste = 0;
			$uExiste = UsuarioInstituido::obterUsuarioPeloNome($dados['nome']);
			if($uExiste !== null){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." já está cadastrado.";// Registro pulado.";
				$nomeExiste = 1;
				//continue;
			}

			// email e senha
			$email = new Email(null);
			$senha = randomico(true, 16);
			if(array_key_exists('email', $dados)){
				$id = Email::obterIDPeloEndereco( $dados['email'] );	
				if ($id !== false) {
					if($nomeExiste){	
						$feedback[] = "Registro da linha de nome ".$dados['nome']." já tem este email ".$dados['email']." e nome cadastrado. Registro pulado.";
						continue;
					}

					$feedback[] = "Registro da linha de nome ".$dados['nome']." já tem o email ".$dados['email']." cadastrado. Ativando recurso de Primeiro Acesso.";
					$primeiroAcesso = true;	
				}
				else{
					$email = Email::obterNovoEmail($dados['email']);

					if(array_key_exists('senha', $dados)){
						$senha = $dados['senha'];
						
						$email->fixarEstadoDaConfirmacao(1);
						$email->fixarChaveConfirmacao(null);
						$email->fixarDataUltimoEnvio(null);
						$email->salvar();
					}
					else{	
						$senhaViaEmail = true;
					}
				}
			}

			// usuario
			$usuario = UsuarioInstituido::obterNovoUsuarioInstituido($email, $senha, array(6));	
			$usuario->fixarEndereco(new Endereco(null));	
			$usuario->fixarNome($dados['nome']);
			$usuario->fixarEmail($email);
			$usuario->fixarSenha($senha);

			if ($usuario->obterID() == null){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." falha ao registrar usuario.";
				continue;
			}

			// supervisor
			$novoSupervisor = Supervisor::obterNovoSupervisor($usuario);

			if(array_key_exists('escola', $dados)){
				$escolasPossiveis = Instituicao::obterArrayInstituicoesParaFormulario();

				$tmp_escolas = array();
				foreach ($dados['escola'] as $dek => $dev) {
					if(array_key_exists($dev, $escolasPossiveis)){
						$tmp_escolas[] = $dev;
					}
				}

				if(count($tmp_escolas)>0){
					$instSelecionados = json_encode($tmp_escolas);
					$novoSupervisor->fixarInstituicoes($instSelecionados);
					$instTmp = new Instituicao($tmp_escolas[0]);
					$usuario->fixarInstituicao($instTmp);
				}
			}			

			if (!$usuario->salvar()) {
				$feedback[] = "Registro da linha de nome ".$dados['nome']." falha ao salvar registro de usuario.";
				continue;
			}

			if ($novoSupervisor->obterID() != null && $novoSupervisor->salvar()){
				$feedback[] = "Registro da linha de nome ".$dados['nome']." cadastrado com sucesso.";
				$importados++;
			}
			else{
				continue;
			}

			if ($primeiroAcesso && !PrimeiroAcesso::usuarioTemPrimeiroAcesso($usuario)){
				PrimeiroAcesso::obterNovoPrimeiroAcesso($usuario);
				$feedback[] = "Registro da linha de nome ".$dados['nome']." está com recurso de Primeiro Acesso.";
			}

			if (!$primeiroAcesso) {
				if ($senhaViaEmail){
					@RecuperadorSenhasUsuario::obterNovoRecuperadorSenhasUsuario($usuario);
					$feedback[] = "Registro da linha de nome ".$dados['nome']." a senha será enviada por email.";
				}
					
				//@DespachadorEmailsUsuario::enviarEmailValidacaoEmail( $usuario->obterEmail(), $senhaViaEmail );
			}
		}

		$return['importados'] = $importados;
		$return['feedback'] = $feedback;
		$this->feedback = $return;

		echo"<pre>";print_r($return);echo"</pre>";exit;

		//return $return;
	}

	protected function _gerarDadosImportados ()
	{
		$i = @$_POST['cabecalho'] == 1 ? 1 : 0;	
		$obj = array();

		$emailsImportados = array();
		$escolas = Instituicao::obterArrayInstituicoesParaFormulario();

		for ( $i; $i < count($this->_dados); $i++ )	{	
			$instituicoes = array();	
			$instit_nomes = array();	
			foreach ($this->_correspondenciasSel as $j => $cID) {				
				$valor = isset($this->_dados[$i][$j]) ? $this->_dados[$i][$j] : null;

				switch ($cID) {
					case 'nome':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) >= 2 && strlen($valor) <= 255 )
							$obj[$cID] = $valor;
						break;
					case 'email':
						if ( strlen($valor) > 0 && Filtrador::email($valor) && !in_array($valor, $emailsImportados) )
							$obj[$cID] = $valor;
							$emailsImportados[] = $valor;	
						break;
					case 'senha':
						if ( strlen($valor) >= 4 && strlen($valor) <= 16 )
							$obj[$cID] = $valor;
						break;
					case 'escola':
						$valor = ImportadorCVS::limparCharEspeciais($valor);
						if ( strlen($valor) >= 2 && strlen($valor) <= 255 )
							$obj[$cID] = $valor;
						break;
					default:
						if(stripos($cID, 'escola') !== false){
							$cs = array_search($valor, $escolas);
							if($cs !== false){
								$instituicoes[] = $cs;
								$instit_nomes[] = $valor;
							}
						}
						break;
				}
			}

			$obj['escola'] = $instituicoes;
			$obj['escola_nomes'] = $instit_nomes;
			$this->_dadosImportados[] = $obj;
		}
	}
}

?>