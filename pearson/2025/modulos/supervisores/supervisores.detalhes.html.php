<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio"><strong><?= $supervisor->obterUsuario()->obterNome(); ?></strong></div>

<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('supervisores', 'detalhar', array('id' => $supervisor->obterID())); ?>">Informações do supervisor </a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('supervisores', 'detalhar', array('id' => $supervisor->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_MAIS_INFO)); ?>">Mais informa&ccedil;&otilde;es</a></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<?
if ($this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO)) {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

		<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		  <tr>
			<td><strong>Aulas</strong></td>
		    <td align="right"><input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('supervisores', 'editar', array('id' => $supervisor->obterID())); ?>'" value="Adicionar aulas" class="botao editar"></td>
		  </tr>
	  	</table>

<?
	$aulas = $supervisor->obterAulas();
	if (count($aulas)) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
		  <td width="15%" class="lp_ColHeader">Turma</td>
		  <td width="20%" class="lp_ColHeader">S&eacute;rie</td>
		  <td class="lp_ColHeader">Disciplina ministrada</td>
		  <td class="lp_ColHeader">Simulado</td>
		</tr>
<?
		foreach ($aulas as &$aula) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
      <td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $aula->obterTurma()->obterID())); ?>"><?= $aula->obterTurma()->obterNome(); ?></a></td>
      <td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('series', 'detalhar', array('id' => $aula->obterTurma()->obterSerie()->obterID())); ?>"><?= $aula->obterTurma()->obterSerie()->obterNome(); ?></a></td>
	  <td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('disciplinas', 'detalhar', array('id' => $aula->obterDisciplina()->obterID())); ?>"><?= $aula->obterDisciplina()->obterNome(); ?></a></td>
	  <td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('simulados', 'detalhar', array('id' => $aula->obterSimulado()->obterID())); ?>"><?= $aula->obterSimulado()->obterNome(); ?></a></td>
    </tr>
<?
		}
?>
	</table>
<?
	} else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
		  <td><em>Sem aulas </em></td>
	    </tr>
	</table>
<?
	}
?>

	<div class="vd_BlocoEspacador">
<?
	Core::carregarModulo(array('nome' => '_componente_usuario', 'classe' => 'MComponenteUsuario', 'guardar_como' => '_componente_usuario'));
	Core::modulo('_componente_usuario')->mostrarDetalhes($supervisor->obterUsuario(), Gerenciador_URL::gerarLink('supervisores', 'detalhar', array('id' => $supervisor->obterID())), 'mais_info');
	
	echo Core::modulo('_componente_usuario')->obterSaida();
?>
	</div>

	</td>
  </tr>
</table>
<?
} else {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

	<div class="vd_BlocoBotoes">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
	  <tr>
		<td><input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('supervisores', 'editar', array('id' => $supervisor->obterID())); ?>'" value="Editar" class="botao editar"></td>
		<td align="right"><input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('supervisores', 'remover', array('id' => $supervisor->obterID())); ?>'; }" value="Remover" class="botao remover"></td>
	  </tr>
	</table>
	</div>

<?
Core::carregarModulo(array('nome' => '_componente_usuario', 'classe' => 'MComponenteUsuario', 'guardar_como' => '_componente_usuario'));
Core::modulo('_componente_usuario')->mostrarDetalhes($supervisor->obterUsuario(), Gerenciador_URL::gerarLink('supervisores', 'editar', array('id' => $supervisor->obterID())));

echo Core::modulo('_componente_usuario')->obterSaida();
?>
	
	</td>
  </tr>
</table>
<?
}
?>