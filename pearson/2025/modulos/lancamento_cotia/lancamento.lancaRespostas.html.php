<STYLE type="text/css">
	p.p {
		border-bottom: 1px solid #ccc;
		margin:10px 0 10px 0;
		text-align:left;
	}

	.tbl_alunos tr th{
		border: 1px solid #ccc;
		text-align:center !important;
	}

	.tbl_alunos tr td{
		border: 1px solid #ccc;
		text-align:center !important;
	}

	#avisos {
		background: none repeat scroll 0 0 #038c2e;
		border-radius: 5px 5px 5px 5px;
		padding: 10px;
		width: 60%;
		margin:20px;
		color: #FFFFFF;
		font-size: 20px;
	}

	#bnt-load, #bnt-lancamento{
		margin:0 50px;
	}

	.bolinhas {
		width:20px;
	}

	.ausente-aluno, .teste-um, .qtudo, .lixeira{
		width:40px;
	}

	.nome-aluno{
		width: 320px;
	}

	img[src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
ajax_falha.gif"]{
		height:20px;
	}

	.even { background-color:#FFF; }
	.odd { background-color:#eee; }
	.odd-fh { background-color:#eee; }

	#bnt-lancamento:hover {
		background-color: green !important;
		cursor: pointer;
		-webkit-transition: background-color 0.7s ease;
		-moz-transition: background-color 0.7s ease;
		transition: background-color 0.7s ease;
	}

	#btn-back {
		width: 200px;
		height: 55px;
		font-size:12px;
		font-weight: bold;
		border-radius: 5px 5px 5px 5px;
		border: 1px solid grey;
		cursor: pointer;

	}

	.switch {
		position: absolute;
		margin-left: -9999px;
		visibility: hidden;
	}

	.switch + label {
		display: block;
		position: relative;
		cursor: pointer;
		outline: none;
		user-select: none;

	}

	.switch--shadow + label {
		padding: 2px;
		width: 60px;
		height: 30px;
		background-color: #dddddd;
		border-radius: 60px;
	}

	.switch--shadow + label:before {
		display: block;
		position: absolute;
		top: 1px;
		left: 1px;
		bottom: 1px;
		content: '';
	}
	.switch--shadow + label:after {
		display: block;
		position: absolute;
		top: 1px;
		left: 1px;
		bottom: 1px;
	}
	.switch--shadow + label:before {
		right: 1px;
		background-color: #e18c8c;
		border-radius: 60px;
		transition: all 0.4s;
	}
	.switch--shadow + label:after {
		width: 32px;
		background-color: #ddd;
		border-radius: 100%;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
		transition: all 0.4s;
		content: '';
	}

	.switch--shadow:checked + label:before {
		background-color: #8ce196;
	}
	.switch--shadow:checked + label:after {
		transform: translateX(30px);
	}
</STYLE>

<script type="text/javascript">
    var JAVA_URL_BASE = "<?= Core::diretiva('JAVA_URL_BASE'); ?>";     
</script>
<!-- Font Awesome ( Icones / fontes exportadas) -->
<script src="https://kit.fontawesome.com/544f3b41ce.js" crossorigin="anonymous"></script>
<link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>
<!--modal box novo aluno e anular-->
<script src="modulos/lancamento_cotia/js/jquery.modal_box_novo_aluno.js"></script>
<script src="modulos/lancamento_cotia/js/jquery.modal_box_anular_inscricao_aluno.js"></script>
<script src="modulos/lancamento_cotia/js/jquery.modal_box_tutorial.js"></script>

<script type="text/javascript">
	jQuery(document).ready(function() {
	 	if(navigator.appName=='Microsoft Internet Explorer'){
			msg = 'Seu navegador n&atilde;o oferece suporte para a realiza&ccedil;&atilde;o do simulado.<br>Por favor utilize algum dos navegadores abaixo.<br><ul><li style="list-style-type:none;"><a target="_blank" href="https://www.google.com/chrome?hl=pt-br"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
chrome20px.png" style="border:0;"> - Google Chrome</a></li><li style="list-style-type:none;"><a target="_blank" href="http://br.mozdev.org/firefox/download/"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
firefox20px.png" style="border:0;"> - Mozilla Firefox</a></li></ul>';
			jQuery('#form_lancamento_alunos').html('<div id="avisos"></div>');
			jQuery('#avisos').attr('style','border:3px solid #EE1111;');
			jQuery('#avisos').html(msg).fadeIn('fast');

		    jQuery("html, body").animate({ scrollTop: 0 }, "slow");
		    jQuery("html, body").animate({ scrollLeft: 0 }, "slow");
		}

		jQuery('#bnt-lancamento').click(function(){
		    openModalConfirmation("Deseja efetivar o lan&ccedil;amento agora?","efetuarLancamento()","closeModalConfirmation()");
		});
	});

	window.onbeforeunload = function(){
		return "Os dados n&atilde;o salvos ser&atilde;o perdidos. Deseja sair dessa p&aacute;gina?";
	}
</script>

<center>
	<form action="./?m=lancamento_cotia&a=lancamento_cotia" method="post" id="form_lancamento_alunos">
		<input type="hidden" id="id_turma_selecionada_lancamento" name="id_turma_selecionada_lancamento" value="<?php echo$_turma; ?>">
		<input type="hidden" id="id_simulado_selecionada_lancamento" name="id_simulado_selecionada_lancamento" value="<?php echo$_simulado; ?>">
		<input type="hidden" id="lancamento_sendo_lancado" name="lancamento_sendo_lancado" value="1" />
		<h2>FOLHA DE RESPOSTAS</h2>

		<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp" style="margin-bottom: 10px; background-color: #F8FDB6;">
			<th colspan="4" class="descricao" style="text-align: left; background-color: #F8FDB6; color: #545454; font-weight: normal; font-size: 14px; padding: 10px;">
				<strong>Professores, aten&ccedil;&atilde;o ao preenchimento correto das notas em decimais.</strong>
			</th>
		</table>

		<table width="90%" border="0">
			<tr>
				<td width="75"><p class="p"><strong style="font-size:18px;">Escola:</strong>
					<label style="font-size:18px; padding-left: 10px">
						<?php echo $intituicao_nome; ?>
					</label>
					</p>
				</td>
				<td style="width: 30%;">
					<p class="p"><strong style="font-size:18px;">Ano/Turma:</strong>
						<label style="font-size:18px; padding-left: 10px">
							<?php echo $ext_serie;?> -
						</label>
						<label style="font-size:18px;">
							<?php echo $turma_nome; ?>
						</label>
					</p>
				</td>
			</tr>
			<tr>
				<td COLSPAN=2>
					<p class="p"><strong style="font-size:18px;">Avalia&ccedil;&atilde;o:</strong>
						<label style="font-size:18px; padding-left: 10px">				
							<?php echo $simuNome; ?>
						</label>
					</p>
				</td>
			</tr>
			<tr>
				<td COLSPAN=2>
					<input type="hidden" id="professor" value="<?php echo $idProf; ?>"/>
				</td>
			</tr>
			<tr style="display:block;">
				<td style="padding: 2px; width: 75%;">
					<div id="inscrever_anular_aluno">
						<div class="grid-container-aluno">
							<div class="inscrever-aluno play_tutorial">
								<span>
									<i class="fa-solid fa-play" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											TUTORIAL
										</label>
									</i>
								</span>							
							</div>
							<div></div>
							<div class="inscrever-aluno novo_aluno">
								<span>
									<i class="fa-solid fa-user-plus" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											INSCREVER ALUNO
										</label>
									</i>
								</span>							
							</div>
							<div></div> 
							<div class="inscrever-aluno delete_aluno">
								<span>
									<i class="fa-solid fa-ban" style="font-size:20px;"> <!-- Icone font-awesome -->
										<label style="font-family: Roboto; font-size:12px; padding-left: 8px; cursor:pointer;">
											ANULAR INSCRI&Ccedil;&Atilde;O
										</label>
									</i>
								</span>
							</div>
						</div>
					</div>
				</td>
				<td style="">
					<div style="display: flex; aling-items: center; float: right;">
						<div class="switch__container" style="display: flex; aling-items: center;">
							<input id="switch-shadow" name="alunosPorOrdemAlfabetica" class="switch switch--shadow" type="checkbox" />
							<label for="switch-shadow"></label>
						</div>
						<div style="display: flex; aling-items: center;">
							<label style="font-size: 14px; padding: 8px">Ordenar nomes em ordem alfab&eacute;tica</label>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<?php
						if($tipo_rel == 'relatorios_campos'){
							echo'<div style="background-color: #eee;border-radius: 10px;border: 2px solid #333;">
								<ul>
								 	<li style="font-size: 14px;"><strong>Pr&eacute;-sil&aacute;bico</strong> deve ser preenchido com <strong>1,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Sil&aacute;bico</strong> deve ser preenchido com <strong>2,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Sil&aacute;bico convencional</strong> deve ser preenchido com <strong>3,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Sil&aacute;bico alfab&eacute;tico</strong> deve ser preenchido com <strong>4,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Alfab&eacute;tico</strong> deve ser preenchido com <strong>5,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Produtor de mem&oacute;ria</strong> deve ser preenchido com <strong>6,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Produtor de autoria recente</strong> deve ser preenchido com <strong>7,00</strong>.</li>
									<li style="font-size: 14px;"><strong>Produtor de autoria</strong> deve ser preenchido com <strong>8,00</strong>.</li>
								</ul>
							</div>';
						}
					?>
				</td>
			</tr>
			<tr>
				<td COLSPAN=2>
					<TABLE CLASS="tbl_alunos" cellspacing="0" CELLPADDING="1" WIDTH="100%" style="border: 1px solid #ccc;">
						<TR class="odd" style="overflow-x: scroll;">
							<TH  style="width: 100%; border-bottom: 1px solid #ccc; font-size: 15px;" colspan="100" ROWSPAN="2">QUEST&Otilde;ES</TH>
						</TR>
						<TR class="odd" id="questions_header"></TR>
						<TR class="odd" id="nome-coluna"></TR>
					</TABLE>
				</td>
			</tr>
		</table>
		<br/><br/>

		<div <?php if(!isset($aviso)) echo'style="display:none; "'; ?>id="avisos">
			<?php if(isset($aviso)) echo $aviso; ?>
		</div>
		<br/>
		<br/>

		<img id="waiting" src="modulos/lancamento_cotia/loading_orange.gif" style="width: 100px;"/>

		<br>
		<br>
		<br>
		<span style="font-size: 40px; color: green"><i class="fa-solid fa-memo-circle-check"></i></span>
		<input type="button" id="bnt-lancamento" value="CONFIRMAR LAN&Ccedil;AMENTO!" style="font-family: Roboto; background-color: #c76502; box-shadow: 0 8px 12px 0 rgba(0,0,0,0.24), 0 4px 7px 0 rgba(0,0,0,0.19);border: 1px solid rgba(103, 103, 103, 0.8); border-radius: 5px 5px 5px 5px; color: #FFFFFF; font-size: 17px; height: 55px; width: 300px; font-weight: bold; float: center"/>
		<input type="button" id="btn-back" value="TROCAR DE TURMA" style="font-family: Roboto;" onClick="javascript:window.location='./?m=lancamento_cotia&a=lancamento_cotia&r_referencia=1';"/>

		<table width="90%" border="0">
			<tr>
				<td>
					<?php
						if($tipo_rel == 'relatorios_campos'){
							echo'';
						}
					?>
				</td>
			</tr>
		</table>

		<div id="incluir_aluno" class="modal-box">
			<header> <a href="javascript:closeModalIncluirAluno();" class="js-modal-close close">&times;</a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/><p style="text-align:left; font-size:18px; opacity:70%"> - INSCREVER ALUNO</p>
			</header>
			<div class="modal-body-novo-aluno">
					<label class="modal-font-novo-aluno" style="padding-bottom:15px"></label>
					<p style="margin-left: 30px;font-weight: bold;font-size: 14px">RA (Somente os 9 n&uacute;meros, sem o digito)</p>
					<p>
					<div class="mtse">
						<div class="zerosra" style="display: inline-block;">
							<div class="mtse2" style="width: 50px; display: inline-block; text-align: right">000</div>

							<input class="mtse2" id="matricula_incluir_aluno" type="text" maxlength="9" style="font-size: 14px; font-family: Verdana, Geneva, Tahoma, sans-serif; text-align:left;color:rgb(122, 122, 122)"/>
						</div>	    
					</div>
					
					<p>
					<label class="labelalunoincluir" style="font-weight: bold;font-size: 12px; display: none;">Nome do Aluno(a)</label>
					<a class="nomealunoincluir" style="display: none;">
					<p>
						<input id="nome_incluir_aluno" placeholder="Digite o nome do aluno" class="incluiraluno-input" maxlength="40" />
						<select id="nome_incluir_aluno_existente" class="selecione-input" style="display:inline-block;background-color: #07003B; color: white; font-size: 14px; width: 100%;">		</select>			    
					</a>

			</div>
			
			<footer>
				<a href="javascript:incluirAluno();" class="btn-ok btn-small">&#10004; &nbsp; Incluir</a>
				<a href="javascript:closeModalIncluirAluno();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Cancelar</a>
			</footer>
		</div>

		<div id="anular_aluno" class="modal-box">
			<header> <a href="javascript:closeModalAnularInscricaoAluno();" class="js-modal-close close">&times;</a>
			<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/><p style="text-align:left; font-size:18px; opacity:70%"> - ANULAR INSCRI&Ccedil;&Atilde;O</p>

			</header>
			<div class="modal-body-anular-aluno">
				<p class="modal-font-anular-aluno"></p>
				<p>
					<label style="font-weight: bold;font-size: 14px;">Selecione o Aluno:</label>
					<p>
					<select id="anular_aluno_select" class="selecione-input" style="width: 100%; background-color: #07003B; color: white;">
					</select>


			</div>
			<footer>
				<a href="javascript:anularnscricao();" class="btn-ok btn-small">&#10004; &nbsp;Anular Inscri&ccedil;&atilde;o</a>
				<a href="javascript:closeModalAnularInscricaoAluno();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Cancelar</a>
			</footer>
		</div>

		<div id="play_tutorial" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalTutorial();" class="js-modal-close close">&times;</a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - TUTORIAL</p>
			</header>
			<div class="modal-body-novo-aluno">
				<STYLE type="text/css">
					/* * {box-sizing:border-box} */

					/* Slideshow container */
					.slideshow-container {
					  border: solid #aaa 1px;
					  max-width: 800px;
					  position: relative;
					  margin: auto;
					  width: 800px;
					  height: 450px;
					}

					/* Hide the images by default */
					.mySlides {
					  display: none;
					}

					/* Next & previous buttons */
					.prev, .next {
					  cursor: pointer;
					  position: absolute;
					  top: 50%;
					  width: auto;
					  margin-top: -22px;
					  padding: 10px;
					  color: white;
					  font-weight: bold;
					  font-size: 18px;
					  transition: 0.6s ease;
					  border-radius: 0 3px 3px 0;
					  user-select: none;
					  background-color: rgba(0,0,0,0.1);
					}

					/* Position the "next button" to the right */
					.prev {
					  left: 0;
					  border-radius: 3px 0 0 3px;
					}

					/* Position the "next button" to the right */
					.next {
					  right: 0;
					  border-radius: 3px 0 0 3px;
					}

					/* On hover, add a black background color with a little bit see-through */
					.prev:hover, .next:hover {
					  text-decoration: none;
					  background-color: rgba(0,0,0,0.5);
					}

					/* Caption text */
					.text {
					  color: #f2f2f2;
					  font-size: 15px;
					  padding: 8px 12px;
					  position: absolute;
					  bottom: 8px;
					  width: 100%;
					  text-align: center;
					}

					/* Number text (1/3 etc) */
					.numbertext {
					  color: #aaa;
					  font-size: 12px;
					  padding: 8px 12px;
					  position: absolute;
					  top: 0;
					  right: 0;
					}

					/* The dots/bullets/indicators */
					.dot {
					  cursor: pointer;
					  height: 10px;
					  width: 10px;
					  margin: 0 2px;
					  background-color: #bbb;
					  border-radius: 50%;
					  display: inline-block;
					  transition: background-color 0.6s ease;
					}

					.active, .dot:hover {
					  background-color: #717171;
					}

					/* Fading animation */
					.fade {
					  -webkit-animation-name: fade;
					  -webkit-animation-duration: 1.5s;
					  animation-name: fade;
					  animation-duration: 1.5s;
					}

					@-webkit-keyframes fade {
					  from {opacity: .4}
					  to {opacity: 1}
					}

					@keyframes fade {
					  from {opacity: .4}
					  to {opacity: 1}
					}
				</STYLE>

				<div class="slideshow-container">

				  <!-- Full-width images with number and caption text -->
				  <div class="mySlides fade">
				    <div class="numbertext">1 / 13</div>
				    <img src="modulos/principal/slides/1.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">2 / 13</div>
				    <img src="modulos/principal/slides/2.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">3 / 13</div>
				    <img src="modulos/principal/slides/3.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">4 / 13</div>
				    <img src="modulos/principal/slides/4.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">5 / 13</div>
				    <img src="modulos/principal/slides/5.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">6 / 13</div>
				    <img src="modulos/principal/slides/6.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">7 / 13</div>
				    <img src="modulos/principal/slides/7.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">8 / 13</div>
				    <img src="modulos/principal/slides/8.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">9 / 13</div>
				    <img src="modulos/principal/slides/9.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">10 / 13</div>
				    <img src="modulos/principal/slides/10.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">11 / 13</div>
				    <img src="modulos/principal/slides/11.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">12 / 13</div>
				    <img src="modulos/principal/slides/12.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <div class="mySlides fade">
				    <div class="numbertext">13 / 13</div>
				    <img src="modulos/principal/slides/13.png" style="width:800px; height:450px;">
				    <!-- <div class="text">Caption Text</div> -->
				  </div>

				  <!-- Next and previous buttons -->
				  <a class="prev" onclick="plusSlides(-1)">&#10094;</a>
				  <a class="next" onclick="plusSlides(1)">&#10095;</a>
				</div>
				<br>

				<!-- The dots/circles -->
				<div style="text-align:center">
				  <span class="dot" onclick="currentSlide(1)"></span>
				  <span class="dot" onclick="currentSlide(2)"></span>
				  <span class="dot" onclick="currentSlide(3)"></span>
				  <span class="dot" onclick="currentSlide(4)"></span>
				  <span class="dot" onclick="currentSlide(5)"></span>
				  <span class="dot" onclick="currentSlide(6)"></span>
				  <span class="dot" onclick="currentSlide(7)"></span>
				  <span class="dot" onclick="currentSlide(8)"></span>
				  <span class="dot" onclick="currentSlide(9)"></span>
				  <span class="dot" onclick="currentSlide(10)"></span>
				  <span class="dot" onclick="currentSlide(11)"></span>
				  <span class="dot" onclick="currentSlide(12)"></span>
				  <span class="dot" onclick="currentSlide(13)"></span>
				</div> 

				<script type="text/javascript">
					var slideIndex = 1;
					showSlides(slideIndex);

					// Next/previous controls
					function plusSlides(n) {
					  showSlides(slideIndex += n);
					}

					// Thumbnail image controls
					function currentSlide(n) {
					  showSlides(slideIndex = n);
					}

					function showSlides(n) {
					  var i;
					  var slides = document.getElementsByClassName("mySlides");
					  var dots = document.getElementsByClassName("dot");
					  if (n > slides.length) {slideIndex = 1}
					  if (n < 1) {slideIndex = slides.length}
					  for (i = 0; i < slides.length; i++) {
					      slides[i].style.display = "none";
					  }
					  for (i = 0; i < dots.length; i++) {
					      dots[i].className = dots[i].className.replace(" active", "");
					  }
					  slides[slideIndex-1].style.display = "block";
					  dots[slideIndex-1].className += " active";
					} 
				</script>
			</div>
			
			<footer>
				<a href="javascript:closeModalTutorial();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

		<script src="modulos/lancamento_cotia/js/jquery.lancamento.js"></script>
		<script src="includes/JavaScript/iMask/jquery.mask.js"></script>
		<script src="includes/JavaScript/iMask/jquery.maskMoney.js"></script>
	</form>
</center>