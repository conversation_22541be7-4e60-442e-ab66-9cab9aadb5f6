<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$tabela['titulo'] = 'Alunos por nível de alfabetização nas escolas da rede';//.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio())
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th><?= $this->obterBotaoAlterador('nome', false, 'Escola') ;?></th><? } ?>
		<th width="5%" style="padding-left: 7px; padding-right: 7px;" nowrap="nowrap"><?= $this->obterBotaoAlterador('nivel', false, 'Nível') ;?> </th>
		<th width="60%" nowrap="nowrap">
			<? //<div style="width: 20%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;; border-right: 1px solid black;">0 - 20%</div><div style="width: 20%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;; border-right: 1px solid black;">21 - 40%</div><div style="width: 20%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;; border-right: 1px solid black;">41 - 60%</div><div style="width: 20%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;; border-right: 1px solid black;">61 - 80%</div><div style="width: 20%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;;">81 - 100%</div> ?>
		</th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td nowrap="nowrap" <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>><?= @$d['nome']; ?> </td><? } ?>
 
<?
 	if ($this->_colunas['rendimento']) {
		$cor = ProvinhaBrasil::$NIVEL_5_COR;
		$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
 
 		if ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_1) {
			$cor = ProvinhaBrasil::$NIVEL_1_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_1_NOME;
 		} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_2) {
			$cor = ProvinhaBrasil::$NIVEL_2_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_2_NOME;
 		} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_3) {
			$cor = ProvinhaBrasil::$NIVEL_3_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_3_NOME;
 		} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_4) {
			$cor = ProvinhaBrasil::$NIVEL_4_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_4_NOME;
 		} else {
			$cor = ProvinhaBrasil::$NIVEL_5_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
 		}
 
		echo '<td nowrap="nowrap" style="text-align: center; color: '. ProvinhaBrasil::COR_TEXTO .'; background-color: '. $cor .'"> '. $nivel .' </td>';
 	}
?>

	<td style="border-bottom-width: <?= $i+1 >= count($dados_mostrar) ? 1 : 0; ?>px; border-top-width: 0px; padding: 0px;">
<?
	if ( isset($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_1 ]) && $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_1 ] > 0 )
		printf('<div style="background-color: %s; color: %s; width: %s%%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;">%s</div>',
			ProvinhaBrasil::$NIVEL_1_COR,
			ProvinhaBrasil::COR_TEXTO,
			$d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_1 ],
			($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_1 ] > 5 ? $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_1 ] . '%' : '&nbsp; ')
		);

	if ( isset($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_2 ]) && $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_2 ] > 0 )
		printf('<div style="background-color: %s; color: %s; width: %s%%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;">%s</div>',
			ProvinhaBrasil::$NIVEL_2_COR,
			ProvinhaBrasil::COR_TEXTO,
			$d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_2 ],
			($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_2 ] > 5 ? $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_2 ] . '%' : '&nbsp; ')
		);

	if ( isset($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_3 ]) && $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_3 ] > 0 )
		printf('<div style="background-color: %s; color: %s; width: %s%%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;">%s</div>',
			ProvinhaBrasil::$NIVEL_3_COR,
			ProvinhaBrasil::COR_TEXTO,
			$d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_3 ],
			($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_3 ] > 5 ? $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_3 ] . '%' : '&nbsp; ')
		);

	if ( isset($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_4 ]) && $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_4 ] > 0 )
		printf('<div style="background-color: %s; color: %s; width: %s%%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;">%s</div>',
			ProvinhaBrasil::$NIVEL_4_COR,
			ProvinhaBrasil::COR_TEXTO,
			$d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_4 ],
			($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_4 ] > 5 ? $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_4 ] . '%' : '&nbsp; ')
		);

	if ( isset($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_5 ]) && $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_5 ] > 0 )
		printf('<div style="background-color: %s; color: %s; width: %s%%; text-align: center; display:-moz-inline-stack; display:inline-block; zoom:1; *display:inline;">%s</div>',
			ProvinhaBrasil::$NIVEL_5_COR,
			ProvinhaBrasil::COR_TEXTO,
			$d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_5 ],
			($d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_5 ] > 5 ? $d['rendimentos_porcentagem'][ ProvinhaBrasil::$NIVEL_5 ] . '%' : '&nbsp; ')
		);
?>
	</td>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table width="100%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_1_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_2_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_3_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_4_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_5_TEXTO; ?></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
	</table>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>