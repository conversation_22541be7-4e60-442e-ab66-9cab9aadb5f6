<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false
);

/*$selecionado = Simulado::trocaSimulado();
$simuCheck = new Simulado($selecionado);
$simuCheck->carregar();
$simuDisc = $simuCheck->obterDisciplinaPB();*/

$nome = $this->_relatorio->obterNome();

/*if($simuDisc == 'MAT'){
	$nome = str_replace("habilidades", "competências", $nome);
	$HAT = ProvinhaBrasil::COM_ADQUIRIDA_TEXTO;
	$HEAT = ProvinhaBrasil::COM_EM_AQUISICAO_TEXTO;
	$HNAT = ProvinhaBrasil::COM_NAO_ADQUIRIDA_TEXTO;
}
else{*/
	$HAT = ProvinhaBrasil::HAB_ADQUIRIDA_TEXTO;
	$HEAT = ProvinhaBrasil::HAB_EM_AQUISICAO_TEXTO;
	$HNAT = ProvinhaBrasil::HAB_NAO_ADQUIRIDA_TEXTO;
/*}*/

$tabela['titulo'] = $nome .' - '. (stristr($turma, 'turma') === false ? 'Turma ' . $turma : $turma).' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong><br />
	<strong>%s</strong><br />
	<strong>Professor:</strong> %s<br />
	<strong>Turma:</strong> %s',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio()),
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome(),
	$this->_config['professor']->obterUsuario()->obterNome(),
	$turma
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th><?= $this->obterBotaoAlterador('nome', false, 'Aluno') ;?></th><? } ?>
		<th valign="middle" width="5%" style="text-align: center; padding-left: 7px; padding-right: 7px;"><?= $this->obterBotaoAlterador('nivel', false, 'Nível') ;?></th>
<?
	if ( count($this->_dados) ) {
		foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ){
			if (isset($d['rendimento_por_habilidade'][$hID])) {
				echo '<th style="text-align: center;" width="10px" nowrap="nowrap">'. $hNome .' </th>';
			}
		}
	}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'_'. $tID .'.png" border="0" />';

	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];

?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td><?= @$d['nome']; ?> <?= $d['_inscricao']->obterAluno()->obterPortadorNecessidade() ? '*' : ''; ?></td><? } ?>
<?
		//$cor = ProvinhaBrasil::$NIVEL_5_COR;
		$cor = '#F6F6F6';
		$texte_cor = $this->obterModoVisualizacao() == self::PDF ? '#303030' : '#737373';
		$nivel = ProvinhaBrasil::$NIVEL_5_NOME;

		if ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_1) {
			//$cor = ProvinhaBrasil::$NIVEL_1_COR;
			$nivel = ProvinhaBrasil::$NIVEL_1_NOME;
		} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_2) {
			//$cor = ProvinhaBrasil::$NIVEL_2_COR;
			$nivel = ProvinhaBrasil::$NIVEL_2_NOME;
		} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_3) {
			//$cor = ProvinhaBrasil::$NIVEL_3_COR;
			$nivel = ProvinhaBrasil::$NIVEL_3_NOME;
		} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_4) {
			//$cor = ProvinhaBrasil::$NIVEL_4_COR;
			$nivel = ProvinhaBrasil::$NIVEL_4_NOME;
		} else {
			//$cor = ProvinhaBrasil::$NIVEL_5_COR;
			$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
		}

		if ($d['rendimento'] !== NULL)
			//echo '<td nowrap="nowrap" style="text-align: center; color: '. ProvinhaBrasil::COR_TEXTO .'; background-color: '. $cor .'"> '. $nivel .' </td>';
			echo '<td nowrap="nowrap" style="text-align: center; color: '. $texte_cor/*ProvinhaBrasil::COR_TEXTO*/ .'; background-color: '. $cor .'">'. $nivel .' </td>';
		else
			echo '<td>&nbsp;</td>';
?>
<?
		foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ) {
			if (isset($d['rendimento_por_habilidade'][$hID])) {
				$img = 'rend_ruim';

				if ($d['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM)
					$img = 'rend_bom';
				elseif ($d['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM)
					$img = 'rend_medio';

				$img .= '.gif';

				echo '<td align="center"><img title="'. @sprintf('%d%%', $d['rendimento_por_habilidade'][$hID]['rendimento']) .'" style="padding-top: 2px;" width="12" height="12" src="'. Core::diretiva('ESTILO:DIRETORIO:media').$img .'" border="0" /> </td>';
			}
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div align="center" style="margin-top: 5px;">
	<?php
	if(Core::diretiva('RELATORIO:Exibir_Legenda_Alunos_Especiais') == '1'){
		echo"* alunos com necessidades especiais";
	}
	?>
	</div>

	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;" class="">
	<table align="center" width="60%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra" style="margin-bottom: 5px;">
		<tr>
			<td width="14px"><img width="12" height="12" src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>rend_bom.gif" border="0" /> </td>
			<td nowrap="nowrap"><?= $HAT; ?> &nbsp;&nbsp;&nbsp;&nbsp; </td>

			<td width="14px"><img width="12" height="12" src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>rend_medio.gif" border="0" /> </td>
			<td nowrap="nowrap"><?= $HEAT; ?> &nbsp;&nbsp;&nbsp;&nbsp; </td>

			<td width="14px"><img width="12" height="12" src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>rend_ruim.gif" border="0" /> </td>
			<td nowrap="nowrap"><?= $HNAT; ?> </td>
		</tr>
		<? /* ?>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>

		<tr>
			<td colspan="2">Para a melhor compreensão dos resultados é necessário consultar o Guia de Correção disponibilizado pela Secretaria de Educação. </td>
		</tr>
		<? */ ?>
	</table>

<?
		foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ) {
			echo '<strong>'.$hNome.'</strong> - '.$this->_analizadorSimulado->descricoesHabilidades[$hID].' &nbsp; &nbsp; ';
		}
?>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>