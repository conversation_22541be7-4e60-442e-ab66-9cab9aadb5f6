<? if (!defined('CORE_INCLUIDO')) exit(); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?= Core::modulo('index')->obterCharset(); ?>" />
<link href="<?= Core::diretiva('ESTILO:DIRETORIO:estilo') . Core::diretiva('ESTILO:ARQUIVO:css_pdf'); ?>" rel="stylesheet" type="text/css" />
</head>
<body>

<?
	$i = count($relHTML);
	foreach($relHTML as $relTabela) {
?>
		<? if (@$relTabela['descricao'] != null): ?><div style="padding-bottom: 10px; line-height: 88%" class="medio"><? //<strong>Data:</strong> $this->_obterData(); <br /> ?><?= @$relTabela['descricao']; ?></div><? endif; ?>
		<? if (@$relTabela['titulo'] != null): ?><div style="padding-bottom: 15px; line-height: 88%; font-weight: bold;" align="center" class="rlt_painel_Titulo gigante"><?= @$relTabela['titulo']; ?></div><? endif; ?>

<?
		if (isset($relTabela['grafico']) && !is_array($relTabela['grafico']) && !empty($relTabela['grafico']))
			echo '<div style="padding-bottom: 10px;" align="center">'. $relTabela['grafico'] .'</div>';
		elseif (isset($relTabela['grafico']) && is_array($relTabela['grafico'])) {
			foreach($relTabela['grafico'] as $grafico)
				echo '<div style="padding-bottom: 10px;" align="center">'. $grafico .'</div>';
		}
?>

<?
		if (isset($relTabela['td']) && $relTabela['td'] != null) {
			$largura = '';
			if ((!isset($relTabela['usar_largura_maxima']) && !isset($relTabela['usar_largura_fixa']))|| $relTabela['usar_largura_maxima'])
				$largura = 'width="100%"';
			elseif (isset($relTabela['usar_largura_fixa']) && $relTabela['usar_largura_fixa'] != NULL)
				$largura = 'width="'.$relTabela['usar_largura_fixa'].'"';
?>
		<table <?= $largura; ?> align="center" cellpadding="1" cellspacing="0" class="rltli">
			<?= @$relTabela['td']; ?>
		</table>
<?
		}
?>

		<? if (@$relTabela['extra_antes_td_extra']) echo '<div class="minusculo">' . @$relTabela['extra'] . '</div>'; ?>

<?
		if ( isset($relTabela['td_extra']) && $relTabela['td_extra'] != null ) {
			if ( !isset($relTabela['usar_largura_maxima_td_extra']) && isset($relTabela['usar_largura_maxima']) )
				$relTabela['usar_largura_maxima_td_extra'] = $relTabela['usar_largura_maxima'];
?>
		<table style="margin-top: 10px;" <?= !isset($relTabela['usar_largura_maxima_td_extra']) || $relTabela['usar_largura_maxima_td_extra'] ? 'width="100%"' : ''; ?> align="center" cellpadding="2" cellspacing="0" class="rltli">
			<?= @$relTabela['td_extra']; ?>
		</table>
<?
		}
?>

		<? if (!isset($relTabela['extra_antes_td_extra']) || !@$relTabela['extra_antes_td_extra']) echo '<div class="minusculo">' . @$relTabela['extra'] . '</div>'; ?>

		<? if (@$relTabela['nao_quebrar_pagina'] != true): ?>
		<div style="padding-top: 5px" class="minusculo">
			<? 
				/*$simuladoDoUsuario = $_SESSION['_seletor_simulados.selecionado'];//Core::diretiva('_seletor_simulados.selecionado');
				
				$simulado = new Simulado($simuladoDoUsuario);
				$simulado->carregar();

				$teste = $simulado->obterEdicaoPB();
				$tipo = $simulado->obterDisciplinaPB();
				$ano = $simulado->obterAnoPB();
				$id = $simulado->obterID();*/

				if (@empty($relTabela['footer'])): 
			?>
				<?php echo "";//Core::diretiva('BOTREL_FooterText_'.$teste.'_'.$tipo.'_'.$ano.'_'.$id); ?>
			<? else: ?>
				<? echo "<center>".$relTabela['footer']."</center>"; ?> 
			<? endif; ?>
		</div>
		<? endif; ?>
<?
		if (--$i > 0) {
			if (@$relTabela['nao_quebrar_pagina'] != true) {
				echo '<div style="page-break-after: always;"></div>';
			} else {
				if ( @$relTabela['quebra_invisivel'] != true)
					echo '<hr color="#cccccc" noshade="noshade" size="1px" style="margin-top: 20px; margin-bottom: 15px;" />';
				else
					echo '<hr color="#ffffff" noshade="noshade" size="0px" style="margin-top: 10px; margin-bottom: 7px;" />';
			}
		}
	}
?>

<?
	if (false || Core::moduloCarregado('_bloco_notas') && @Core::modulo('_bloco_notas')->temDados()) {
?>
	<hr color="#ccc" noshade="noshade" size="1" style="margin-top: 20px; margin-bottom: 20px;" />

	<table width="100%" cellpadding="2" cellspacing="0" class="rltli">
		<?= @Core::modulo('_bloco_notas')->obterSaida(); ?>
	</table>
<?
	}
?>

</body>
</html>