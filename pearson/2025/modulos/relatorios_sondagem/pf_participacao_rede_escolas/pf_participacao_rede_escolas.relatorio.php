<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemSondagem', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFParticipacaoRedeEscolas extends RelatorioListagemSondagem {
	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'fase' => true,
			'presentes_rel' => true,
			'ausentes_rel' => true,
			'presentes_abs' => true,
			'ausentes_abs' => true,
			'total' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'presentes_rel' => 0,
			'ausentes_rel' => 0,
			'presentes_abs' => 0,
			'ausentes_abs' => 0,
			'total' => 0,
			'_rede' => false
		);

		$this->_config = array(

		);

		$this->_fases = 0;

		$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$pagina_atual = 0;
			$dados_mostrar = array();
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == 999 || !$total) {
					include 'pf_participacao_rede_escolas.relatorio.tabela.html.php';
					$dados_mostrar = array();
					$pagina_atual++;
				}
			}

			include 'modulos/relatorios_sondagem/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		$total = count($this->_dados);
		$pagina_atual = 0;
		$dados_mostrar = array();
		foreach ( $this->_dados as $k => &$d ) {
			$dados_mostrar[] = $k;
			$total--;

			if (count($dados_mostrar) == 999 || !$total) {
				include 'pf_participacao_rede_escolas.relatorio.tabela.html.php';
				$dados_mostrar = array();
				$pagina_atual++;
			}
		}

		include 'modulos/relatorios_sondagem/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		$this->_sugestao['presentes_rel'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		/*if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));*/

		// coloca a rede depois
		/*$idREDE = null;
		foreach ($this->_dados as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados[$idREDE])) {
			$rede = $this->_dados[$idREDE];
			unset($this->_dados[$idREDE]);
			$this->_dados[] = $rede;
		}*/
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ($this->_ordenacao->multiplaOrdenacao as $n => $tipo) {
			if ($n == 'rendimento' || $n == 'presentes_rel'){
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			}
			else{
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);
			}

			if ($retorno){
				break;
			}
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_sondagem/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		$this->_analizadorSimulado->carregarInscritos(true);
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();

		$this->_fases = $this->_seletorSimulados->simulado->obterDuracao();

		$participacaoPorInstituicao = array();
		foreach ($this->_analizadorSimulado->inscritos as $iID => $inscricao) {
			$eID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($participacaoPorInstituicao[$eID])){
				$participacaoPorInstituicao[$eID] = array();
				for ($i=1; $i <= $this->_fases; $i++) { 
					if (!isset($participacaoPorInstituicao[$eID][$i])){
						$participacaoPorInstituicao[$eID][$i] = $this->_modelo;		
					}
				}
			}

			for ($i=1; $i <= $this->_fases; $i++) { 
				$participacaoPorInstituicao[$eID][$i]['total']++;
			}

			$inscricao->verificarAusentePorFases();
			$oapf = $inscricao->obterAusentePorFases();
			foreach ($oapf as $oapfK => $oapfV) {
				$participacaoPorInstituicao[$eID][$oapfK]['presentes_abs']++;
			}
		}

		foreach ($participacaoPorInstituicao as $eID => $dado) {
			foreach ($dado as $dkey => $dvalue) {
				$participacaoPorInstituicao[$eID][$dkey]['nome'] = $this->_analizadorSimulado->nomesInstituicoes[$eID];
				$participacaoPorInstituicao[$eID][$dkey]['ausentes_abs'] = $participacaoPorInstituicao[$eID][$dkey]['total'] - $participacaoPorInstituicao[$eID][$dkey]['presentes_abs'];

				if ($participacaoPorInstituicao[$eID][$dkey]['total'] > 0) {
					$tempPorcentagem = self::calcularPorcentagemPerfeita(array($participacaoPorInstituicao[$eID][$dkey]['presentes_abs'], $participacaoPorInstituicao[$eID][$dkey]['ausentes_abs']));
					$participacaoPorInstituicao[$eID][$dkey]['presentes_rel'] = $tempPorcentagem[0];
					$participacaoPorInstituicao[$eID][$dkey]['ausentes_rel'] = $tempPorcentagem[1];
				}
			}

			$this->_dados = $participacaoPorInstituicao;
		}

		// REDE
		$dado = array();
		if(count($this->_dados)>0){
			foreach ($this->_dados as $fase) {
				foreach ($fase as $fk => $d) {
					if(count($this->_modelo)<=0){
						$dado[$fk] = $this->_modelo;
					}						
					$dado[$fk]['nome'] = 'REDE';
					$dado[$fk]['_rede'] = true;
				}
			}

			foreach ($this->_dados as $fase) {
				foreach ($fase as $fk => $d) {
					$dado[$fk]['total'] = $dado[$fk]['total'] + $d['total'];
					$dado[$fk]['presentes_abs'] = $dado[$fk]['presentes_abs'] + $d['presentes_abs'];
					$dado[$fk]['ausentes_abs'] = $dado[$fk]['ausentes_abs'] + $d['ausentes_abs'];

					if ($dado[$fk]['total'] > 0) {
						$tempPorcentagem = self::calcularPorcentagemPerfeita(array($dado[$fk]['presentes_abs'], $dado[$fk]['ausentes_abs']));
						$dado[$fk]['presentes_rel'] = $tempPorcentagem[0];
						$dado[$fk]['ausentes_rel'] = $tempPorcentagem[1];
					}
				}
			}

			$this->_dados['_rede'] = $dado;
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>