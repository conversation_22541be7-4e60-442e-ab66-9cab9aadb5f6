<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false
);

$tabela['titulo'] = $this->_relatorio->obterNome();//.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio())
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['escola']) { ?><th style="text-align: center;"><?= $this->obterBotaoAlterador('escola', false, 'Escola') ;?></th><? } ?>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?></th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'.png" border="0" />';
	$tabela['grafico'] .= '<div id="grafico" style="min-width: 400px; height: 400px; margin: 0 auto;"></div>';

	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['escola']) { ?><td align="center"><?= @$d['escola']; ?></td><? } ?>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_1]; ?> %</td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_2]; ?> %</td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_3]; ?> %</td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_4]; ?> %</td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_5]; ?> %</td>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); $tabela['td'] = null; ob_start();
?>
	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table width="100%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_1_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_2_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_3_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_4_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_5_TEXTO; ?></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
	</table>
	</div>

<?
$dg = array();
$d = $this->_dados[0];

$dg[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_1];
$dg[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_2];
$dg[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_3];
$dg[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_4];
$dg[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_5];

$dgp = RelatorioListagemSondagem::calcularPorcentagemPerfeita($dg);
//foreach ($dados_porcentagem as $k => $v) {
//	$labels[$k] = str_replace("\n", ' - ', $labels[$k]).' ';
//}
//echo"d<pre>";print_r($d);echo"</pre><br>";
//echo"dg<pre>";print_r($dg);echo"</pre><br>";
//echo"dgp<pre>";print_r($dgp);echo"</pre><br>";

$dg[0] = 0;
$dgp[0] = 0;
?>

	<script type="text/javascript">
	jQuery(function () {
    	// Radialize the colors
		Highcharts.getOptions().colors = Highcharts.map(Highcharts.getOptions().colors, function(color) {
		    return {
		        radialGradient: { cx: 0.5, cy: 0.3, r: 0.7 },
		        stops: [
		            [0, color],
		            [1, Highcharts.Color(color).brighten(-0.3).get('rgb')] // darken
		        ]
		    };
		});
		
		// Build the chart
        jQuery('#grafico').highcharts({
            chart: {
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false
            },
            title: {
                text: ''
            },
            tooltip: {
        	    pointFormat: '{series.name}: <b>{point.y}%</b>',
            	percentageDecimals: 0
            },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        color: '#000000',
                        connectorColor: '#000000',
                        formatter: function() {
                            return '<b>'+ this.point.name +'</b>: '+ this.y +' %';
                        }
                    }
                }
            },
            series: [{
                type: 'pie',
                name: 'Alfabetização na rede',
                data: [
                    {
                        name: '<?= ProvinhaBrasil::$NIVEL_1_NOME; ?> - (<?= $dg[0]; ?>)',
                        color: '<?= ProvinhaBrasil::$NIVEL_1_COR; ?>',
                        y: <?= $dgp[0]; ?>
                    },
                    {
                        name: '<?= ProvinhaBrasil::$NIVEL_2_NOME; ?> - (<?= $dg[1]; ?>)',
                        color: '<?= ProvinhaBrasil::$NIVEL_2_COR; ?>',
                        y: <?= $dgp[1]; ?>
                    },
                    {
                        name: '<?= ProvinhaBrasil::$NIVEL_3_NOME; ?> - (<?= $dg[2]; ?>)',
                        color: '<?= ProvinhaBrasil::$NIVEL_3_COR; ?>',
                        y: <?= $dgp[2]; ?>
                    },
                    {
                        name: '<?= ProvinhaBrasil::$NIVEL_4_NOME; ?> - (<?= $dg[3]; ?>)',
                        color: '<?= ProvinhaBrasil::$NIVEL_4_COR; ?>',
                        y: <?= $dgp[3]; ?>
                    },
                    {
                        name: '<?= ProvinhaBrasil::$NIVEL_5_NOME; ?> - (<?= $dg[4]; ?>)',
                        color: '<?= ProvinhaBrasil::$NIVEL_5_COR; ?>',
                        y: <?= $dgp[4]; ?>,
                        sliced: true,
                        selected: true
                    }
                ]
            }]
        });
    });
	</script>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>