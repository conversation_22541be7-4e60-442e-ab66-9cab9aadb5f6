<?
if (!defined('CORE_INCLUIDO')) exit(); 

Core::incluir('RelatorioListagemSondagem', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('ProvinhaBrasil', 'ProvinhaBrasil/', true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);

class RLPBRedePorcentagemAlunosPorNivelV2 extends RelatorioListagemSondagem {
	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'escola' => true,
			'rendimentos' => true,
			'rendimento' => true
		);

		$this->_modelo = array(
			'escola' => null,
			'rendimentos' => array(),
			'rendimentos_porcentagem' => array(),
			'rendimento' => null,
			'_rede' => false
		);

		$this->_config = array(

		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			include 'pb_rede_porcentagem_alunos_por_nivel_v2.relatorio.tabela.html.php';

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF();
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_obterDados();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			include 'pb_rede_porcentagem_alunos_por_nivel_v2.relatorio.tabela.html.php';

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		//if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			//usort($this->_dados, array($this, '_ordenarDados'));

		$this->gerarGrafico();
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'escola' )
				@$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);
			else
				@$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_sondagem/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'escola' => 'Escola'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () { 
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			//$this->_dados = &$tempCache['dados'];
			//return;
		}


		$this->_analizadorSimulado->carregarInscritos(true);
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();

		$rendimentoRede = array(
			ProvinhaBrasil::$NIVEL_1 => 0,
			ProvinhaBrasil::$NIVEL_2 => 0,
			ProvinhaBrasil::$NIVEL_3 => 0,
			ProvinhaBrasil::$NIVEL_4 => 0,
			ProvinhaBrasil::$NIVEL_5 => 0,
			'total_inscritos' => 0
		);

		foreach ( $this->_analizadorSimulado->rendimentoGlobal as $iID => $desempenho ) {
			// não entra na média se for portador de necessidades especiais
			if ($this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
				continue;

			$rendimentoRede['total_inscritos']++;

			if ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_1) {
				$rendimentoRede[ProvinhaBrasil::$NIVEL_1]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_2) {
				$rendimentoRede[ProvinhaBrasil::$NIVEL_2]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_3) {
				$rendimentoRede[ProvinhaBrasil::$NIVEL_3]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_4) {
				$rendimentoRede[ProvinhaBrasil::$NIVEL_4]++;
			} else {
				$rendimentoRede[ProvinhaBrasil::$NIVEL_5]++;
			}
		}

		// RENDIMENTO REDE
		$dado = $this->_modelo;
		$dado['escola'] = 'REDE';
		$dado['rendimentos'] = $rendimentoRede;
		$dado['total_inscritos'] = $rendimentoRede['total_inscritos'];

		unset($dado['rendimentos']['total_inscritos']);

		$dado['rendimentos_porcentagem'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

		$dado['_rede'] = true;

		$this->_dados[] = $dado;


		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_pie.php';
		require_once 'jpgraph/jpgraph_pie3d.php';

		$grafico = new PieGraph(800, 800, $this->_tituloCache.'.png', (int) CACHE_RELATORIOS / 60, false);
		$grafico->SetAntiAliasing(true);
		$grafico->setFrame(false);

		$dados = array();
		$d = $this->_dados[0];

		$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_1];
		$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_2];
		$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_3];
		$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_4];
		$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_5];

		$labels = array(
			ProvinhaBrasil::$NIVEL_1_NOME . "\n". $dados[0] .' (%d%%)',
			ProvinhaBrasil::$NIVEL_2_NOME . "\n". $dados[1] .' (%d%%)',
			ProvinhaBrasil::$NIVEL_3_NOME . "\n". $dados[2] .' (%d%%)',
			ProvinhaBrasil::$NIVEL_4_NOME . "\n". $dados[3] .' (%d%%)',
			ProvinhaBrasil::$NIVEL_5_NOME . "\n". $dados[4] .' (%d%%)'
		);

		$dados_porcentagem = RelatorioListagemSondagem::calcularPorcentagemPerfeita($dados);
		foreach ($dados_porcentagem as $k => $v) {
			//if ($v < 15)
			//{
				$labels[$k] = str_replace("\n", ' - ', $labels[$k]).' ';
			//}
			//else
			//{
			//	$labels[$k] = '  '.$labels[$k];
			//}
		}

		/*if (@$dados_porcentagem[0] > 1 && @$dados_porcentagem[1] < 20)
			$labels[1] = "\n\n".$labels[1];

		if (@$dados_porcentagem[0] == 0 && @$dados_porcentagem[1] < 10 && @$dados_porcentagem[2] < 20)
			$labels[2] = "\n\n".$labels[2];

		if (@$dados_porcentagem[0] > 0 && @$dados_porcentagem[1] < 15 && @$dados_porcentagem[2] < 15)
			$labels[2] = "\n\n".$labels[2];

		if (@$dados_porcentagem[0] > 0 && @$dados_porcentagem[0] < 5 && @$dados_porcentagem[1] < 10)
			$labels[1] = "\n\n".$labels[1];

		$labels[0] = $labels[0]."\n";*/

		$labelsCor = array(
			ProvinhaBrasil::$NIVEL_1_COR,
			ProvinhaBrasil::$NIVEL_2_COR,
			ProvinhaBrasil::$NIVEL_3_COR,
			ProvinhaBrasil::$NIVEL_4_COR,
			ProvinhaBrasil::$NIVEL_5_COR
		);	

		foreach($dados as $k => $v) {
			if ($v == 0) {
				unset($dados[$k]);
				unset($labels[$k]);
				unset($labelsCor[$k]);
			}
		}

		$dados = array_reverse($dados);
		$labels = array_reverse($labels);
		$labelsCor = array_reverse($labelsCor);

		$pie = new PiePlot( $dados );
		$pie->SetGuideLines(true, true, true);
		$pie->SetGuideLinesAdjust(3.1);
		//$pie->ExplodeSlice(2);
		//$pie->legend->SetPos(0.1,0.2);
		$pie->value->SetFont(FF_ARIAL,FS_BOLD, 12);
		$pie->value->SetColor( '#000000' );//ProvinhaBrasil::COR_TEXTO );
		$pie->SetLabelType(PIE_VALUE_ADJPER);
		$pie->SetLabels( $labels, 1.0 );
		//$pie->SetLabelPos(0.7);
		//$grafico->legend->SetFont(FF_ARIAL,FS_NORMAL, 10);
		//$grafico->legend->SetColor(ProvinhaBrasil::COR_TEXTO);
		//$grafico->legend->SetFillColor('white');
		//$grafico->legend->SetShadow(false);
		//$pie->SetLegends($legend);
		$pie->SetSliceColors( $labelsCor );
		$pie->SetSize(0.3);

		if (count($dados) > 1)
			$pie->SetStartAngle(90);

		$grafico->Add($pie);
		$grafico->Stroke();
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'turma' ),
							 'linhas' => array(),
							 'config' => array() );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>