url = JAVA_URL_BASE+'/simulado/all';

jQuery.get(url, function (jsons) {
    if (jsons) {
        jsons.each(function (json) {

            var option = "<option value='"+json.id+"'>"+json.nome+" / "+json.id+"</option>";

            jQuery('#inscricao_automatica_de').append(option);
            jQuery('#inscricao_automatica_para').append(option);
        });

    } else {
        jQuery('#waiting').hide();
        openModal(json.error,"closeModal()");
    }
});

function blockCombo(checked){
    if (checked) {
        jQuery('#inscricao_automatica_de').prop('disabled', false) ;
        jQuery('#inscricao_automatica_para').prop('disabled', false) ;
        jQuery('#de_para_linha').show();

    }else {
        jQuery('#inscricao_automatica_de').prop('disabled', true) ;
        jQuery('#inscricao_automatica_para').prop('disabled', true) ;
        jQuery('#de_para_linha').hide();

    }
}
function moreThanOneChecked(moreThanOneChecked) {
    if(moreThanOneChecked){
        jQuery('.myButton').hide();
    }else{
        jQuery('.myButton').show();
    }

}

jQuery(document).ready(function() {
    jQuery('#waiting').hide();
jQuery('.myButton').click(function () {
    inscrever();
});

jQuery('#inscricao_automatica_para_todos_texto').click(function () {
      blockCombo(!jQuery(this).is(':checked') && !jQuery('#inscricao_automatica_para_proximos_bimestre').is(':checked') );
      moreThanOneChecked(jQuery(this).is(':checked') && jQuery('#inscricao_automatica_para_proximos_bimestre').is(':checked'));

});

jQuery('#inscricao_automatica_para_proximos_bimestre').click(function () {
      blockCombo(!jQuery(this).is(':checked') && !jQuery('#inscricao_automatica_para_todos_texto').is(':checked'));
      moreThanOneChecked(jQuery(this).is(':checked') && jQuery('#inscricao_automatica_para_todos_texto').is(':checked'));
});

});


function inscrever(){

    jQuery('#waiting').show();

    jQuery('#inscricao_automatica_table>table>tbody').find('#de_para_linha').each(function () {
        var url = "";

        if(!jQuery('#inscricao_automatica_para_todos_texto').is(':checked') &&
            !jQuery('#inscricao_automatica_para_proximos_bimestre').is(':checked')){
            url = JAVA_URL_BASE+'/inscrever/simulado/';
            url += jQuery(this).find('#inscricao_automatica_de').val() ;
            url += '/';
            url += jQuery(this).find('#inscricao_automatica_para').val() ;
        }

        if(jQuery('#inscricao_automatica_para_todos_texto').is(':checked') &&
            !jQuery('#inscricao_automatica_para_proximos_bimestre').is(':checked')){
            url = JAVA_URL_BASE+'/inscrever/simulado/todas/producao/texto';
        }

        jQuery.get(url, function (json) {
            if (json == true) {
                openModal('Alunos inscritos com sucesso!',"closeModal()");
                jQuery('#waiting').hide();
            } else {
                error_msg = "<br/> <br/> Erro ao inscrever simulado de: "+jQuery(this).find('#inscricao_automatica_de').val()+
                        " para : "+jQuery(this).find('#inscricao_automatica_para').val();
                openModal(json.error + error_msg,"closeModal()");
                jQuery('#waiting').hide();
            }
            closeModalInscricaoAutomatica();
        });

    });


}

function removerLinha(){
	if(jQuery('tr[id=de_para_linha]').size() > 1){
		 jQuery('tr[id=de_para_linha]:last').remove();
	}  
}

function adicionarLinha(){
    jQuery('#de_para_linha').after(jQuery('#de_para_linha').clone());
}
openModalInscricaoAutomatica();

function openModalInscricaoAutomatica(){

    jQuery('#nome_incluir_aluno').show();
    jQuery("body").append("<div class='modal-overlay js-modal-close'></div>");
    jQuery('.modal-body>p').text("");
    jQuery(".modal-overlay").fadeTo(500, 0.7);
    jQuery('#inscricao_automatica').fadeIn();
    resizeModalIncluir();
    document.body.scrollTop=0;
}

function resizeModalIncluir(){

    jQuery(window).resize(function() {
        jQuery("#inscricao_automatica").css({
            top: (jQuery(window).height() - jQuery("#inscricao_automatica").outerHeight()) / 2,
            left: (jQuery(window).width() - jQuery("#inscricao_automatica").outerWidth()) / 2
        });
    });


    jQuery(window).resize();

}

function closeModalInscricaoAutomatica(){

    jQuery("#inscricao_automatica").fadeOut(500, function() {
        jQuery(".modal-overlay").remove();
    });

    jQuery(".modal-overlay").fadeOut(500, function() {
        jQuery(".modal-overlay").remove();
    });


}