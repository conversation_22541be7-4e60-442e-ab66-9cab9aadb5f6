<?
if (!defined('CORE_INCLUIDO')) { exit(); }
?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('painel_relatorio')->obterSaida(); ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<div class="vd_BlocoEspacador">
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="rlt_painel">
  <tr>
    <td class="rlt_painel_Botoes" style="padding:8px;font-size: 14px;font-weight: bold;line-height: 32px;">
    	<div style="width:45%;float:left;">
			<? 
				$botaoPDF = true;
				$RELS_SEM_BOTAO_PDF = str_replace(' ', '', Core::diretiva('RELS_SEM_BOTAO_PDF'));
				if(!empty($RELS_SEM_BOTAO_PDF)){
					$RELS_SEM_BOTAO_PDF = explode(',', $RELS_SEM_BOTAO_PDF);

					if(count($RELS_SEM_BOTAO_PDF) > 0){
						if(in_array($this->_relatorio->obterID(), $RELS_SEM_BOTAO_PDF)){
							$botaoPDF = false;
						}
					}
				}

				if($botaoPDF){
			?>
					<img border="0" style="float:left; margin-right: 10px; margin-left: 20px;" src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>pdf-folder-32p.png">
	   				<?=  $this->_obterLinkBotao(self::BT_PDF); ?>
	    			<?= $this->_relatorio->obterDescricaoUso() != null ? ' | '. $this->_obterLinkBotao(self::BT_SUGESTAO_LEITURA) : ''; ?>
			<? 
				}

				$botaoXLS = false;
				$RELS_COM_BOTAO_XLS = str_replace(' ', '', Core::diretiva('RELS_COM_BOTAO_XLS'));
				if(!empty($RELS_COM_BOTAO_XLS)){
					$RELS_COM_BOTAO_XLS = explode(',', $RELS_COM_BOTAO_XLS);

					if(count($RELS_COM_BOTAO_XLS) > 0){
						if(in_array($this->_relatorio->obterID(), $RELS_COM_BOTAO_XLS)){
							$botaoXLS = true;
						}
					}
				}

				if($botaoXLS){
			?>
					<img border="0" style="float:left; margin-right: 10px; margin-left: 20px; height: 32px;" src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>excel_256x256.png">
	   				<?=  $this->_obterLinkBotao(self::BT_XLS); ?>
			<? 
				}
			?>
		</div>
		<div style="width:45%;float:right;">
	    <?= $this->_obterLinkBotao(self::BT_BLOCO_NOTAS); ?>
	    <img border="0" style="margin-right: 10px; float: right; width: 32px;" src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>bloco-notas-32p.png">
	    </div>
    </td>
  </tr>
  <tr>
    <td>
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td width="16" class="rlt_painel_Borda" style="background-position: left;" nowrap="nowrap">&nbsp;</td>
    <td class="rlt_painel_Relatorio">
<?
	$i = count($relHTML);
	foreach($relHTML as $relTabela) {
?>
		<? if (@$relTabela['descricao'] != null): ?>
			<div style="padding-bottom: 10px; line-height: 88%; width:100%;" class="medio">
				<table border=0 cellpadding=0 cellspacing=0 style="width:99%;">
					<tr>
						<td><?= @$relTabela['descricao']; ?></td>	
						<td class="normal" style="text-align: right;"><?= '<strong>Acesso: </strong>', $this->_obterData() ?></td>							
					</tr>
				</table>
			</div>
		<? endif; ?>
		<? if (@$relTabela['titulo'] != null): ?>
			<div style="padding-bottom: 10px;" align="center" class="rlt_painel_Titulo maior">
				<?= @$relTabela['titulo']; ?>
			</div>
		<? endif; ?>
<?
		if (isset($relTabela['grafico']) && !is_array($relTabela['grafico']) && !empty($relTabela['grafico']))
			echo '<div style="padding-bottom: 20px;" align="center">'. $relTabela['grafico'] .'</div>';
		elseif (isset($relTabela['grafico']) && is_array($relTabela['grafico'])) {
			foreach($relTabela['grafico'] as $grafico)
				echo '<div style="padding-bottom: 20px;" align="center">'. $grafico .'</div>';
		}
?>

<?
		if (isset($relTabela['td']) && $relTabela['td'] != null) {
?>
		<table style="width:<?= @$relTabela['tamanho']; ?>" align="center" cellpadding="3" cellspacing="0" class="rltl">
			<?= @$relTabela['td']; ?>
		</table>
<?
		}
?>

		<? if (@$relTabela['extra_antes_td_extra']) echo @$relTabela['extra']; ?>

<?
		if ( isset($relTabela['td_extra']) && $relTabela['td_extra'] != null ) {
?>
		<table style="width:<?= @$relTabela['tamanho_extra']; ?>" style="margin-top: 20px;" align="center" cellpadding="3" cellspacing="0" class="rltl">
			<?= @$relTabela['td_extra']; ?>
		</table>
<?
		}
?>

		<? if (!isset($relTabela['extra_antes_td_extra']) || !@$relTabela['extra_antes_td_extra']) echo @$relTabela['extra']; ?>

<?
		if (--$i > 0) {
			if ( @$relTabela['quebra_invisivel'] != true)
				echo '<hr color="#ccc" noshade="noshade" size="1" style="margin-top: 20px; margin-bottom: 20px;" />';
			else
				echo '<hr color="#ffffff" noshade="noshade" size="0px" style="margin-top: 10px; margin-bottom: 7px;" />';

		}
	}
?>
    </td>
    <td width="16" class="rlt_painel_Borda" style="background-position: left;" nowrap="nowrap">&nbsp;</td>
  </tr>
</table>

	</td>
  </tr>
  <tr>
    <td class="rlt_painel_Botoes" style="padding:8px; font-weight: bold; text-align: center;">
    	<a href="javascript:void(0);" onclick="document.body.scrollTop = 0; document.documentElement.scrollTop = 0;">Voltar ao Topo</a>
    </td>
  </tr>
</table>
</div>
<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>
<?= @Core::modulo('_bloco_notas')->obterSaida(); ?>