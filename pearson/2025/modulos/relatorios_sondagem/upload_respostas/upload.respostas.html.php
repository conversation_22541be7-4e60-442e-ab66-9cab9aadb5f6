<div id="result">

</div>


<script>
    function url_parameter(sParam)
    {
        var sPageURL = window.location.search.substring(1);
        var sURLVariables = sPageURL.split('&');
        for (var i = 0; i < sURLVariables.length; i++)
        {
            var sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] == sParam)
            {
                return sParameterName[1];
            }
        }

        return "";
    }

    jQuery.get( "<?= Core::diretiva('JAVA_URL_BASE'); ?>/uploadMultiFile", function( data ) {
        jQuery( "#result" ).html( data );
    });

    function oneFile(){
        jQuery.get( "<?= Core::diretiva('JAVA_URL_BASE'); ?>/uploadOneFile", function( data ) {
            jQuery( "#result" ).html( data );
        });
    }

    function multiplesFile(){
        jQuery.get( "<?= Core::diretiva('JAVA_URL_BASE'); ?>/uploadMultiFile", function( data ) {
            jQuery( "#result" ).html( data );
        });
    }



</script>