<? 
if (!defined('CORE_INCLUIDO')) { exit(); }
?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('painel_relatorio')->obterSaida(); ?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<div class="vd_BlocoEspacador">
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="rlt_painel">
  <tr>
    <td class="rlt_painel_Botoes" style="font-weight: bold;"><?= $this->_obterLinkBotao(self::BT_IMPRESSAO); ?> | <?= $this->_obterLinkBotao(self::BT_PDF); ?><?= $this->_relatorio->obterDescricaoUso() != null ? ' | '. $this->_obterLinkBotao(self::BT_SUGESTAO_LEITURA) : ''; ?> | <?= $this->_obterLinkBotao(self::BT_BLOCO_NOTAS); ?></td>
  </tr>
  <tr>
    <td>
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td width="16" class="rlt_painel_Borda" style="background-position: left;" nowrap="nowrap">&nbsp;</td>
    <td class="rlt_painel_Relatorio">
	<table width="100%" border="0" cellspacing="1" cellpadding="3" style="margin-bottom: 8px;">
	  <tr>
		<td><div class="rlt_painel_Titulo medio"><?= $this->_relatorio->obterNome(); ?></div>
		<div><?= $this->_textoFiltro; ?></div> </td>
<? if ( !isset($esconderTotal) || !$esconderTotal ) { ?>
		<td width="20%" align="right" nowrap="nowrap" class="rlt_painel_Titulo medio">Total: <?= count($this->_dados); ?></td>
<? } ?>
	  </tr>
	</table>

	<table align="center" cellpadding="3" cellspacing="0" class="rltl">
		<?= $tabela['td']; ?>
	</table>
	</td>
    <td width="16" class="rlt_painel_Borda" style="background-position: left;" nowrap="nowrap">&nbsp;</td>
  </tr>
</table>

	</td>
  </tr>
  <tr>
    <td class="rlt_painel_Botoes" style="font-weight: bold;"><?= $this->_obterLinkBotao(self::BT_IMPRESSAO); ?> | <?= $this->_obterLinkBotao(self::BT_PDF); ?></td>
  </tr>
</table>
</div>
<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>
<?= @Core::modulo('_bloco_notas')->obterSaida(); ?>