/**
** Classe JTabelaDeRendimentoDetalhado
**/
var JTabelaDeRendimentoDetalhado = new Class({
	initialize: function(tabela, div_externo){
		this.tabela = $(tabela);
		this.div_externo = $(div_externo);
		
		this.div_externo.setOpacity(0);
	},
	
	mostrarRendimentoDetalhado: function(rendimentos, objeto_posicionador) {
		var tabelaBody = this.tabela.getChildren()[0];
		tabelaBody.getChildren().each(function (tr, i) {
			if (i > 0)
				tr.remove();
		});


		rendimentos.each(function (rendimento) {
			var rendimentoTR = new Element('tr', { 'class': 'colData' });
			
			if (rendimento[2])
				rendimentoTR.setStyles({
										'background-color': '#eeefff',
										'font-weight': 'bold' });
			
			rendimentoTR.adopt(new Element('td', { 'align': 'center' }).setHTML(rendimento[0]));
			rendimentoTR.adopt(new Element('td', { 'align': 'center' }).setHTML(rendimento[1]));
			tabelaBody.adopt(rendimentoTR);
		});		


		var coord = $(objeto_posicionador).getCoordinates();
		this.div_externo.setStyle('top', coord.top);
		this.div_externo.setStyle('left', coord.left + coord.width + 4);
		this.div_externo.setOpacity(1);
	},
	
	esconder: function() {
		this.div_externo.setOpacity(0);
	}
});