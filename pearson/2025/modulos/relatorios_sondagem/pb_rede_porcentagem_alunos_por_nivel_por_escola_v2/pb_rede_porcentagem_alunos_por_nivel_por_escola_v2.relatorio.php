<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemSondagem', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);

class RLPBRedePorcentagemAlunosPorNivelPorEscolaV2 extends RelatorioListagemSondagem {
	const POR_PAGINA = 35;

	protected $_analizadorSimulado;

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'escola' => true,
			'rendimentos' => true,
			'rendimento' => true
		);

		$this->_modelo = array(
			'escola' => null,
			'rendimentos' => array(),
			'rendimentos_porcentagem' => array(),
			'rendimento' => null,
			'_rede' => false
		);

		$this->_config = array(

		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$dados_mostrar = array();
			$pagina_atual = 0;
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == self::POR_PAGINA || !$total) {
					include 'pb_rede_porcentagem_alunos_por_nivel_por_escola_v2.relatorio.tabela.html.php';
					$dados_mostrar = array();
					$pagina_atual++;
				}
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF();
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_obterDados();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$dados_mostrar = array();
			$pagina_atual = 0;
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == self::POR_PAGINA || !$total) {
					include 'pb_rede_porcentagem_alunos_por_nivel_por_escola_v2.relatorio.tabela.html.php';
					$dados_mostrar = array();
					$pagina_atual++;
				}
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		// coloca a rede antes
		foreach ($this->_dados as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados[$idREDE])) {
			$rede = $this->_dados[$idREDE];
			unset($this->_dados[$idREDE]);
			$this->_dados[] = $rede;
		}
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'escola' )
				@$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);
			else
				@$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_sondagem/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'escola' => 'Escola'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			//$this->_dados = &$tempCache['dados'];
			//return;
		}


		$this->_analizadorSimulado->carregarInscritos(true);
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado->calcularRendimentoPorSerie();

		$nomePorInstituicao = $this->_analizadorSimulado->nomesInstituicoesSimplificados;
		$rendimentoPorEscola = array();
		$rendimentoRede = array(
			ProvinhaBrasil::$NIVEL_1 => 0,
			ProvinhaBrasil::$NIVEL_2 => 0,
			ProvinhaBrasil::$NIVEL_3 => 0,
			ProvinhaBrasil::$NIVEL_4 => 0,
			ProvinhaBrasil::$NIVEL_5 => 0,
			'total_inscritos' => 0
		);

		foreach ( $this->_analizadorSimulado->rendimentoGlobal as $iID => $desempenho ) {
			// não entra na média se for portador de necessidades especiais
			if ($this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
				continue;

			$instID = @$this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($rendimentoPorEscola[$instID]))
				$rendimentoPorEscola[$instID] = array(
					ProvinhaBrasil::$NIVEL_1 => 0,
					ProvinhaBrasil::$NIVEL_2 => 0,
					ProvinhaBrasil::$NIVEL_3 => 0,
					ProvinhaBrasil::$NIVEL_4 => 0,
					ProvinhaBrasil::$NIVEL_5 => 0,
					'total_inscritos' => 0
				);

			$rendimentoPorEscola[$instID]['total_inscritos']++;
			$rendimentoRede['total_inscritos']++;

			if ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_1) {
				$rendimentoPorEscola[$instID][ProvinhaBrasil::$NIVEL_1]++;
				$rendimentoRede[ProvinhaBrasil::$NIVEL_1]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_2) {
				$rendimentoPorEscola[$instID][ProvinhaBrasil::$NIVEL_2]++;
				$rendimentoRede[ProvinhaBrasil::$NIVEL_2]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_3) {
				$rendimentoPorEscola[$instID][ProvinhaBrasil::$NIVEL_3]++;
				$rendimentoRede[ProvinhaBrasil::$NIVEL_3]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_4) {
				$rendimentoPorEscola[$instID][ProvinhaBrasil::$NIVEL_4]++;
				$rendimentoRede[ProvinhaBrasil::$NIVEL_4]++;
			} else {
				$rendimentoPorEscola[$instID][ProvinhaBrasil::$NIVEL_5]++;
				$rendimentoRede[ProvinhaBrasil::$NIVEL_5]++;
			}
		}

		// ESCOLAS
		foreach( $rendimentoPorEscola as $instID => $rendimentos ) {
			$dado = $this->_modelo;
			$dado['escola'] = @$nomePorInstituicao[$instID];
			$dado['rendimentos'] = $rendimentos;
			$dado['total_inscritos'] = $rendimentos['total_inscritos'];

			if (isset($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['total_pontos']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['total_pontos']);

			unset($dado['rendimentos']['total_inscritos']);

			$dado['rendimentos_porcentagem'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

			$this->_dados[] = $dado;
		}

		// RENDIMENTO REDE
		$dado = $this->_modelo;
		$dado['escola'] = '<strong>REDE</strong>';
		$dado['rendimentos'] = $rendimentoRede;
		$dado['total_inscritos'] = $rendimentoRede['total_inscritos'];

		if (isset($this->_analizadorSimulado->rendimentoPorSerie[ ProvinhaBrasil::$serie->obterID() ]['total_pontos']))
			$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorSerie[ ProvinhaBrasil::$serie->obterID() ]['total_pontos']);

		unset($dado['rendimentos']['total_inscritos']);

		$dado['rendimentos_porcentagem'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

		$dado['_rede'] = true;

		$this->_dados[] = $dado;

		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'turma' ),
							 'linhas' => array(),
							 'config' => array() );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>