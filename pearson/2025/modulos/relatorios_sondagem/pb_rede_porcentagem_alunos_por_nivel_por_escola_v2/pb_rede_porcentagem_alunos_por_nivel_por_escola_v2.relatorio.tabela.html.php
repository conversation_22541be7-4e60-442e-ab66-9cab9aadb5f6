<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false
);

$tabela['titulo'] = 'Alunos por nível de alfabetização nas escolas da rede';//.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio())
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['escola']) { ?><th valign="top"><?= $this->obterBotaoAlterador('escola', false, 'Escola') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th valign="top" style="text-align: center;"><?= $this->obterBotaoAlterador('rendimento', false, 'Nível') ;?></th><? } ?>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?></th>
		<th style="text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?></th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	//$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'_'. $pagina_atual .'.png" border="0" />';

	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['escola']) { ?><td nowrap="nowrap" <?= @$d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>><?= @$d['escola']; ?></td><? } ?>
<?
	if ($this->_colunas['rendimento']) {
		$cor = ProvinhaBrasil::$NIVEL_5_COR;

		if ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_1){
			$cor = ProvinhaBrasil::$NIVEL_1_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_1_NOME;
 		}
		elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_2){
			$cor = ProvinhaBrasil::$NIVEL_2_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_2_NOME;
 		}
		elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_3){
			$cor = ProvinhaBrasil::$NIVEL_3_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_3_NOME;
 		}
		elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_4){
			$cor = ProvinhaBrasil::$NIVEL_4_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_4_NOME;
 		}
		else{
			$cor = ProvinhaBrasil::$NIVEL_5_COR;
 			$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
 		}

		$borda = '';//($cor == ProvinhaBrasil::$NIVEL_4_COR || $cor == ProvinhaBrasil::$NIVEL_5_COR ? 'border-left: 2px red solid; border-right: 2px red solid;' : '' );

		echo '<td style="'.$borda.'font-weight: bold; text-align: center; color: '. ProvinhaBrasil::COR_TEXTO .'; background-color: '. $cor .'">'. $nivel .'</td>';
	}
?>
		<td align="center" <?= @$d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>> <strong><?= @$d['rendimentos_porcentagem'][ProvinhaBrasil::$NIVEL_1]; ?>%</strong> ( <?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_1]; ?> ) </td>
		<td align="center" <?= @$d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>> <strong><?= @$d['rendimentos_porcentagem'][ProvinhaBrasil::$NIVEL_2]; ?>%</strong> ( <?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_2]; ?> ) </td>
		<td align="center" <?= @$d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>> <strong><?= @$d['rendimentos_porcentagem'][ProvinhaBrasil::$NIVEL_3]; ?>%</strong> ( <?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_3]; ?> ) </td>
		<td style="" align="center" <?= @$d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>> <strong><?= @$d['rendimentos_porcentagem'][ProvinhaBrasil::$NIVEL_4]; ?>%</strong> ( <?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_4]; ?> ) </td>
		<td style="" align="center" <?= @$d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?>> <strong><?= @$d['rendimentos_porcentagem'][ProvinhaBrasil::$NIVEL_5]; ?>%</strong> ( <?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_5]; ?> ) </td>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table width="100%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_1_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_2_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_3_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_4_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_5_TEXTO; ?></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
	</table>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>