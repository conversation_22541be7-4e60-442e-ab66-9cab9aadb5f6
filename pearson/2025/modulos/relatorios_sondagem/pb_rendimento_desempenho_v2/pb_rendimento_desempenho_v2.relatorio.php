<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemSondagem', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);

class RLPBRendimentoDesempenhoV2 extends RelatorioListagemSondagem {
	const POR_PAGINA = 29;

	protected $_dadosExtra = array();

	protected $_numeroAlunosPorNivel;

	protected $_turmas_mostrar = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true
		);

		$this->_modelo = array(
			'turma' => null,
			'nome' => null,
			'rendimento' => null,
			'_inscricao' => null,
			'_turma' => false,
		);

		$this->_config = array(
			'professor' => null,
			'diretor' => null
		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();

		$this->_numeroAlunosPorNivel = array(
			ProvinhaBrasil::$NIVEL_1 => 0,
			ProvinhaBrasil::$NIVEL_2 => 0,
			ProvinhaBrasil::$NIVEL_3 => 0,
			ProvinhaBrasil::$NIVEL_4 => 0,
			ProvinhaBrasil::$NIVEL_5 => 0
		);
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total_mostrar = array();
			foreach($this->_turmas_mostrar as $tID => $tNome) {
				$turma = $tNome->obterNome();
				foreach ( $this->_dados as $k => &$d ) {
					if ($d['turma'] == $turma && $d['rendimento'] !== NULL) {
						@$total_mostrar[$turma]++;
					}
				}
			}
			
			foreach($this->_turmas_mostrar as $tID => $tNome) {
				$turma = $tNome->obterNome();

				$total = $total_mostrar[$turma];
				$dados_mostrar = array();
				$mostrados = 0;
				
				foreach ( $this->_dados as $k => &$d ) {
					
					if ($d['turma'] == $turma) {
						$dados_mostrar[] = $k;
						$total--;
					}
					
					if (count($dados_mostrar) == $total_mostrar[$turma]) {
						$mostrados += count($dados_mostrar);
						include 'pb_rendimento_desempenho_v2.relatorio.tabela.html.php';
						$dados_mostrar = array();
					}
					
					else if (count($dados_mostrar) == self::POR_PAGINA) {
						$mostrados += count($dados_mostrar);
						include 'pb_rendimento_desempenho_v2.relatorio.tabela.html.php';
						$impresso = 1;
						$dados_mostrar = array();
					}
					
					else if (!$total && $mostrados < $total_mostrar[$turma]) {
						$mostrados += count($dados_mostrar);													   	 	 
						include 'pb_rendimento_desempenho_v2.relatorio.tabela.html.php';
						$dados_mostrar = array();
					}
					
				}
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total_mostrar = array();
			foreach($this->_turmas_mostrar as $tID => $tNome) {
				$turma = $tNome;
				foreach ( $this->_dados as $k => &$d ) {
					if ($d['turma'] == $turma && $d['rendimento'] !== NULL) {
						$total_mostrar[$turma]++;
					}
				}
			}
			
			foreach($this->_turmas_mostrar as $tID => $tNome) {
				$turma = $tNome;

				$total = $total_mostrar[$turma];
				$dados_mostrar = array();
				$mostrados = 0;
				
				foreach ( $this->_dados as $k => &$d ) {
					
					if ($d['turma'] == $turma) {
						$dados_mostrar[] = $k;
						$total--;
					}
					
					if (count($dados_mostrar) == $total_mostrar[$turma]) {
						$mostrados += count($dados_mostrar);
						include 'pb_rendimento_desempenho_v2.relatorio.tabela.html.php';
						$dados_mostrar = array();
					}
					
					else if (count($dados_mostrar) == self::POR_PAGINA) {
						$mostrados += count($dados_mostrar);
						include 'pb_rendimento_desempenho_v2.relatorio.tabela.html.php';
						$impresso = 1;
						$dados_mostrar = array();
					}
					
					else if (!$total && $mostrados < $total_mostrar[$turma]) {
						$mostrados += count($dados_mostrar);													   	 	 
						include 'pb_rendimento_desempenho_v2.relatorio.tabela.html.php';
						$dados_mostrar = array();
					}
					
				}
			}
			
			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {					
		if ($this->_config['diretor'] != null) {
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR &&
					Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ){
					$this->_config['diretor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			}else {
				if ( Core::registro('usuario') == null){
					Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Diretor inválido!');
				}else{
					$this->_config['diretor'] = Core::registro('usuario');
				}
			}

			$this->_config['professor'] = null;
		} else {
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR &&
					Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ){
					$this->_config['professor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			}else {
				if ( CarregadorUsuarioEspecifico::obterProfessor() == null ){
					Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Professor inválido!');
				}else{
					$this->_config['professor'] = CarregadorUsuarioEspecifico::obterProfessor();
				}
			}

			$this->_config['diretor'] = null;
			//$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmasComAulas(); 
			//if($simuID == null){
			//	$_ordenacao = new OrdenacaoPerfilFalso('perfil_falso'); 
			//	$valores = $_ordenacao->obterValoresDeOrdenacao(); 
			//
			//	$simuID = $valores['simulado']; 
			//	$turmID = $valores['turma']; 
			//
			//	$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmaPorAulaSimuladoTurma($simuID,$turmID);
			//}else{
				//$simuID = $_SESSION['_seletor_simulados.selecionado.relatorios_sondagem'];
				//$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmaPorSimulado($simuID);
			//}

			$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmaPorSimulado($this->_seletorSimulados->simulado->obterID());
		}		

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
		$this->_sugestao['nome'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		// coloca alunos sem notas depois
		$ids_alunos = array();
		foreach ($this->_dados as $k => &$d) {
			if ($d['rendimento'] === null)
				$ids_alunos[] = $k;
		}

		foreach($ids_alunos as $k) {
			if (isset($this->_dados[$k])) {
				$obj = $this->_dados[$k];
				unset($this->_dados[$k]);
				$this->_dados[] = $obj;
			}
		}
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_sondagem/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		$this->_analizadorSimulado->carregarInscritos();

		if ($this->_config['diretor'] != null)
			$this->_turmas_mostrar = $this->_analizadorSimulado->nomesTurmas;

		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();

		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			$tID = @$this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if ( !isset($this->_turmas_mostrar[$tID]) )
				continue;

			$dado = $this->_modelo;

			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
			$dado['_inscricao'] = $inscricao;

			if ( count($this->_analizadorSimulado->respostas[$iID]) )
				$dado['rendimento'] = $this->_analizadorSimulado->rendimentoGlobal[$iID]['total_pontos'];

			$this->_dados[] = $dado;
		}

		// INSERE ALUNO MÉDIA
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado->calcularRendimentoPorTurma();

		foreach($this->_turmas_mostrar as $tID => $tObj) {
			$dado = $this->_modelo;

			$tNome = $tObj->obterNome();

			$dado['nome'] = '<strong>'. (stristr($tNome, 'turma') === false ? 'Turma ' . $tNome : $tNome) .'</strong>';
			$dado['turma'] = $tNome;
			$dado['_inscricao'] = new Inscricao(99999999);
			$dado['_inscricao']->fixarAluno( new Aluno(99999999) );
			$dado['_turma'] = true;

			if (isset($this->_analizadorSimulado->rendimentoPorTurma[$tID]['total_pontos']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorTurma[$tID]['total_pontos']);

			$this->_dados[] = $dado;
		}


		// ACHA DADOS EXTRAS
		$rendimentoPorTurma = array();
		$rendimentoPorSerie = array();

		foreach ( $this->_analizadorSimulado->rendimentoGlobal as $iID => $desempenho ) {
			// não entra na média se for portador de necessidades especiais
			if ($this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
				continue;

			$tID = @$this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterTurma()->obterID();
			$sID = @$this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($rendimentoPorTurma[$tID]))
				$rendimentoPorTurma[$tID] = array(
					ProvinhaBrasil::$NIVEL_1 => 0,
					ProvinhaBrasil::$NIVEL_2 => 0,
					ProvinhaBrasil::$NIVEL_3 => 0,
					ProvinhaBrasil::$NIVEL_4 => 0,
					ProvinhaBrasil::$NIVEL_5 => 0,
					'total_inscritos' => 0
				);

			if (!isset($rendimentoPorSerie[$sID]))
				$rendimentoPorSerie[$sID] = array(
					ProvinhaBrasil::$NIVEL_1 => 0,
					ProvinhaBrasil::$NIVEL_2 => 0,
					ProvinhaBrasil::$NIVEL_3 => 0,
					ProvinhaBrasil::$NIVEL_4 => 0,
					ProvinhaBrasil::$NIVEL_5 => 0,
					'total_inscritos' => 0
				);

			$rendimentoPorTurma[$tID]['total_inscritos']++;
			$rendimentoPorSerie[$sID]['total_inscritos']++;

			if ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_1) {
				$rendimentoPorTurma[$tID][ProvinhaBrasil::$NIVEL_1]++;
				$rendimentoPorSerie[$sID][ProvinhaBrasil::$NIVEL_1]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_2) {
				$rendimentoPorTurma[$tID][ProvinhaBrasil::$NIVEL_2]++;
				$rendimentoPorSerie[$sID][ProvinhaBrasil::$NIVEL_2]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_3) {
				$rendimentoPorTurma[$tID][ProvinhaBrasil::$NIVEL_3]++;
				$rendimentoPorSerie[$sID][ProvinhaBrasil::$NIVEL_3]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_4) {
				$rendimentoPorTurma[$tID][ProvinhaBrasil::$NIVEL_4]++;
				$rendimentoPorSerie[$sID][ProvinhaBrasil::$NIVEL_4]++;
			} else {
				$rendimentoPorTurma[$tID][ProvinhaBrasil::$NIVEL_5]++;
				$rendimentoPorSerie[$sID][ProvinhaBrasil::$NIVEL_5]++;
			}
		}

		foreach( $rendimentoPorTurma as $tID => $rendimentos ) {
			$dado = array(
				'turma' => null,
				'rendimentos' => array(),
				'total_inscritos' => 0,
				'_serie' => false
			);
			$dado['turma'] = $this->_analizadorSimulado->nomesTurmas[$tID];
			$dado['rendimentos'] = $rendimentos;
			$dado['total_inscritos'] = $rendimentos['total_inscritos'];

			unset($dado['rendimentos']['total_inscritos']);

			$dado['rendimentos'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

			$this->_dadosExtra[] = $dado;
		}

		foreach( $rendimentoPorSerie as $sID => $rendimentos ) {
			$dado = array(
				'turma' => null,
				'rendimentos' => array(),
				'total_inscritos' => 0,
				'_serie' => false
			);
			$dado['turma'] = '<strong>ESCOLA</strong>'; //$this->_analizadorSimulado->nomesSeries[$sID];
			$dado['rendimentos'] = $rendimentos;
			$dado['total_inscritos'] = $rendimentos['total_inscritos'];
			$dado['_serie'] = true;

			unset($dado['rendimentos']['total_inscritos']);

			$dado['rendimentos'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

			$this->_dadosExtra[] = $dado;
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>