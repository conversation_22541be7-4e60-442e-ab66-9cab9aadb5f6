<? 
if (!defined('CORE_INCLUIDO')) exit();
	
$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'usar_largura_maxima_td_extra' => false,
	'td_extra' => null
);

$turma = $turma;//->obterNome();

$tabela['titulo'] = $this->_relatorio->obterNome();// .' - '. (stristr($turma, 'turma') === false ? 'Turma ' . $turma : $turma).' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong><br />
	<strong>%s</strong><br />
	<strong>Turma:</strong> %s',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio()),
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome(),
	$turma
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th><?= $this->obterBotaoAlterador('nome', false, 'Aluno') ;?></th><? } ?>
		<th style="text-align: center;width: 10%;" nowrap="nowrap"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </th>
		<th style="text-align: center;width: 10%;" nowrap="nowrap"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </th>
		<th style="text-align: center;width: 10%;" nowrap="nowrap"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </th>
		<th style="text-align: center;width: 10%;" nowrap="nowrap"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </th>
		<th style="text-align: center;width: 10%;" nowrap="nowrap"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
			
		if ($d['rendimento'] !== null) {
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td nowrap="nowrap" <?= $d['_inscricao']->obterID() == 99999999 ? 'bgcolor="#dddddd"' : ''; ?>><?= @$d['nome']; ?> <?= $d['_inscricao']->obterAluno()->obterPortadorNecessidade() ? '*' : ''; ?></td><? } ?>
<?
	//if ($d['rendimento'] !== null && $d['rendimento'] > 0)
		//$d['rendimento'] = (int) floor($d['rendimento']);

	ob_start();

	// NÍVEL 1
	if ($d['rendimento'] === null || $d['rendimento'] <= 0)
		echo '<td style="border-bottom-width: 0px; border-top-width: 0px">&nbsp;</td>';
	else {
		$tamanho = $d['rendimento'] < ProvinhaBrasil::$NIVEL_1 ? $d['rendimento'] * 100 / (ProvinhaBrasil::$NIVEL_1 - 1) : 100;
		echo '<td style="padding: 0px; border-bottom-width: 0px; border-top-width: 0px"><div style="height: 12px; background-color: '.ProvinhaBrasil::$NIVEL_1_COR.'; width: '. (int) $tamanho .'%">&nbsp;</div></td>';
	}

	// NÍVEL 2
	if ($d['rendimento'] === null || $d['rendimento'] <= ProvinhaBrasil::$NIVEL_1)
		echo '<td style="border-bottom-width: 0px; border-top-width: 0px">&nbsp;</td>';
	else {
		$tamanho = $d['rendimento'] > (ProvinhaBrasil::$NIVEL_1 - 1) ? ($d['rendimento'] < ProvinhaBrasil::$NIVEL_2 ? ($d['rendimento'] - ProvinhaBrasil::$NIVEL_1 + 1) * 100 / (ProvinhaBrasil::$NIVEL_2 - ProvinhaBrasil::$NIVEL_1) : 100) : 0;
		echo '<td style="padding: 0px; border-bottom-width: 0px; border-top-width: 0px"><div style="height: 12px; background-color: '.ProvinhaBrasil::$NIVEL_2_COR.'; width: '. (int) $tamanho .'%">&nbsp;</div></td>';
	}

	// NÍVEL 3
	$borda = $d['_turma'] ? '' : '';
	if ($d['rendimento'] === null || $d['rendimento'] <= ProvinhaBrasil::$NIVEL_2)
		echo '<td style="border-bottom-width: 0px; border-top-width: 0px; '.$borda.'">&nbsp;</td>';
	else {
		$tamanho = $d['rendimento'] > (ProvinhaBrasil::$NIVEL_2 - 1) ? ($d['rendimento'] < ProvinhaBrasil::$NIVEL_3 ? ($d['rendimento'] - ProvinhaBrasil::$NIVEL_2 + 1) * 100 / (ProvinhaBrasil::$NIVEL_3 - ProvinhaBrasil::$NIVEL_2) : 100) : 0;
		echo '<td style="padding: 0px; border-bottom-width: 0px; border-top-width: 0px; '.$borda.'"><div style="height: 12px; background-color: '.ProvinhaBrasil::$NIVEL_3_COR.'; width: '. (int) $tamanho .'%">&nbsp;</div></td>';
	}

	// NÍVEL 4
	if ($d['rendimento'] === null || $d['rendimento'] <= ProvinhaBrasil::$NIVEL_3)
		echo '<td style="border-bottom-width: 0px; border-top-width: 0px;">&nbsp;</td>';
	else {
		$tamanho = $d['rendimento'] > (ProvinhaBrasil::$NIVEL_3 - 1) ? ($d['rendimento'] < ProvinhaBrasil::$NIVEL_4 ? ($d['rendimento'] - ProvinhaBrasil::$NIVEL_3 + 1) * 100 / (ProvinhaBrasil::$NIVEL_4 - ProvinhaBrasil::$NIVEL_3) : 100) : 0;
		echo '<td style="padding: 0px; border-bottom-width: 0px; border-top-width: 0px;"><div style="height: 12px; background-color: '.ProvinhaBrasil::$NIVEL_4_COR.'; width: '. (int) $tamanho .'%">&nbsp;</div></td>';
	}

	// NÍVEL 5
	$borda = $d['_turma'] ? '' : '';
	if ($d['rendimento'] === null || $d['rendimento'] <= ProvinhaBrasil::$NIVEL_4)
		echo '<td style="border-bottom-width: 0px; border-top-width: 0px; '.$borda.'">&nbsp;</td>';
	else {
		$tamanho = $d['rendimento'] > (ProvinhaBrasil::$NIVEL_4 - 1) ? ($d['rendimento'] < ProvinhaBrasil::$NIVEL_5 ? ($d['rendimento'] - ProvinhaBrasil::$NIVEL_4 + 1) * 100 / (ProvinhaBrasil::$NIVEL_5 - ProvinhaBrasil::$NIVEL_4) : 100) : 0;
		echo '<td style="padding: 0px; border-bottom-width: 0px; border-top-width: 0px; '.$borda.'"><div style="height: 12px; background-color: '.ProvinhaBrasil::$NIVEL_5_COR.'; width: '. (int) $tamanho .'%">&nbsp;</div></td>';
	}

	$tmp_td = ob_get_clean();

	if ($i == count($dados_mostrar) - 1)
		$tmp_td = str_replace('border-bottom-width: 0px;', '', $tmp_td);

	echo $tmp_td;
?>
	</tr>
<?	} //fim do if de rendimento nulo
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div align="center" style="margin-top: 5px;">
	<?php
	if(Core::diretiva('RELATORIO:Exibir_Legenda_Alunos_Especiais') == '1'){
		echo"* alunos com necessidades especiais";
	}
	?>
	</div>

	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table width="100%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_1_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_2_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_3_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_4_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_5_TEXTO; ?></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
	</table>
	</div>
<?
$tabela['extra'] = ob_get_clean(); ob_start();

if ( count($this->_dadosExtra) ) {
	$i = 0;
	foreach ( $this->_dadosExtra as $k => &$d ) {
		if ( !$d['_serie'] && $turma != $d['turma'] /* && !in_array($d['turma'], $this->_turmas_mostrar)*/ )
			continue;

		if ( $i == 0 ) {
?>
		<tr>
		<th>Turma</th>
		<th style="padding-left: 7px; padding-right: 7px; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </th>
		<th style="padding-left: 7px; padding-right: 7px; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </th>
		<th style="padding-left: 7px; padding-right: 7px; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </th>
		<th style="padding-left: 7px; padding-right: 7px; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </th>
		<th style="padding-left: 7px; padding-right: 7px; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>; background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </th>
		</tr>
<?
		}
?>
	<tr <? if (true/*count($this->_turmas_mostrar) < 2 || $turma != $d['turma']*/) { ?>onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData"<? } else {?> bgcolor="#dddddd" <? } ?>>
		<td align="center"><?= @$d['turma']; ?></td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_1]; ?> %</td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_2]; ?> %</td>
		<td align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_3]; ?> %</td>
		<td <? if (!$d['_serie']): ?>style=""<? endif; ?> align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_4]; ?> %</td>
		<td <? if (!$d['_serie']): ?>style=""<? endif; ?> align="center"><?= @$d['rendimentos'][ProvinhaBrasil::$NIVEL_5]; ?> %</td>
	</tr>
<?
		$i++;
	}
}
$tabela['td_extra'] = ob_get_clean();
$relHTML[] = $tabela;

?>