<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false,//$this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => true,
	'quebra_invisivel' => true
);

$tabela['titulo'] = 'Domínio das habilidades'.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong><br />
	<strong>%s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio()),
	$instNome
);

ob_start();
?>
	<tr>
		<th rowspan="2" style="text-align: center; padding-left: 15px; padding-right: 15px;">&nbsp; </th>
<?
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ($d['_instituicao_id'] != $instID || $d['_escola']) continue;

		$cor = ''; //($i % 2) ? '#eeefff' : '';
?>
		<th colspan="3" style="background-color: <?= $cor; ?>; text-align: center; border-bottom-color: white;"><?= ProvinhaBrasil::obterTurmaComNome($d['nome']); ?></th>
<?
		$i++;
	}

?>
	</tr>
	<tr>
<?
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ($d['_instituicao_id'] != $instID || $d['_escola']) continue;
?>
		<th style="background-color: <?= ProvinhaBrasil::HAB_ADQUIRIDA_COR; ?>; padding-left: 4px; padding-right: 4px;">&nbsp;DE </th>
		<th style="background-color: <?= ProvinhaBrasil::HAB_EM_AQUISICAO_COR; ?>; padding-left: 4px; padding-right: 4px;">&nbsp;DV </th>
		<th style="background-color: <?= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_COR; ?>; padding-left: 4px; padding-right: 4px;">&nbsp;ND </th>
<?
		$i++;
	}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ) {
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td style="text-align: center; font-weight: bold;" nowrap="nowrap"><?= $hNome; ?> </td>
<?
		$j = 0;
		foreach ( $this->_dados as $k => &$d ) {
			if ($d['_instituicao_id'] != $instID || $d['_escola']) continue;

			$cor = ($j % 2) ? '#eeefff' : '';
?>
			<td style="background-color: <?= $cor; ?>; text-align: center;"><?= isset($d['habilidade'][$hID][ ProvinhaBrasil::HAB_ADQUIRIDA ]) ? $d['habilidade'][$hID][ ProvinhaBrasil::HAB_ADQUIRIDA ] : ''; ?> </td>
			<td style="background-color: <?= $cor; ?>; text-align: center;"><?= isset($d['habilidade'][$hID][ ProvinhaBrasil::HAB_EM_AQUISICAO ]) ? $d['habilidade'][$hID][ ProvinhaBrasil::HAB_EM_AQUISICAO ] : ''; ?> </td>
			<td style="background-color: <?= $cor; ?>; text-align: center;"><?= isset($d['habilidade'][$hID][ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]) ? $d['habilidade'][$hID][ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] : ''; ?> </td>
<?
			$j++;
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div align="center" style="margin-top: 5px;">Valores em porcentagem</div>

	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;" class="">
	<table width="100%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra" style="margin-bottom: 5px;">
		<tr>
			<td width="14px" style="background-color: <?= ProvinhaBrasil::HAB_ADQUIRIDA_COR; ?>; text-align: center;">DE </td>
			<td nowrap="nowrap"> alunos com a habilidade desenvolvida &nbsp;&nbsp;&nbsp;&nbsp; </td>
		</tr>
		<tr>
			<td width="14px" style="background-color: <?= ProvinhaBrasil::HAB_EM_AQUISICAO_COR; ?>; text-align: center;">DV </td>
			<td nowrap="nowrap"> alunos com a habilidade em desenvolvimento &nbsp;&nbsp;&nbsp;&nbsp; </td>
		</tr>
		<tr>
			<td width="14px" style="background-color: <?= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_COR; ?>; text-align: center;">ND </td>
			<td nowrap="nowrap"> alunos com a habilidade não desenvolvida </td>
		</tr>
		<? /* ?>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>

		<tr>
			<td colspan="2">Para a melhor compreensão dos resultados é necessário consultar o Guia de Correção disponibilizado pela Secretaria de Educação. </td>
		</tr>
		<? */ ?>
	</table>

<?
		foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ) {
			echo '<strong>'.$hNome.'</strong> - '.$this->_analizadorSimulado->descricoesHabilidades[$hID].'<br />';
		}
?>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>