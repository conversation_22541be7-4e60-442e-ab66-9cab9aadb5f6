<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => true,
	'quebra_invisivel' => true
);

$tabela['titulo'] = 'Alunos por nível de desenvolvimento na escola';//.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong><br />
	<strong>%s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio()),
	$instNome
);

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ($d['_instituicao_id'] != $instID || $d['_escola']) continue;

		$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'_'. $instID .'.png" border="0" />';

		$i++;
	}
}

$relHTML[] = $tabela;
?>