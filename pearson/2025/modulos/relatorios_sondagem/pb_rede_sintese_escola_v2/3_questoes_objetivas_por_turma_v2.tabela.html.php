<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => true,
	'quebra_invisivel' => true
);

$tabela['titulo'] = 'Rendimento nas questões objetivas'.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
/*
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong><br />
	<strong>%s</strong>',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio()),
	$instNome
);
*/

ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="10%" style="text-align: center;"><?= $this->obterBotaoAlterador('nome', false, 'Turma') ;?> </th><? } ?>
<?
	foreach ( $this->_titulosQuestoes as $qID => $questao )
		echo '<th style="text-align: center;" nowrap="nowrap">'. $questao['nome'] .' </th>';
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ($d['_instituicao_id'] != $instID || $d['_escola']) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td align="center" nowrap="nowrap"><?= @$d['nome']; ?> </td><? } ?>
<?
		foreach ( $this->_titulosQuestoes as $qID => $questao )
			echo '<td align="center">'. @$d['rendimento_por_questao'][$qID] .' </td>';
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div align="center" style="margin-top: 5px;">Valores em porcentagem</div>
<?
$tabela['extra'] = ob_get_clean(); /*ob_start();

if ( count($this->_dadosExtra) ) {
	$i = 0;
	foreach ( $this->_dadosExtra as $k => &$d ) {
		//if (!in_array($k, $dados_mostrar)) continue;
?>
		<tr>
			<th rowspan="4" valign="middle" nowrap="nowrap"><?= $d['turma']; ?> </th>
			<th >&nbsp; </th>
			<th align="center">A</th>
			<th align="center">B</th>
			<th align="center">C</th>
			<th align="center">D</th>
			<th align="center">E</th>
			<th align="center">F</th>
			<th align="center">em branco</th>
		</tr>

		<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<td align="center" width="30px" nowrap="nowrap"><strong>25</strong></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['A']; ?></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['B']; ?></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['C']; ?></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['D']; ?></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['E']; ?></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['F']; ?></td>
			<td align="center" width="30px" nowrap="nowrap"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_25 ]['em_branco']; ?></td>
		</tr>

		<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<td align="center"><strong>26</strong></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['A']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['B']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['C']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['D']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['E']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['F']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_26 ]['em_branco']; ?></td>
		</tr>

		<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<td align="center"><strong>27</strong></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_27 ]['A']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_27 ]['B']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_27 ]['C']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_27 ]['D']; ?></td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_27 ]['E']; ?></td>
			<td align="center">&nbsp;</td>
			<td align="center"><?= (int) @$d[ ProvinhaBrasil::QUESTAO_27 ]['em_branco']; ?></td>
		</tr>
<?
		$i++;
	}
}
$tabela['td_extra'] = ob_get_clean();*/
$relHTML[] = $tabela;
?>