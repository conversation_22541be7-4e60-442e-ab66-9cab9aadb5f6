<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemSondagem', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);

class RLPBRedeSinteseEscolaV2 extends RelatorioListagemSondagem {
	const GRAFICO_HAB_NAO_ADQUIRIDA_COR = ProvinhaBrasil::HAB_NAO_ADQUIRIDA_COR; // '#eeeeee';
	const GRAFICO_HAB_EM_AQUISICAO_COR = ProvinhaBrasil::HAB_EM_AQUISICAO_COR; // '#cccccc';
	const GRAFICO_HAB_ADQUIRIDA_COR = ProvinhaBrasil::HAB_ADQUIRIDA_COR; // '#999999';

	const GRAFICO_HAB_NAO_ADQUIRIDA_TEXTO_COR = '#ffffff';
	const GRAFICO_HAB_EM_AQUISICAO_TEXTO_COR = '#ffffff';
	const GRAFICO_HAB_ADQUIRIDA_TEXTO_COR = '#ffffff';


	const TOTAL = 1;
	const PARTICIPANTES = 2;
	const AUSENTES = 3;

	protected $_por_pagina = 28;

	protected $_totalInscritosIniciaisPorEscola = array();

	protected $_dados_habilidades = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimentos' => array(),
			'rendimentos_porcentagem' => array(),
			'rendimento_por_questao' => array(),
			'participacao' => array(),
			'habilidade' => array(),
			'habilidade_abs' => array(),
			'_escola' => false,
			'_instituicao_id' > null,
			'_turma_id' => null
		);

		$this->_config = array(
			'diretor' => null
		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			foreach($this->_analizadorSimulado->nomesInstituicoes as $instID => $instNome) {
				include '1_grafico_niveis_na_escola_v2.tabela.html.php';
				include '2_porcentagens_por_turma_v2.tabela.html.php';
				//include '3_questoes_objetivas_por_turma_v2.tabela.html.php';
				//include '4_questoes_escrever_por_turma.tabela.html.php';
				//include '5_habilidades_por_turma.tabela.html.php';
				//include '6_participacao_por_turma_v2.tabela.html.php';
				if (TRUE || $this->_config['diretor'] || $this->obterModoVisualizacao() != self::PDF) {
					foreach ( $this->_dados as &$d_superior ) {
						if ($d_superior['_instituicao_id'] != $instID || $d_superior['_escola']) continue;

						$total = 0;
						foreach ( $this->_dados_habilidades as &$d ) {
							if ($d['_turma_id'] == $d_superior['_turma_id']  && $d['rendimento'] !== NULL)
								$total++;
						}
						
						$total_mostrar = $total;
						$dados_mostrar = array();
						$mostrados = array();
						foreach ( $this->_dados_habilidades as $sk => &$d ) {
							if ($d['_turma_id'] != $d_superior['_turma_id'] && $d['rendimento'] !== NULL)
								continue;

							$dados_mostrar[] = $sk;
							
							$total--;

							if (count($dados_mostrar) == $total_mostrar) {
								$mostrados[$d['_turma_id']] += count($dados_mostrar);
								//include 'pb_habilidades_por_aluno_v2.relatorio.tabela.html.php';
								$dados_mostrar = array();
							}

							else if (count($dados_mostrar) == $this->_por_pagina) {
								$mostrados[$d['_turma_id']]  += count($dados_mostrar);
								//include 'pb_habilidades_por_aluno_v2.relatorio.tabela.html.php';
								$dados_mostrar = array();
							}

							else if (!$total && $mostrados[$d['_turma_id']] < $total_mostrar) { 
								$mostrados[$d['_turma_id']] += count($dados_mostrar);													   	 	 
								//include 'pb_habilidades_por_aluno_v2.relatorio.tabela.html.php';
								$dados_mostrar = array();
							}		
						}
					}
				}
				//include '7_graficos_habilidades_por_turma.tabela.html.php';
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF();
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_obterDados();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			foreach($this->_analizadorSimulado->nomesInstituicoes as $instID => $instNome) {
				include '1_grafico_niveis_na_escola_v2.tabela.html.php';
				include '2_porcentagens_por_turma_v2.tabela.html.php';
				//include '3_questoes_objetivas_por_turma_v2.tabela.html.php';
				//include '4_questoes_escrever_por_turma.tabela.html.php';
				//include '5_habilidades_por_turma.tabela.html.php';
				//include '6_participacao_por_turma_v2.tabela.html.php';
				if (TRUE || $this->_config['diretor'] || $this->obterModoVisualizacao() != self::PDF) {
					foreach ( $this->_dados as &$d_superior ) {
						if ($d_superior['_instituicao_id'] != $instID || $d_superior['_escola']) continue;

						$total = 0;
						foreach ( $this->_dados_habilidades as &$d ) {
							if ($d['_turma_id'] == $d_superior['_turma_id']  && $d['rendimento'] !== NULL)
								$total++;
						}
						
						$total_mostrar = $total;
						$dados_mostrar = array();
						$mostrados = array();
						foreach ( $this->_dados_habilidades as $sk => &$d ) {
							if ($d['_turma_id'] != $d_superior['_turma_id'] && $d['rendimento'] !== NULL)
								continue;

							$dados_mostrar[] = $sk;
							
							$total--;

							if (count($dados_mostrar) == $total_mostrar) {
								$mostrados[$d['_turma_id']] += count($dados_mostrar);
								//include 'pb_habilidades_por_aluno_v2.relatorio.tabela.html.php';
								$dados_mostrar = array();
							}

							else if (count($dados_mostrar) == $this->_por_pagina) {
								$mostrados[$d['_turma_id']]  += count($dados_mostrar);
								//include 'pb_habilidades_por_aluno_v2.relatorio.tabela.html.php';
								$dados_mostrar = array();
							}

							else if (!$total && $mostrados[$d['_turma_id']] < $total_mostrar) { 
								$mostrados[$d['_turma_id']] += count($dados_mostrar);													   	 	 
								//include 'pb_habilidades_por_aluno_v2.relatorio.tabela.html.php';
								$dados_mostrar = array();
							}		
						}
					}
				}
				//include '7_graficos_habilidades_por_turma.tabela.html.php';
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		if ($this->_config['diretor'] != null) {
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR &&
					Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
					$this->_config['diretor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			else {
				if ( Core::registro('usuario') == null)
					Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Diretor inválido!');
				else
					$this->_config['diretor'] = Core::registro('usuario');
			}
		}

		$this->_sugestao['nome'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		// posiciona corretamente
		$idsEscolas = array();
		foreach ($this->_dados as $k => &$d) {
			if ($d['_escola']) $idsEscolas[] = $k;
		}

		foreach ($idsEscolas as $id) {
			if (isset($this->_dados[$id])) {
				$dado = $this->_dados[$id];
				unset($this->_dados[$id]);
				array_push($this->_dados, $dado);
			}
		}


		// ordena alunos
		$this->_ordenacao->multiplaOrdenacao = array(
			'rendimento' => Ordenacao::ASC,
			'nome' => Ordenacao::ASC
		);

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados_habilidades) )
			usort($this->_dados_habilidades, array($this, '_ordenarDados'));

		// coloca alunos sem notas depois
		$ids_alunos = array();
		foreach ($this->_dados_habilidades as $k => &$d) {
			if ($d['rendimento'] === null)
				$ids_alunos[] = $k;
		}

		foreach($ids_alunos as $k) {
			if (isset($this->_dados_habilidades[$k])) {
				$obj = $this->_dados_habilidades[$k];
				unset($this->_dados_habilidades[$k]);
				$this->_dados_habilidades[] = $obj;
			}
		}

		$this->gerarGrafico();
		$this->gerarGraficoHabilidades();
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_sondagem/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if ( $this->_config['diretor'] !== null )
			$this->_tituloCache .= '_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			//$this->_dados = &$tempCache['dados'];
			//$this->_titulosQuestoes = &$tempCache['_titulosQuestoes'];
			//$this->_totalInscritosIniciaisPorEscola = &$tempCache['_totalInscritosIniciaisPorEscola'];
			//return;
		}


		$this->_analizadorSimulado->carregarInscritos( $this->_config['diretor'] == null );
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorHabilidadeDosInscritos();

		// PARTICIPAÇÃO
		$participacaoPorTurma = array();
		$participacaoPorEscola = array();

		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			$tID = @$inscricao->obterAluno()->obterTurma()->obterID();
			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($participacaoPorTurma[$tID]))
				$participacaoPorTurma[$tID] = array(
					self::TOTAL => array(),
					self::PARTICIPANTES => 0,
					self::AUSENTES => 0
				);

			if (!isset($participacaoPorEscola[$instID]))
				$participacaoPorEscola[$instID] = array(
					self::TOTAL => array(),
					self::PARTICIPANTES => 0,
					self::AUSENTES => 0
				);

			$participacaoPorTurma[$tID][ self::TOTAL ][] = $iID;
			$participacaoPorEscola[$instID][ self::TOTAL ][] = $iID;

			if (!isset($this->_totalInscritosIniciaisPorEscola[$instID]))
				$this->_totalInscritosIniciaisPorEscola[$instID] = 0;

			$this->_totalInscritosIniciaisPorEscola[$instID]++;
		}

		// HABILIDADES POR ALUNO
		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			$dado = array(
				'turma' => null,
				'nome' => null,
				'rendimento' => null,
				'rendimento_por_habilidade' => array(),
				'_inscricao' => null,
				'_turma_id' => null,
				'_turma' => false
			);

			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
			$dado['_turma_id'] = @$inscricao->obterAluno()->obterTurma()->obterID();
			$dado['_inscricao'] = $inscricao;

			if ( count($this->_analizadorSimulado->respostas[$iID]) ) {
				$dado['rendimento'] = $this->_analizadorSimulado->rendimentoGlobal[$iID]['total_pontos'];

				$dado['rendimento_por_habilidade'] = $this->_analizadorSimulado->rendimentoPorHabilidade[$iID];
			}

			$this->_dados_habilidades[] = $dado;
		}

		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado->calcularRendimentoPorTurmaPorQuestao();
		$this->_analizadorSimulado->calcularRendimentoPorInstituicaoPorQuestao();
		$this->_analizadorSimulado->calcularRendimentoPorTurma();
		$this->_analizadorSimulado->calcularRendimentoPorInstituicao();

		foreach ($participacaoPorTurma as $tID => &$participacao) {
			foreach($participacao[self::TOTAL] as $iID) {
				if (!array_key_exists($iID, $this->_analizadorSimulado->inscritos))
					$participacao[self::AUSENTES]++;
			}

			$participacao[self::TOTAL] = count($participacao[self::TOTAL]);
			$participacao[self::PARTICIPANTES] = $participacao[self::TOTAL] - $participacao[self::AUSENTES];
		}

		foreach ($participacaoPorEscola as $instID => &$participacao) {
			foreach($participacao[self::TOTAL] as $iID) {
				if (!array_key_exists($iID, $this->_analizadorSimulado->inscritos))
					$participacao[self::AUSENTES]++;
			}

			$participacao[self::TOTAL] = count($participacao[self::TOTAL]);
			$participacao[self::PARTICIPANTES] = $participacao[self::TOTAL] - $participacao[self::AUSENTES];
		}


		// RENDIMENTOS QUESTOES DE ESCREVER
		$marcacoesPorTurma = array();
		$marcacoesPorEscola = array();

		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			// não entra nas marcacoes se for portador de necessidades especiais
			if ($inscricao->obterAluno()->obterPortadorNecessidade())
				continue;

			$tID = @$inscricao->obterAluno()->obterTurma()->obterID();
			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();


			if (!isset($marcacoesPorTurma[$tID]))
				$marcacoesPorTurma[$tID] = array(
					ProvinhaBrasil::QUESTAO_25 => array(),
					ProvinhaBrasil::QUESTAO_26 => array(),
					ProvinhaBrasil::QUESTAO_27 => array(),
					'total_inscritos' => 0
				);

			if (!isset($marcacoesPorEscola[$instID]))
				$marcacoesPorEscola[$instID] = array(
					ProvinhaBrasil::QUESTAO_25 => array(),
					ProvinhaBrasil::QUESTAO_26 => array(),
					ProvinhaBrasil::QUESTAO_27 => array(),
					'total_inscritos' => 0
				);

			$marcacoesPorTurma[$tID]['total_inscritos']++;
			$marcacoesPorEscola[$instID]['total_inscritos']++;


			// encontra a marcação para cada questão
			if ( isset($this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_25 ]['valor']) &&
					isset(MultiplaEscolha::$opcoes[ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_25 ]['valor'] ]) ) {
				@$marcacoesPorTurma[$tID][ ProvinhaBrasil::QUESTAO_25 ][ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_25 ]['valor'] ]++;
				@$marcacoesPorEscola[$instID][ ProvinhaBrasil::QUESTAO_25 ][ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_25 ]['valor'] ]++;
			} else {
				@$marcacoesPorTurma[$tID][ ProvinhaBrasil::QUESTAO_25 ][ 'em_branco' ]++;
				@$marcacoesPorEscola[$instID][ ProvinhaBrasil::QUESTAO_25 ][ 'em_branco' ]++;
			}

			if ( isset($this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_26 ]['valor']) &&
					isset(MultiplaEscolha::$opcoes[ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_26 ]['valor'] ]) ) {
				@$marcacoesPorTurma[$tID][ ProvinhaBrasil::QUESTAO_26 ][ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_26 ]['valor'] ]++;
				@$marcacoesPorEscola[$instID][ ProvinhaBrasil::QUESTAO_26 ][ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_26 ]['valor'] ]++;
			} else {
				@$marcacoesPorTurma[$tID][ ProvinhaBrasil::QUESTAO_26 ][ 'em_branco' ]++;
				@$marcacoesPorEscola[$instID][ ProvinhaBrasil::QUESTAO_26 ][ 'em_branco' ]++;
			}

			if ( isset($this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_27 ]['valor']) &&
					isset(MultiplaEscolha::$opcoes[ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_27 ]['valor'] ]) ) {
				@$marcacoesPorTurma[$tID][ ProvinhaBrasil::QUESTAO_27 ][ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_27 ]['valor'] ]++;
				@$marcacoesPorEscola[$instID][ ProvinhaBrasil::QUESTAO_27 ][ (int) $this->_analizadorSimulado->respostas[ $iID ][ ProvinhaBrasil::QUESTAO_27 ]['valor'] ]++;
			} else {
				@$marcacoesPorTurma[$tID][ ProvinhaBrasil::QUESTAO_27 ][ 'em_branco' ]++;
				@$marcacoesPorEscola[$instID][ ProvinhaBrasil::QUESTAO_27 ][ 'em_branco' ]++;
			}
		}

		foreach( $this->_analizadorSimulado->simuladoQuestoesPorID as $qID => $questao ) {
			if ($questao->obterPontosQuestao() > 0)
				$this->_titulosQuestoes[$qID] = array('nome' => $questao->obterIdentificador(), 'enunciado' => $questao->obterEnunciado() );
		}

		$porcentagemPorTurma = array();
		$porcentagemPorEscola = array();

		foreach ( $this->_analizadorSimulado->rendimentoGlobal as $iID => $desempenho ) {
			// não entra na média se for portador de necessidades especiais
			if ($this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
				continue;

			$tID = @$this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterTurma()->obterID();
			$instID = @$this->_analizadorSimulado->inscritos[$iID]->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($porcentagemPorTurma[$tID]))
				$porcentagemPorTurma[$tID] = array(
					ProvinhaBrasil::$NIVEL_1 => 0,
					ProvinhaBrasil::$NIVEL_2 => 0,
					ProvinhaBrasil::$NIVEL_3 => 0,
					ProvinhaBrasil::$NIVEL_4 => 0,
					ProvinhaBrasil::$NIVEL_5 => 0,
					'total_inscritos' => 0
				);

			if (!isset($porcentagemPorEscola[$instID]))
				$porcentagemPorEscola[$instID] = array(
					ProvinhaBrasil::$NIVEL_1 => 0,
					ProvinhaBrasil::$NIVEL_2 => 0,
					ProvinhaBrasil::$NIVEL_3 => 0,
					ProvinhaBrasil::$NIVEL_4 => 0,
					ProvinhaBrasil::$NIVEL_5 => 0,
					'total_inscritos' => 0
				);

			$porcentagemPorTurma[$tID]['total_inscritos']++;
			$porcentagemPorEscola[$instID]['total_inscritos']++;

			if ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_1) {
				$porcentagemPorTurma[$tID][ProvinhaBrasil::$NIVEL_1]++;
				$porcentagemPorEscola[$instID][ProvinhaBrasil::$NIVEL_1]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_2) {
				$porcentagemPorTurma[$tID][ProvinhaBrasil::$NIVEL_2]++;
				$porcentagemPorEscola[$instID][ProvinhaBrasil::$NIVEL_2]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_3) {
				$porcentagemPorTurma[$tID][ProvinhaBrasil::$NIVEL_3]++;
				$porcentagemPorEscola[$instID][ProvinhaBrasil::$NIVEL_3]++;
			} elseif ($desempenho['total_pontos'] <= ProvinhaBrasil::$NIVEL_4) {
				$porcentagemPorTurma[$tID][ProvinhaBrasil::$NIVEL_4]++;
				$porcentagemPorEscola[$instID][ProvinhaBrasil::$NIVEL_4]++;
			} else {
				$porcentagemPorTurma[$tID][ProvinhaBrasil::$NIVEL_5]++;
				$porcentagemPorEscola[$instID][ProvinhaBrasil::$NIVEL_5]++;
			}
		}

		// HABILIDADES
		$habilidadesPorTurma = $habilidadesPorTurmaAbs = array();

		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			// não entra nas marcacoes se for portador de necessidades especiais
			if ($inscricao->obterAluno()->obterPortadorNecessidade())
				continue;

			$tID = @$inscricao->obterAluno()->obterTurma()->obterID();

			if (!isset($habilidadesPorTurma[$tID]))
				$habilidadesPorTurma[$tID] = array();

			foreach($this->_analizadorSimulado->rendimentoPorHabilidade[$iID] as $hID => $hRendimento) {
				if (!isset($habilidadesPorTurma[$tID][$hID])) {
					$habilidadesPorTurma[$tID][$hID] = array(
						ProvinhaBrasil::HAB_ADQUIRIDA => 0,
						ProvinhaBrasil::HAB_EM_AQUISICAO => 0,
						ProvinhaBrasil::HAB_NAO_ADQUIRIDA => 0
					);
				}

				$hab = ProvinhaBrasil::HAB_NAO_ADQUIRIDA;

				if ($hRendimento['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM)
					$hab = ProvinhaBrasil::HAB_ADQUIRIDA;
				elseif ($hRendimento['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM)
					$hab = ProvinhaBrasil::HAB_EM_AQUISICAO;

				$habilidadesPorTurma[$tID][$hID][$hab]++;
			}
		}

		foreach($habilidadesPorTurma as $tID => $hTurmas) {
			foreach($hTurmas as $hID => $hPorcentagens) {
				$habilidadesPorTurmaAbs[$tID][$hID] = $hPorcentagens;
				$habilidadesPorTurma[$tID][$hID] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $hPorcentagens );
			}
		}

		// TURMAS
		foreach( $porcentagemPorTurma as $tID => $porcentagens ) {
			$dado = $this->_modelo;
			$dado['nome'] = $this->_analizadorSimulado->nomesTurmas[$tID];
			$dado['rendimentos'] = $porcentagens;
			$dado['total_inscritos'] = $porcentagens['total_inscritos'];
			$dado['_instituicao_id'] = $this->_analizadorSimulado->instituicoesPorTurma[$tID];
			$dado['_turma_id'] = $tID;

			if (isset($participacaoPorTurma[$tID]))
				$dado['participacao'] = $participacaoPorTurma[$tID];

			if (isset($this->_analizadorSimulado->rendimentoPorTurma[$tID]['total_pontos']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorTurma[$tID]['total_pontos']);

			unset($dado['rendimentos']['total_inscritos']);

			$dado['rendimentos_porcentagem'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

			// RENDIMENTO POR QUESTÃO
			if (isset($this->_analizadorSimulado->rendimentoPorTurmaPorQuestao[$tID])) {
				foreach($this->_analizadorSimulado->rendimentoPorTurmaPorQuestao[$tID] as $qID => $desempenho)
					$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
			}

			// RENDIMENTO QUESTOES DE ESCREVER
			$dado['rendimento_por_questao'][ProvinhaBrasil::QUESTAO_25] = array();
			$dado['rendimento_por_questao'][ProvinhaBrasil::QUESTAO_26] = array();
			$dado['rendimento_por_questao'][ProvinhaBrasil::QUESTAO_27] = array();

			if ( $dado['total_inscritos'] > 0 ) {
				unset($marcacoesPorTurma[$tID]['total_inscritos']);
				foreach ($marcacoesPorTurma[$tID] as $mQ => $mV) {
					$marcacoesPorTurma[$tID][$mQ] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $mV );

					foreach ($marcacoesPorTurma[$tID][$mQ] as $item => $num_marcacoes) {
						if ($item != 'em_branco')
							$dado['rendimento_por_questao'][$mQ][ MultiplaEscolha::$opcoes[$item] ] = $num_marcacoes;
						else
							$dado['rendimento_por_questao'][$mQ][ 'em_branco' ] = $num_marcacoes;
					}

					if (!isset($dado['rendimento_por_questao'][$mQ]['em_branco']))
						$dado['rendimento_por_questao'][$mQ]['em_branco'] = 0;
				}
			}

			// HABILIDADES
			$dado['habilidade'] = $habilidadesPorTurma[$tID];
			$dado['habilidade_abs'] = $habilidadesPorTurmaAbs[$tID];

			$this->_dados[] = $dado;
		}

		// ESCOLAS
		foreach( $porcentagemPorEscola as $instID => $porcentagens ) {
			$dado = $this->_modelo;
			$dado['nome'] = 'Escola'; //$this->_analizadorSimulado->nomesInstituicoes[$instID];
			$dado['rendimentos'] = $porcentagens;
			$dado['total_inscritos'] = $porcentagens['total_inscritos'];
			$dado['_escola'] = true;
			$dado['_instituicao_id'] = $instID;

			if (isset($participacaoPorEscola[$instID]))
				$dado['participacao'] = $participacaoPorEscola[$instID];

			if (isset($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['total_pontos']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['total_pontos']);

			unset($dado['rendimentos']['total_inscritos']);

			$dado['rendimentos_porcentagem'] = RelatorioListagemSondagem::calcularPorcentagemPerfeita( $dado['rendimentos'] );

			/*
			// RENDIMENTO POR QUESTÃO
			if (isset($this->_analizadorSimulado->rendimentoPorInstituicaoPorQuestao[$instID])) {
				foreach($this->_analizadorSimulado->rendimentoPorInstituicaoPorQuestao[$instID] as $qID => $desempenho)
					$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
			}
			*/

			$this->_dados[] = $dado;
		}

		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados,
			'_titulosQuestoes' => &$this->_titulosQuestoes,
			'_totalInscritosIniciaisPorEscola' => $this->_totalInscritosIniciaisPorEscola
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_pie.php';
		require_once 'jpgraph/jpgraph_pie3d.php';


		foreach($this->_analizadorSimulado->nomesInstituicoes as $instID => $instNome) {

				// 260
				$grafico = new PieGraph(700, 360, $this->_tituloCache.'_'.$instID.'.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetAntiAliasing(true);
				$grafico->setFrame(false);

				$dados = array();
				foreach($this->_dados as &$d) {
					if ($d['_escola'] && $d['_instituicao_id'] == $instID) {
						/*
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_1];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_5];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_4];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_3];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_2];
						*/
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_1];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_2];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_3];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_4];
						$dados[] = $d['rendimentos'][ProvinhaBrasil::$NIVEL_5];

						break;
					}
				}

				if (!array_sum($dados))
					continue;


				$labels = array(
					$dados[0] .' (%d%%)',
					$dados[1] .' (%d%%)',
					$dados[2] .' (%d%%)',
					$dados[3] .' (%d%%)',
					$dados[4] .' (%d%%)',
				);

				$labels = array(
					ProvinhaBrasil::$NIVEL_1_NOME . "\n". $dados[0] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_2_NOME . "\n". $dados[1] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_3_NOME . "\n". $dados[2] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_4_NOME . "\n". $dados[3] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_5_NOME . "\n". $dados[4] .' (%d%%)'
				);

				/*
				$labels = array(
					ProvinhaBrasil::$NIVEL_1_NOME . "\n". $dados[0] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_5_NOME . "\n". $dados[1] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_4_NOME . "\n". $dados[2] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_3_NOME . "\n". $dados[3] .' (%d%%)',
					ProvinhaBrasil::$NIVEL_2_NOME . "\n". $dados[4] .' (%d%%)'
				);
				*/
/*
				$legend = array(
					ProvinhaBrasil::$NIVEL_1_NOME,
					ProvinhaBrasil::$NIVEL_5_NOME,
					ProvinhaBrasil::$NIVEL_4_NOME,
					ProvinhaBrasil::$NIVEL_3_NOME,
					ProvinhaBrasil::$NIVEL_2_NOME
				);

				$legend = array(
					ProvinhaBrasil::$NIVEL_1_NOME,
					ProvinhaBrasil::$NIVEL_2_NOME,
					ProvinhaBrasil::$NIVEL_3_NOME,
					ProvinhaBrasil::$NIVEL_4_NOME,
					ProvinhaBrasil::$NIVEL_5_NOME
				);*/

				$dados_porcentagem = RelatorioListagemSondagem::calcularPorcentagemPerfeita($dados);
				foreach ($dados_porcentagem as $k => $v) {
					if ($v < 15)
						$labels[$k] = str_replace("\n", ' - ', $labels[$k]).' ';
				}

				if (@$dados_porcentagem[0] > 1 && @$dados_porcentagem[1] < 20)
					$labels[1] = "\n\n".$labels[1];

				if (@$dados_porcentagem[0] == 0 && @$dados_porcentagem[1] < 10 && @$dados_porcentagem[2] < 20)
					$labels[2] = "\n\n".$labels[2];

				if (@$dados_porcentagem[0] > 0 && @$dados_porcentagem[1] < 15 && @$dados_porcentagem[2] < 15)
					$labels[2] = "\n\n".$labels[2];

				$labels[0] = $labels[0]."\n";

				/*
				$labelsCor = array(
					ProvinhaBrasil::$NIVEL_1_COR,
					ProvinhaBrasil::$NIVEL_5_COR,
					ProvinhaBrasil::$NIVEL_4_COR,
					ProvinhaBrasil::$NIVEL_3_COR,
					ProvinhaBrasil::$NIVEL_2_COR
				);
				*/

				$labelsCor = array(
					ProvinhaBrasil::$NIVEL_1_COR,
					ProvinhaBrasil::$NIVEL_2_COR,
					ProvinhaBrasil::$NIVEL_3_COR,
					ProvinhaBrasil::$NIVEL_4_COR,
					ProvinhaBrasil::$NIVEL_5_COR
				);

				foreach($dados as $k => $v) {
					if ($v == 0) {
						unset($dados[$k]);
						unset($labels[$k]);
						//unset($legend[$k]);
						unset($labelsCor[$k]);
					}
				}

				$dados = array_reverse($dados);
				$labels = array_reverse($labels);
				$labelsCor = array_reverse($labelsCor);
				//$legend = array_reverse($legend);

				$pie = new PiePlot( $dados );
				$pie->SetGuideLines(true, true, true);
				$pie->SetGuideLinesAdjust(3.1);
				$pie->value->SetFont(FF_ARIAL,FS_BOLD, 10);
				$pie->value->SetColor( '#000000' );//ProvinhaBrasil::COR_TEXTO );
				$pie->SetLabelType(PIE_VALUE_ADJPER);
				$pie->SetLabels( $labels, 1.0 );
				$pie->SetSliceColors( $labelsCor );
				$pie->SetSize(0.3);

				if (count($dados) > 1)
					$pie->SetStartAngle(90);

				$grafico->Add($pie);
				$grafico->Stroke();
		}
	}

	protected function gerarGraficoHabilidades() {
		return;

		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		foreach($this->_dados as $d) {
			if ($d['_escola'] || $d['_turma_id'] == null) continue;

				$grafico = new Graph( 700 , 180, $this->_tituloCache.'_hab_'.$d['_turma_id'].'.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetMargin(34, 0, 10, 30);
				$grafico->setFrame(false);
				//$grafico->title->Set('REDE');
				//$grafico->SetMarginColor('white');
				$grafico->SetScale('textint', 0, 100, 0, 0);
				$grafico->yaxis->scale->ticks->Set(20);
				$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$grafico->yaxis->SetLabelFormat('%d%%');
				//$grafico->yaxis->title->Set('Quantidade de alunos (%)');
				$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
				$grafico->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);
				//$grafico->Set90AndMargin(140, 5, 5, 5);

				$labelsX = $barras = array();
				$dadosProporcoes = array(
					ProvinhaBrasil::HAB_ADQUIRIDA => array(),
					ProvinhaBrasil::HAB_EM_AQUISICAO => array(),
					ProvinhaBrasil::HAB_NAO_ADQUIRIDA => array()
				);

				foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ) {
					if (!isset($d['habilidade'][$hID]))
						continue;

					$labelsX[] = $hNome;
					$dadosProporcoes[ ProvinhaBrasil::HAB_ADQUIRIDA ][] = $d['habilidade'][$hID][ ProvinhaBrasil::HAB_ADQUIRIDA ];
					$dadosProporcoes[ ProvinhaBrasil::HAB_EM_AQUISICAO ][] = $d['habilidade'][$hID][ ProvinhaBrasil::HAB_EM_AQUISICAO ];
					$dadosProporcoes[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ][] = $d['habilidade'][$hID][ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ];
				}

				$grafico->xaxis->SetTickLabels($labelsX);

				$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_ADQUIRIDA ] );
				$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ]->SetFillColor(self::GRAFICO_HAB_ADQUIRIDA_COR);

				$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_EM_AQUISICAO ] );
				$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ]->SetFillColor(self::GRAFICO_HAB_EM_AQUISICAO_COR);

				$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] );
				$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]->SetFillColor(self::GRAFICO_HAB_NAO_ADQUIRIDA_COR);

				$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ]->value->SetColor( self::GRAFICO_HAB_ADQUIRIDA_TEXTO_COR );
				$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ]->value->SetColor( self::GRAFICO_HAB_EM_AQUISICAO_TEXTO_COR );
				$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]->value->SetColor( self::GRAFICO_HAB_NAO_ADQUIRIDA_TEXTO_COR );

				foreach ($barras as &$barra) {
					$barra->value->Show();
					$barra->value->SetFormat('%d%%');
					//$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
					$barra->SetValuePos('center');
					$barra->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
				}


				// new GroupBarPlot
				$gruposBarras = new AccBarPlot( array(
					$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ],
					$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ],
					$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]
				) );

				$gruposBarras->SetWidth(0.64);

				$grafico->Add($gruposBarras);
				$grafico->Stroke();
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>