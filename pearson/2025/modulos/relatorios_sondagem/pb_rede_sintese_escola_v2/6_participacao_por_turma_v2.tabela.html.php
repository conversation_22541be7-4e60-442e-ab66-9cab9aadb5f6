<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false,//$this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => false,
	'quebra_invisivel' => false
);

$tabela['titulo'] = 'Participação por turma'.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];

ob_start();
?>
	<tr>
		<th style="text-align: center;"><?= $this->obterBotaoAlterador('nome', false, 'Turma') ;?> </th>
		<th style="text-align: center;">Total de alunos </th>
		<th style="text-align: center;">Participantes </th>
		<th style="text-align: center;">Ausentes </th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ($d['_instituicao_id'] != $instID) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td <?= $d['_escola'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d['nome']; ?> </td>
		<td <?= $d['_escola'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d['participacao'][self::TOTAL]; ?> </td>
		<td <?= $d['_escola'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d['participacao'][self::PARTICIPANTES]; ?> </td>
		<td <?= $d['_escola'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d['participacao'][self::AUSENTES]; ?> </td>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>

<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>