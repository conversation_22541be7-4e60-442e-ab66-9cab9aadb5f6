<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => false,
	'quebra_invisivel' => false
);

$tabela['titulo'] = 'Rendimento nas questões de escrita'.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];

ob_start();
?>
	<tr>
		<th valign="middle" nowrap="nowrap" style="text-align: center; border-bottom-color: white;">Turma </th>
		<th style="padding-left: 5px; padding-right: 5px;">Qst. </th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">A</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">B</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">C</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">D</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">&oslash; </th>
		<th align="center" style="padding-left: 5px; padding-right: 5px; border-bottom-color: white;">&nbsp; </th>

		<th style="padding-left: 5px; padding-right: 5px;">Qst. </th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">A</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">B</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">C</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">D</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">&oslash; </th>
		<th align="center" style="padding-left: 5px; padding-right: 5px; border-bottom-color: white;">&nbsp; </th>

		<th style="padding-left: 5px; padding-right: 5px;">Qst. </th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">A</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">B</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">C</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">D</th>
		<th align="center" style="padding-left: 5px; padding-right: 5px;">&oslash; </th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if ($d['_instituicao_id'] != $instID || $d['_escola']) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
		<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<td valign="middle" align="center" nowrap="nowrap" style="border-left-color: white; border-bottom-color: white; border-top-color: white;"><?= $d['nome']; ?> </td>
			<td align="center"><strong>25</strong></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['A']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['B']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['C']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['D']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['em_branco']; ?></td>
			<td align="center" style="border-bottom-color: white; border-top-color: white;">&nbsp; </td>

			<td align="center"><strong>26</strong></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_26 ]['A']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_26 ]['B']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_26 ]['C']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_26 ]['D']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['em_branco']; ?></td>
			<td align="center" style="border-bottom-color: white; border-top-color: white;">&nbsp; </td>

			<td align="center"><strong>27</strong></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_27 ]['A']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_27 ]['B']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_27 ]['C']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_27 ]['D']; ?></td>
			<td align="center"><?= (int) @$d['rendimento_por_questao'][ ProvinhaBrasil::QUESTAO_25 ]['em_branco']; ?></td>
		</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div align="center" style="margin-top: 5px;">Valores em porcentagem. | &oslash; - em branco</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>