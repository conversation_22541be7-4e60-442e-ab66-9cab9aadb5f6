<script type="text/javascript">
window.addEvent('domready', function() {
	$$('div.rel_sugestao_visualizacao').toggle();
});
</script>
<?
//MANUTENCAO
$MANUTENCAO = Core::diretiva('MANUTENCAO');
if(!empty($MANUTENCAO)){
	$MANUTENCAO = strtolower($MANUTENCAO);
	if($MANUTENCAO == 'sim' && Core::registro('usuario')->obterGrupo() != 1){
		@Core::registro('autenticador')->logOut();
		Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:login')) );
		Core::modulo('redirecionador')->redirecionar();
	}
}

//LOGINS PERMITIDOS
$LOGINS_PERMITIDOS = str_replace(' ', '', Core::diretiva('LOGINS_PERMITIDOS'));
if(!empty($LOGINS_PERMITIDOS)){
	$LOGINS_PERMITIDOS = explode(',', '1,'.$LOGINS_PERMITIDOS);

	if(count($LOGINS_PERMITIDOS) > 0){
		if(!in_array(Core::registro('usuario')->obterGrupo(), $LOGINS_PERMITIDOS)){
			@Core::registro('autenticador')->logOut();
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:login')) );
			Core::modulo('redirecionador')->redirecionar();
		}
	}
}

//EMAILS EXCLUSIVOS
$allowMail = Core::diretiva('LOGIN_EXCLUSIVO_EMAIL');
if(!empty($allowMail)){
	$opcoes = json_decode(strtolower(Core::diretiva('LOGIN_EXCLUSIVO_EMAIL')),true);

	$usuario = new UsuarioInstituido(Core::registro('usuario')->obterID());
	$usuario->carregar();

	$dominio_email = explode('@', $usuario->obterEmail()->obterEndereco());

	if(array_key_exists($usuario->obterGrupo(), $opcoes)){
		$dominio_email_allowed = $opcoes[$usuario->obterGrupo()];

		if($dominio_email[1] != $dominio_email_allowed){
			@Core::registro('autenticador')->logOut();
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:login')) );
			Core::modulo('redirecionador')->redirecionar();
		}
	}
}

$ugrupo = Core::registro('usuario')->obterGrupo();
$relRestrito = strtolower(Core::diretiva('RELS_RESTRITO_SONDAGEM'));		
$relRestritoGL = Core::diretiva('RELS_RESTRITO_GRUPOS_LIBERADOS_SONDAGEM'); // formato -> x,y,z
$relRestritoData = Core::diretiva('RELS_RESTRITO_DATA_LIBERACAO_SONDAGEM'); // formato -> dd/mm/aa hh:mm

$dtc = new DateTime();
$utc = $dtc->setTimezone(new DateTimeZone('America/Sao_Paulo'));
$drl = $dtc->createFromFormat("d/m/Y H:i", $relRestritoData)->getTimestamp();

if(($ugrupo !== '1' && in_array($ugrupo, explode(',', $relRestritoGL)) !== true) && ($relRestrito === 'sim' || $drl >= time())){
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('No momento os relatórios não estão disponíveis para visualização!');
?>
<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
		<td height="200" colspan="2" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
</table>
<?
}
elseif(in_array(Core::registro('usuario')->obterGrupo(), array(3)) && Core::modulo('_perfil_falso')->_perfil_turma == NULL){
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('Selecione alguma turma!');
?>
<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
		<td height="200" colspan="2" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
</table>
<?
}
elseif ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as &$d ) {
?>
<div <?= $i++ > 0 ? 'class="vd_BlocoEspacador"': null; ?>>
<table width="100%" border="0" cellpadding="3" cellspacing="0" class="lp" style="margin: 0 auto; width: 740px;">
	<tr>
	  <td style="font-size: 20px; color: #444444;" class="lp_SuperHeader"><?= $d['nome']; ?></td>
<?
	if ($this->_numerSugestoesRelatorios) {
?>
	  <td width="2%" nowrap="nowrap" align="right" style="background: none repeat scroll 0 0 #DDDDDD;"><a title="clique para mostrar/esconder os relatórios pré-formatadas" onclick="$$('div.rel_sugestao_visualizacao').toggle();" href="javascript: void(0);">relatórios pré-formatados</a></td>
<?
	}
?>
	</tr>
<?
		foreach ( $d['relatorios'] as &$relatorio ) {
?>
	<tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
	  <td colspan="2">
		        <?
				$display = 'display:block';
				if(Core::registro('usuario')->obterGrupo() == 2 and Core::registro('usuario')->obterID() != 1 and $relatorio->obterID() == 38 or Core::registro('usuario')->obterNome() != 'Rafael Buchalla' and  $relatorio->obterID() == 39  or Core::registro('usuario')->obterGrupo() == 3 and $relatorio->obterID() == 40 or  Core::registro('usuario')->obterGrupo() == 2 and $relatorio->obterID() == 40) {
					$display = 'display:none';
				}?>
             <div style="<? echo $display ?>">
             	  <? if(Core::registro('usuario')->obterGrupo() == 1)://if (Core::registro('permissoes')->temPermissao('relatorios.reordenar')):  ?>
             	  	<a title="Subir" href="<?= Gerenciador_URL::gerarLink('relatorios_sondagem', 'reordenar', array('id' => $relatorio->obterID(), 'direcao' => 'cima')); ?>"><img border=0 src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>arrow_up.png" /></a>
             	  	<a title="Descer" href="<?= Gerenciador_URL::gerarLink('relatorios_sondagem', 'reordenar', array('id' => $relatorio->obterID(), 'direcao' => 'baixo')); ?>"><img border=0 src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>arrow_down.png" /></a>
             	  <? endif; ?>

             	  <span style="color:#0E487A; width: 27px;display: inline-block;text-align: right;">
             	  	<? echo $relatorio->obterID(); ?>. 
             	  </span>
				  <img src="<?= Core::diretiva('ESTILO:DIRETORIO:media'); ?>relatorio.png" style="" />
				  <a turma_seletor id_relatorio="<?=$relatorio->obterID()?>" style="font-size: 14px;line-height: 25px;margin-left: 5px;" title="Visualizar relatório" href="<?= Gerenciador_URL::gerarLink('relatorios', 'detalhar', array('id' => $relatorio->obterID())); ?>">
				  	<? if($relatorio->obterOculto()){ ?>
				  	<strong style="color:#777777;">
				  		<?= $relatorio->obterNome(); ?>
				  		<? echo' [Oculto]'; ?>
				  	</strong>
				  <? }else{ ?>
				  	<strong>
				  		<?= $relatorio->obterNome(); ?>
				  	</strong>
				  <? } ?>
				  </a>
				  <? if(Core::registro('usuario')->obterGrupo() == 1)://if (Core::registro('permissoes')->temPermissao('permissoes.gerenciar')): ?>(<a title="Visualizar relatório" href="<?= Gerenciador_URL::gerarLink('relatorios_sondagem', 'editar', array('id' => $relatorio->obterID())); ?>">editar</a>)<? endif; ?>
				  <div><?= $relatorio->obterDescricao(); ?></div>
			 </div>
<?
			if (count(@$this->_sugestoesRelatorios[$relatorio->obterID()])) {
				echo '<div class="rel_sugestao_visualizacao" style="padding-left: 10px; padding-top: 10px;"> <ul style="margin-top: 0px; padding-left: 24px;">';

				foreach ( $this->_sugestoesRelatorios[$relatorio->obterID()] as $srID => $srNome )
					echo '<li><a title="Visualizar esse relatório '. $srNome .'" href="'. Gerenciador_URL::gerarLink('relatorios_sondagem', 'detalhar', array('id' => $relatorio->obterID(), 'sugestao' => $srID)) .'">'. $srNome .'</a></li>';
				
				echo '</ul></div>';
			}
?>
	  </td>
	</tr>

<?
		}
?>
</table>
</div>
<?
	}
} else {
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('Nenhum relatório encontrado!');
?>
<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
		<td height="200" colspan="2" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
</table>
<?
}
?>
