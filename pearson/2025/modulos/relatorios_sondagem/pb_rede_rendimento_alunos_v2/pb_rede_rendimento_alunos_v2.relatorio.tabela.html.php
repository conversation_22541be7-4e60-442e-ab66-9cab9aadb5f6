<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$tabela['titulo'] = $this->_relatorio->obterNome();//.' - '.$_SESSION['_seletor_simulados.selecionado.disciplina'];
$tabela['descricao'] = sprintf(
	'<strong>Secretaria Municipal de Educação - %s</strong><br />
	<strong>%s</strong>
	%s',
	(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio()),
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome(),
	($this->_config['professor'] === null ? (Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNomeDiretor() != null ? '<br /><strong>Diretor:</strong> ' . Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNomeDiretor() : '') : '<br /><strong>Professor:</strong> ' . $this->_config['professor']->obterUsuario()->obterNome())
);

if ($this->_config['diretor'] == NULL && $this->_config['professor'] == NULL) {
	$tabela['descricao'] = sprintf(
		'<strong>Secretaria Municipal de Educação - %s</strong>',
		(Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio() == NULL ? MUNICIPIO : Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterMunicipio())
	);
}

ob_start(); 
?>
	<tr>
		<? if ($this->_colunas['escola']) { ?><th width="35%"><?= $this->obterBotaoAlterador('escola', false, 'Escola') ;?></th><? } ?>
		<? if ($this->_colunas['turma']) { ?><th style="text-align: center;" width="10%"><?= $this->obterBotaoAlterador('turma', false, 'Turma') ;?></th><? } ?>
		<? if ($this->_colunas['nome']) { ?><th><?= $this->obterBotaoAlterador('nome', false, 'Aluno') ;?></th><? } ?>
		<? if ($this->_colunas['nivel_anterior']) { ?><th nowrap="nowrap" valign="middle" width="5%" style="text-align: center; padding-left: 7px; padding-right: 7px;"><?= $this->obterBotaoAlterador('nivel_anterior', false, ProvinhaBrasil::TESTE_1_NOME) ;?></th><? } ?>
		<? if ($this->_colunas['nivel']) { ?><th nowrap="nowrap" valign="middle" width="5%" style="text-align: center; padding-left: 7px; padding-right: 7px;"><?= $this->obterBotaoAlterador('nivel', false, ($this->_colunas['nivel_anterior'] ? ProvinhaBrasil::TESTE_2_NOME : 'Nível')) ;?></th><? } ?>
		<? if ($this->_colunas['variacao']) { ?><th nowrap="nowrap" width="5%" style="padding-left: 7px; padding-right: 7px;"><?= $this->obterBotaoAlterador('variacao', false, ' ') ;?></th><? } ?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];

		if (0){}//$d['rendimento'] == 0 && $d['rendimento_anterior'] == 0) {}
		
		else {
			
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['escola']) { ?><td nowrap="nowrap"><?= @$d['escola']; ?></td><? } ?>
		<? if ($this->_colunas['turma']) { ?><td nowrap="nowrap" align="center"> <?= @$d['turma']; ?> </td><? } ?>
		<? if ($this->_colunas['nome']) { ?><td>&nbsp; <?= @$d['nome']; ?> </td><? } ?>
<?
		if ($this->_colunas['nivel_anterior']) {
			$cor = ProvinhaBrasil::$NIVEL_5_COR;
			$nivel = ProvinhaBrasil::$NIVEL_5_NOME;

			if ($d['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_1_ANTERIOR) {
				$cor = ProvinhaBrasil::$NIVEL_1_COR;
				$nivel = ProvinhaBrasil::$NIVEL_1_NOME;
			} elseif ($d['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_2_ANTERIOR) {
				$cor = ProvinhaBrasil::$NIVEL_2_COR;
				$nivel = ProvinhaBrasil::$NIVEL_2_NOME;
			} elseif ($d['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_3_ANTERIOR) {
				$cor = ProvinhaBrasil::$NIVEL_3_COR;
				$nivel = ProvinhaBrasil::$NIVEL_3_NOME;
			} elseif ($d['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_4_ANTERIOR) {
				$cor = ProvinhaBrasil::$NIVEL_4_COR;
				$nivel = ProvinhaBrasil::$NIVEL_4_NOME;
			} else {
				$cor = ProvinhaBrasil::$NIVEL_5_COR;
				$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
			}

			if ($d['rendimento_anterior'] !== NULL)
				echo '<td nowrap="nowrap" style="text-align: center; color: '. ProvinhaBrasil::COR_TEXTO .'; background-color: '. $cor .'"> '. $nivel .' </td>';
			else
				echo '<td style="text-align:center;color:#737373;">Ausente</td>';
		}
?>
<?
		if ($this->_colunas['nivel']) {
			$cor = ProvinhaBrasil::$NIVEL_5_COR;
			$nivel = ProvinhaBrasil::$NIVEL_5_NOME;

			if ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_1) {
				$cor = ProvinhaBrasil::$NIVEL_1_COR;
				$nivel = ProvinhaBrasil::$NIVEL_1_NOME;
			} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_2) {
				$cor = ProvinhaBrasil::$NIVEL_2_COR;
				$nivel = ProvinhaBrasil::$NIVEL_2_NOME;
			} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_3) {
				$cor = ProvinhaBrasil::$NIVEL_3_COR;
				$nivel = ProvinhaBrasil::$NIVEL_3_NOME;
			} elseif ($d['rendimento'] <= ProvinhaBrasil::$NIVEL_4) {
				$cor = ProvinhaBrasil::$NIVEL_4_COR;
				$nivel = ProvinhaBrasil::$NIVEL_4_NOME;
			} else {
				$cor = ProvinhaBrasil::$NIVEL_5_COR;
				$nivel = ProvinhaBrasil::$NIVEL_5_NOME;
			}

			if($d['rendimento'] === 'faltou'){
				echo '<td style="text-align:center;color:#737373;">Ausente</td>';
				$d['variacao'] = ProvinhaBrasil::VARIACAO_NAO_APLICAVEL;
			}
			elseif ($d['rendimento'] !== NULL){
				echo '<td nowrap="nowrap" style="text-align: center; color: '. ProvinhaBrasil::COR_TEXTO .'; background-color: '. $cor .'"> '. $nivel .'</td>';
			}
			else{
				echo '<td style="text-align:center;color:#737373;">Ausente</td>';
			}
		}
?>
	<? if ($this->_colunas['variacao']) { ?>
		<td align="center" style=" font-weight: bold;">
			<?= @$d['variacao']; ?>
		</td>
	<? } ?>
	</tr>
<? }
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean(); 
//$tabela['td'] = null; 
ob_start();
?>
	<div style="margin-top: 10px; border: 1px #cccccc solid; padding: 5px;">
	<table width="100%" cellpadding="1" cellspacing="0" class="rlt_tabela_extra">
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_1_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_1_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_1_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_2_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_2_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_2_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_3_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_3_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_3_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_4_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_4_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_4_TEXTO; ?></td>
		</tr>
		<tr>
			<td width="34px" style="background-color: <?= ProvinhaBrasil::$NIVEL_5_COR; ?>; text-align: center; color: <?= ProvinhaBrasil::COR_TEXTO; ?>;"><?= ProvinhaBrasil::$NIVEL_5_NOME; ?> </td>
			<td><?= ProvinhaBrasil::$NIVEL_5_TEXTO; ?></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
		<tr>
			<td colspan="2" style="height: 1px;"></td>
		</tr>
	</table>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>