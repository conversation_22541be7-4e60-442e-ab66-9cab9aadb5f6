<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemSondagem', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);

class RLPBRedeRendimentoAlunosV2 extends RelatorioListagemSondagem {
	protected $_por_pagina = 44;

	protected $_turmas_mostrar = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'escola' => true,
			'turma' => true,
			'nome' => true,
			'rendimento' => true,
			'nivel_anterior' => false,
			'nivel' => true,
			'variacao' => false
		);

		$this->_modelo = array(
			'escola' => null,
			'turma' => null,
			'nome' => null,
			'rendimento' => null,
			'rendimento_anterior' => null,
			'variacao' => null,
			'_inscricao' => null
		);

		$this->_config = array(
			'professor' => null,
			'diretor' => null
		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		if ($this->obterModoVisualizacao() != self::PDF)
			$this->_por_pagina = 999999;

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$dados_mostrar = array();
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == $this->_por_pagina || !$total) {
					include 'pb_rede_rendimento_alunos_v2.relatorio.tabela.html.php';
					$dados_mostrar = array();
				}
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF();
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->_ajustarParametros();

		//Funções de PDF
		$this->fixarModoVisualizacao(self::PDF);
		//$this->_sugestao['escola'] = Ordenacao::ASC;
		//$this->_sugestao['turma'] = Ordenacao::ASC;
		//$this->_sugestao['rendimento'] = Ordenacao::ASC;
		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();
		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		$this->_seletorSimulados->ajustarSimuladoSelecionado();
		//Funções de PDF
		
		$this->_obterDados();

		if ($this->obterModoVisualizacao() != self::PDF)
			$this->_por_pagina = 999999;

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$dados_mostrar = array();
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == $this->_por_pagina || !$total) {
					include 'pb_rede_rendimento_alunos_v2.relatorio.tabela.html.php';
					$dados_mostrar = array();
				}
			}

			include 'modulos/relatorios_sondagem/relatorio.pb20082'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		if ($this->_config['diretor'] != null) {
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR &&
					Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
					$this->_config['diretor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			else {
				if ( Core::registro('usuario') == null)
					Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Diretor inv&aacute;lido!');
				else
					$this->_config['diretor'] = Core::registro('usuario');
			}

			$this->_por_pagina--;
			$this->_config['professor'] = null;
		}

		if ($this->_config['professor'] != null) {
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR &&
					Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			{
					$this->_config['professor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado(); 
			}else {
				if ( CarregadorUsuarioEspecifico::obterProfessor() == null )
					Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios', 'listar'), 'Professor inv&aacute;lido!');
				else
					$this->_config['professor'] = CarregadorUsuarioEspecifico::obterProfessor();
			}

			$this->_por_pagina--;
			$this->_config['diretor'] = null;

			//$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmasComAulas(); 
			//if($simuID == null){
			//	$_ordenacao = new OrdenacaoPerfilFalso('perfil_falso'); 
			//	$valores = $_ordenacao->obterValoresDeOrdenacao(); 
			//
			//	$simuID = $valores['simulado']; 
			//	$turmID = $valores['turma']; 
			//
			//	$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmaPorAulaSimuladoTurma($simuID,$turmID);
			//}else{
				$simuID = $_SESSION['_seletor_simulados.selecionado'];
				$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmaPorSimulado($simuID);
			//}
		}
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		// ordena dados
		$teste1e2 = $teste2 = $teste1 = array();

		foreach($this->_dados as $k => $d) {
			if ($d['rendimento_anterior'] != NULL && $d['rendimento'] != NULL)
				$teste1e2[] = $k;
			elseif ($d['rendimento'] != NULL)
				$teste2[] = $k;
			else
				$teste1[] = $k;
		}

		$testes = array_merge($teste1e2, $teste2, $teste1);
		$dados = array();

		foreach($testes as $k) {
			$dados[] = $this->_dados[$k];
		}

		$this->_dados = $dados;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_sondagem/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		if ($this->_colunas['variacao']) {
			Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.filtro.html.php' );
		}else{
			Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_sondagem/_painel_relatorio/_painel_relatorio.html.php' );
		}

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

			if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'escola' => 'Escola',
				'turma' => 'Turma',
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('escola'), array('turma'), array('nome') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if ( $this->_config['professor'] !== null )
			$this->_tituloCache .= '_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			//$this->_dados = &$tempCache['dados'];
			//return;
		}
		
		$this->_analizadorSimulado->carregarInscritos(($this->_config['diretor'] == null && $this->_config['professor'] == null));
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		
		$desistentes = array();
		if ($this->_config['diretor'] === null && $this->_config['professor'] === null) {
			$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram(true);
			$desistentes = $this->_analizadorSimulado->obterDesistencias();
		}
		else{
			$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		}

		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();

		if ($this->_colunas['variacao']) {
			$filtroRendimento = $this->_ordenacao->obterFiltroDeRendimento();

			if($filtroRendimento['positivo'] == '1' || $filtroRendimento['negativo'] == '1' || $filtroRendimento['nulo'] == '1' || $filtroRendimento['ausenteTesteUm'] == '1' || $filtroRendimento['ausenteTesteDois'] == '1'){
				if($filtroRendimento['positivo'] != '1'){
					$this->_analizadorSimulado->eliminarInscritosComRendimentoCrescente();
				}

				if($filtroRendimento['negativo'] != '1'){
					$this->_analizadorSimulado->eliminarInscritosComRendimentoDecrescente();
				}

				if($filtroRendimento['nulo'] != '1'){
					$this->_analizadorSimulado->eliminarInscritosComRendimentoEstatico();
				}

				if($filtroRendimento['ausenteTesteUm'] != '1'){
					$this->_analizadorSimulado->eliminarInscritosComTesteUmNulo();
				}

				if($filtroRendimento['ausenteTesteDois'] != '1'){
					$this->_analizadorSimulado->eliminarInscritosComTesteDoisNuloV2();
				}
			}
		}

		$nomePorInstituicao = $this->_analizadorSimulado->nomesInstituicoesSimplificados;

		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			$tID = @$inscricao->obterAluno()->obterTurma()->obterID();

			if ( $this->_config['professor'] != null && !isset($this->_turmas_mostrar[$tID]) )
				continue;

			$dado = $this->_modelo;

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();
			$aID = @$inscricao->obterID();

			$dado['escola'] = @$nomePorInstituicao[$instID];
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
 
			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();

			$dado['rendimento'] = $this->_analizadorSimulado->rendimentoGlobal[$iID]['total_pontos'];

			$dado['rendimento_anterior'] = $inscricao->obterNotaAnterior();

			$nivel = $nivel_anterior = ProvinhaBrasil::NIVEL_5_COMUM;

			if ($dado['rendimento'] === NULL)
				$nivel = NULL;
			elseif ($dado['rendimento'] <= ProvinhaBrasil::$NIVEL_1)
				$nivel = ProvinhaBrasil::NIVEL_1_COMUM;
			elseif ($dado['rendimento'] <= ProvinhaBrasil::$NIVEL_2)
				$nivel = ProvinhaBrasil::NIVEL_2_COMUM;
			elseif ($dado['rendimento'] <= ProvinhaBrasil::$NIVEL_3)
				$nivel = ProvinhaBrasil::NIVEL_3_COMUM;
			elseif ($dado['rendimento'] <= ProvinhaBrasil::$NIVEL_4)
				$nivel = ProvinhaBrasil::NIVEL_4_COMUM;

			foreach ($desistentes as $dkey => $dvalue) {
				if(in_array($aID, $dvalue)){
					$dado['rendimento'] = 'faltou';
				}
			}

			if ($dado['rendimento_anterior'] === NULL)
				$nivel_anterior = NULL;
			elseif ($dado['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_1_ANTERIOR)
				$nivel_anterior = ProvinhaBrasil::NIVEL_1_COMUM;
			elseif ($dado['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_2_ANTERIOR)
				$nivel_anterior = ProvinhaBrasil::NIVEL_2_COMUM;
			elseif ($dado['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_3_ANTERIOR)
				$nivel_anterior = ProvinhaBrasil::NIVEL_3_COMUM;
			elseif ($dado['rendimento_anterior'] <= ProvinhaBrasil::$NIVEL_4_ANTERIOR)
				$nivel_anterior = ProvinhaBrasil::NIVEL_4_COMUM;

			$dado['variacao'] = ProvinhaBrasil::VARIACAO_NAO_APLICAVEL;

			if ($nivel !== NULL && $nivel_anterior !== NULL) {
				if ($nivel > $nivel_anterior)
					$dado['variacao'] = ProvinhaBrasil::VARIACAO_POSITIVA;
				elseif ($nivel < $nivel_anterior)
					$dado['variacao'] = ProvinhaBrasil::VARIACAO_NEGATIVA;
				else
					$dado['variacao'] = ProvinhaBrasil::VARIACAO_NULA;
			}

			$this->_dados[] = $dado;
		}

		$this->_ordenacao->fixarResultado(count($this->_dados));

		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'turma' ),
							 'linhas' => array(),
							 'config' => array() );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>