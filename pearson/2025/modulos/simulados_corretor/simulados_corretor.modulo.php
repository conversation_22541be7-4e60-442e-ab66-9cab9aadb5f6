<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('Professor', null, true);
Core::incluir('Resposta', null, true);
Core::incluir('Questao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Aluno', null, true);
Core::incluir('UsuarioInstituido', null, true);

include_once('simulados_corretor.realizar.listagem.php');

class MSimuladosCorretor extends Modulo
{
	public function __construct ()
	{
		parent::__construct();
		Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		Core::modulo('js')->incluirArquivo('includes/JavaScript/jMasks/maskedinput.js');
		Core::modulo('js')->incluirArquivo('includes/JavaScript/jMasks/meio.mask.js');
	}

	public function aListarSimuladosParaCorrigir () {
		Core::modulo('navegador')->habilitarAtalho('adicionar', false);
		
		$this->_listagem = new LSimuladosParaCorretor();
		$this->_listagem->prepararListagem();

		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}

	public function aCoirrigirSimulado ()
	{
		$simu = new Simulado( (int) @$_REQUEST['id'] );

		if ( !$simu->carregar() || !$simu->validarInstituicao() ) {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_corretor', 'lista'), 'Avalia&ccedil;&atilde;o inv&aacute;lida!');
		} else {
			Core::modulo('navegador')->habilitarAtalho('adicionar', false);
			
			$msg = "";

			$qid = null;
			if(isset($_POST['questaoId']) && !empty($_POST['questaoId'])){
				$qid = $_POST['questaoId'];
			}

			$aid = null;
			if(isset($_POST['alunoId']) && !empty($_POST['alunoId'])){
				$aid = $_POST['alunoId'];
			}

			if(isset($_POST['env']) && !empty($_POST['env'])){
				$action = $this->aCorrigirResposta();

				if($action == 'questao'){
					if(isset($_POST['proxim']) && !empty($_POST['proxim'])){
						$qid = $_POST['proxim'];
					}
				}
				elseif($action == 'aluno'){
					if(isset($_POST['proxim']) && !empty($_POST['proxim'])){
						$aid = $_POST['proxim'];
					}
				}
			}

			$uli = Core::registro('autenticador')->obterUsuario()->obterID();

			$usuario = new UsuarioInstituido($uli);
			$usuario->carregar();

			$idP = Professor::obterIDProfessorPeloUsuario($usuario);

			$professor = new Professor($idP); 
			$professor->carregar($usuario);
			$simuladosP = $professor->obterSimuladosComItensERespostasCorretoresSelecionados();

			$simulado['numq'] = 0;
			$simulado['nome'] = $simu->obterNome();
			$simulado['simu'] = $simu->obterID();

			$simulado['questoes'] = $simuladosP[$simulado['simu']];
			$simulado['numq'] = count($simulado['questoes']);

			$vazio = 1;
			foreach ($simulado['questoes'] as $sqk => $sqv) {
				foreach ($sqv as $sqk2 => $sqv2) {
					if(count($sqv2)){
						$vazio = 0;
					}
				}
			}

			if ($vazio) {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_corretor', 'listar'), 'Avalia&ccedil;&atilde;o inv&aacute;lida!');
				return;
			}

			$simulado['prof'] = $idP;
			$simulado['user'] = $uli;
			$simulado['msg'] = $msg;

			//outra questao
			if(isset($qid) && !empty($qid)){
				$iquestao = $qid;
			}
			else{
				$iquestao = current($simulado['questoes']);
				$iquestao = key($simulado['questoes']);
			}

			$cquestao = new Questao($iquestao);
			$cquestao->carregar();

			//outro aluno
			if(isset($aid) && !empty($aid)){
				$inscricao = new Inscricao($aid);
				$inscricao->carregar();

				$respostac = Resposta::obterRespostaPorQuestaoEInscicao($cquestao, $inscricao);
				if($respostac != null){
					$iresposta = $respostac->obterID();
					$resposta = $iresposta;
				}
				else{
					$iresposta = 0;
					$resposta = $simulado['questoes'][$iquestao][0][$iresposta];
				}
			}
			else{
				$iresposta = 0;
				$resposta = $simulado['questoes'][$iquestao][0][$iresposta];
			}

			$cresposta = new Resposta($resposta);
			$cresposta->carregar();

			$simulado['aluno'] = $cresposta->obterInscricao()->obterID();

			$corretor1 = $cresposta->obterIDProfessor();
			$corretor2 = $cresposta->obterIDProfessor2();
			$corretor3 = $cresposta->obterIDProfessor3();

			if($idP == $corretor1){
				$simulado['corretor_valor'] = $cresposta->obterValorProfessor();
				$simulado['corretor_commt'] = $cresposta->obterObservacaoProfessorTexto();
			}
			elseif($idP == $corretor2){
				$simulado['corretor_valor'] = $cresposta->obterValorProfessor2();
				$simulado['corretor_commt'] = $cresposta->obterObservacaoProfessorTexto2();
			}
			elseif($idP == $corretor3){
				$simulado['corretor_valor'] = $cresposta->obterValorProfessor3();
				$simulado['corretor_commt'] = $cresposta->obterObservacaoProfessorTexto3();
			}
			else{
				$simulado['corretor_valor'] = '';
				$simulado['corretor_commt'] = '';
			}

			$todasAsQuestoes = $simulado['questoes'];
			$todasAsQuestoesArr = array();
			$todasOsAlunosArr = array();

			$totalRespostas = 0;
			$totalRespostasCorrigidas = 0;
			$totalRespostasCorrigidasPorcentagem = 0;

			foreach ($todasAsQuestoes as $taqk => $taqv) {
				$cquestao2 = new Questao($taqk);
				$cquestao2->carregar();

				$status = 'Pendente';
				$make = ' ';
				$disab = ' ';
				if($cquestao2->obterID() == $iquestao){
					$status = 'Atual';
					$make = ' selected="selected" ';
				}

				$rcheck = Resposta::obterRespostaPorQuestaoEInscicao($cquestao2, $cresposta->obterInscricao());

				if($rcheck == null){
					$status = '';
					$disab = ' disabled="disabled" ';
				}
				else{
					if($rcheck->obterCorrigida()){
						continue;
					}

					$corretor10 = $rcheck->obterIDProfessor();
					$corretor20 = $rcheck->obterIDProfessor2();
					$corretor30 = $rcheck->obterIDProfessor3();

					if($idP == $corretor10){
						$status = 'Corrigida';
					}
					elseif($idP == $corretor20){
						$status = 'Corrigida';
					}
					elseif($idP == $corretor30){
						$status = 'Corrigida';
					}
				}

				$todasAsQuestoesArr[] = "<option value='".$cquestao2->obterID()."' ".$make." ".$disab.">".$cquestao2->obterIdentificador()." - ".$status."</option>";
			
				$todasOsAlunos = $todasAsQuestoes[$taqk][0];
				foreach ($todasOsAlunos as $toak => $toav) {
					$cresposta2 = new Resposta($toav);
					$cresposta2->carregar();

					$id = $cresposta2->obterInscricao()->obterID();
					$nome = $cresposta2->obterInscricao()->obterAluno()->obterUsuario()->obterNome();
					$letra = iconv('ISO-8859-1', 'ASCII//IGNORE', substr($nome, 0, 1));

					$todasOsAlunosArr[$letra][$id] = $nome;

					$totalRespostas++;

					$corretor12 = $cresposta2->obterIDProfessor();
					$corretor22 = $cresposta2->obterIDProfessor2();
					$corretor32 = $cresposta2->obterIDProfessor3();

					if($idP == $corretor12){
						$totalRespostasCorrigidas++;
					}
					elseif($idP == $corretor22){
						$totalRespostasCorrigidas++;
					}
					elseif($idP == $corretor32){
						$totalRespostasCorrigidas++;
					}					
				}
			}

			if($totalRespostas == 0){
				$totalRespostasCorrigidasPorcentagem = 0;
			}
			else{
				$totalRespostasCorrigidasPorcentagem = round(($totalRespostasCorrigidas*100)/$totalRespostas);
			}

			$todasOsAlunosOpt = array();
			foreach ($todasOsAlunosArr as $tOAk => $tOAv) {
				$todasOsAlunosOpt[] = '<option disabled="disabled">'.$tOAk.'</option>';

				foreach ($tOAv as $tOAk2 => $tOAv2) {
					$status2 = 'Pendente';
					$make2 = ' ';
					$disab2 = ' ';
					if($cresposta->obterInscricao()->obterID() == $tOAk2){
						$status2 = 'Atual';
						$make2 = ' selected="selected" ';
					}

					$icheck2 = new Inscricao($tOAk2);
					$icheck2->carregar();

					$rcheck2 = Resposta::obterRespostaPorQuestaoEInscicao($cquestao, $icheck2);

					if($rcheck2 == null){
						$status2 = '';
						$disab2 = ' disabled="disabled" ';
					}
					else{
						$corretor102 = $rcheck2->obterIDProfessor();
						$corretor202 = $rcheck2->obterIDProfessor2();
						$corretor302 = $rcheck2->obterIDProfessor3();

						if($idP == $corretor102){
							$status2 = 'Corrigida';
						}
						elseif($idP == $corretor202){
							$status2 = 'Corrigida';
						}
						elseif($idP == $corretor302){
							$status2 = 'Corrigida';
						}
					}

					$todasOsAlunosOpt[] = "<option value='".$tOAk2."' ".$make2." ".$disab2.">".$tOAv2." - ".$status2."</option>";
				}
			}

			if(time() < $simu->obterDataCorrecaoInicio(false)){
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_corretor', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o deste simulado on-line ainda n&atilde;o come&ccedil;ou.');
				return;
			}

			if(!$simu->obterDataCorrecaoFim(false) || time() <= $simu->obterDataCorrecaoFim(false)){
				Core::modulo('js')->incluirArquivo('includes/JavaScript/jMasks/meio.mask.js');
				Core::modulo('js')->incluirArquivo('includes/JavaScript/jMasks/maskedinput.js');
				$this->_iniciarRenderizacao(Modulo::HTML);
					include('simulados_corretor.realizar.avaliacao.html.php');
				$this->_finalizarRenderizacao();
			}
			else{
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('simulados_corretor', 'listar'), 'O per&iacute;odo para realiza&ccedil;&atilde;o deste simulado on-line j&aacute; acabou.');
				return;
			}
		}

		return true;
	}

	public function aCorrigirResposta()
	{
		$env=$simuladoId=$usuarioId=$alunoId=$questaoId=$respostaId=$action=$resposta=$comentario=null;
		$simulado=$usuario=$professor=$inscricao=$questao=$resposta=null;
		$resutado = 0;

		if(isset($_POST['env'])){
			$env = $_POST['env'];

			if($env != 1){
				Core::modulo('redirecionador')->adicionarFalha('Problema ', ' ao registrar sua nota para essa resposta.');
				Core::modulo('redirecionador')->fixarMensagem(null, 'Correção de resposta...');

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());

				return;
			}
		}

		if(isset($_POST['simuladoId'])){
			$simuladoId = $_POST['simuladoId'];

			$simulado = new Simulado($simuladoId);
			$simulado->carregar();			
		}

		if(isset($_POST['usuarioId'])){
			$usuarioId = $_POST['usuarioId'];

			$uli = Core::registro('autenticador')->obterUsuario()->obterID();

			$usuario = new UsuarioInstituido($uli);
			$usuario->carregar();

			$idP = Professor::obterIDProfessorPeloUsuario($usuario);

			$professor = new Professor($idP); 
			$professor->carregar($usuario);	
		}

		if(isset($_POST['alunoId'])){
			$alunoId = $_POST['alunoId'];

			$inscricao = new Inscricao($alunoId);
			$inscricao->carregar();	
		}

		if(isset($_POST['questaoId'])){
			$questaoId = $_POST['questaoId'];

			$questao = new Questao($questaoId);
			$questao->carregar();
		}

		if(isset($_POST['respostaId'])){
			$respostaId = $_POST['respostaId'];

			$resposta = new Resposta($respostaId);
			$resposta->carregar();	
		}

		$resultado = 0;
		if(isset($_POST['nota'])){
			$nota = $_POST['nota'];

			if(isset($_POST['comentario'])){
				$comentario = $_POST['comentario'];
			}

			$resultado = $resposta->validacaoEnem($professor,$nota,$comentario);
		}

		if(isset($_POST['action']) && $resultado){
			$action = $_POST['action'];

			return $action;
		}
		else{
			Core::modulo('redirecionador')->adicionarFalha('Problema ', ' ao registrar sua nota para essa resposta.');
			Core::modulo('redirecionador')->fixarMensagem(null, 'Correção de resposta...');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());

			return;
		}

		return 0;
	}
}

?>