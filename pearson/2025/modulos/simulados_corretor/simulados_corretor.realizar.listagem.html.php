<?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?>

<?= Core::modulo('ord_simulados')->obterSaida(); ?>

<?= Core::modulo('procurar_simulados')->obterSaida(); ?>

<?
if ( $this->_paginacao->paginador->precisaDePaginacao() ) {
?>
	<div class="vd_BlocoBotoes" align="right"><?= $this->_paginacao->obterSaida(); ?></div>
<?
}
?>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
	<tr>
	  <td class="lp_ColHeader"><?= $this->obterBotaoAlterador('s_nome'); ?></td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Data inicial de correção</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Data final de correção</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Nº Questões</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Nº Questões para Correção</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Inscritos</td>
	  <td width="1%" nowrap="nowrap" class="lp_ColHeader" align="center">Inscritos Corrigidos</td>
	</tr>
<?
if ( count($this->_dados) ) {
	//$camposIDs = $this->_formulario->obterHTML('ids', Formulario::HTML_CAMPO, true);
	$i = 0;
	foreach ( $this->_dados as &$d ) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
		<td nowrap="nowrap"><strong>
		<?php 
			echo '<a title="Realizar o simulado on-line" href="'.Gerenciador_URL::gerarLink('simulados_corretor', 'corrigir', array('id' => $d->obterID())).'">'.$d->obterNome().'</a>';
		?>
		</strong></td>
		<td nowrap="nowrap" align="center"><strong><?= strftime('%d/%m/%Y', $d->obterDataCorrecaoInicio()); ?></strong></td>
		<td nowrap="nowrap" align="center"><strong><?= strftime('%d/%m/%Y', $d->obterDataCorrecaoFim()); ?></strong></td>
		<td nowrap="nowrap" align="center"><strong><?= $d->obterNumeroQuestoes(); ?></strong></td>
		<td nowrap="nowrap" align="center"><strong><?= count($this->_qntd_questoes[$d->obterID()]); ?></strong></td>
		<td nowrap="nowrap" align="center"><strong><?= $d->obterNumeroInscritos(); ?></strong></td>
		<td nowrap="nowrap" align="center"><strong><?= $this->continha[$d->obterID()]; ?></strong></td>
    </tr>
<?
	}
} else {
	Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
	Core::modulo('alerta')->prepararAlerta('Nenhum simulado encontrado!');
?>
	<tr>
		<td height="200" colspan="99" align="center"><?= Core::modulo('alerta')->obterSaida(); ?></td>
	</tr>
<?
}
?>
</table>

  <table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_BlocoBotoes">
    <tr>
      <td><?php //echo$this->_formulario->obterHTML('simular', Formulario::HTML_CAMPO, true); ?></td>
<?
	if ( $this->_paginacao->paginador->precisaDePaginacao() ) {
?>
      <td align="right"><?= $this->_paginacao->obterSaida(); ?></td>
<?
	}
?>
    </tr>
</table>
<?= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>