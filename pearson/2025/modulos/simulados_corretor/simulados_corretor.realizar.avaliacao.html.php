<script type="text/javascript">
jQuery(document).ready(function() {
	jQuery(window).resize(function() {
		resizer();
	});

	resizer();

	netCheckEng('<?php echo"http://".$_SERVER["SERVER_NAME"]; ?>');

	var msg = "<?=$simulado['msg'];?>";
	if(msg){
		jQuery('.alert_box').html(msg).fadeIn('fast');
		window.scroll(0,0);
	} 
});

function resizer(){
	var width = 0;
	var widthTotal = jQuery(window).width();
	width = widthTotal-160-120;

	jQuery('.sr_left').attr('style','width:'+width+'px;');
}

var keepAliveTimeout = 1000 * 5;
var lastStatusOff = 0;
function netCheckEng(url){
    jQuery.ajax({
        type: 'GET',
        url: url+'#'+Math.ceil(Math.random()*567676475)+Math.ceil(Math.random()*100000)+Math.ceil(Math.random()*867486748674867475),
        cache: false,
        dataType: 'script',
        success: function(data){
            if(lastStatusOff){
                alert("Sua conexão foi restabelecida com o servidor!");
                lastStatusOff = 0;
            }

            setTimeout(function(){
                netCheckEng(url);
            }, keepAliveTimeout);
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){
            var answer = 0;
            if(!lastStatusOff){
                answer = confirm("Sua conexão com o servidor foi perdida! Deseja recarregar a página? Os dados não salvos serão perdidos!");
            }

            if (answer){
				window.location.reload();
			}

            lastStatusOff = 1;
	
        	setTimeout(function(){
                netCheckEng(url);
            }, keepAliveTimeout);
        }
    });
}

var numQs = 0;
numQs = <?=$simulado['numq'];?>;

function fadeQ(id){
	jQuery('.sr_botoes').hide();
	jQuery('.sr_questionario_general > div').hide();

	jQuery('#'+id).fadeIn(150);
	jQuery('.sr_botoes').fadeIn(150);
}

var token_pdf = 1;
window.onbeforeunload = function(){ 
	if(token_pdf) { 
		return "Os dados não salvos serão perdidos. Deseja sair dessa página?"; 
	}
	else{
		token_pdf = 1;
	}
}

var percent = <?php echo$totalRespostasCorrigidasPorcentagem; ?>;
function cemPC(){
	jQuery('.alert_box').attr('class','alert_box alert_box_sucesso').html('Parabéns, você concluiu a correção dessa avaliação.').fadeIn('fast');
	window.scroll(0,0);
}
</script>

<form name="form_seletor_corretor" method="post" action="<?= Gerenciador_URL::gerarLinkPelaEntrada(); ?>">
	<input type="hidden" name="simuladoId" id="simuladoId" value="<?=$simulado['simu'];?>" />
	<input type="hidden" name="usuarioId" id="usuarioId" value="<?php echo$simulado['user']; ?>" />
	<input type="hidden" name="alunoId" id="alunoId" value="<?php echo$simulado['aluno']; ?>" />
	<input type="hidden" name="questaoId" id="questaoId" value="<?php echo$iquestao; ?>"/>
	<input type="hidden" name="respostaId" id="respostaId" value="<?php echo$resposta; ?>"/>
</form>

<div class="sr_general">
	<div class="sr_left" style="width: 100%;">
		<div class="sr_title" style="display: block; height: 25px;">
			<div style="float: left;font-weight: bold;"><?php echo$simulado['nome']; ?></div>
			<div style="float: right;color: #0066CE;">Corrido <?php echo$totalRespostasCorrigidasPorcentagem; ?>% (<?php echo$totalRespostasCorrigidas; ?>/<?php echo$totalRespostas; ?>)</div>
		</div>

		<div class="alert_box alert_box_erro" style="display:none;"></div>

		<div class="sr_questionario_general">
			<form name="form_env_corretor" method="post" action="<?= Gerenciador_URL::gerarLinkPelaEntrada(); ?>">
				<input type="hidden" name="env" id="env" value="1"/>
				<input type="hidden" name="simuladoId" value="<?=$simulado['simu'];?>" />
				<input type="hidden" name="usuarioId" value="<?php echo$simulado['user']; ?>" />
				<input type="hidden" name="alunoId" value="<?php echo$simulado['aluno']; ?>" />
				<input type="hidden" name="questaoId" value="<?php echo$iquestao; ?>"/>
				<input type="hidden" name="respostaId" value="<?php echo$resposta; ?>"/>
				<input type="hidden" name="action" id="action" value=""/>
				<input type="hidden" name="proxim" id="proxim" value=""/>

				<div id="questao">
					<div class="sr_num_questao" style="height: inherit;padding: inherit;">
						<span style="line-height: 26px;padding-left: 10px;">Questão: </span>
						<select id="id_questao_selecionada" name="id_questao_selecionada" size="1" style="border: 0 none;border-radius: 0 5px 0 5px;color: #333333;float: right;font-size: 18px;height: 32px;padding: 2px 3px;">
							<?php
							foreach ($todasAsQuestoesArr as $tAQk => $tAQv) {
								echo $tAQv;
							}
							?>
						</select>
					</div>
					<div class="sr_aluno_questao" style="height: inherit;padding: inherit;">
						<span style="line-height: 26px;padding-left: 10px;">Aluno: </span>
						<select id="id_aluno_selecionada" name="id_aluno_selecionada" size="1" style="border: 0 none;border-radius: 0 5px 0 5px;color: #333333;float: right;font-size: 18px;height: 32px;padding: 2px 3px;">
							<?php
							foreach ($todasOsAlunosOpt as $tAQk2 => $tAQv2) {
								echo $tAQv2;
							}
							?>
						</select>
					</div>
					<div class="sr_texto_questao">
						<p class="sr_questao_titulo" style="font-size:15px;">
							<?php echo$cquestao->obterEnunciado(); ?>
						</p>
						<p class="sr_questao_texto" style="margin-left:20px;font-size: 18px;">
							<span>Resposta: </span><br>
							<?php echo$cresposta->obterValor(); ?>
						</p>
					</div>
					<div class="sr_resposta_questao">
						<table border="0" style="text-align: center; width: 100%;">
							<tr>
								<td class="sr_votacom_td">
									<div id="vota" class="sr_vota">
										<p style="font-size:24px;">Nota:</p>
										<input type="text" class="sr_cvalor somatorio nota" alt="nota" name='nota' id='nota' value="<?php echo$simulado['corretor_valor']; ?>" size='5' maxlength='5' style="height:30px;" />
									</div>
								</td>
								<td class="sr_votacom_td">
									<div id="cmmt" class="sr_cmmt">
										<p style="font-size:24px;">Comentário:</p>
										<textarea name='comentario' id='comentario'><?php echo$simulado['corretor_commt']; ?></textarea>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div class="sr_botoes">
					<table border="0" style="text-align: center; width: 100%;">
						<tr>
							<td>
								<a id="btn_responder_questao" class="sr_button button" onclick="javascript:responderQ();" style='cursor:pointer;'>
									<img style="height:36px;" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'stock_task20.png';?>">
									<span>Lançar nota e corrigir mesma<br>questão de outro aluno</span>
								</a>
							</td>
							<td>
								<a id="btn_responder_aluno" class="sr_button button" onclick="javascript:responderQ();" style='cursor:pointer;'>
									<img style="height: 33px;margin-top: 5px;" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'user1.png';?>">
									<span>Lançar nota e corrigir outra<br>questão deste mesmo aluno</span>
								</a>
							</td>
						</tr>
					</table>
				</div>
			</form>
		</div>
	</div>
</div>

<script type="text/javascript">
jQuery('#btn_responder_questao').click(function() {
	token_pdf = 0;
	jQuery('#action').val('aluno');
	var vl = jQuery('#id_questao_selecionada').find(":selected").next().val();
	jQuery('#questaoId').val(vl);
	var vlw = jQuery('#id_aluno_selecionada').find(":selected").next().val();
	jQuery('#proxim').val(vlw);
	form_env_corretor.submit();
});

jQuery('#btn_responder_aluno').click(function() {
	token_pdf = 0;
	jQuery('#action').val('questao');
	var vl = jQuery('#id_aluno_selecionada').find(":selected").next().val();
	jQuery('#alunoId').val(vl);
	var vlw = jQuery('#id_questao_selecionada').find(":selected").next().val();
	jQuery('#proxim').val(vlw);
	form_env_corretor.submit();
});

jQuery('#id_questao_selecionada').change(function() {
	var vl = jQuery(this).val();
	jQuery('#questaoId').val(vl);
	form_seletor_corretor.submit();
});

jQuery('#id_aluno_selecionada').change(function() {
	var vl = jQuery(this).val();
	jQuery('#alunoId').val(vl);
	form_seletor_corretor.submit();
});

if(percent == 100){
	cemPC();
}

jQuery.mask.masks.nota = {
			   mask:'99,99',
			   type:'reverse',
		       'maxLength': 5,
		       textAlign: false
	       };

jQuery("#nota").setMask();
</script>