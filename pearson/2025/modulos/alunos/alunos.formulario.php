<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('JDesabilitadorDeCampos', 'JavaScript/JDesabilitadorDeCampos/', true);

include_once('alunos.formulario.exception.php');

class FAlunos extends Formulario
{
	protected $_formularioUsuario;
	
	public function __construct ($info = array())
	{
		parent::__construct($info);
		
		$this->_formularioUsuario = Core::modulo('_componente_usuario')->obterFormulario();
	}

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'matricula',
												'etiqueta' => 'Matrícula',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MIN => 2, Campo::TAM_MAX => 50),
												'html_tamanho_maximo' => 50,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterMatricula(),
												'html_ordem' => 1
							  )) );
							  
		$portadorSel = null;
		$corSel = null;
		if ( !$this->foiEnviado() && $this->obterEstado() == self::EDITANDO )
			$portadorSel = (int) $this->_dados->obterPortadorNecessidade();			
			$corSel = $this->_dados->obterCor();	
			
		$cor = Aluno::obterArrayCores();
		
		$this->adicionarCampo( new Campo(array(
			'nome' => 'portador_necessidade',
			'etiqueta' => 'Portador de necessidades especiais',
			'valor' => $portadorSel,
			'requerimento' => Campo::OPCIONAL,
			'tipo' => Campo::NATURAL,
			'argumentos' => array(Campo::IGUAL => 1),
			'html_valor' => 1,
			'html_tipo' => Campo::HTML_CAIXA_SELECAO,
			'html_ordem' => 2
		)) );

		/*$this->adicionarCampo( new Campo(array(
			'nome' => 'cor',
			'etiqueta' => 'Cor',
			'valor' => $corSel,
			'requerimento' => Campo::REQUERIDO,
			'tipo' => Campo::TEXTO,
			'argumentos' => array(Campo::POSSIBILIDADES => array_keys($cor)),
			'html_valor' => $cor,
			'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
			'html_tipo' => Campo::HTML_MENU,
			//'html_ordem' => 2
		)) );*/
							  
		$turmas = array( 'NULA' => 'Nenhuma' );
		$turmasSel = null;
		
		if ( in_array('1', Core::registro('usuario')->obterGrupos()) || in_array('2', Core::registro('usuario')->obterGrupos())  || in_array('11', Core::registro('usuario')->obterGrupos())) {
			foreach ( Turma::obterArrayTurmasParaFormulario(true, $this->_dados->obterUsuario()->obterInstituicao()) as $k => $v ) {
				$turmas[$k] = $v;
			}
		} else {
			if ($this->_dados->obterTurma()->obterID() != null) {
				$turmas = array( $this->_dados->obterTurma()->obterID() => $this->_dados->obterTurma()->obterNome() );
			}
		}
		
		if ( !$this->foiEnviado() && $this->_dados->obterTurma()->obterID() != null ) {
			$turmasSel = $this->_dados->obterTurma()->obterID();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'turmas',
												'etiqueta' => 'Turma',
												'valor' => $turmasSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($turmas)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $turmas,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 3
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'numero_chamada',
												'etiqueta' => 'Número de chamada',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::MAX => 999),
												'html_tamanho_maximo' => 3,
												'html_tamanho' => 3,
												'html_tamanho_tipo' => Campo::HTML_TAM_CARACTERES,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterNumeroChamada(),
												'html_ordem' => 4
							  )) );
				
		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar'
							  )) );
		
		$this->_formularioUsuario->fixarEstado($this->obterEstado());
		$this->_formularioUsuario->prepararEntrada($this->_entrada);
		$ordem = $this->_formularioUsuario->carregarFormulario($this->_dados->obterUsuario(), array('grupos' => array(4 => true)), 5);
		
		foreach ($this->_formularioUsuario->obterCampos() as $k => $v) {
			$this->_campos[$k] = $v;
		}
		
		$this->_campos['enviar']->fixar('html_ordem', $ordem);
				
		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outro aluno');		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próximo aluno');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			$this->_formularioUsuario->checarFormulario();
		}
		catch (FComponenteUsuario_Exception $e)
		{
			$this->_erros = $this->_formularioUsuario->obterErros();
			throw new FAlunos_Exception($e->getMessage());
		}
		catch (Formulario_Exception $e)
		{
			throw new FAlunos_Exception($e->getMessage());
		}
	}
	
	public function &executar ()
	{
		$usuario = $this->_formularioUsuario->executar();
	
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $usuario != false && $usuario->obterID() != null && $this->_adicionar($usuario) ) {
				Core::modulo('redirecionador')->fixarMensagem('Aluno adicionado com sucesso!', 'Adicionando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o aluno!', 'Adicionando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $usuario != false && $this->_editar($usuario) ) {
				Core::modulo('redirecionador')->fixarMensagem('Aluno editado com sucesso!', 'Editando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar o aluno!', 'Editando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		return $this->_dados;
	}
	
	public function foiEnviado ()
	{
		$retorno = parent::foiEnviado();
		
		$this->_formularioUsuario->fixarFoiEnviado($retorno);
		
		return $retorno;
	}
	
	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}
		
		$this->_dados = new Aluno($id);
		
		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->validarInstituicao() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('alunos', 'listar'), 'Aluno inv&aacute;lido!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$usuario = new UsuarioInstituido(null);
			$usuario->fixarInstituicao( Core::registro('instituicao') );
			$usuario->fixarEndereco( new Endereco(null) );
			$usuario->fixarEmail( new Email(null) );
			$this->_dados->fixarUsuario( $usuario );
			$this->_dados->fixarTurma( new Turma(null) );
		}
	}
	
	protected function _adicionar (UsuarioInstituido &$usuario)
	{
		$this->_dados = Aluno::obterNovoAluno($usuario);
		
		$fda = $this->_fixarDadosAluno();
		if($fda){
			return $this->_dados->salvar();
		}
		
		return false;
	}
	
	protected function _editar (UsuarioInstituido &$usuario)
	{
		$this->_dados->fixarUsuario($usuario);
		
		$fda = $this->_fixarDadosAluno();
		if($fda){
			return $this->_dados->salvar();
		}
		
		return false;
	}
	
	protected function _fixarDadosAluno ()
	{
		$turma = $this->_campos['turmas']->obter('valor');
		if ($turma == 'NULA') {
			$turma = null;
		}
		
		$matricula = $this->_campos['matricula']->obter('valor');

		$existe = 0;
		$dalunos = Core::registro('db')->query('SELECT * FROM alunos INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario WHERE alunos.a_matricula LIKE "'.$matricula.'" AND alunos.a_id <> "'.$this->_dados->obterID().'" LIMIT 1;');
		if ($dalunos->num_rows) {
			while ($dar = $dalunos->fetch_assoc()){
				$existe = 1;
			}
		}

		if($existe){
			Core::modulo('redirecionador')->fixarMensagem('Matrícula já existente!', 'Aten&ccedil;&atilde;o...');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			return false;
		}
		
		$this->_dados->fixarTurma(new Turma($turma));
		
		$this->_dados->fixarMatricula( $this->_campos['matricula']->obter('valor') );
		$this->_dados->fixarNumeroChamada( $this->_campos['numero_chamada']->obter('valor') );
		$this->_dados->fixarPortadorNecessidade( $this->_campos['portador_necessidade']->obter('valor') == 1 );
		//$this->_dados->fixarCor( $this->_campos['cor']->obter('valor') );
			
		return true;
	}
	
}

?>