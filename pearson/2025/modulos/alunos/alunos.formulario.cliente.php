<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('JDesabilitadorDeCampos', 'JavaScript/JDesabilitadorDeCampos/', true);

include_once('alunos.formulario.exception.cliente.php');

class FAlunosCliente extends Formulario
{
	protected $_formularioUsuario;
	
	public function __construct ($info = array())
	{
		parent::__construct($info);
		
		$this->_formularioUsuario = Core::modulo('_componente_cliente')->obterFormulario();
	}

	public function carregarFormulario ()
	{
		// PRIMEIRO: Verificar se é uma requisição AJAX para turmas
		if (isset($_POST['ajax_turmas']) && isset($_POST['instituicao_id'])) {
			$this->_processarAjaxTurmas();
			// Não chegará aqui devido ao exit no método acima
		}

		parent::carregarFormulario();
		$this->_carregar();
		
		// Campo Matrícula
		$this->adicionarCampo( new Campo(array( 'nome' => 'matricula',
												'etiqueta' => 'Matrícula',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MIN => 2, Campo::TAM_MAX => 50),
												'html_tamanho_maximo' => 50,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterMatricula(),
												'html_ordem' => 1
							  )) );
		
		// SEGUNDO: Carregar o componente cliente para ter acesso às instituições
		$this->_formularioUsuario->fixarEstado($this->obterEstado());
		$this->_formularioUsuario->prepararEntrada($this->_entrada);
		
		// CRUCIAL: Fixar o contexto como 'aluno' para o componente cliente
		$ordem = $this->_formularioUsuario->carregarFormulario(
			$this->_dados->obterUsuario(), 
			array(
				'grupos' => array(4 => true), 
				'contexto' => 'aluno'  // Forçar contexto aluno
			), 
			3
		);
		
		foreach ($this->_formularioUsuario->obterCampos() as $k => $v) {
			$this->_campos[$k] = $v;
		}
		
		// TERCEIRO: Campo Turmas - carregado dinamicamente baseado na instituição SELECIONADA
		$turmas = array( 'NULA' => 'Nenhuma' );
		$turmasSel = null;
		
		// Obter instituição selecionada do componente cliente já carregado
		$instituicaoSelecionada = null;
		
		if ($this->foiEnviado() && isset($_POST['instituicoes'])) {
			$instituicaoSelecionada = $_POST['instituicoes'];
		} elseif (isset($this->_campos['instituicoes']) && $this->_campos['instituicoes']->obter('valor')) {
			$instituicaoSelecionada = $this->_campos['instituicoes']->obter('valor');
		} elseif ($this->_dados->obterUsuario()->obterInstituicao()->obterID() != null) {
			$instituicaoSelecionada = $this->_dados->obterUsuario()->obterInstituicao()->obterID();
		} elseif (Core::registro('instituicao')->obterID() != null) {
			$instituicaoSelecionada = Core::registro('instituicao')->obterID();
		}
		
		// Carregar turmas apenas da instituição selecionada
		if ($instituicaoSelecionada && $instituicaoSelecionada != 'NULA' && $instituicaoSelecionada != '') {
			try {
				$instituicaoObj = new Instituicao($instituicaoSelecionada);
				if ($instituicaoObj->carregar()) {
					$turmas = array( 'NULA' => 'Nenhuma' );
					$turmasArray = Turma::obterArrayTurmasParaFormulario(true, $instituicaoObj);
					
					foreach ( $turmasArray as $k => $v ) {
						$turmas[$k] = $v;
					}
				}
			} catch (Exception $e) {
				// Em caso de erro, manter apenas "Nenhuma"
			}
		} else {
			$turmas = array( 'NULA' => 'Selecione uma instituição primeiro' );
		}
		
		// Definir turma selecionada
		if ( !$this->foiEnviado() && $this->_dados->obterTurma()->obterID() != null ) {
			$turmasSel = $this->_dados->obterTurma()->obterID();
		} elseif ($this->foiEnviado() && isset($_POST['turmas'])) {
			$turmasSel = $_POST['turmas'];
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'turmas',
												'etiqueta' => 'Turma',
												'valor' => $turmasSel,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($turmas)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $turmas,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
							  )) );
				
		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Salvar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar'
							  )) );
		
		$this->_campos['enviar']->fixar('html_ordem', $ordem);
				
		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outro aluno');		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próximo aluno');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	/**
	 * Processa requisições AJAX para carregar turmas por instituição
	 * DEVE ser chamado ANTES de qualquer output HTML
	 */
	private function _processarAjaxTurmas()
	{
		// Limpar qualquer output anterior
		if (ob_get_level()) {
			ob_clean();
		}
		
		// Headers JSON
		header('Content-Type: application/json; charset=utf-8');
		header('Cache-Control: no-cache, must-revalidate');
		header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
		
		$turmas = array();
		
		try {
			$instituicaoId = $_POST['instituicao_id'] ?? null;
			
			if ($instituicaoId && $instituicaoId != '' && $instituicaoId != 'NULA') {
				$instituicao = new Instituicao($instituicaoId);
				if ($instituicao->carregar()) {
					$turmas['NULA'] = 'Nenhuma';
					$turmasEncontradas = Turma::obterArrayTurmasParaFormulario(true, $instituicao);
					
					foreach ($turmasEncontradas as $k => $v) {
						$turmas[$k] = $v;
					}
				} else {
					$turmas['NULA'] = 'Instituição não encontrada';
				}
			} else {
				$turmas['NULA'] = 'Selecione uma instituição primeiro';
			}
		} catch (Exception $e) {
			$turmas['NULA'] = 'Erro ao carregar turmas';
		}
		
		// Retornar JSON limpo
		echo json_encode($turmas, JSON_UNESCAPED_UNICODE);
		exit;
	}
	
	public function checarFormulario ()
	{
		try
		{		
			parent::checarFormulario();
			
			$this->_formularioUsuario->checarFormulario();
		}
		catch (FComponenteCliente_Exception $e)
		{
			$this->_erros = $this->_formularioUsuario->obterErros();
			throw new FAlunosCliente_Exception($e->getMessage());
		}
		catch (Formulario_Exception $e)
		{
			throw new FAlunosCliente_Exception($e->getMessage());
		}
	}
	
	public function &executar ()
	{
		$usuario = $this->_formularioUsuario->executar();
	
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $usuario != false && $usuario->obterID() != null && $this->_adicionar($usuario) ) {
				Core::modulo('redirecionador')->fixarMensagem('Aluno adicionado com sucesso!', 'Adicionando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o aluno!', 'Adicionando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $usuario != false && $this->_editar($usuario) ) {
				Core::modulo('redirecionador')->fixarMensagem('Aluno editado com sucesso!', 'Editando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar o aluno!', 'Editando aluno...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		return $this->_dados;
	}
	
	public function foiEnviado ()
	{
		$retorno = parent::foiEnviado();
		
		$this->_formularioUsuario->fixarFoiEnviado($retorno);
		
		return $retorno;
	}
	
	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}
		
		$this->_dados = new Aluno($id);
		
		if ( $id != null ) {
			// MODIFICAÇÃO: Remover && $this->_dados->validarInstituicao()
			if ( $this->_dados->carregar() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('alunos', 'listarcliente'), 'Aluno inv&aacute;lido!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			$usuario = new UsuarioInstituido(null);
			// MODIFICAÇÃO: Não forçar instituição específica
			// $usuario->fixarInstituicao( Core::registro('instituicao') );
			$usuario->fixarEndereco( new Endereco(null) );
			$usuario->fixarEmail( new Email(null) );
			$this->_dados->fixarUsuario( $usuario );
			$this->_dados->fixarTurma( new Turma(null) );
		}
	}
	
	protected function _adicionar (UsuarioInstituido &$usuario)
	{
		// Primeiro criar o aluno
		$this->_dados = Aluno::obterNovoAluno($usuario);
		
		// Depois fixar os dados específicos do aluno
		if ($this->_fixarDadosAluno()) {
			// Salvar o aluno (que também salva o usuário)
			$resultado = $this->_dados->salvar();
			return $resultado;
		}
		
		return false;
	}
	
	protected function _editar (UsuarioInstituido &$usuario)
	{
		// Fixar o usuário
		$this->_dados->fixarUsuario($usuario);
		
		// Fixar dados específicos do aluno
		if ($this->_fixarDadosAluno()) {
			// Salvar tudo
			$resultado = $this->_dados->salvar();
			return $resultado;
		}
		
		return false;
	}
	
	protected function _fixarDadosAluno ()
	{
		$turma = $this->_campos['turmas']->obter('valor');
		$matricula = $this->_campos['matricula']->obter('valor');
		
		// Verificar se matrícula já existe (apenas se não estiver vazia)
		if (!empty($matricula)) {
			$existe = 0;
			$alunoId = $this->_dados->obterID() ?: 0;
			$sql = "SELECT * FROM alunos INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario WHERE alunos.a_matricula = '" . Core::registro('db')->escape($matricula) . "' AND alunos.a_id != '" . Core::registro('db')->escape($alunoId) . "' LIMIT 1";
			
			$dalunos = Core::registro('db')->query($sql);
			if ($dalunos && $dalunos->num_rows) {
				$existe = 1;
			}
			if ($dalunos) $dalunos->free();

			if($existe){
				Core::modulo('redirecionador')->fixarMensagem('Matrícula já existente!', 'Aten&ccedil;&atilde;o...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
				return false;
			}
		}
		
		// Fixar turma
		if ($turma && $turma != 'NULA' && $turma != '' && is_numeric($turma)) {
			$turmaObj = new Turma($turma);
			if ($turmaObj->carregar()) {
				$this->_dados->fixarTurma($turmaObj);
			} else {
				$this->_dados->fixarTurma(new Turma(null));
			}
		} else {
			$this->_dados->fixarTurma(new Turma(null));
		}
		
		// Fixar matrícula
		$this->_dados->fixarMatricula($matricula);
		
		// Valores padrão para campos obrigatórios
		$this->_dados->fixarNumeroChamada($this->_dados->obterNumeroChamada() ?: 0);
		$this->_dados->fixarPortadorNecessidade($this->_dados->obterPortadorNecessidade() ?: false);
		$this->_dados->fixarCor($this->_dados->obterCor() ?: 'BRANCA');
		
		return true;
	}
}

?>