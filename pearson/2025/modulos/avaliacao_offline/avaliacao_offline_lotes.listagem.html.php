<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lotes</title>
    <link rel="stylesheet" href="https://dyubuzjbgoyjh.cloudfront.net/avaliacao_offline/css/avaliacao_offline.css" />
    <link rel="stylesheet" href="https://dyubuzjbgoyjh.cloudfront.net/avaliacao_offline/css/lotes.css" />
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script> <!-- Adiciona Axios -->
</head>

<?php
$apiScan = Core::$gerenciadorDeDiretivas->obterDiretiva('API_SCAN');
?>

<script>
      const API_SCAN = "https://api.escaneamento.avaliarede.com.br/api_scan/avaliare_db_pearson_2025/";
</script>

<body>
    <input type="hidden" id="id_usuario" value="<?php echo Core::registro('autenticador')->obterUsuario()->obterID(); ?>"></input>
    <div class="container">
        <div class="header">
            <h2>Lista de Lotes</h2>
            <div id="backButtonContainer"></div>
        </div>
        <div id="lotesContainer"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Criar botão de voltar
            const backButton = document.createElement('button');
            backButton.textContent = 'Voltar';
            backButton.className = 'back-button';
            backButton.onclick = () => window.location.href = './?m=avaliacao_offline&a=listar';
            
            // Inserir botão de voltar no contêiner backButtonContainer
            const backButtonContainer = document.getElementById('backButtonContainer');
            backButtonContainer.appendChild(backButton);

            fetchLotes();
        });

        async function fetchLotes() {
            try {
                const userId = document.getElementById('id_usuario').value;
                const response = await axios.get(API_SCAN + `listar_lotes/${userId}`, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    withCredentials: true,  // Se precisar enviar cookies ou autenticação
                    crossdomain: true
                });

                const lotes = response.data;

                console.log(response);

                // Gerar HTML para os lotes
                let htmlContent = '';

                lotes.forEach(lote => {
                    htmlContent += `
                        <a href="./?m=avaliacao_offline&a=detalhar&id=${lote.sl_id}" class="lote-card-link">
                            <div class="lote-card">
                                <h3>Lote: ${lote.sl_id}</h3>
                                <p>Data de Criação: ${lote.sl_datetime}</p>
                                <p>Usuário: ${lote.sl_usuario}</p>
                            </div>
                        </a>
                    `;
                });

                const lotesContainer = document.getElementById('lotesContainer');
                lotesContainer.innerHTML = htmlContent;

            } catch (error) {
                console.error("Erro ao buscar lotes:", error);
            }
        }
    </script>
</body>
</html>
