<?
if ( Core::registro('permissoes')->tem<PERSON><PERSON><PERSON><PERSON><PERSON>('principal.texto_boas_vindas.corretor') ) {
	?>

	<STYLE type="text/css">
		.playing {text-decoration: underline;}
		.paused {}
		.progress {}

		a.buttonV{
			background: #eee; /* Old browsers */
			background: #eee -moz-linear-gradient(top, #07003B 0%, #07003B 100%); /* FF3.6+ */
			background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,#07003B), color-stop(100%,#07003B)); /* Chrome,Safari4+ */
			background: #eee -webkit-linear-gradient(top, #07003B 0%,#07003B 100%); /* Chrome10+,Safari5.1+ */
			background: #eee -o-linear-gradient(top, #07003B 0%,#07003B 100%); /* Opera11.10+ */
			background: #eee -ms-linear-gradient(top, #07003B 0%,#07003B 100%); /* IE10+ */
			background: #eee linear-gradient(top, #07003B 0%,#07003B 100%); /* W3C */
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#07003B', endColorstr='#07003B');
			border: 1px solid #07003B;
			padding: 4px 12px;
			-moz-border-radius: 5px;
			-webkit-border-radius: 5px;
			border-radius: 5px;
			color: #444;
			display: inline-block;
			font-size: 11px;
			font-weight: bold;
			text-decoration: none;
			text-shadow: 0 1px #555555;
			cursor: pointer;
			margin: 20px;
			line-height: 21px;
			font-family: "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; }

		a.buttonV:hover{
			color: #222;
			background: #eee; /* Old browsers */
			background: #eee -moz-linear-gradient(top, #100f4f 0%, #100f4f 100%); /* FF3.6+ */
			background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,#100f4f), color-stop(100%,#100f4f)); /* Chrome,Safari4+ */
			background: #eee -webkit-linear-gradient(top, #100f4f 0%,#100f4f 100%); /* Chrome10+,Safari5.1+ */
			background: #eee -o-linear-gradient(top, #100f4f 0%,#100f4f 100%); /* Opera11.10+ */
			background: #eee -ms-linear-gradient(top, #100f4f 0%,#100f4f 100%); /* IE10+ */
			background: #eee linear-gradient(top, #100f4f 0%,#100f4f 100%); /* W3C */
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#100f4f', endColorstr='#100f4f');
			border: 1px solid #100f4f;}

		a.buttonV:active{
			border: 1px solid #666;
			background: #ccc; /* Old browsers */
			background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
			background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
			background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
			background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
			background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
			background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ }
		filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#049CDB', endColorstr='#0064CD');


		table.table_associar_turma {
			font-family:Arial, Helvetica, sans-serif;
			color:#666;
			font-size:12px;
			text-shadow: 1px 1px 0px #fff;
			background:#eaebec;
			margin:20px;
			border:#ccc 1px solid;

			-moz-border-radius:3px;
			-webkit-border-radius:3px;
			border-radius:3px;

			-moz-box-shadow: 0 1px 2px #d1d1d1;
			-webkit-box-shadow: 0 1px 2px #d1d1d1;
			box-shadow: 0 1px 2px #d1d1d1;
		}

		table.table_associar_turma tbody {
			display:block;
			overflow:auto;
			height:250px;
			width:100%;
		}

		table.table_associar_turma th {
			padding:15px;
			border-top:1px solid #fafafa;
			border-bottom:1px solid #e0e0e0;

			background: #ededed;
			background: -webkit-gradient(linear, left top, left bottom, from(#ededed), to(#ebebeb));
			background: -moz-linear-gradient(top,  #ededed,  #ebebeb);
		}
		table.table_associar_turma th:first-child{
			text-align: left;
			padding-left:20px;
		}

		table.table_associar_turma tr:first-child th:first-child{
			-moz-border-radius-topleft:3px;
			-webkit-border-top-left-radius:3px;
			border-top-left-radius:3px;
		}
		table.table_associar_turma tr:first-child th:last-child{
			-moz-border-radius-topright:3px;
			-webkit-border-top-right-radius:3px;
			border-top-right-radius:3px;
		}
		table.table_associar_turma tr{
			text-align: center;
			padding-left:20px;
		}
		table.table_associar_turma tr td:first-child{
			text-align: left;
			padding-left:20px;
			border-left: 0;
		}
		table.table_associar_turma tr td {
			padding:10px;
			border-top: 1px solid #ffffff;
			border-bottom:1px solid #e0e0e0;
			border-left: 1px solid #e0e0e0;
			font-size: 15px;
			background: #fafafa;
			background: -webkit-gradient(linear, left top, left bottom, from(#fbfbfb), to(#fafafa));
			background: -moz-linear-gradient(top,  #fbfbfb,  #fafafa);
		}
		table.table_associar_turma tr.even td{
			background: #f6f6f6;
			background: -webkit-gradient(linear, left top, left bottom, from(#f8f8f8), to(#f6f6f6));
			background: -moz-linear-gradient(top,  #f8f8f8,  #f6f6f6);
		}
		table.table_associar_turma tr:last-child td{
			border-bottom:0;
		}
		table.table_associar_turma tr:last-child td:first-child{
			-moz-border-radius-bottomleft:3px;
			-webkit-border-bottom-left-radius:3px;
			border-bottom-left-radius:3px;
		}
		table.table_associar_turma tr:last-child td:last-child{
			-moz-border-radius-bottomright:3px;
			-webkit-border-bottom-right-radius:3px;
			border-bottom-right-radius:3px;
		}
		table.table_associar_turma tr:hover td{
			background: #f2f2f2;
			background: -webkit-gradient(linear, left top, left bottom, from(#f2f2f2), to(#f0f0f0));
			background: -moz-linear-gradient(top,  #f2f2f2,  #f0f0f0);
		}

		a.add_turma_associada
		{
			background-image: url(estilos/azul/media/old_edit_redo.png);
			background-repeat: no-repeat;
			font-family: Arial, Helvetica, sans-serif;
			color: #666;
			font-size: 20px;
			text-shadow: 1px 1px 0px #fff;
			padding-left: 47px;
			padding-bottom: 10px;
			padding-top: 100px;
		}

		.remove_turma_associada
		{
			background-image: url(estilos/azul/media/falha-pequeno.png);
			background-repeat: no-repeat;
			font-family: Arial, Helvetica, sans-serif;
			color: #666;
			font-size: 22px;
			text-shadow: 1px 1px 0px #fff;
			padding-left: 40px;
			padding-bottom: 0px;
			padding-top: 5px;
		}

		.salvar_turma_associada
		{
			font-family:Arial, Helvetica, sans-serif;
			color:#666;
			font-size:22px;
			text-shadow: 1px 1px 0px #fff;
			display: none;
		}

			a.button img{
				float: left;
    			height: 35px;
			}

			a.button span{
				float: left;
				font-size: 18px;
				height: 40px;
				line-height: 42px;
				margin-left: 10px;
				overflow: hidden;
			}

			a.button {
				background: #eee; /* Old browsers */
				background: #eee -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
				background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
				background: #eee -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
				background: #eee -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
				background: #eee -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
				background: #eee linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
			  border: 1px solid #aaa;
			  border-top: 1px solid #ccc;
			  border-left: 1px solid #ccc;
			  padding: 4px 12px;
			  -moz-border-radius: 3px;
			  -webkit-border-radius: 3px;
			  border-radius: 3px;
			  color: #444;
			  display: inline-block;
			  font-size: 11px;
			  font-weight: bold;
			  text-decoration: none;
			  text-shadow: 0 1px rgba(255, 255, 255, .75);
			  cursor: pointer;
			  margin: 0;
			  line-height: 21px;
			  font-family: "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; 
			}

			a.button:hover {
				color: #222;
				background: #ddd; /* Old browsers */
				background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.3) 0%, rgba(0,0,0,.3) 100%); /* FF3.6+ */
				background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.3)), color-stop(100%,rgba(0,0,0,.3))); /* Chrome,Safari4+ */
				background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Chrome10+,Safari5.1+ */
				background: #ddd -o-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Opera11.10+ */
				background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* IE10+ */
				background: #ddd linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* W3C */
			  border: 1px solid #888;
			  border-top: 1px solid #aaa;
			  border-left: 1px solid #aaa; 
			}

			a.button:active {
				border: 1px solid #666;
				background: #ccc; /* Old browsers */
				background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
				background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
				background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
				background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
				background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
				background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ 
			}
	</STYLE>
	<input type="hidden" id="idProfessor" value='<?echo $this->idProfessor?>' />
	<input type="hidden" id="turmasAssociada" value='<?echo $this->turmasAssociada?>' />
	<input type="hidden" id="qtdAulas" value='<?echo count($this->aulas); ?>' />

		<STYLE type="text/css">
			/* * {box-sizing:border-box} */

			/* Slideshow container */
			.slideshow-container {
			  border: solid #aaa 1px;
			  max-width: 800px;
			  position: relative;
			  margin: auto;
			  width: 800px;
			  height: 450px;
			}

			/* Hide the images by default */
			.mySlides {
			  display: none;
			}

			/* Next & previous buttons */
			.prev, .next {
			  cursor: pointer;
			  position: absolute;
			  top: 50%;
			  width: auto;
			  margin-top: -22px;
			  padding: 10px;
			  color: white;
			  font-weight: bold;
			  font-size: 18px;
			  transition: 0.6s ease;
			  border-radius: 0 3px 3px 0;
			  user-select: none;
			  background-color: rgba(0,0,0,0.1);
			}

			/* Position the "next button" to the right */
			.prev {
			  left: 0;
			  border-radius: 3px 0 0 3px;
			}

			/* Position the "next button" to the right */
			.next {
			  right: 0;
			  border-radius: 3px 0 0 3px;
			}

			/* On hover, add a black background color with a little bit see-through */
			.prev:hover, .next:hover {
			  text-decoration: none;
			  background-color: rgba(0,0,0,0.5);
			}

			/* Caption text */
			.text {
			  color: #f2f2f2;
			  font-size: 15px;
			  padding: 8px 12px;
			  position: absolute;
			  bottom: 8px;
			  width: 100%;
			  text-align: center;
			}

			/* Number text (1/3 etc) */
			.numbertext {
			  color: #aaa;
			  font-size: 12px;
			  padding: 8px 12px;
			  position: absolute;
			  top: 0;
			  right: 0;
			}

			/* The dots/bullets/indicators */
			.dot {
			  cursor: pointer;
			  height: 10px;
			  width: 10px;
			  margin: 0 2px;
			  background-color: #bbb;
			  border-radius: 50%;
			  display: inline-block;
			  transition: background-color 0.6s ease;
			}

			.active, .dot:hover {
			  background-color: #717171;
			}

			/* Fading animation */
			.fade {
			  -webkit-animation-name: fade;
			  -webkit-animation-duration: 1.5s;
			  animation-name: fade;
			  animation-duration: 1.5s;
			}

			@-webkit-keyframes fade {
			  from {opacity: .4}
			  to {opacity: 1}
			}

			@keyframes fade {
			  from {opacity: .4}
			  to {opacity: 1}
			}
		</STYLE>
		<div style="float:left;width: 25%;">
			<div style="font-family: arial, sans-serif;font-size: 14px;font-weight: bold;color: #0E487A; word-spacing: 2px;">
			Seja bem-vindo!
			<br><br>
			Essa é a Plataforma de Avaliações da Pearson.
			<br><br>
			Caso necessite de qualquer suporte para utilizar da melhor 
			maneira<br> todas as informações sobre sua escola, entre em contato conosco,<br> teremos o prazer em lhe atender!
			<br><br>
			Equipe Pearson
		</div>
            <br>
            <br>
			<div style="margin:-20px;">
				<p><a href="./?m=lancamento_fluencia&amp;a=lancamento_fluencia" id="button" class="buttonV" style="text-align: center; font-size: 14px; padding: 5px; color: rgb(238, 238, 238); opacity: 1; width: 260px; display: inline-block;">
					<img border="0" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
stock_task.png" style="border: 0px; width: 45px; float: left; padding-left: 3px">
					<span style="display: inline-block; padding-top: 12px; width: 170px; font-family: Arial;">INICIAR CORREÇÃO<br>FLUÊNCIA LEITORA</span>
				</a></p>
			</div>
			<div style="clear: both; padding: 10px; margin: -10px; width: 230px;">
				<p>
					Para um melhor <u>desempenho</u> e <u>segurança</u> durante a <b>correção</b> utilize somente o navegador:
				</p>
				<ul style="list-style-type: none;">
					<li>
						<a href="https://www.google.com/chrome?hl=pt-br" target="_blank">
							<img style="border:0;height: 16px;" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
gchrome.ico"/>
							<span style="vertical-align: top;">Google Chrome</span>
						</a>
					</li>
				</ul>
			</div>
		</div>
		<script type="text/javascript">
			var slideIndex = 1;
			showSlides(slideIndex);

			// Next/previous controls
			function plusSlides(n) {
			  showSlides(slideIndex += n);
			}

			// Thumbnail image controls
			function currentSlide(n) {
			  showSlides(slideIndex = n);
			}

			function showSlides(n) {
			  var i;
			  var slides = document.getElementsByClassName("mySlides");
			  var dots = document.getElementsByClassName("dot");
			  if (n > slides.length) {slideIndex = 1}
			  if (n < 1) {slideIndex = slides.length}
			  for (i = 0; i < slides.length; i++) {
			      slides[i].style.display = "none";
			  }
			  for (i = 0; i < dots.length; i++) {
			      dots[i].className = dots[i].className.replace(" active", "");
			  }
			  slides[slideIndex-1].style.display = "block";
			  dots[slideIndex-1].className += " active";
			} 
		</script>
	<?
} elseif ( Core::registro('permissoes')->temPermissao('principal.texto_boas_vindas.aplicador') ) {
	?>

	<STYLE type="text/css">
		.playing {text-decoration: underline;}
		.paused {}
		.progress {}

		a.buttonV{
			background: #eee; /* Old browsers */
			background: #eee -moz-linear-gradient(top, #07003B 0%, #07003B 100%); /* FF3.6+ */
			background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,#07003B), color-stop(100%,#07003B)); /* Chrome,Safari4+ */
			background: #eee -webkit-linear-gradient(top, #07003B 0%,#07003B 100%); /* Chrome10+,Safari5.1+ */
			background: #eee -o-linear-gradient(top, #07003B 0%,#07003B 100%); /* Opera11.10+ */
			background: #eee -ms-linear-gradient(top, #07003B 0%,#07003B 100%); /* IE10+ */
			background: #eee linear-gradient(top, #07003B 0%,#07003B 100%); /* W3C */
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#07003B', endColorstr='#07003B');
			border: 1px solid #07003B;
			padding: 4px 12px;
			-moz-border-radius: 5px;
			-webkit-border-radius: 5px;
			border-radius: 5px;
			color: #444;
			display: inline-block;
			font-size: 11px;
			font-weight: bold;
			text-decoration: none;
			text-shadow: 0 1px #555555;
			cursor: pointer;
			margin: 20px;
			line-height: 21px;
			font-family: "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; }

		a.buttonV:hover{
			color: #222;
			background: #eee; /* Old browsers */
			background: #eee -moz-linear-gradient(top, #100f4f 0%, #100f4f 100%); /* FF3.6+ */
			background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,#100f4f), color-stop(100%,#100f4f)); /* Chrome,Safari4+ */
			background: #eee -webkit-linear-gradient(top, #100f4f 0%,#100f4f 100%); /* Chrome10+,Safari5.1+ */
			background: #eee -o-linear-gradient(top, #100f4f 0%,#100f4f 100%); /* Opera11.10+ */
			background: #eee -ms-linear-gradient(top, #100f4f 0%,#100f4f 100%); /* IE10+ */
			background: #eee linear-gradient(top, #100f4f 0%,#100f4f 100%); /* W3C */
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#100f4f', endColorstr='#100f4f');
			border: 1px solid #100f4f;}

		a.buttonV:active{
			border: 1px solid #666;
			background: #ccc; /* Old browsers */
			background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
			background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
			background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
			background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
			background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
			background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ }
		filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#049CDB', endColorstr='#0064CD');


		table.table_associar_turma {
			font-family:Arial, Helvetica, sans-serif;
			color:#666;
			font-size:12px;
			text-shadow: 1px 1px 0px #fff;
			background:#eaebec;
			margin:20px;
			border:#ccc 1px solid;

			-moz-border-radius:3px;
			-webkit-border-radius:3px;
			border-radius:3px;

			-moz-box-shadow: 0 1px 2px #d1d1d1;
			-webkit-box-shadow: 0 1px 2px #d1d1d1;
			box-shadow: 0 1px 2px #d1d1d1;
		}

		table.table_associar_turma tbody {
			display:block;
			overflow:auto;
			height:250px;
			width:100%;
		}

		table.table_associar_turma th {
			padding:15px;
			border-top:1px solid #fafafa;
			border-bottom:1px solid #e0e0e0;

			background: #ededed;
			background: -webkit-gradient(linear, left top, left bottom, from(#ededed), to(#ebebeb));
			background: -moz-linear-gradient(top,  #ededed,  #ebebeb);
		}
		table.table_associar_turma th:first-child{
			text-align: left;
			padding-left:20px;
		}

		table.table_associar_turma tr:first-child th:first-child{
			-moz-border-radius-topleft:3px;
			-webkit-border-top-left-radius:3px;
			border-top-left-radius:3px;
		}
		table.table_associar_turma tr:first-child th:last-child{
			-moz-border-radius-topright:3px;
			-webkit-border-top-right-radius:3px;
			border-top-right-radius:3px;
		}
		table.table_associar_turma tr{
			text-align: center;
			padding-left:20px;
		}
		table.table_associar_turma tr td:first-child{
			text-align: left;
			padding-left:20px;
			border-left: 0;
		}
		table.table_associar_turma tr td {
			padding:10px;
			border-top: 1px solid #ffffff;
			border-bottom:1px solid #e0e0e0;
			border-left: 1px solid #e0e0e0;
			font-size: 15px;
			background: #fafafa;
			background: -webkit-gradient(linear, left top, left bottom, from(#fbfbfb), to(#fafafa));
			background: -moz-linear-gradient(top,  #fbfbfb,  #fafafa);
		}
		table.table_associar_turma tr.even td{
			background: #f6f6f6;
			background: -webkit-gradient(linear, left top, left bottom, from(#f8f8f8), to(#f6f6f6));
			background: -moz-linear-gradient(top,  #f8f8f8,  #f6f6f6);
		}
		table.table_associar_turma tr:last-child td{
			border-bottom:0;
		}
		table.table_associar_turma tr:last-child td:first-child{
			-moz-border-radius-bottomleft:3px;
			-webkit-border-bottom-left-radius:3px;
			border-bottom-left-radius:3px;
		}
		table.table_associar_turma tr:last-child td:last-child{
			-moz-border-radius-bottomright:3px;
			-webkit-border-bottom-right-radius:3px;
			border-bottom-right-radius:3px;
		}
		table.table_associar_turma tr:hover td{
			background: #f2f2f2;
			background: -webkit-gradient(linear, left top, left bottom, from(#f2f2f2), to(#f0f0f0));
			background: -moz-linear-gradient(top,  #f2f2f2,  #f0f0f0);
		}

		a.add_turma_associada
		{
			background-image: url(estilos/azul/media/old_edit_redo.png);
			background-repeat: no-repeat;
			font-family: Arial, Helvetica, sans-serif;
			color: #666;
			font-size: 20px;
			text-shadow: 1px 1px 0px #fff;
			padding-left: 47px;
			padding-bottom: 10px;
			padding-top: 100px;
		}

		.remove_turma_associada
		{
			background-image: url(estilos/azul/media/falha-pequeno.png);
			background-repeat: no-repeat;
			font-family: Arial, Helvetica, sans-serif;
			color: #666;
			font-size: 22px;
			text-shadow: 1px 1px 0px #fff;
			padding-left: 40px;
			padding-bottom: 0px;
			padding-top: 5px;
		}

		.salvar_turma_associada
		{
			font-family:Arial, Helvetica, sans-serif;
			color:#666;
			font-size:22px;
			text-shadow: 1px 1px 0px #fff;
			display: none;
		}

			a.button img{
				float: left;
    			height: 35px;
			}

			a.button span{
				float: left;
				font-size: 18px;
				height: 40px;
				line-height: 42px;
				margin-left: 10px;
				overflow: hidden;
			}

			a.button {
				background: #eee; /* Old browsers */
				background: #eee -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
				background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
				background: #eee -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
				background: #eee -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
				background: #eee -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
				background: #eee linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
			  border: 1px solid #aaa;
			  border-top: 1px solid #ccc;
			  border-left: 1px solid #ccc;
			  padding: 4px 12px;
			  -moz-border-radius: 3px;
			  -webkit-border-radius: 3px;
			  border-radius: 3px;
			  color: #444;
			  display: inline-block;
			  font-size: 11px;
			  font-weight: bold;
			  text-decoration: none;
			  text-shadow: 0 1px rgba(255, 255, 255, .75);
			  cursor: pointer;
			  margin: 0;
			  line-height: 21px;
			  font-family: "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; 
			}

			a.button:hover {
				color: #222;
				background: #ddd; /* Old browsers */
				background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.3) 0%, rgba(0,0,0,.3) 100%); /* FF3.6+ */
				background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.3)), color-stop(100%,rgba(0,0,0,.3))); /* Chrome,Safari4+ */
				background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Chrome10+,Safari5.1+ */
				background: #ddd -o-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Opera11.10+ */
				background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* IE10+ */
				background: #ddd linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* W3C */
			  border: 1px solid #888;
			  border-top: 1px solid #aaa;
			  border-left: 1px solid #aaa; 
			}

			a.button:active {
				border: 1px solid #666;
				background: #ccc; /* Old browsers */
				background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
				background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
				background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
				background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
				background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
				background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ 
			}
	</STYLE>
	<input type="hidden" id="idProfessor" value='<?echo $this->idProfessor?>' />
	<input type="hidden" id="turmasAssociada" value='<?echo $this->turmasAssociada?>' />
	<input type="hidden" id="qtdAulas" value='<?echo count($this->aulas); ?>' />

		<STYLE type="text/css">
			/* * {box-sizing:border-box} */

			/* Slideshow container */
			.slideshow-container {
			  border: solid #aaa 1px;
			  max-width: 800px;
			  position: relative;
			  margin: auto;
			  width: 800px;
			  height: 450px;
			}

			/* Hide the images by default */
			.mySlides {
			  display: none;
			}

			/* Next & previous buttons */
			.prev, .next {
			  cursor: pointer;
			  position: absolute;
			  top: 50%;
			  width: auto;
			  margin-top: -22px;
			  padding: 10px;
			  color: white;
			  font-weight: bold;
			  font-size: 18px;
			  transition: 0.6s ease;
			  border-radius: 0 3px 3px 0;
			  user-select: none;
			  background-color: rgba(0,0,0,0.1);
			}

			/* Position the "next button" to the right */
			.prev {
			  left: 0;
			  border-radius: 3px 0 0 3px;
			}

			/* Position the "next button" to the right */
			.next {
			  right: 0;
			  border-radius: 3px 0 0 3px;
			}

			/* On hover, add a black background color with a little bit see-through */
			.prev:hover, .next:hover {
			  text-decoration: none;
			  background-color: rgba(0,0,0,0.5);
			}

			/* Caption text */
			.text {
			  color: #f2f2f2;
			  font-size: 15px;
			  padding: 8px 12px;
			  position: absolute;
			  bottom: 8px;
			  width: 100%;
			  text-align: center;
			}

			/* Number text (1/3 etc) */
			.numbertext {
			  color: #aaa;
			  font-size: 12px;
			  padding: 8px 12px;
			  position: absolute;
			  top: 0;
			  right: 0;
			}

			/* The dots/bullets/indicators */
			.dot {
			  cursor: pointer;
			  height: 10px;
			  width: 10px;
			  margin: 0 2px;
			  background-color: #bbb;
			  border-radius: 50%;
			  display: inline-block;
			  transition: background-color 0.6s ease;
			}

			.active, .dot:hover {
			  background-color: #717171;
			}

			/* Fading animation */
			.fade {
			  -webkit-animation-name: fade;
			  -webkit-animation-duration: 1.5s;
			  animation-name: fade;
			  animation-duration: 1.5s;
			}

			@-webkit-keyframes fade {
			  from {opacity: .4}
			  to {opacity: 1}
			}

			@keyframes fade {
			  from {opacity: .4}
			  to {opacity: 1}
			}
			.botao {
				display: block;
				width: 270px; /* Largura dos botões */
				height: 50px; /* Altura dos botões */
				margin: 10px auto; /* Margem automática */
				background-color: #808080; /* Cor de fundo dos botões */
				color: white; /* Cor do texto */
				border: none;
				border-radius: 5px; /* Bordas arredondadas */
				font-size: 16px; /* Tamanho da fonte */
				cursor: pointer; /* Cursor ao passar o mouse */
				transition: background-color 0.3s; /* Transição suave da cor de fundo */
				position: relative; /* Posição relativa para adicionar o ícone */
				text-align: justify; /* Justificar o texto */
				padding-right: 10px; /* Adicionar espaço à direita para o ícone */
				box-sizing: border-box; /* Incluir o padding na largura do botão */
			}

			.botao:hover {
				background-color: #C71585; /* Mudança de cor ao passar o mouse */
			}

			.botao img {
			vertical-align: middle; /* Alinha a imagem verticalmente */
			width: 20px; /* Largura do ícone */
			height: auto; /* Altura automática para manter a proporção */
			margin-right: 10px; /* Margem à direita da imagem */
		}

		.buttonInicioFluencia {
			display: inline-block;
			width: 350px;
			text-align: left;
			font-size: 16px;
			padding: 10px;
			color: #eee;
			background-color: #07003B;
			border: none;
			border-radius: 5px;
			text-decoration: none;
			transition: background-color 0.3s;
			vertical-align: middle;
			margin: 15px;
		}

		.buttonInicioFluencia img {
			width: 45px;
			vertical-align: middle;
			margin-right: 10px;
		}

		.buttonInicioFluencia span {
			display: inline-block;
			vertical-align: middle;
			font-family: Arial;
			text-align: center;
		}

		.buttonInicioFluencia:hover {
			background-color: #45a049;
			text-decoration: none;
		}
		</STYLE>

		<script src="modulos/principal/js/modal_box_etapa1.js"></script>
		<script src="modulos/principal/js/modal_box_etapa2.js"></script>
		<script src="modulos/principal/js/modal_box_etapa3.js"></script>
		<script src="modulos/principal/js/modal_box_etapa4.js"></script>
		<script src="modulos/principal/js/modal_box_etapa5.js"></script>
		<script src="modulos/principal/js/modal_box_etapa6.js"></script>

		<div style="text-align: center">
			<table style="width: 100%;">
				<tr>
					<td colspan=2>
						<h2 style="background-color: #ccc; border-radius: 8px; color: red; padding: 20px;">APLICADOR(A):<br>É OBRIGATÓRIO SEGUIR CADA UMA DAS ETAPAS LISTADAS ABAIXO, CLIQUE E LEIA.</h2>
					</td>
				</tr>
				<tr>
					<td>
					
						<button class="botao etapa1"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472144_sq_plus.png"><b>1º ETAPA:</b> Chegada Escola</button>
						<button class="botao etapa2"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472144_sq_plus.png"><b>2º ETAPA:</b> Chegada Turma</button>
						<button class="botao etapa3"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472144_sq_plus.png"><b>3º ETAPA:</b> Lista de Presença<br><label style="padding-left: 30%">(Folha Física)</label></button>
						<button class="botao etapa4"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472144_sq_plus.png"><b>4º ETAPA:</b> Aplicação</button>
						<button class="botao etapa5"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472144_sq_plus.png"><b>5º ETAPA:</b> Alunos Avaliados<br><label style="padding-left: 15%">(Lista de presença on-line)</label></button>
						<button class="botao etapa6"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472144_sq_plus.png"><b>6º ETAPA:</b> Finalização</button>

					</td>
					<td>
						
						<div style="">
						<a href="./?m=fluencia_leitora&amp;a=realizar" class="buttonInicioFluencia">
							<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472134_playback_play.png" alt="Ícone Iniciar" border="0">
							<span style="font-weight: bold">INICIAR APLICAÇÃO<br>(Fluência Leitora)</span>
						</a>
						</div>

						<div style="">
						<a href="./?m=lancamento_presencas&amp;a=lancamento_presencas" class="buttonInicioFluencia">
							<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472134_playback_play.png" alt="Ícone Iniciar" border="0">
							<span style="font-weight: bold">LANÇAMENTO DE PRESENÇAS<br>(Escrita e Matemática)</span>
						</a>
						</div>
						
						<div style="">
						<a href="./?m=lancamento_presencas&amp;a=lancamento_presencas" class="buttonInicioFluencia">
							<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
1372472134_playback_play.png" alt="Ícone Iniciar" border="0">
							<span style="font-weight: bold">LANÇAMENTO DE PRESENÇAS<br>(Fluência Leitora)</span>
						</a>
						</div>

					</td>
				</tr>
			</table>
		</div>

		<div id="etapa1" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalEtapa1();" class="js-modal-close close"></a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - ETAPA 1</p>
			</header>

			<div>
				<div style="width: 90%; padding-left: 50px">			
					<h1 style=" text-align: justify"><b style="color: blue">➨</b> Ao chegar na escola procure a direção para registrar sua chegada por meio do escaneamento do <b style="color: green">QRCode</b>(Modelo abaixo) e realização do login.</h1>
					<h1 style=" text-align: justify"><b style="color: blue">➨</b> Esteja presente no local pontualmente até as <b style="color: red">06h30</b> para aplicação no período da manhã e <b style="color: red">11h30</b> para aplicação no período da tarde.</h1>
					<div style=" margin 0; text-align: center"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
checkin.png" style="max-width: 20%; height: auto; margin 0; text-align: center"></div>
				</div>
			</div>
			
			<footer>
				<a href="javascript:closeModalEtapa1();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

		<div id="etapa2" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalEtapa2();" class="js-modal-close close"></a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - ETAPA 2</p>
			</header>

			<div>
				<div style="width: 90%; padding-left: 50px">
					<h1 style="text-align: justify;"><b style="color: blue">➨</b> Aponte a camera do celular para o <b style="color: green">QRCode</b> da lista de presença antes de iniciar a avaliação.</h1>
					<h1 style="text-align: justify; color: red"><b style="color: blue">➨</b> O QRCode está localizado no canto superior direito da folha.</h1>
					<h1 style="text-align: justify;"><b style="color: blue">➨</b> Somente após a conclusão deste procedimento você estará autorizado(a) a iniciar a avaliação.</h1>
					<div style=" margin 0; text-align: center"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
checkin-camera.png" style="max-width: 80%; height: auto; margin 0; text-align: center"></div>

				</div>
			</div>
			
			<footer>
				<a href="javascript:closeModalEtapa2();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

		<div id="etapa3" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalEtapa3();" class="js-modal-close close"></a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - ETAPA 3</p>
			</header>

			<div>
				<div style="width: 90%; padding-left: 50px">	
					<h1 style="text-align: justify;"><b style="color: blue">➨</b> Preencher a lista de presença localizada no material conforme a imagem abaixo:</h1>	
					<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
listadepresenca.jpg" style="max-width: 100%; height: auto;">
				</div>
			</div>
			
			<footer>
				<a href="javascript:closeModalEtapa3();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

		<div id="etapa4" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalEtapa4();" class="js-modal-close close"></a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - ETAPA 4</p>
			</header>

			<div>
				<div style="width: 90%; padding-left: 50px">
				<br>
					<h1 style="text-align: justify;"><b style="color: blue">➨</b> FLUÊNCIA LEITORA: Clique no botão Iniciar Avaliação conforme a imagem abaixo:</h1>
					<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
botao-iniciarFluencia.png" style="max-width: 100%; height: auto;">
					<h1 style="text-align: justify;"><b style="color: blue">➨</b> ESCRITA: O próprio aluno preenche o nome (faz parte da avaliação).</h1>
					<h1 style="text-align: justify; color: red">ATENÇÃO AO ENTREGAR A FOLHA DE ESCRITA PARA CADA ALUNO!</h1>
					<h1 style="text-align: justify;">Cada folha possui o RA e as iniciais do nome do aluno já preenchidos, verifique a lista da turma para confirmação e entrega.</h1>


					<h1 style="text-align: justify;"><b style="color: blue">➨</b> MATEMÁTICA: Garantir o preenchimento completo do nome do aluno e o RA na capa do caderno.</h1>
				</div>
			</div>
			
			<footer>
				<a href="javascript:closeModalEtapa4();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

		<div id="etapa5" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalEtapa5();" class="js-modal-close close"></a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - ETAPA 5</p>
			</header>

			<div>
				<div style="width: 90%; padding-left: 50px">
				<br>
					<h1 style="text-align: center;">
						<b style="color: blue">➨</b> 
						Utilizando o lançamento de presenças na plataforma e a lista de presenças impressa, indique todos os alunos avaliados nos 3 instrumentos (Fluência, Escrita e Matemática)
						<br><br>
						<img src="./modulos/principal/botoes_presencas.jpg" style="max-width: 100%; height: 200px;">
					</h1>
				</div>
			</div>
			
			<footer>
				<a href="javascript:closeModalEtapa5();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

		
		<div id="etapa6" class="modal-box" style="width: 70% !important;">
			<header>
				<a href="javascript:closeModalEtapa6();" class="js-modal-close close"></a>
				<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/>
				<p style="text-align:left; font-size:18px; opacity:70%"> - ETAPA 6</p>
			</header>

			<div>
				<div style="width: 90%; padding-left: 50px">			
					<h1 style=" text-align: justify"><b style="color: blue">➨</b> Após concluir todas as etapas anteriores, procure a direção escolar para registrar a sua saida da escola através do QRCode, mesmo que você tenha aplicação no período da tarde nessa escola.</h1>
					<div style=" margin 0; text-align: center"><img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
checkout.png" style="max-width: 20%; height: auto; margin 0; text-align: center"></div>
				</div>
			</div>
			
			<footer>
				<a href="javascript:closeModalEtapa6();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Fechar</a>
			</footer>
		</div>

	<?
} elseif ( Core::registro('permissoes')->temPermissao('principal.texto_boas_vindas.professor') ) {
	?>
	<input type="hidden" id="idProfessor" value='<?echo $this->idProfessor?>' />
	<input type="hidden" id="turmasAssociada" value='<?echo $this->turmasAssociada?>' />
	<input type="hidden" id="qtdAulas" value='<?echo count($this->aulas); ?>' />

	<? if(count($this->aulas) <= 0){ ?>

	<div id="turma_associada" style="display: none;">
		<div style="font-size: 24px;"><strong>Caro professor(a):<br></strong>
		Selecione abaixo <u>todas</u> as turmas em que você leciona.
		</div>
		<table style="margin: 0 auto;">
			<tr>
				<td>
					<p style="font-size: 20px;margin-bottom: 0;">Selecione abaixo suas turmas:</p>
					<select class="w3-input" id="turma_associada_select" style="border: 1px solid #aaa;border-radius: 10px;">
						<?  
						foreach ($this->turmas as $escolaNome => $turmas){
							echo '<option id="nameEscola" disabled="disabled">'.$escolaNome.":</option>";
							foreach ($turmas as $tk => $tv) {
								$tv = str_replace($escolaNome, '', $tv);
								echo '<option  value='.$tk.'>'.$tv."</option>";
							}
						} 
						?>
					</select>
				</td>
				<td>
					&nbsp;&nbsp;&nbsp;<a href="javascript:addAssociarturma();" class="add_turma_associada">Adicionar</a>
				</td>
				<td style="padding: 20px;">
					<p style="font-size: 20px;margin-bottom: 0; font-weight: normal;">Turmas selecionadas:</p>
					<table id="turma_associada_tbl" class="table_associar_turma" style="float:left;height: 250px;border: 1px solid #aaa;border-radius: 10px;padding: 5px;min-width: 255px;">
						<tr style="display: none;"></tr>
					</table>
				</td>
			</tr>
		</table>
		<table style="width:100%; border-top: 1px solid #aaa;">
			<tr>
				<td style="text-align: center;">
					<a href="javascript:salvarAssociarturma();" class="salvar_turma_associada button" style='display:none;'>
						<!--<img src='estilos/azul/media/sucesso-pequeno.png'/>-->
						<span>Salvar</span>
					</a>
				</td>
			</tr>
		</table>
	</div>

	<script type="text/javascript">
	     var JAVA_URL_BASE = "<?= Core::diretiva('JAVA_URL_BASE'); ?>";
    
		// Função para detectar dispositivos móveis
		function isMobileDevice() {
			return (typeof window.orientation !== "undefined") 
				|| (navigator.userAgent.indexOf('IEMobile') !== -1)
				|| (navigator.userAgent.match(/(iPhone|iPod|iPad|Android|BlackBerry|Windows Phone)/i))
				|| (window.innerWidth <= 800 && window.innerHeight <= 600);
		}
		
		// Carregar o script apropriado baseado no tipo de dispositivo
		function loadAppropriateScript() {
			var scriptElement = document.createElement('script');
			
			if (isMobileDevice()) {
				console.log('Carregando versão mobile');
				scriptElement.src = "modulos/principal/js/turma_associada_mobile.js";
			} else {
				console.log('Carregando versão web');
				scriptElement.src = "modulos/principal/js/jquery.modal_box_turma_associada.js";
			}
			
			document.head.appendChild(scriptElement);
		}
		
		// Executar o carregamento quando o DOM estiver pronto
		if (document.readyState === "loading") {
			document.addEventListener("DOMContentLoaded", loadAppropriateScript);
		} else {
			loadAppropriateScript();
		}   
	</script>
	<? 
	}
	
} else if ( Core::registro('permissoes')->temPermissao('principal.texto_boas_vindas.diretor') ) {
	?>
	<div style="font-family: arial, sans-serif;font-size: 14px;font-weight: bold;color: #0E487A; word-spacing: 2px;">
		Seja bem-vindo!
		<br><br>
		Essa é a Plataforma de Avaliações da Pearson.
		<br><br>
		Caso necessite de qualquer suporte para utilizar da melhor 
		maneira<br> todas as informações sobre sua escola, entre em contato conosco,<br> teremos o prazer em lhe atender!
		<br><br>
		Equipe Pearson
	</div>

	<?
} else if ( Core::registro('permissoes')->temPermissao('principal.texto_boas_vindas.secretario') ) {
	?>
	 <div style="font-family: arial, sans-serif;font-size: 14px;font-weight: bold;color: #0E487A; word-spacing: 2px;">
        Seja bem-vindo!
        <br><br>
        Essa é a Plataforma de Gestão de Informações da Pearson.
        <br><br>
        Caso necessite de qualquer suporte para utilizar da melhor 
        maneira<br> todas as informações sobre sua rede, entre em contato conosco,<br> teremos o prazer em lhe atender!
        <br><br>
        Equipe Pearson
    </div>
	<?
} else if ( Core::registro('permissoes')->temPermissao('principal.texto_boas_vindas.administrador') ) {
		/* $comando = "netstat -an | grep :80 | wc -l";
		$usuarios_online = trim(shell_exec($comando)); */

		$comando2 = "netstat -an | grep :443 | wc -l";
		$usuarios_online2 = trim(shell_exec($comando2));

		$usuarios_online = $usuarios_online2;
	?>
	<div style="font-family: arial, sans-serif;font-size: 14px;font-weight: bold;color: #0E487A">
		<h3>Usuários on-line aproximadamente no momento: <?php echo$usuarios_online; ?></h3>
	</div>
	<?
}
?>
