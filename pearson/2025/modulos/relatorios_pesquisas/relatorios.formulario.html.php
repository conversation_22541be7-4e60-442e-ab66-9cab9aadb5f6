<? if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido'); ?>
<div class="vd_Titulo medio">
<?
if ($this->_formulario->obterEstado() == Formulario::ADICIONANDO) {
	echo '<strong>Adicionando relatório</strong>';
} else {
	echo '<strong>'. $this->_formulario->obterDados()->obterNome() .'</strong> - Editando relatório';
}
?>
</div>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th class="selecionada">Informações do relatório</th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>

	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?>
	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'); ?>'" value="Cancelar" class="botao cancelar">
	<input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('relatorios', 'remover', array('id' => $this->_formulario->obterDados()->obterID())); ?>'; }" value="Remover" class="botao remover">
	</div>

  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
	<tr>
	  <th colspan="4" class="titulo">Informações do relatório </th>
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('nome', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="25%"><?= $this->_formulario->obterHTML('nome', Formulario::HTML_CAMPO, true); ?> </td>
	  <th><?= $this->_formulario->obterHTML('oculto', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="25%"><?= $this->_formulario->obterHTML('oculto', Formulario::HTML_CAMPO, true); ?> </td>
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('grupo', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="25%"><?= $this->_formulario->obterHTML('grupo', Formulario::HTML_CAMPO, true); ?> </td>
	  <td></td>
	  <td></td>
	</tr>
  </table>

	</td>
  </tr>
</table>
<?= $this->_formulario->obterHTML('id', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoEditarEmOrdem(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>