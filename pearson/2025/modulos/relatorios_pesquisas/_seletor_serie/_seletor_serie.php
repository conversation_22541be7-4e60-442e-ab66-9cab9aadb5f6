<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('CarregadorUsuarioEspecifico', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('PesquisaParticipante', null, true);

class MSeletorSerie
{
	protected $_ordenacao;
	protected $_pesquisa = null;
	protected $_series = array();
	protected $_selecionado = null;
	public $serieID = null;

	public function __construct (Ordenacao &$ordenacao)	{
		$this->_ordenacao = $ordenacao;
	}

	public function fixarPesquisa(Pesquisa $pesq){
		$this->_pesquisa = $pesq;
		$this->_prepararListaSeries();
	}

	protected function _prepararListaSeries () {
		$this->_series = array();

		if($this->_pesquisa != null){
			$rs = Core::registro('db')->query( sprintf(
				  'SELECT * FROM pesquisas_participantes
				  INNER JOIN alunos ON alunos.a_id = pesquisas_participantes.pp_aluno
				  INNER JOIN turmas ON turmas.t_id = alunos.a_turma
				  INNER JOIN series ON series.s_id = turmas.t_serie
				  WHERE pp_pesquisa = %s',
				  $this->_pesquisa->obterID()) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$this->_series[$row['s_id']] = $row['s_nome'];
				}
			}
			$rs->free();
		}
	}

	public function configurarCampoSeletorSeries ($rel_secao = '') {
		$seriesFormulario = $this->_series;

		if (Core::diretiva('_seletor_series.selecionado.relatorios_pesquisas') !== false && isset($this->_series[Core::diretiva('_seletor_series.selecionado.relatorios_pesquisas')]))
			$selecionado = Core::diretiva('_seletor_series.selecionado.relatorios_pesquisas');

		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_serie',
												'etiqueta' => '<strong>Selecione o ano</strong>',
												'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($seriesFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $seriesFormulario,
												'valor_pos_erro' => $selecionado,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function ajustarSeriePesquisaSelecionada ()
	{
		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = @$this->_ordenacao->campo('seletor_serie')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_series[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$this->_selecionado = key($this->_series);

			$serieDoUsuario = Core::diretiva('_seletor_series.selecionado.relatorios_pesquisas');	
			if(array_key_exists($serieDoUsuario, $this->_series)){
				$this->_selecionado = $serieDoUsuario;								
			}
		}

		if($this->_selecionado == null){
			reset($this->_series);
			$this->_selecionado = key($this->_series);
		}

		$this->_ordenacao->campo('seletor_serie')->fixar('valor', $this->_selecionado);
		Core::fixarDiretiva('_seletor_series.selecionado.relatorios_pesquisas', $this->_selecionado);
		$this->serieID = $this->_selecionado;
	}
}

?>