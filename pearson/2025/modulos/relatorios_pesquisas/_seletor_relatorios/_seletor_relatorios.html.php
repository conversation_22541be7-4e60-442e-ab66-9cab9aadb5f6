<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<form action="<?= Gerenciador_URL::gerarLinkCustomizado(array('turma_mostrar' => @$_GET['turma_mostrar'])); ?>" method="get" name="form_seletor_relatorios">
	<input name="<?= Core::diretiva('CORE:PARAMETRO:modulo'); ?>" type="hidden" value="relatorios_pesquisas" />
	<input name="<?= Core::diretiva('CORE:PARAMETRO:acao'); ?>" type="hidden" value="detalhar" />
	<input name="turma_mostrar" type="hidden" value="<?= @$_GET['turma_mostrar']; ?>" />
	<select name="id" size="1" onChange="this.form.submit();" style="width: 100%">
<?
foreach ($this->_relatorios as &$r) {
	echo '<option value="'. $r->obterID() .'" '. ($r->obterID() == $this->_selecionado->obterID() ? 'selected="selected"' : '') .'>'. $r->obterNome() .'</option>';
}
?>
	</select>
</form>