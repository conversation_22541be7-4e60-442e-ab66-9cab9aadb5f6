<?
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('JTransferidorDeItensDeMenu', 'JavaScript/JTransferidorDeItensDeMenu/', true);

class FRelatorios_Exception extends Formulario_Exception { }

class FRelatorios extends Formulario
{
	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$ordem = 1;

		$this->adicionarCampo( new Campo(array( 'nome' => 'nome',
												'etiqueta' => 'Nome',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 255),
												'html_tamanho_maximo' => 255,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterNome(),
												'html_ordem' => $ordem++
							  )) );

		$grupos = Relatorio::obterArrayGruposRelatorios();
		$grupoSelecionada = null;

		if ( !$this->foiEnviado() ) {
			if ( $this->obterEstado() == self::ADICIONANDO && Core::diretiva('_relatorios.grupo.ultima_selecao') != false &&
					array_key_exists(Core::diretiva('_relatorios.grupo.ultima_selecao'), $grupos))
				$grupoSelecionada = Core::diretiva('_relatorios.grupo.ultima_selecao');
			elseif ($this->_dados->obterGrupo() != null && array_key_exists($this->_dados->obterGrupo(), $grupos))
				$grupoSelecionada = $this->_dados->obterGrupo();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'grupo',
												'etiqueta' => 'Grupo de listagem',
												'valor' => $grupoSelecionada,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($grupos)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $grupos,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => $ordem++,
							  )) );

		$ocultoSel = null;
		if ( !$this->foiEnviado() ) {
			if ( $this->obterEstado() == self::ADICIONANDO )
				$ocultoSel = 0;
			elseif ( $this->obterEstado() == self::EDITANDO && $this->_dados->obterOculto() )
				$ocultoSel = 1;
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'oculto',
												'etiqueta' => 'Oculto?',
												'valor' => $ocultoSel,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_ordem' => $ordem++
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Enviar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => $ordem++
							  )) );
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e)
		{
			throw new FRelatorios_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Relatório adicionado com sucesso!', 'Adicionando relatório...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar o relatório!', 'Adicionando relatório...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Relatório editado com sucesso!', 'Editando relatório...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar o relatório!', 'Editando relatório...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}

		Core::fixarDiretiva('_relatorios.grupo.ultima_selecao', $this->_campos['grupo']->obter('valor'));

		return $this->_dados;
	}

	protected function _carregar ()
	{
		$id = null;

		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}

		$this->_dados = new Relatorio($id);

		if ( $id != null ) {
			if ( $this->_dados->carregar() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'), 'Relatório inválido!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
		}
	}

	protected function _adicionar ()
	{
		return false;
	}

	protected function _editar ()
	{
		$this->_dados->fixarNome( $this->_campos['nome']->obter('valor') );
		$this->_dados->fixarGrupo( $this->_campos['grupo']->obter('valor') );
		$this->_dados->fixarOculto( $this->_campos['oculto']->obter('valor') );

		return $this->_dados->salvar();
	}
}

?>