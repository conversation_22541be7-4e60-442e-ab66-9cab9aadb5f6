<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('CarregadorUsuarioEspecifico', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('PesquisaParticipante', null, true);

class MSeletorPesquisas
{
	public $pesquisa;

	protected $_pesquisas;
	protected $_selecionado = null;
	protected $_ordenacao;

	public function __construct (Ordenacao &$ordenacao, $auto_preparar = true)	{
		$this->_ordenacao = $ordenacao;

		$this->pesquisa = new Pesquisa(null);

		$this->_prepararListaPesquisas();
	}

	public function configurarCampoSeletorPesquisas ($rel_secao = '') {
		$pesquisasFormulario = array(Campo::NULO => '');
		foreach ($this->_pesquisas as $id => $nome) {
			$pesquisasFormulario[$id] = $nome;
		}

		$selecionado = null;
		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_pesquisa',
												'etiqueta' => '<strong>Selecione uma pesquisa</strong>',
												//'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($pesquisasFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $pesquisasFormulario,
												//'valor_pos_erro' => $selecionado,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function ajustarPesquisaSelecionado ()
	{
		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = $this->_ordenacao->campo('seletor_pesquisa')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_pesquisas[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$perfil_falso = Core::modulo('_perfil_falso');
			$perfil_falso_tipo = $perfil_falso->obterTipoPerfilSelecionado();
			$perfil_falso_user = $perfil_falso->obterPerfilSelecionado();
			$pesquisaDoUsuario = Core::diretiva('_seletor_pesquisas.selecionado.relatorios_pesquisas');

			if($perfil_falso_tipo == MPerfilFalso::ALUNO){
				$aluno = $perfil_falso->obterPerfilSelecionado();
				$this->_selecionado = Inscricao::obterPesquisaPeloAluno($aluno);
			}
			elseif($perfil_falso_tipo == MPerfilFalso::PROFESSOR){
				if(array_key_exists($pesquisaDoUsuario, $this->_pesquisas)){
					$this->_selecionado = $pesquisaDoUsuario;								
				}
				else{
					reset($this->_pesquisas);
					$this->_selecionado = key($this->_pesquisas);												
				}					
			}
			elseif($perfil_falso_tipo == MPerfilFalso::DIRETOR){
				$escola = $perfil_falso->obterPerfilSelecionado();
				$turmas = Turma::obterArrayTurmasParaFormulario(false, $escola->obterInstituicao());

				$simus = Pesquisa::obterPesquisasParaFormularioPorSecao('relatorios_pesquisas');

				$sids = array();
				foreach ($simus as $simusK => $simusV) {
					$sids[] = $simusK;
				}

				reset($this->_pesquisas);
				$this->_selecionado = key($this->_pesquisas);
				if(count($sids)>0){
					if(in_array($pesquisaDoUsuario, $sids)){
						$this->_selecionado = $pesquisaDoUsuario;				
					}
					else{
						$this->_selecionado = reset($sids);					
					}
				}				
			}
			else{
				$this->_selecionado = $pesquisaDoUsuario;
				if($pesquisaDoUsuario == null){
					reset($this->_pesquisas);
					$this->_selecionado = key($this->_pesquisas);			
				}
			}			
		};

		if($this->_selecionado == null){
			reset($this->_pesquisas);
			$this->_selecionado = key($this->_pesquisas);
		}

		$this->_ordenacao->campo('seletor_pesquisa')->fixar('valor', $this->_selecionado);
		Core::fixarDiretiva('_seletor_pesquisas.selecionado.relatorios_pesquisas', $this->_selecionado);
		$this->pesquisa = new Pesquisa($this->_selecionado);
		$this->pesquisa->carregar();
	}

	protected function _prepararListaPesquisas () {
		$this->_pesquisas = array();

		$professor = null;

		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ){
			$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		}

		if ( $professor != null ) {
			$turmasPossiveis = array();
			foreach ( $professor->obterAulas() as $aula ) {
				$turmasPossiveis[] = @$aula->obterTurma()->obterID();
			}

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT pesquisas.*, series.s_nome AS series_s_nome FROM pesquisas
				  INNER JOIN turmas ON turmas.t_id = %s
				  INNER JOIN series ON series.s_id = turmas.t_serie
				  WHERE pesquisas.s_instituicao = %s AND p_secao_rels = "relatorios_pesquisas" 
				  ORDER BY p_ordem',
				  Core::registro('db')->formatarValor( array_pop($turmasPossiveis) ),
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(time() >= $row['p_data_inicio_realizacao'] && time() <= $row['p_data_fim_realizacao']){
						continue;
					}

					if ( strstr($row['s_nome'], $row['series_s_nome']) === false ) continue;

					if ( !isset($this->_pesquisas[$row['p_id']]) )
						$this->_pesquisas[$row['p_id']] = $row['p_nome'];//strftime('%d/%m/%Y', $row['p_data_inicio_realizacao']) . ' | ' .$row['p_nome'];
				}
			}
			$rs->free();
		} else {
			$aluno = null;
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO &&
					Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$aluno = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			else
				$aluno = CarregadorUsuarioEspecifico::obterAluno();

			$sqlPesquisa = null;
			if ( $aluno != null )
				$sqlPesquisa = ' INNER JOIN pesquisas_participantes ON pesquisas_participantes.pp_aluno = '. Core::registro('db')->formatarValor( $aluno->obterID() ) .' AND pesquisas_participantes.pi_pesquisa = p_id ';

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT pesquisas.*, pesquisas_itens.pi_id FROM pesquisas
				  %s
				  INNER JOIN pesquisas_itens ON pesquisas_itens.pi_pesquisa = p_id
				  WHERE pi_id IS NOT NULL AND p_secao_rels = "relatorios_pesquisas" 
				  ORDER BY p_data_inicio_realizacao DESC, p_nome ASC',
				  $sqlPesquisa) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(time() >= $row['p_data_inicio_realizacao'] && time() <= $row['p_data_fim_realizacao']){
						continue;
					}

					if ( !isset($this->_pesquisas[$row['p_id']]) )
						$this->_pesquisas[$row['p_id']] = $row['p_nome'];//strftime('%d/%m/%Y', $row['p_data_inicio_realizacao']) . ' | ' .$row['p_nome'];
				}
			}
			$rs->free();
		}
	}

}

?>