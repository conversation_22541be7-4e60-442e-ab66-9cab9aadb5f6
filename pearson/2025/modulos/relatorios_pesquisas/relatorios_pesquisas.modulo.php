<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Log', 'Log/');

Core::incluir('Relatorio', null, true);
Core::incluir('ExportadorPDF', null, true);
include_once('Gerenciador_Diretivas/Gerenciador_Diretivas.php');

include_once('relatorios.listagem.php');

class MRelatoriosPesquisas extends Modulo
{
	protected $_listagem;
	protected $_formulario;
	protected $_relatorioListagem;

	public function __construct () 
	{
		parent::__construct();
		Core::carregarModulo( array('nome' => '_perfil_falso_pesquisas', 'classe' => 'MPerfilFalso', 'guardar_como' => '_perfil_falso') );
	}

    public function aListarRelatorios ()
    {
		Core::modulo('_perfil_falso')->prepararSeletorPerfil();
		Core::modulo('_perfil_falso')->prepararAtalho();
		Core::modulo('_perfil_falso')->prepararPerfilSelecionado();

		$this->_listagem = new LRelatorios();

		$this->_listagem->prepararListagem();

		$this->_iniciarRenderizacao(Modulo::HTML);
		echo Core::modulo('_perfil_falso')->obterSaida();
		echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}

	public function aDetalharRelatorio (Relatorio &$relatorio = null)
	{
		//MANUTENCAO
		$MANUTENCAO = Core::diretiva('MANUTENCAO');
		if(!empty($MANUTENCAO)){
			$MANUTENCAO = strtolower($MANUTENCAO);
			if($MANUTENCAO == 'sim' && Core::registro('usuario')->obterGrupo() != 1){
				@Core::registro('autenticador')->logOut();
				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:login')) );
				Core::modulo('redirecionador')->redirecionar();
			}
		}

		//LOGINS PERMITIDOS
		$LOGINS_PERMITIDOS = str_replace(' ', '', Core::diretiva('LOGINS_PERMITIDOS'));
		if(!empty($LOGINS_PERMITIDOS)){
			$LOGINS_PERMITIDOS = explode(',', '1,'.$LOGINS_PERMITIDOS);

			if(count($LOGINS_PERMITIDOS) > 0){
				if(!in_array(Core::registro('usuario')->obterGrupo(), $LOGINS_PERMITIDOS)){
					@Core::registro('autenticador')->logOut();
					Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:login')) );
					Core::modulo('redirecionador')->redirecionar();
				}
			}
		}

		//EMAILS EXCLUSIVOS
		$allowMail = Core::diretiva('LOGIN_EXCLUSIVO_EMAIL');
		if(!empty($allowMail)){
			$opcoes = json_decode(strtolower(Core::diretiva('LOGIN_EXCLUSIVO_EMAIL')),true);

			$usuario = new UsuarioInstituido(Core::registro('usuario')->obterID());
			$usuario->carregar();

			$dominio_email = explode('@', $usuario->obterEmail()->obterEndereco());

			if(array_key_exists($usuario->obterGrupo(), $opcoes)){
				$dominio_email_allowed = $opcoes[$usuario->obterGrupo()];

				if($dominio_email[1] != $dominio_email_allowed){
					@Core::registro('autenticador')->logOut();
					Redirecionador::fixarEndereco( Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:login')) );
					Core::modulo('redirecionador')->redirecionar();
				}
			}
		}

		$relRestrito = strtolower(Core::diretiva('RELS_RESTRITO_PESQUISAS'));		
		$relRestritoData = Core::diretiva('RELS_RESTRITO_DATA_LIBERACAO_PESQUISAS'); // formato -> dd/mm/aa hh:mm

		$dtc = new DateTime();
		$utc = $dtc->setTimezone(new DateTimeZone('America/Sao_Paulo'));
		$hj = $dtc->createFromFormat("d/m/Y H:i", $relRestritoData)->getTimestamp();

		if(Core::registro('usuario')->obterGrupo() !== '1' && (strtolower($relRestrito) === 'sim' || $hj >= time())){
			Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
			Core::modulo('alerta')->prepararAlerta('No momento os relatórios não estão disponíveis para visualização!');
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'), 'No momento os relatórios não estão disponíveis para visualização!');
		}

		Core::modulo('_perfil_falso')->prepararPerfilSelecionado();

		if(in_array(Core::registro('usuario')->obterGrupo(), array(3)) && Core::modulo('_perfil_falso')->_perfil_turma == NULL){
			Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta'));
			Core::modulo('alerta')->prepararAlerta('Selecione alguma turma!');
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'), 'Selecione alguma turma!');
			return;
		}

		if ($relatorio == null)
			$relatorio = new Relatorio( (int) @$_GET['id'] );

		$vLog['tipo'] = "VIEW_REL";
        $vLog['valor'] = $relatorio->obterID();
        $vLog['referencia'] = Core::registro('usuario')->obterID();
        Log::logar($vLog);

		@$relatorio->carregar();

		$ugrupo = Core::registro('usuario')->obterGrupo();
		if($relatorio->obterOculto() && $ugrupo !== '1'){
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'), 'Relatório inválido!');
		}

		$permissao = true;
		if ( Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ) {
			if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()))
				$permissao = false;
		} else if ( !$relatorio->validarPermissao() )
			$permissao = false;

		if ( $permissao ) {
			$this->_relatorioListagem = $this->obterRelatorioListagem($relatorio);

			if ($this->_relatorioListagem != null) {
				$this->_relatorioListagem->prepararRelatorio($relatorio);

				$this->_iniciarRenderizacao(Modulo::HTML);
				//if (Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null)
				//echo Core::modulo('_perfil_falso')->obterSaida();

				echo $this->_relatorioListagem->obterSaida();
				$this->_finalizarRenderizacao();

				return true;
			}
		}

		Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'), 'Relatório inválido!');
	}

	public function &obterRelatorioListagem (Relatorio &$relatorio) 
	{
		if ( $relatorio != null ) {
			if ( !class_exists( $relatorio->obterClasse() ) ) {
				if ( @include_once( sprintf('%s/%s.relatorio.php', $relatorio->obterArquivo(), $relatorio->obterArquivo()) ) )	{
					$nomeClasse = $relatorio->obterClasse();
					if ( $relatorioListagem = new $nomeClasse() )
						return $relatorioListagem;
				}
			} else {
				$nomeClasse = $relatorio->obterClasse();
				if ( $relatorioListagem = new $nomeClasse() )
					return $relatorioListagem;
			}
		}

		$rel = null;

		return $rel;
	}

	public function aReordenar() 
	{
		Core::modulo('_perfil_falso')->prepararPerfilSelecionado();

		$direcao = @$_GET['direcao'];
		$rel = new Relatorio((int) @$_GET['id']);
		if ($rel->carregar()) {
			$rs = Core::registro('db')->query(
			  'SELECT * FROM relatorios 
			  INNER JOIN relatorios_grupos ON relatorios_grupos.rg_id = relatorios.r_grupo 
			  LEFT JOIN permissoes ON permissoes.p_id = r_permissao 
			  WHERE r_oculto != "1" AND r_secao = "relatorios_pesquisas" 
			  ORDER BY rg_ordem ASC, r_ordem ASC, r_nome ASC');

			$relatorios = array();
			if ($rs->num_rows) {
				$i = 1; $rel_pos = 0; 
				while ($row = $rs->fetch_assoc()) {
					$relatorio = new Relatorio($row['r_id']);
					$relatorio->carregar();
					$relatorio->fixarGrupo($row['r_grupo']);
					$relatorio->fixarPermissao($row['r_permissao'], $row['p_nome']);

					if (Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null) {
						if(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado() != null) {
							if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado())){
								continue;
							}
						}
					} 
					else if (!$relatorio->validarPermissao()){
						continue;
					}

					if ($row['r_id'] == $rel->obterID()){
						$rel_pos = $i;
					}
					$relatorios[$i++] = $row['r_id'];
				}

				if ($i > 1) {
					$rel_trocar = $direcao == 'cima' ? $rel_pos - 1 : $rel_pos + 1;
					if($rel_trocar < 0 || $rel_trocar > count($relatorios)){
						return;
					}

					$atualizacoes = array();
					foreach ($relatorios as $rk => $rv) {
						if($rv == $rel->obterID() && $direcao == 'cima'){
							$atualizacoes[($rel_trocar*10)-5] = $rv;
						}
						elseif($rv == $rel->obterID() && $direcao == 'baixo'){
							$atualizacoes[($rel_trocar*10)+5] = $rv;
						}
						else{
							$atualizacoes[$rk*10] = $rv;
						}
					}

					foreach($atualizacoes as $ordem => $rID){
						Core::registro('db')->query('
							UPDATE relatorios SET r_ordem = '.Core::registro('db')->formatarValor((int) $ordem).'
							WHERE r_id = '.Core::registro('db')->formatarValor($rID)
						);
					}
				}
			}
		}
		$rs->free();

		$this->aListarRelatorios();
	}

	public function aRemoverRelatorios ($ids = null) {
		if ( $ids != null ) {
			foreach ($ids as $id) {
				$obj = new Relatorio($id);

				if ( !$obj->carregar() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}

				if (!$obj->remover()) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'relatório não foi removido;');
				} else {
					Core::modulo('redirecionador')->adicionarSucesso($obj->obterNome(), 'relatório removido com sucesso;');
				}
			}

			Core::modulo('redirecionador')->fixarMensagem(null ,'Removendo relatórios...');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			if (isset($_GET['id']) && (int) $_GET['id'] > 0)
				return $this->aRemoverRelatorios( array((int) $_GET['id']) );

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}

	public function aNovoRelatorio () {
		$this->_formulario = new FRelatorios( array('nome' => 'form_relatorio', 'acao' => Gerenciador_URL::gerarLink('relatorios_pesquisas', 'editar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$id = $this->_formulario->executar()->obterID();

				Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar') );

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e) {}
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('relatorios.formulario.html.php');
		$this->_finalizarRenderizacao();
	}

	public function aProcessarFormulario () {
		return $this->aNovoRelatorio();
	}

	//===============================================================================>
	// BOT!
	//===============================================================================>

	private function finishBot(){
		echo"<br>finishBot!";
		exit;
	}

	public function memoria($titulo = null) 
	{
		if ($titulo != null)
			echo $titulo . ': ';
		echo round(memory_get_usage() / 1024 / 1024, 2) . ' MB<br />';
	}

	private function _downloadPdfs()
	{
		$url = '<H1><a href="';
		$url = $url . "http" . (($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://") . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
		$index = strpos( $url,'?');
		$url = substr($url, 0, $index);
		$url = $url . 'upload/pdfs/"' .'>Clique para acessar os pdfs<a/></H1>';
		return $url;
	}

	public function aEmpacotarRelatoriosEscolas () 
	{
		register_shutdown_function(array($this, 'finishBot'));

		$gerenciador = new Gerenciador_Diretivas();

		$RUN = $gerenciador->obterDiretiva('_run_bot_escola');
		if($RUN == '1'){
			exit('O bot de Escolas já está executando.');
		}
		else{
			$gerenciador->adicionarDiretivaDoSistemaSimples('_run_bot_escola', '1');
		}

		header("Connection: close");

		session_cache_expire(60*24);
		ignore_user_abort(1); // run script in background
		set_time_limit(0); // run script forever 
		ini_set('memory_limit', '10000M'); // 2GB
		ini_set('max_execution_time', 0);
		ini_set('output_buffering','on');
		ob_implicit_flush();

        echo str_repeat(" ", 500); 
        ob_flush(); 
        flush();

		PRINT("START TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");
		$this->memoria('PRE ESCOLAS');

		//limpando cache
		Core::registro('cache')->clean(Zend_Cache::CLEANING_MODE_ALL);

		$it1 = new RecursiveDirectoryIterator("upload/graficos/");
		foreach(new RecursiveIteratorIterator($it1) as $file) {
			@chmod(dirname($file), 0777);
			@chmod($file, 0777);
			@unlink($file);
		}

		//limpando pdfs e htmls.
		$limparPDFS = $gerenciador->obterDiretiva('BOT:LimparPDFsGerados');
		if($limparPDFS){
			$it2 = new RecursiveDirectoryIterator("upload/pdfs/");
			foreach(new RecursiveIteratorIterator($it2) as $file) {		
			    $fileNome = $file->getPathname();
			    if(strpos($fileNome, 'Turmas') === false && strpos($fileNome, 'Rede') === false && strpos($fileNome, 'Aprendizagem') === false && strpos($fileNome, 'kit') === false){
				    $FILE = array_flip(explode('.', $file));
				    if (isset($FILE['html']) || isset($FILE['pdf'])) {
						@chmod(dirname($file), 0777);
						@chmod($file, 0777);
						@unlink($file);
				    }
				}
			}
		}

		$pathCMD = str_replace('modulos/relatorios_pesquisas', '', realpath(dirname(__FILE__)));

		$relatorios_gerados = array(
			'diretor' => array()
		);

		$simuladosTmp = array();
		echo "Bimestre gerado no bot - BIMESTRE:CORRENTE - ".$gerenciador->obterDiretiva('BIMESTRE:CORRENTE')."<br>";
		foreach (array_keys(Simulado::obterSimuladosParaFormulario(false, $gerenciador->obterDiretiva('BIMESTRE:CORRENTE'))) as $simuladoID) {
			$simuladosTmp[$simuladoID] = new Simulado($simuladoID);
			$simuladosTmp[$simuladoID]->carregar();
		}	

		$ano = $gerenciador->obterDiretiva('RELATORIO:BOT:ANO');
		echo "Séries geradas no bot - RELATORIO:BOT:ANO - ".$ano."<br>";
		$anos = explode(",", $ano);

		$simulados = array();
		foreach ($anos as $ak => $av) {
			foreach($simuladosTmp as $simuladoID => $simulado) {
				$ss = $simulado->obterSerieAvaliacao();
				$snome = $ss->obterNome();

				if($snome == $av){
					$simulados[$simuladoID] = $simulado;
				}
			}
		}

		// PERFIL DE DIRETOR 
		$relatiosSecretario = $gerenciador->obterDiretiva('RELATORIO:ORDEM:DIRETOR');
		echo "Relatorios gerado no bot - RELATORIO:ORDEM:DIRETOR - ".$relatiosSecretario."<br>";
		$relatorios = explode(",", $relatiosSecretario);

		$relatoriosSemSelecaoSimulado = array(32, 36);
		$instituicoes = Instituicao::obterArrayInstituicoesParaFormulario();

		echo"<br>";
		foreach ($relatorios as $rPosicao => $rID) {
			$rPosicao = ((int)$rPosicao+1)*1000;
			$pasta = 'upload/pdfs/';			

			$relatorio = new Relatorio($rID);
			@$relatorio->carregar();

			foreach ($instituicoes as $iID => $iNome) {
				Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::DIRETOR, $iID);

				$permissao = true;
				if ( Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ) {
					if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()))
						$permissao = false;
				} 
				elseif ( !$relatorio->validarPermissao() )
					$permissao = false;

				if ( $permissao ) {
					if (!in_array($rID, $relatoriosSemSelecaoSimulado)) {
						foreach($simulados as $simuladoID => $simulado) {
							if($simulado->obterProdTxt()){
								$rPosicao += 1;
							}

							unset($relatorioListagem);
							$relatorioListagem = $this->obterRelatorioListagem( $relatorio );

							if ($relatorioListagem != null) {
								if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF'))
									continue;

								$nomeParaPasta = @iconv('ISO-8859-1', 'ASCII//IGNORE', $iNome); // bug de escrita do nome do arquivo

								$rPosicao = str_pad($rPosicao , 10, '0' , STR_PAD_LEFT);
								$relNomeTmp = $relatorio->obterNome().' ('.ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()).')';

								if ($rID == 35)
									$nomeParaPasta = @iconv('ISO-8859-1', 'ASCII//IGNORE', $iNome.'/'.'Alunos'); // bug de escrita do nome do arquivo

								$relatorioListagem->fixarSeletorSimulados($simulado);
								$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
								$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($relatorio, $pasta.$nomeParaPasta.'/',($rID == 35 ? ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()) : $rPosicao).' - '.$relNomeTmp);
								if($gerado === false){
									continue;
								}

								echo date("d/m/y H:i:s").": ";
								echo $pasta.$nomeParaPasta.'/'.($rID == 35 ? ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()) : $rPosicao).' - '.$relatorio->obterNome().'.html'; echo"<br>";
								echo str_repeat(" ", 500); ob_flush(); flush();

								$relNome = @iconv('ISO-8859-1', 'ASCII//IGNORE', $pasta.$nomeParaPasta.'/'.($rID == 35 ? ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()) : $rPosicao).' - '.$relNomeTmp.'.html.'.$relatorioListagem->orientacao_PDF);
								$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$relNome."' > /dev/null 2>/dev/null &";
								$PID = system($cmd);

								echo date("d/m/y H:i:s").": ";
								echo $pathCMD.$relNome; echo"<br>";
								echo str_repeat(" ", 500); ob_flush(); flush();

								if (!isset($relatorios_gerados['diretor'][$iNome]))
									$relatorios_gerados['diretor'][$iNome] = array();

								$relatorios_gerados['diretor'][$iNome][] = $rPosicao .' - '. $relatorio->obterNome();
							}

							$rPosicao += 1;
						}
					} 
					else {
						if($simulado->obterProdTxt()){
							$rPosicao += 1;
						}

						unset($relatorioListagem);
						$relatorioListagem = $this->obterRelatorioListagem( $relatorio );

						if ($relatorioListagem != null) {
							if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF'))
								continue;

							$rPosicao = str_pad($rPosicao , 10, '0' , STR_PAD_LEFT);
							$nomeParaPasta = @iconv('ISO-8859-1', 'ASCII//IGNORE', $iNome); // bug de escrita do nome do arquivo

							$relatorioListagem->fixarSeletorSimulados($simulado);
							$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
							$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($relatorio, $pasta.$nomeParaPasta.'/',$rPosicao.' - '.$relatorio->obterNome());
							if($gerado === false){
								continue;
							}

							echo date("d/m/y H:i:s").": ";
							echo $pasta.$nomeParaPasta.'/'.$rPosicao.' - '.$relatorio->obterNome().'.html'; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							$relNome = @iconv('ISO-8859-1', 'ASCII//IGNORE', $pasta.$nomeParaPasta.'/'.$rPosicao.' - '.$relatorio->obterNome().'.html.'.$relatorioListagem->orientacao_PDF);
							$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$relNome."' > /dev/null 2>/dev/null &";
							$PID = system($cmd);

							echo date("d/m/y H:i:s").": ";
							echo $pathCMD.$relNome; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							if (!isset($relatorios_gerados['diretor'][$iNome]))
								$relatorios_gerados['diretor'][$iNome] = array();

							$relatorios_gerados['diretor'][$iNome][] = $relatorio->obterNome();
						}

						$rPosicao += 1;
					}
				}
			}
		}
		echo"<br>";$this->memoria('POS ESCOLAS'); 

		PRINT("FINISH TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");

		$pathCMDconcat = "http".(($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://").$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
		$index = strpos($pathCMDconcat,'?');
		$pathCMDconcat = substr($pathCMDconcat, 0, $index);
		$pathCMDconcat = $pathCMDconcat.'upload/pdfs/concat_escolas.php';
		$PID = system("/usr/bin/wget --server-response --timeout=60 --output-document=output_concat.txt ".$pathCMDconcat." > /dev/null 2>/dev/null &");
		echo "Executando CONCATENAR em PARALELO - ".date("d/m/y H:i:s")." -> ".$PID."<br>";

		$msgTexto = "Alerta do BOT - Etapa 1 - Escolas<br>O Bot terminou!<br><br>Verifique em: ".$this->_downloadPdfs();
		@DespachadorEmailsUsuario::enviarAlertaBOT('Alerta do BOT - Etapa 1 - Escolas',$msgTexto);

		$countHTMLS = $countPDFS = 0;
		$it2 = new RecursiveDirectoryIterator("upload/pdfs/");
		foreach(new RecursiveIteratorIterator($it2) as $file) {		
		    $fileNome = $file->getPathname();
		    if(strpos($fileNome, 'Turmas') === false && strpos($fileNome, 'Rede') === false && strpos($fileNome, 'Aprendizagem') === false && strpos($fileNome, 'kit') === false){
			    $FILE = array_flip(explode('.', $file));
			    if (isset($FILE['html'])) {
			    	$countHTMLS++;
			    }
			    elseif(isset($FILE['pdf'])) {
			    	$countPDFS++;
			    }
			}
		}

		echo"<br>Htmls gerados: $countHTMLS <br>Pdfs gerados: $countPDFS<br>";

		$this->_iniciarRenderizacao(Modulo::HTML);
		echo '<strong>Os seguintes relat?rios foram gerados:</strong><br /><br />';
		foreach($relatorios_gerados as $rTipo => $relatorios) {
			echo '<strong>Relat?rios de '. $rTipo .':</strong><br /><ul>';
			if ($rTipo == 'diretor') {
				foreach($relatorios as $iNome => $relatoriosInstituicao) {
					echo '<li>'. $iNome . '<ul>';
					foreach ($relatoriosInstituicao as $rTitulo){
						echo '<li>'. $rTitulo . '</li>';
					}
					echo '</ul></li>';
				}
			} 
			echo '</ul>';
		}
		$this->memoria($this->_downloadPdfs());
		$this->_finalizarRenderizacao();

		$gerenciador->removerDiretivaSimples('_run_bot_escola');

		exit;
	}

	public function aEmpacotarRelatoriosRede () 
	{
		register_shutdown_function(array($this, 'finishBot'));

		$gerenciador = new Gerenciador_Diretivas();

		$RUN = $gerenciador->obterDiretiva('_run_bot_rede');
		if($RUN == '1'){
			exit('O bot de Rede já está executando.');
		}
		else{
			$gerenciador->adicionarDiretivaDoSistemaSimples('_run_bot_rede', '1');
		}

		header("Connection: close");

		session_cache_expire(60*24);
		ignore_user_abort(1); // run script in background
		set_time_limit(0); // run script forever 
		ini_set('memory_limit', '10000M'); // 2GB
		ini_set('max_execution_time', 0);
		ini_set('output_buffering','on');
		ob_implicit_flush();

        echo str_repeat(" ", 500); 
        ob_flush(); 
        flush();

		PRINT("START TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");
		$this->memoria('PRE REDE'); 

		//limpando cache
		Core::registro('cache')->clean(Zend_Cache::CLEANING_MODE_ALL);

		$it1 = new RecursiveDirectoryIterator("upload/graficos/");
		foreach(new RecursiveIteratorIterator($it1) as $file) {
			@chmod(dirname($file), 0777);
			@chmod($file, 0777);
			@unlink($file);
		}

		//limpando pdfs e htmls.
		$limparPDFS = $gerenciador->obterDiretiva('BOT:LimparPDFsGerados');
		if($limparPDFS){
	    	if(realpath("upload/pdfs/Rede/") !== false AND is_dir(realpath("upload/pdfs/Rede/"))){
				$it2 = new RecursiveDirectoryIterator("upload/pdfs/Rede/");
				foreach(new RecursiveIteratorIterator($it2) as $file) {		
				    $FILE = array_flip(explode('.', $file));
				    if (isset($FILE['html']) || isset($FILE['pdf'])) {
						@chmod(dirname($file), 0777);
						@chmod($file, 0777);
						@unlink($file);
				    }
				}
			}
		}

		$pathCMD = str_replace('modulos/relatorios_pesquisas', '', realpath(dirname(__FILE__)));

		$relatorios_gerados = array(
			'rede' => array()
		);

		$simuladosTmp = array();
		echo "Bimestre gerado no bot - BIMESTRE:CORRENTE - ".$gerenciador->obterDiretiva('BIMESTRE:CORRENTE')."<br>";
		foreach (array_keys(Simulado::obterSimuladosParaFormulario(false, $gerenciador->obterDiretiva('BIMESTRE:CORRENTE'))) as $simuladoID) {
			$simuladosTmp[$simuladoID] = new Simulado($simuladoID);
			$simuladosTmp[$simuladoID]->carregar();
		}	

		$ano = $gerenciador->obterDiretiva('RELATORIO:BOT:ANO');
		echo "Séries geradas no bot - RELATORIO:BOT:ANO - ".$ano."<br>";
		$anos = explode(",", $ano);

		$simulados = array();
		foreach ($anos as $ak => $av) {
			foreach($simuladosTmp as $simuladoID => $simulado) {
				$ss = $simulado->obterSerieAvaliacao();
				$snome = $ss->obterNome();

				if($snome == $av){
					$simulados[$simuladoID] = $simulado;
				}
			}
		}

		// PERFIL DE SECRET?RIO
		$relatiosSecretario = $gerenciador->obterDiretiva('RELATORIO:ORDEM:SECRETARIO');
		echo "Relatorios gerado no bot - RELATORIO:ORDEM:SECRETARIO - ".$relatiosSecretario."<br>";
		$relatorios = explode(",", $relatiosSecretario);

		$relatoriosSemSelecaoSimulado = array(6);
		Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::SECRETARIO);

		echo"<br>";
		foreach ($relatorios as $rPosicao => $rID) {
			$rPosicao = ((int)$rPosicao+1)*1000;
			$pasta = 'upload/pdfs/Rede/';

			unset($relatorio);
			$relatorio = new Relatorio($rID);
			@$relatorio->carregar();

			$permissao = true;
			if ( Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ) {
				if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()))
					$permissao = false;
			} 
			elseif ( !$relatorio->validarPermissao() )
				$permissao = false;

			if ( $permissao ) {
				if (!in_array($rID, $relatoriosSemSelecaoSimulado)) {
					foreach($simulados as $simuladoID => $simulado) {
						if($simulado->obterProdTxt()){
							$rPosicao += 1;
						}

						unset($relatorioListagem);
						$relatorioListagem = $this->obterRelatorioListagem( $relatorio );
						if ($relatorioListagem != null) {
							if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF'))
								continue;

							$rPosicao = str_pad($rPosicao , 10, '0' , STR_PAD_LEFT);

							$relatorioListagem->fixarSeletorSimulados($simulado);
							$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
							$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($relatorio, $pasta, $rPosicao.' - '.ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()) .' - '. $relatorio->obterNome());
							if($gerado === false){
								continue;
							}

							echo date("d/m/y H:i:s").": ";
							echo $pasta.$rPosicao.' - '.ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()).' - '.$relatorio->obterNome().'.html'; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();
							
							$relNome = @iconv('ISO-8859-1', 'ASCII//IGNORE', $pasta.$rPosicao.' - '.ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()).' - '.$relatorio->obterNome().'.html.'.$relatorioListagem->orientacao_PDF);
							$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$relNome."' > /dev/null 2>/dev/null &";
							$PID = system($cmd);

							echo date("d/m/y H:i:s").": ";
							echo $pathCMD.$relNome; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							$relatorios_gerados['rede'][] = $rPosicao.' - '.ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome()) .' - '. $relatorio->obterNome();
						}

						$rPosicao += 1;
					}
				} 
				else {
					if($simulado->obterProdTxt()){
						$rPosicao += 1;
					}

					unset($relatorioListagem);
					$relatorioListagem = $this->obterRelatorioListagem( $relatorio );
					if ($relatorioListagem != null) {
						if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF'))
							continue;

						$rPosicao = str_pad($rPosicao , 10, '0' , STR_PAD_LEFT);

						$relatorioListagem->fixarSeletorSimulados($simulado);
						$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
						$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($relatorio, $pasta, $rPosicao.' - '.$relatorio->obterNome());
						if($gerado === false){
							continue;
						}

						echo date("d/m/y H:i:s").": ";
						echo $pasta.$rPosicao.' - '.$relatorio->obterNome().'.html'; echo"<br>";
						echo str_repeat(" ", 500); ob_flush(); flush();

						$relNome = @iconv('ISO-8859-1', 'ASCII//IGNORE', $pasta.$rPosicao.' - '.$relatorio->obterNome().'.html.'.$relatorioListagem->orientacao_PDF);
						$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$relNome."' > /dev/null 2>/dev/null &";
						$PID = system($cmd);

						echo date("d/m/y H:i:s").": ";
						echo $pathCMD.$relNome; echo"<br>";
						echo str_repeat(" ", 500); ob_flush(); flush();

						$relatorios_gerados['rede'][] = $rPosicao.' - '.$relatorio->obterNome();
					}

					$rPosicao += 1;
				}
			}
		}
		echo"<br>";$this->memoria('POS REDE'); 

		PRINT("FINISH TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");

		$pathCMDconcat = "http".(($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://").$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
		$index = strpos($pathCMDconcat,'?');
		$pathCMDconcat = substr($pathCMDconcat, 0, $index);
		$pathCMDconcat = $pathCMDconcat.'upload/pdfs/concat_rede.php';
		$PID = system("/usr/bin/wget --server-response --timeout=60 --output-document=output_concat.txt ".$pathCMDconcat." > /dev/null 2>/dev/null &");
		echo "Executando CONCATENAR em PARALELO - ".date("d/m/y H:i:s")." -> ".$PID."<br>";

		$msgTexto = "Alerta do BOT - Etapa 2 - Rede<br>O Bot terminou!<br><br>Verifique em: ".$this->_downloadPdfs();
		@DespachadorEmailsUsuario::enviarAlertaBOT('Alerta do BOT - Etapa 2 - Rede',$msgTexto);

		$countHTMLS = $countPDFS = 0;
    	if(realpath("upload/pdfs/Rede/") !== false AND is_dir(realpath("upload/pdfs/Rede/"))){
			$it2 = new RecursiveDirectoryIterator("upload/pdfs/Rede/");
			foreach(new RecursiveIteratorIterator($it2) as $file) {		
			    $FILE = array_flip(explode('.', $file));
			    if (isset($FILE['html'])) {
			    	$countHTMLS++;
			    }
			    elseif(isset($FILE['pdf'])) {
			    	$countPDFS++;
			    }
			}
		}

		echo"<br>Htmls gerados: $countHTMLS <br>Pdfs gerados: $countPDFS<br>";

		$this->_iniciarRenderizacao(Modulo::HTML);
		echo '<strong>Os seguintes relat?rios foram gerados:</strong><br /><br />';
		foreach($relatorios_gerados as $rTipo => $relatorios) {
			echo '<strong>Relat?rios de '. $rTipo .':</strong><br /><ul>';
			if ($rTipo == 'rede') {
				foreach($relatorios as $rTitulo){
					echo '<li>'. $rTitulo . '</li>';
				}
			}
			echo '</ul>';
		}
		$this->memoria($this->_downloadPdfs());
		$this->_finalizarRenderizacao();

		$gerenciador->removerDiretivaSimples('_run_bot_rede');

		exit;
	}

	public function aEmpacotarRelatoriosTurmas () 
	{
		register_shutdown_function(array($this, 'finishBot'));

		$gerenciador = new Gerenciador_Diretivas();

		$RUN = $gerenciador->obterDiretiva('_run_bot_turmas');
		if($RUN == '1'){
			exit('O bot de Turmas já está executando.');
		}
		else{
			$gerenciador->adicionarDiretivaDoSistemaSimples('_run_bot_turmas', '1');
		}

		header("Connection: close");

		session_cache_expire(60*24);
		ignore_user_abort(1); // run script in background
		set_time_limit(0); // run script forever 
		ini_set('memory_limit', '10000M'); // 2GB
		ini_set('max_execution_time', 0);
		ini_set('output_buffering','on');
		ob_implicit_flush();

        echo str_repeat(" ", 500); 
        ob_flush(); 
        flush();

		PRINT("START TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");
		$this->memoria('PRE TURMAS'); 

		//limpando cache
		Core::registro('cache')->clean(Zend_Cache::CLEANING_MODE_ALL);

		$it1 = new RecursiveDirectoryIterator("upload/graficos/");
		foreach(new RecursiveIteratorIterator($it1) as $file) {
			@chmod(dirname($file), 0777);
			@chmod($file, 0777);
			@unlink($file);
		}

		//limpando pdfs e htmls.
		$limparPDFS = $gerenciador->obterDiretiva('BOT:LimparPDFsGerados');
		if($limparPDFS){
			if(!isset($_GET['serie'])){
				$it2 = new RecursiveDirectoryIterator("upload/pdfs/");
				foreach(new RecursiveIteratorIterator($it2) as $file) {		
				    $fileNome = $file->getPathname();
				    if(strpos($fileNome, 'Turmas') !== false && strpos($fileNome, 'Rede') === false && strpos($fileNome, 'Aprendizagem') === false && strpos($fileNome, 'kit') === false){
					    $FILE = array_flip(explode('.', $file));
					    if (isset($FILE['html']) || isset($FILE['pdf'])) {
							@chmod(dirname($file), 0777);
							@chmod($file, 0777);
							@unlink($file);
					    }
					}
				}
			}
		}

		$pathCMD = str_replace('modulos/relatorios_pesquisas', '', realpath(dirname(__FILE__)));

		$relatorios_gerados = array(
			'professor' => array()
		);

		$simuladosTmp = array();
		echo "Bimestre gerado no bot - BIMESTRE:CORRENTE - ".$gerenciador->obterDiretiva('BIMESTRE:CORRENTE')."<br>";
		foreach (array_keys(Simulado::obterSimuladosParaFormulario(false, $gerenciador->obterDiretiva('BIMESTRE:CORRENTE'))) as $simuladoID) {
			$simuladosTmp[$simuladoID] = new Simulado($simuladoID);
			$simuladosTmp[$simuladoID]->carregar();
		}	

		$ano = $gerenciador->obterDiretiva('RELATORIO:BOT:ANO');
		echo "Séries geradas no bot - RELATORIO:BOT:ANO - ".$ano."<br>";
		$anos = explode(",", $ano);

		if(isset($_GET['serie'])){
			$anosV2 = $_GET['serie'];
		}
		else{
			$anosV2 = $anos[0];
		}

		echo "Gerando Séries: ".$anosV2."<br>";
		$nextAnos = array_search($anosV2, $anos);
		$nextAnos = $anos[$nextAnos+1];

		$anosV2 = array($anosV2);
		if($nextAnos == null){
			$serieRedirect = '';
		}
		else{
			$pathCMDconcat = Gerenciador_URL::gerarLink('relatorios_pesquisas','empacotar_turmas', array('serie' => urlencode($nextAnos)));

			$serieRedirect = '
				<script language="JavaScript">
					//if(confirm("Foi gerado a serie '.$anosV2[0].'! Clique em confirmar para gerar a proxima serie '.$nextAnos.'!")){
						window.location="'.$pathCMDconcat.'";
					//}
				</script>
			';
		}

		$simulados = array();
		foreach ($anosV2 as $ak => $av) {
			foreach($simuladosTmp as $simuladoID => $simulado) {
				$ss = $simulado->obterSerieAvaliacao();
				$snome = $ss->obterNome();

				if($snome == $av){
					$simulados[$simuladoID] = $simulado;
				}
			}
		}

		// PERFIL DE PROFESSOR
		$relatiosSecretario = $gerenciador->obterDiretiva('RELATORIO:ORDEM:PROFESSOR');
		echo "Relatorios gerado no bot - RELATORIO:ORDEM:PROFESSOR - ".$relatiosSecretario."<br>";
		$relatorios = explode(",", $relatiosSecretario);

		$professores = array();
		$turma = new Turma();

		$rs = Core::registro('db')->query('
			SELECT t_id, t_nome, t_serie, t_instituicao, i_id, i_nome, s_id, s_nome, s_instituicao
			FROM turmas
			INNER JOIN instituicoes ON i_id = t_instituicao
			INNER JOIN series ON s_id = t_serie
			ORDER BY i_nome, s_id, t_nome
		');

		$index = 0;
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$simulado_turma = array();
				$simulado_turma[0] = $row['s_nome'].' '.$gerenciador->obterDiretiva('BIMESTRE:CORRENTE').'º bimestre';
				#$simulado_turma[1] = $row['s_nome'].' '.$gerenciador->obterDiretiva('BIMESTRE:CORRENTE').'º bimestre Produção de Texto';//Produção de Texto

				foreach ($simulado_turma as $sPosicao => $simuladoNome) {
					$professores[$index++] = array(
						'id_turma' => $row['t_id'],
						'nome' => ProvaFloripa::obterTurmaComNome($row['t_nome']),
						'serie_id' => $row['s_id'],
						'serie_nome' => $simuladoNome,
						'instituicao_id' => $row['t_instituicao'],
						'instituicao_nome' => $row['i_nome']
					);
				}
				$simulado_turma = array();
			}
		}
		$rs->free();

		echo"<br>";
		foreach ($relatorios as $rPosicao => $rID) {
			$rPosicao = ((int)$rPosicao+1)*100;
			$pasta = 'upload/pdfs/';

			$relatorio = new Relatorio($rID);
			@$relatorio->carregar();

			foreach ($professores as $tID => $professor) {
				$rPosicao = $rPosicao+1000000;

				Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::PROFESSOR, array('turma' => $professor['id_turma'], 'instituicao' => $professor['instituicao_id']));

				$permissao = true;
				if ( Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ) {
					if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()))
						$permissao = false;
				} 
				elseif ( !$relatorio->validarPermissao() )
					$permissao = false;

				if ( $permissao ) {
					foreach($simulados as $simuladoID => $simulado) {
						if($simulado->obterProdTxt() == 1 && $rID != 26){
							continue;
						}

						if($simulado->obterSerieAvaliacao()->obterID() != $professor['serie_id']){
							continue;
						}

						//reordenar os rels para colocar prod em segundo lugar.
						if($simulado->obterProdTxt()){
							$rPosicaoTmp = $rPosicao+1;
							$ehPT = '_PT';
						}
						elseif(strpos(strtolower($simulado->obterNome()), 'especial') !== false){
							$rPosicaoTmp = $rPosicao;
							$ehPT = '_ESP';
						}
						else{
							$rPosicaoTmp = $rPosicao;
							$ehPT = '';
						}

						$rPosicaoTmp = str_pad($rPosicaoTmp , 10, '0' , STR_PAD_LEFT);

						$relNome = $relatorio->obterNome().$ehPT;

						unset($relatorioListagem);
						$relatorioListagem = $this->obterRelatorioListagem($relatorio);

						if ($relatorioListagem != null) {
							if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF'))
								continue;

							$nomeParaPasta = @iconv('ISO-8859-1', 'ASCII//IGNORE', $professor['instituicao_nome'].'/'.'Turmas'); // bug de escrita do nome do arquivo
							$relatorioListagem->fixarSeletorSimulados($simulado);
							$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
							$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($professor['id_turma'], $relatorio, $pasta.$nomeParaPasta.'/', $rPosicaoTmp.' - '.$professor['serie_nome'].' - '.$professor['nome'].' - '.$relNome);
							if($gerado === false){
								continue;
							}

							echo date("d/m/y H:i:s").": ";
							echo $pasta.$nomeParaPasta.'/'.$rPosicaoTmp.' - '.$professor['serie_nome'].' - '.$professor['nome'].' - '.$relNome.'.html'; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							$relNome = @iconv('ISO-8859-1', 'ASCII//IGNORE', $pasta.$nomeParaPasta.'/'.$rPosicaoTmp.' - '.$professor['serie_nome'].' - '.$professor['nome'].' - '.$relNome.'.html.'.$relatorioListagem->orientacao_PDF);
							$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$relNome."' > /dev/null 2>/dev/null &";
							$PID = system($cmd);

							echo date("d/m/y H:i:s").": ";
							echo $pathCMD.$relNome; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							$iNome = $professor['instituicao_nome'].' - '.$professor['serie_nome'].' - '.$professor['nome'];

							if (!isset($relatorios_gerados['professor'][$iNome]))
								$relatorios_gerados['professor'][$iNome] = array();

							$relatorios_gerados['professor'][$iNome][] = $relatorio->obterNome();
						}
					}
				}	
			}
		}		
		echo"<br>";$this->memoria('POS TURMAS'); 

		PRINT("FINISH TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");

		if(empty($serieRedirect)){
			$pathCMDconcat = "http".(($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://").$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
			$index = strpos($pathCMDconcat,'?');
			$pathCMDconcat = substr($pathCMDconcat, 0, $index);
			$pathCMDconcat = $pathCMDconcat.'upload/pdfs/concat_turmas.php';
			$PID = system("/usr/bin/wget --server-response --timeout=60 --output-document=output_concat.txt ".$pathCMDconcat." > /dev/null 2>/dev/null &");
			echo "Executando CONCATENAR em PARALELO - ".date("d/m/y H:i:s")." -> ".$PID."<br>";

			$msgTexto = "Alerta do BOT - Etapa 3 - Turmas<br>O Bot terminou!<br><br>Verifique em: ".$this->_downloadPdfs();
			@DespachadorEmailsUsuario::enviarAlertaBOT('Alerta do BOT - Etapa 3 - Turmas',$msgTexto);

			$it2 = new RecursiveDirectoryIterator("upload/pdfs/");
			foreach(new RecursiveIteratorIterator($it2) as $file) {		
			    $fileNome = $file->getPathname();
			    if(strpos($fileNome, 'Turmas') !== false && strpos($fileNome, 'Rede') === false && strpos($fileNome, 'Aprendizagem') === false && strpos($fileNome, 'kit') === false){
				    $FILE = array_flip(explode('.', $file));
				    if (isset($FILE['html'])) {
				    	$countHTMLS++;
				    }
				    elseif(isset($FILE['pdf'])) {
				    	$countPDFS++;
				    }
				}
			}

			echo"<br>Htmls gerados: $countHTMLS <br>Pdfs gerados: $countPDFS<br>";
		}
		else{
			PRINT($serieRedirect);
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
		echo '<strong>Os seguintes relat?rios foram gerados:</strong><br /><br />';
		foreach($relatorios_gerados as $rTipo => $relatorios) {
			echo '<strong>Relat?rios de '. $rTipo .':</strong><br /><ul>';
			if ($rTipo == 'professor') {
				foreach($relatorios as $iNome => $relatoriosInstituicao) {
					echo '<li>'. $iNome . '<ul>';
					foreach ($relatoriosInstituicao as $pNome => $relatoriosProfessor) {
						echo '<li>'. $relatoriosProfessor . '<ul>';
						echo '</ul></li>';
					}
					echo '</ul></li>';
				}
			} 
			echo '</ul>';
		}
		$this->memoria($this->_downloadPdfs());
		$this->_finalizarRenderizacao();

		$gerenciador->removerDiretivaSimples('_run_bot_turmas');

		exit;
	}

	public function aEmpacotarRelatoriosBoletim () 
	{
		register_shutdown_function(array($this, 'finishBot'));

		$gerenciador = new Gerenciador_Diretivas();

		$RUN = $gerenciador->obterDiretiva('_run_bot_boletim');
		if($RUN == '1'){
			exit('O bot de Boletim já está executando.');
		}
		else{
			$gerenciador->adicionarDiretivaDoSistemaSimples('_run_bot_boletim', '1');
		}

		header("Connection: close");

		session_cache_expire(60*24);
		ignore_user_abort(1); // run script in background
		set_time_limit(0); // run script forever 
		ini_set('memory_limit', '10000M'); // 2GB
		ini_set('max_execution_time', 0);
		ini_set('output_buffering','on');
		ob_implicit_flush();

        echo str_repeat(" ", 500); 
        ob_flush(); 
        flush();

		PRINT("START TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");
		$this->memoria('PRE BOLETIM');

		$pathCMD = str_replace('modulos/relatorios_pesquisas', '', realpath(dirname(__FILE__)));

		$relatorios_gerados = array(
			'boletim' => array()
		);

		$simuladosTmp = array();
		echo "Bimestre gerado no bot - BIMESTRE:CORRENTE - ".$gerenciador->obterDiretiva('BIMESTRE:CORRENTE')."<br>";
		foreach (array_keys(Simulado::obterSimuladosParaFormulario(false, $gerenciador->obterDiretiva('BIMESTRE:CORRENTE'))) as $simuladoID) {
			$simuladosTmp[$simuladoID] = new Simulado($simuladoID);
			$simuladosTmp[$simuladoID]->carregar();
		}	

		$ano = $gerenciador->obterDiretiva('RELATORIO:BOT:ANO');
		echo "Séries geradas no bot - RELATORIO:BOT:ANO - ".$ano."<br>";
		$anos = explode(",", $ano);

		$simulados = array();
		foreach ($anos as $ak => $av) {
			foreach($simuladosTmp as $simuladoID => $simulado) {
				$ss = $simulado->obterSerieAvaliacao();
				$snome = $ss->obterNome();

				if($simulado->obterProdTxt()){
					continue;
				}

				if($snome == $av){
					$simulados[$simuladoID] = $simulado;
				}
			}
		}

		$instituicoes = Instituicao::obterArrayInstituicoesParaFormulario();

		// PERFIL DE DIRETOR 
		$relatiosBoletim = $gerenciador->obterDiretiva('RELATORIO:BOT:BOLETIM');
		echo "Relatorios gerado no bot - RELATORIO:BOT:BOLETIM - ".$relatiosBoletim."<br>";
		$relatorios = explode(",", $relatiosBoletim);

		//limpando cache
		Core::registro('cache')->clean(Zend_Cache::CLEANING_MODE_ALL);

		$it1 = new RecursiveDirectoryIterator("upload/graficos/"); 
		foreach(new RecursiveIteratorIterator($it1) as $file) {
			foreach ($relatorios as $rPosicao => $rID) {
				if(strpos($file, $rID) !== false){
					@chmod(dirname($file), 0777);
					@chmod($file, 0777);
					@unlink($file);
				}
			}
		}

		//limpando pdfs e htmls.
		$limparPDFS = $gerenciador->obterDiretiva('BOT:LimparPDFsGerados');
		if($limparPDFS){
			if(is_dir("upload/pdfs/Boletim")){
				$it2 = new RecursiveDirectoryIterator("upload/pdfs/Boletim");
				foreach(new RecursiveIteratorIterator($it2) as $file) {		
				    $fileNome = $file->getPathname();
				    $FILE = array_flip(explode('.', $file));
				    if (isset($FILE['html']) || isset($FILE['pdf'])) {
						@chmod(dirname($file), 0777);
						@chmod($file, 0777);
						@unlink($file);
					}
				}
			}
		}

		echo"<br>";
		foreach ($relatorios as $rPosicao => $rID) {
			$rPosicao = ((int)$rPosicao+1)*1000;
			$pasta = 'upload/pdfs/Boletim/';			

			$relatorio = new Relatorio($rID);
			@$relatorio->carregar();

			foreach ($instituicoes as $iID => $iNome) {
				Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::DIRETOR, $iID);

				$permissao = true;
				if (Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null) {
					if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado())){
						$permissao = false;
					}
				} 
				elseif (!$relatorio->validarPermissao()){
					$permissao = false;
				}

				if ($permissao) {
					foreach($simulados as $simuladoID => $simulado) {
						unset($relatorioListagem);
						$relatorioListagem = $this->obterRelatorioListagem($relatorio);

						if ($relatorioListagem != null) {
							if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF')){
								continue;
							}

							$nomeParaPasta = @iconv('ISO-8859-1', 'ASCII//IGNORE', $iNome); // bug de escrita do nome do arquivo

							$rPosicao = str_pad($rPosicao , 10, '0' , STR_PAD_LEFT);
							$relNomeTmp = $relatorio->obterNome();

							$relatorioListagem->fixarSeletorSimulados($simulado);
							$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
							$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($relatorio, $pasta.$nomeParaPasta.' - ',$rPosicao.' - '.$relNomeTmp);
							if($gerado === false){
								continue;
							}

							echo date("d/m/y H:i:s").": ";
							echo $pasta.$nomeParaPasta.' - '.$rPosicao.' - '.$relatorio->obterNome().'.html'; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							$relNome = @iconv('ISO-8859-1', 'ASCII//IGNORE', $pasta.$nomeParaPasta.' - '.$rPosicao.' - '.$relNomeTmp.'.html.'.$relatorioListagem->orientacao_PDF);
							$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$relNome."' > /dev/null 2>/dev/null &";
							$PID = system($cmd);

							echo date("d/m/y H:i:s").": ";
							echo $pathCMD.$relNome; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();

							if (!isset($relatorios_gerados['boletim'][$iNome])){
								$relatorios_gerados['boletim'][$iNome] = array();
							}

							$relatorios_gerados['boletim'][$iNome][] = $rPosicao .' - '. $relatorio->obterNome();
						}

						$rPosicao += 1;
					} 
				}
			}
		}
		echo"<br>";$this->memoria('POS BOLETIM'); 

		PRINT("FINISH TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");

		$pathCMDconcat = "http".(($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://").$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
		$index = strpos($pathCMDconcat,'?');
		$pathCMDconcat = substr($pathCMDconcat, 0, $index);
		$pathCMDconcat = $pathCMDconcat.'upload/pdfs/concat_boletim.php';
		$PID = system("/usr/bin/wget --server-response --timeout=60 --output-document=output_concat.txt ".$pathCMDconcat." > /dev/null 2>/dev/null &");
		echo "Executando CONCATENAR em PARALELO - ".date("d/m/y H:i:s")." -> ".$PID."<br>";

		$msgTexto = "Alerta do BOT - Boletim<br>O Bot terminou!<br><br>Verifique em: ".$this->_downloadPdfs();
		@DespachadorEmailsUsuario::enviarAlertaBOT('Alerta do BOT - Boletim',$msgTexto);

		$countHTMLS = $countPDFS = 0;
		$it2 = new RecursiveDirectoryIterator("upload/pdfs/Boletim");
		foreach(new RecursiveIteratorIterator($it2) as $file) {		
		    $fileNome = $file->getPathname();
		    $FILE = array_flip(explode('.', $file));
		    if (isset($FILE['html'])) {
		    	$countHTMLS++;
		    }
		    elseif(isset($FILE['pdf'])) {
		    	$countPDFS++;
		    }
		}

		echo"<br>Htmls gerados: $countHTMLS <br>Pdfs gerados: $countPDFS<br>";

		$this->_iniciarRenderizacao(Modulo::HTML);
		echo '<strong>Os seguintes relatorios foram gerados:</strong><br /><br />';
		foreach($relatorios_gerados as $rTipo => $relatorios) {
			echo '<strong>Relat?rios de '.$rTipo.':</strong><br /><ul>';
			if ($rTipo == 'boletim') {
				foreach($relatorios as $iNome => $relBoletim) {
					echo '<li>'.$relBoletim. '</li>';
				}
			} 
			echo '</ul>';
		}
		$this->memoria($this->_downloadPdfs());
		$this->_finalizarRenderizacao();

		$gerenciador->removerDiretivaSimples('_run_bot_boletim');

		exit;
	}

	public function aEmpacotarRelatoriosGabaritos () 
	{
		register_shutdown_function(array($this, 'finishBot'));

		$gerenciador = new Gerenciador_Diretivas();

		$RUN = $gerenciador->obterDiretiva('_run_bot_gabaritos');
		if($RUN == '1'){
			exit('O bot de Gabaritos já está executando.');
		}
		else{
			$gerenciador->adicionarDiretivaDoSistemaSimples('_run_bot_gabaritos', '1');
		}

		header("Connection: close");

		session_cache_expire(60*24);
		ignore_user_abort(1); // run script in background
		set_time_limit(0); // run script forever 
		ini_set('memory_limit', '10000M'); // 2GB
		ini_set('max_execution_time', 0);
		ini_set('output_buffering','on');
		ob_implicit_flush();

        echo str_repeat(" ", 500); 
        ob_flush(); 
        flush();

		PRINT("START TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");
		$this->memoria('PRE GABARITO');

		$pasta = 'upload/pdfs/Gabaritos/';
		$pathCMD = str_replace('modulos/relatorios_pesquisas', '', realpath(dirname(__FILE__)));

		$relatorios_gerados = array(
			'gabaritos' => array()
		);

		$layouts = json_decode($gerenciador->obterDiretiva('GABARITO:layout'),true); //{"ID_SERIE":"NOME_LAYOUT"}

		$simulados = array();
		$serieSimulado = array();
		$simuladosPermitidos = $gerenciador->obterDiretiva('RELATORIO:BOT:GABARITO:AVALIACOES');
		echo "Avaliações geradas no bot - RELATORIO:BOT:GABARITO:AVALIACOES - ".$simuladosPermitidos."<br>";
		foreach (array_keys(Simulado::obterSimuladosParaFormularioII(false)) as $simuladoID) {
			if(!in_array($simuladoID, explode(',', $simuladosPermitidos))){continue;}

			$simulados[$simuladoID] = new Simulado($simuladoID);
			$simulados[$simuladoID]->carregar();

			$serieAv = $simulados[$simuladoID]->obterSerieAvaliacao();
			$serieAv->carregar();
			$serieID = $serieAv->obterID();

			$serieSimulado[$layouts[$serieID]] = $simulados[$simuladoID];
		}

		// PERFIL DE DIRETOR 
		$relatiosGabarito = $gerenciador->obterDiretiva('RELATORIO:BOT:GABARITO');
		echo "Relatorios gerado no bot - RELATORIO:BOT:GABARITO - ".$relatiosGabarito."<br>";
		$relatorios = explode(",", $relatiosGabarito);

		//limpando cache
		Core::registro('cache')->clean(Zend_Cache::CLEANING_MODE_ALL);

		$it1 = new RecursiveDirectoryIterator("upload/graficos/"); 
		foreach(new RecursiveIteratorIterator($it1) as $file) {
			foreach ($relatorios as $rPosicao => $rID) {
				if(strpos($file, $rID) !== false){
					@chmod(dirname($file), 0777);
					@chmod($file, 0777);
					@unlink($file);
				}
			}
		}

		//limpando pdfs e htmls.
		$limparPDFS = $gerenciador->obterDiretiva('BOT:LimparPDFsGerados');
		if($limparPDFS){
			if(is_dir($pasta)){
				$it2 = new RecursiveDirectoryIterator($pasta);
				foreach(new RecursiveIteratorIterator($it2) as $file) {		
				    $fileNome = $file->getPathname();
				    $FILE = array_flip(explode('.', $file));
				    if (isset($FILE['html']) || isset($FILE['pdf'])) {
						@chmod(dirname($file), 0777);
						@chmod($file, 0777);
						@unlink($file);
					}
				}
			}
		}

		foreach ($relatorios as $rPosicao => $rID) {
			$relatorio = new Relatorio($rID);
			@$relatorio->carregar();

			$permissao = true;
			//...o funcionamento dessa verificação de permissão tá estranha!
			/*if (Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null) {
				if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado())){
					$permissao = false;
				}
			} 
			elseif (!$relatorio->validarPermissao()){
				$permissao = false;
			}*/

			if ($permissao) {
				foreach($serieSimulado as $layout => $simulado) {
					//gerar folha de calibração
					unset($relatorioListagem);
					$relatorioListagem = $this->obterRelatorioListagem($relatorio);
					if ($relatorioListagem != null) {
						if (method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDFComDadosFalsos')){
							Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::DIRETOR, 1);

							$alunoNome = 'FOLHA DE CALIBRACAO';
							$escolaNome = '0_CALIBRACAO';
							$turmaNome = '';

							$serieAv = $simulado->obterSerieAvaliacao();
							$serieAv->carregar();
							$serieNome = $serieAv->obterNome();

							$relNome = $relatorio->obterNome();
							$relNomeTmp = $relNome.'.html.'.$relatorioListagem->orientacao_PDF;
							$pathPasta = trim($pasta.@iconv('ISO-8859-1', 'ASCII//IGNORE', $escolaNome.'/'));
							$nomeArquivo1 = trim(@iconv('ISO-8859-1', 'ASCII//IGNORE', $alunoNome.'_'.$layout.'_'.$relNome));
							$nomeArquivo2 = trim(@iconv('ISO-8859-1', 'ASCII//IGNORE', $alunoNome.'_'.$layout.'_'.$relNomeTmp));

							$relatorioListagem->fixarSeletorSimulados($simulado);
							$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
							$gerado = $relatorioListagem->prepararRelatorioParaPacotePDFComDadosFalsos($relatorio, $pathPasta, $nomeArquivo1);
							if($gerado === false){
								continue;
							}							

							$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$pathPasta.$nomeArquivo2."' > /dev/null 2>/dev/null &";
							$PID = system($cmd);

							echo date("d/m/y H:i:s").": ";
							echo $pathPasta.$nomeArquivo2; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();	

							$relatorios_gerados['gabaritos'][] = $pathPasta.$nomeArquivo2;	
						}
					}

					//gerar folha de aluno novo
					unset($relatorioListagem);
					$relatorioListagem = $this->obterRelatorioListagem($relatorio);
					if ($relatorioListagem != null) {
						if (method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDFAlunoNovo')){
							Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::DIRETOR, 1);

							$alunoNome = 'FOLHA DE NOVO ALUNO';
							$escolaNome = '0_ALUNO_NOVO';
							$turmaNome = '';

							$serieAv = $simulado->obterSerieAvaliacao();
							$serieAv->carregar();
							$serieNome = $serieAv->obterNome();

							$relNome = $relatorio->obterNome();
							$relNomeTmp = $relNome.'.html.'.$relatorioListagem->orientacao_PDF;
							$pathPasta = trim($pasta.@iconv('ISO-8859-1', 'ASCII//IGNORE', $escolaNome.'/'));
							$nomeArquivo1 = trim(@iconv('ISO-8859-1', 'ASCII//IGNORE', $alunoNome.'_'.$layout.'_'.$relNome));
							$nomeArquivo2 = trim(@iconv('ISO-8859-1', 'ASCII//IGNORE', $alunoNome.'_'.$layout.'_'.$relNomeTmp));

							$relatorioListagem->fixarSeletorSimulados($simulado);
							$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
							$gerado = $relatorioListagem->prepararRelatorioParaPacotePDFAlunoNovo($relatorio, $pathPasta, $nomeArquivo1);
							if($gerado === false){
								continue;
							}							

							$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$pathPasta.$nomeArquivo2."' > /dev/null 2>/dev/null &";
							$PID = system($cmd);

							echo date("d/m/y H:i:s").": ";
							echo $pathPasta.$nomeArquivo2; echo"<br>";
							echo str_repeat(" ", 500); ob_flush(); flush();	

							$relatorios_gerados['gabaritos'][] = $pathPasta.$nomeArquivo2;	
						}
					}
				}
			}

			if ($permissao) {
				foreach($simulados as $simuladoID => $simulado) {					
					//gera folha de alunos
					$alunos = Core::registro('db')->query('
						SELECT si_simulado, a_id, u_nome, i_nome, t_nome
						FROM simulados_inscricoes 
						INNER JOIN alunos ON a_id = si_aluno
						INNER JOIN usuarios ON u_id = a_usuario 
						INNER JOIN turmas ON t_id = a_turma
						INNER JOIN instituicoes ON i_id = u_instituicao
						WHERE si_simulado = '.$simuladoID.' 
						ORDER BY si_simulado, i_nome, t_nome, u_nome ASC;
					');

					if ($alunos->num_rows) {
						while ($row2 = $alunos->fetch_assoc()) {
							unset($relatorioListagem);
							$relatorioListagem = $this->obterRelatorioListagem($relatorio);
							if ($relatorioListagem != null) {
								if (!method_exists($relatorioListagem, 'prepararRelatorioParaPacotePDF')){
									continue;
								}

								$alunoID = $row2['a_id'];
								$alunoNome = $row2['u_nome'];
								$escolaNome = $row2['i_nome'];
								$turmaNome = $row2['t_nome'];

								$serieAv = $simulado->obterSerieAvaliacao();
								$serieAv->carregar();

								$serieNome = $serieAv->obterNome();

								$serieID = $serieAv->obterID();
								$layout = $layouts[$serieID];

								Core::modulo('_perfil_falso')->forcarPerfil(MPerfilFalso::ALUNO, $alunoID);

								$relNome = $relatorio->obterNome();
								$relNomeTmp = $relNome.'.html.'.$relatorioListagem->orientacao_PDF;
								//$pathPasta = $pasta.@iconv('ISO-8859-1', 'ASCII//IGNORE', $simulado->obterNome().'/'.$escolaNome.'/');
								$pathPasta = trim($pasta.@iconv('ISO-8859-1', 'ASCII//IGNORE', $escolaNome.'/'));
								$nomeArquivo1 = trim(@iconv('ISO-8859-1', 'ASCII//IGNORE', $serieNome.'_'.$layout.'_'.$turmaNome.'_'.$alunoNome.'_'.$relNome));
								$nomeArquivo2 = trim(@iconv('ISO-8859-1', 'ASCII//IGNORE', $serieNome.'_'.$layout.'_'.$turmaNome.'_'.$alunoNome.'_'.$relNomeTmp));

								$nomeArquivo1 = preg_replace("[^a-zA-Z0-9_ .]", "", $nomeArquivo1);
								$nomeArquivo2 = preg_replace("[^a-zA-Z0-9_ .]", "", $nomeArquivo2);

								$relatorioListagem->fixarSeletorSimulados($simulado);
								$relatorioListagem->obterSeletorSimulados()->simulado = $simulado;
								$gerado = $relatorioListagem->prepararRelatorioParaPacotePDF($relatorio, $pathPasta, $nomeArquivo1);
								if($gerado === false){
									continue;
								}

								$cmd = "/usr/bin/php -d -f '/home/<USER>/dompdf/dompdf/dompdf.php' -- -o '".$relatorioListagem->orientacao_PDF."' -b '".$pathCMD."' '".$pathCMD.$pathPasta.$nomeArquivo2."' > /dev/null 2>/dev/null &";
								$PID = system($cmd);

								echo date("d/m/y H:i:s").": ";
								echo $pathPasta.$nomeArquivo2; echo"<br>";
								echo str_repeat(" ", 500); ob_flush(); flush();	

								$relatorios_gerados['gabaritos'][] = $pathPasta.$nomeArquivo2;		
							}
						}
					}
					$alunos->free();
				}
			}
		}

		echo"<br>";$this->memoria('POS GABARITO'); 

		PRINT("FINISH TIME - HTML + PDF: ".date('d/m/Y H:i:s')."<br>");

		$pathCMDconcat = "http".(($_SERVER['SERVER_PORT'] == 443) ? "s://" : "://").$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
		$index = strpos($pathCMDconcat,'?');
		$pathCMDconcat = substr($pathCMDconcat, 0, $index);
		$pathCMDconcat = $pathCMDconcat.'upload/pdfs/concat_gabarito.php';
		$PID = system("/usr/bin/wget --server-response --timeout=60 --output-document=output_concat.txt ".$pathCMDconcat." > /dev/null 2>/dev/null &");
		echo "Executando CONCATENAR em PARALELO - ".date("d/m/y H:i:s")." -> ".$PID."<br>";

		$msgTexto = "Alerta do BOT - Gabaritos<br>O Bot terminou!<br><br>Verifique em: ".$this->_downloadPdfs();
		@DespachadorEmailsUsuario::enviarAlertaBOT('Alerta do BOT - Gabaritos',$msgTexto);

		$countHTMLS = $countPDFS = 0;
		if(is_dir($pasta)){
		$it2 = new RecursiveDirectoryIterator($pasta);
			foreach(new RecursiveIteratorIterator($it2) as $file) {		
			    $fileNome = $file->getPathname();
			    $FILE = array_flip(explode('.', $file));
			    if (isset($FILE['html'])) {
			    	$countHTMLS++;
			    }
			    elseif(isset($FILE['pdf'])) {
			    	$countPDFS++;
			    }
			}
		}

		echo"<br>Htmls gerados: $countHTMLS <br>Pdfs gerados: $countPDFS<br>";

		$this->_iniciarRenderizacao(Modulo::HTML);

		echo '<strong>Os seguintes relatorios foram gerados:</strong><br /><br />';
		echo '<strong>Relatorios de Gabaritos:</strong><br /><ul>';
		foreach($relatorios_gerados as $rTipo => $rels) {
			echo '<li>'.$rels. '</li>';
		}
		echo '</ul>';

		$this->memoria($this->_downloadPdfs());
		$this->_finalizarRenderizacao();

		$gerenciador->removerDiretivaSimples('_run_bot_gabaritos');

		exit;
	}
}
?>