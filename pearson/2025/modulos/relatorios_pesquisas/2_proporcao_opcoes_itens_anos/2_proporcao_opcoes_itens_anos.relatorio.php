<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelListPesquisa', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

class Rel_2ProporcaoOpcoesItensAnos extends RelListPesquisa {
	public function __construct () {
		parent::__construct();

		$this->gtCor = '#000000';

		$this->opcoesCores = array(
			'0'=>'#eeeeee',
			'1'=>'#f1501f',
			'2'=>'#f4d826',
			'4'=>'#8be37d',
			'8'=>'#539e48',
		);

		$this->_dados = null;

		$this->_imagem = array();

		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '2_proporcao_opcoes_itens_anos.relatorio.html.php';

		include 'modulos/relatorios/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null,ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '2_proporcao_opcoes_itens_anos.relatorio.html.php';

		include 'modulos/relatorios/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo,ExportadorPDF::RETRATO, $pasta);
	}

	protected function _obterDados () {
		if ( $this->_seletorPesquisas->pesquisa->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->pesquisa);

		$this->_obterDadosInscricoes();

		$this->gerarGrafico();
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_pesquisas/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_pesquisas/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorPesquisas = new MSeletorPesquisas($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorPesquisas();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorPesquisas->ajustarPesquisaselecionado();
	}

	protected function _obterDadosInscricoes () {
		$this->_analizadorPesquisa->carregarParticipantes();
		$this->_analizadorPesquisa->carregarItensPesquisa();
		$this->_analizadorPesquisa->carregarRespostasPorItem();

		$dado = array();
		foreach ($this->_analizadorPesquisa->obterItensPesquisaII() as $iid => $iclass) {
			$iclass->carregarCompetencias();

			$clista = '';
			$last_key = end(array_keys($iclass->obterCompetencias()));
			foreach ($iclass->obterCompetencias() as $ock => $ocv) {
				$clista .= $ocv->obterNome();
				if ($ock !== $last_key) {
			        $clista .= ', ';
			    }
			}		

			$dado[$iid]['identificador'] = $iclass->obterIdentificador();
			$dado[$iid]['enunciado'] = $iclass->obterEnunciado();
			$dado[$iid]['competencias'] = $clista;
			$dado[$iid]['temResposta'] = 0;

			foreach ($this->_analizadorPesquisa->obterSeriesParticipantes() as $sid => $snome) {
				$dado[$iid]['anos'][$sid]['nome'] = $snome;

				$dado[$iid]['anos'][$sid]['opcoes'][0]['nome'] = 'EM BRANCO';
				$dado[$iid]['anos'][$sid]['opcoes'][0]['qtd'] = 0;
				foreach ($iclass->obterOpcoes() as $k1 => $oclass) {
					$dado[$iid]['anos'][$sid]['opcoes'][$oclass->obterNumero()]['nome'] = $oclass->obterTexto();
					$dado[$iid]['anos'][$sid]['opcoes'][$oclass->obterNumero()]['qtd'] = 0;
				}
			}

			foreach ($this->_analizadorPesquisa->obterRespostasPorItem() as $pid => $participantes) {
				$participante = new PesquisaParticipante($pid);
				$participante->carregar();
				$dadosAluno = $participante->obterAluno();
				$dadosAluno->carregar();
				$dadosTurma = $dadosAluno->obterTurma();
				$dadosSerie = $dadosTurma->obterSerie();
				$ano = $dadosSerie->obterID();

				$rid = $participantes[$iid];

				$rval = PesquisaResposta::carregarValorPorId($rid);
				if($rval !== NULL){
					$dado[$iid]['anos'][$ano]['opcoes'][$rval]['qtd'] += 1;
					$dado[$iid]['temResposta'] = 1;
				}
				else{
					$dado[$iid]['anos'][$ano]['opcoes'][0]['qtd'] += 1;
				}
			}
		}

		$this->_dados = $dado;
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		foreach ($this->_dados as $dk => $dv) {
			$this->_imagem[$dk]['ttl'] = '<p style="font-size: 15px;">('.$dv['identificador'].') '.strip_tags($dv['enunciado']).'</p>';
			$this->_imagem[$dk]['ttl'] .= '<p style="font-size: 13px;">Compet&#234;ncia socioemocional: '.$dv['competencias'].'</p>';

			if(!$dv['temResposta']){ continue; }

			$titulo = CACHE_PREFIXO.'_r'.$this->_relatorio->obterID().'_p'.$this->_seletorPesquisas->pesquisa->obterID().'_i'.$dk.'.png';
			$this->_imagem[$dk]['img'] = $titulo;

			$barras = array();
			$barras[1] = 0;
			$barras[2] = 0;
			$barras[4] = 0;
			$barras[8] = 0;

		    $grafico = new Graph(700 , count($dv['anos']) * 20 + (25*5),  $titulo, 0, false);

			$grafico->SetMargin(34, 0, 10, 30);
			$grafico->setFrame(true);
			$grafico->SetScale('textint', 0, 100, 0, 0);
			$grafico->yaxis->scale->ticks->Set(100);
			$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
			$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
			$grafico->yaxis->HideLabels();
			$grafico->xaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
		    $grafico->Set90AndMargin(100, 5, 5, 90);

			$grafico->legend->Pos(0.2,0.996,'left','bottom');
			$grafico->legend->SetLayout(LEGEND_VERT);
			$grafico->legend->SetFillColor('white');
			$grafico->legend->SetFrameWeight(1);
			$grafico->legend->SetShadow(false);
			$grafico->legend->SetReverse(true);
			$grafico->legend->SetFont(FF_ARIAL,FS_NORMAL, 9);

			$total = array();
			foreach ($dv['anos'] as $dk2 => $dv2) {
				$total[$dk2] = 0;
				foreach ($dv2['opcoes'] as $dk3 => $dv3) {
					$total[$dk2] += $dv3['qtd'];
				}
			}

			$valores = array();
			foreach ($dv['anos'] as $dk2 => $dv2) {
				$total100 = 0;
				if($total[$dk2] > 0){
					$total100 = 100/$total[$dk2];
				}

				foreach ($dv2['opcoes'] as $dk3 => $dv3) {
					$valores[$dk3][] = $dv3['qtd']*$total100;
				}
			}

			$sortPP = array();
			foreach ($valores as $vk => $vv) {
				foreach ($vv as $vk2 => $vv2) {
					$sortPP[$vk2][$vk] = $vv2;
				}
			}

			foreach ($sortPP as $dk2 => $dv2) {
				$sortPP[$dk2] = RelListPesquisa::calcularPorcentagemPerfeita($sortPP[$dk2]);
			}

			$valores = array();
			foreach ($sortPP as $vk => $vv) {
				foreach ($vv as $vk2 => $vv2) {
					$valores[$vk2][$vk] = $vv2;
				}
			}

			foreach ($dv['anos'] as $dk2 => $dv2) {
				foreach ($dv2['opcoes'] as $dk3 => $dv3) {
					$barras[$dk3] = new BarPlot($valores[$dk3]);
				}
			}

			$gruposBarras = new AccBarPlot( 
				array(
					$barras[0],
					$barras[1],
					$barras[2],
					$barras[4],
					$barras[8]
				) 
			);
			$grafico->Add($gruposBarras);

			$gruposBarras->SetColor('gray');
			$gruposBarras->SetWeight(1);
		
			$labelsX = array();
			foreach ($dv['anos'] as $dk2 => $dv2) {
				foreach ($dv2['opcoes'] as $dk3 => $dv3) {
					$barras[$dk3]->SetFillColor($this->opcoesCores[$dk3]);
					$barras[$dk3]->SetLegend($dv3['nome']);
					$barras[$dk3]->value->Show();
					$barras[$dk3]->value->SetFormat('%d%%');
					$barras[$dk3]->value->SetColor( $this->gtCor );
					$barras[$dk3]->SetValuePos('center');
					$barras[$dk3]->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
				}

				$labelsX[] = $dv2['nome'].' ('.$total[$dk2].')';
			}

			$grafico->xaxis->SetTickLabels($labelsX);

			$gruposBarras->SetWidth(1);

			$grafico->Stroke();
		}
	}
}

?>