<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('modulos/_ordenacao/_ordenacao_padrao.ordenacao.php');

class Ordenacao_RelatoriosPesquisas extends Ordenacao_Padrao
{
	public $repetirCabecalho = null;
	public $multiplaOrdenacao = array();

	public $_contagem = 0;
	
	protected $_itensMultiplaOrdenacao = array();

	public function prepararOrdenacao ()
	{
		parent::prepararOrdenacao();
		
		@$this->_campos['atualizar']->fixar('etiqueta', 'Atualizar relatório');
	}

	public function reAtualizaOrdenacoesNoBancoDeDados () {
		//if ( $this->foiEnviado() )
		$this->_atualizaOrdenacoesNoBancoDeDados();
	}


	public function executarPedidoDeOrdenacao ()
	{
		try
		{
			parent::executarPedidoDeOrdenacao();
			
			if (isset($this->_campos['repetir_cabecalho'])) { $this->repetirCabecalho = (int) $this->_campos['repetir_cabecalho']->obter('valor'); }
			
			for ( $i = 0; $i < count($this->_itensMultiplaOrdenacao); $i++ ) {
				$ordenarPor = $this->_campos[ ($i == 0 ? 'ordenar_por' : 'multiplo_ordenar_por_' . $i) ]->obter('valor');
				$tipoOrdem = $this->_campos[ ($i == 0 ? 'tipo_ordem' : 'multiplo_tipo_ordem_' . $i) ]->obter('valor');
				
				if ( $ordenarPor != Campo::NULO && !isset( $this->multiplaOrdenacao[$ordenarPor] ) )
					$this->multiplaOrdenacao[$ordenarPor] = $tipoOrdem;
			}
		}
		catch (Ordenacao_Exception $e) { }
	}

	public function obterItensMultiplaOrdenacao () {
		return $this->_itensMultiplaOrdenacao;
	}

	public function obterMultiplaOrdenacaoSQL ($incluirOrderBy = false) {
		$sql = array();
		foreach ($this->multiplaOrdenacao as $n => $v) {
			$sql[] = "$n $v";
		}
		
		if ( count($sql) ) {
			$sql = implode(', ', $sql);
			
			if ($incluirOrderBy)
				$sql = ' ORDER BY ' . $sql;
		} else {
			$sql = '';
		}
		
		return $sql;
	}
	
	public function adicionarItemParaMultiplaOrdenacao ($valores, $padrao = null, $padraoTipOrdem = self::ASC, $incluirItemNulo = true) {
		$novosValores = array(Campo::NULO => '');
		if ( $incluirItemNulo ) {
			foreach ($valores as $n => $v) {
				$novosValores[$n] = $v;
			}
		} else {
			$novosValores = $valores;
		}
	
		$this->_itensMultiplaOrdenacao[] = array($novosValores, $padrao, $padraoTipOrdem);
	}

	public function configurarCampoMultiplaOrdenacao () {
		$valoresTipoOrdem = array(self::ASC => 'Ascendente', self::DESC => 'Descendente');

		for ( $i = 0; $i < count($this->_itensMultiplaOrdenacao); $i++ ) {
			$etiqueta = null;

			if ( $i == 0 )
				$etiqueta = 'Ordenar por';
			else if ( count($this->_itensMultiplaOrdenacao) > 2 && $i == count($this->_itensMultiplaOrdenacao) - 1 )
				$etiqueta = 'E finalmente por';
			else
				$etiqueta = 'E depois por';

			$this->adicionarCampo( new Campo(array( 'nome' => ($i == 0 ? 'ordenar_por' : 'multiplo_ordenar_por_' . $i),
													'etiqueta' => $etiqueta,
													'valor' => $this->_itensMultiplaOrdenacao[$i][1],
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($this->_itensMultiplaOrdenacao[$i][0])),
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $this->_itensMultiplaOrdenacao[$i][0],
													'valor_pos_erro' => $this->_itensMultiplaOrdenacao[$i][1]
								  )), null, true );

			$this->adicionarCampo( new Campo(array( 'nome' => ($i == 0 ? 'tipo_ordem' : 'multiplo_tipo_ordem_' . $i),
													'etiqueta' => 'Tipo de ordem',
													'valor' => $this->_itensMultiplaOrdenacao[$i][2],
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'argumentos' => array(Campo::POSSIBILIDADES => array_keys($valoresTipoOrdem)),
													'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
													'html_tipo' => Campo::HTML_MENU,
													'html_valor' => $valoresTipoOrdem,
													'valor_pos_erro' => $this->_itensMultiplaOrdenacao[$i][2]
								  )), null, true );
		}
	}

	public function configurarCampoRepetirCabecalho ($valores = null, $padrao = null, $linha = 1)
	{	
		if ( $valores == null )
			$valores = array('0' => 'Não repetir', '1' => 'A cada 1 registro', '2' => 'A cada 2 registros', '5' => 'A cada 5 registros', '10' => 'A cada 10 registros', '20' => 'A cada 20 registros', '30' => 'A cada 30 registros', '60' => 'A cada 60 registros', '90' => 'A cada 90 registros', '130' => 'A cada 130 registros', '160' => 'A cada 160 registros', '200' => 'A cada 200 registros');

		$this->adicionarCampo( new Campo(array( 'nome' => 'repetir_cabecalho',
												'etiqueta' => 'Repetir cabeçalho',
												'valor' => $padrao,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($valores)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $valores,
												'valor_pos_erro' => $padrao
							  )), $linha, true );
	}
	
	public function configurarCampoMenuSelecao ($nome, $etiqueta, $valores = array(), $padrao = null, $linha = 1, $salvar = true) {
		if ( isset($this->_campos[$nome]) )
			return false;
	
		$this->adicionarCampo( new Campo(array( 'nome' => $nome,
												'etiqueta' => $etiqueta,
												'valor' => $padrao,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($valores)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $valores,
												'valor_pos_erro' => $padrao
							  )), $linha, $salvar );
	}
	
	public static function ordenarNumero ($n1, $n2, $tipoOrdem = self::ASC) {
		if ( $n1 == $n2 )
			return 0;
			
		if ($n1 === null || $n2 === null) {
			if ($n1 === null)
				return $tipoOrdem == self::ASC ? -1 : 1;
			else
				return $tipoOrdem == self::ASC ? 1 : -1;
		}
		
		if ( $tipoOrdem == self::ASC )
			return $n1 > $n2 ? 1 : -1;
		else
			return $n2 > $n1 ? 1 : -1;
	}
	
	public static function ordenarString ($s1, $s2, $tipoOrdem = self::ASC, $ordemNatural = false) {
		$i = $tipoOrdem == self::ASC ? 1 : -1;			
	
		if ( !$ordemNatural )
			return ($i * strcoll( $s1, $s2 ));
		else
			return ($i * strnatcmp( $s1, $s2 ));
	}

	public function fixarResultado ($num)
	{
		$this->_contagem = $num;
	}

	public function obterResultado ()
	{
		return $this->_contagem;
	}
}

?>