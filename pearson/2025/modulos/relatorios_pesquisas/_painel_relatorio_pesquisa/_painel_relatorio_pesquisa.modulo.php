<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('modulos/_ordenacao/_ordenacao.modulo.php');
include_once('_ordenacao_relatorios.ordenacao.php');

class MPainelRelatorioPesquisa extends MOrdenacao
{
	protected $_relatorio;
	protected $_botaoVoltar = array();

	public function __construct ()
	{
		parent::__construct();

		$this->prepararBotaoVoltar('Voltar para os relatórios', Gerenciador_URL::gerarLink('relatorios_pesquisas', 'listar'));
	}

	public function carregarPainel ()
	{
		if ( $this->_ordenacao != null )
			$this->carregarOrdenacao();
	}

	public function prepararSeletorRelatorios () {
		Core::carregarModulo(array('nome' => '_seletor_relatorios', 'pasta' => 'relatorios_pesquisas/', 'classe' => 'MSeletorRelatorios', 'guardar_como' => '_seletor_relatorios'));
		
		Core::modulo('_seletor_relatorios')->limitarPorGrupo( $this->_relatorio );
		Core::modulo('_seletor_relatorios')->prepararSeletor();
	}

	public function fixarOrdenacao (Ordenacao &$ordenacao)
	{
		parent::fixarOrdenacao($ordenacao);
	}

	public function fixarRelatorio (Relatorio &$relatorio)
	{
		$this->_relatorio = $relatorio;
	}
	
	public function prepararBotaoVoltar ($nome, $endereco = null)
	{
		$this->_botaoVoltar = array('nome' => $nome, 'endereco' => $endereco, 'habilitado' => ($nome != null));
	}
}

?>