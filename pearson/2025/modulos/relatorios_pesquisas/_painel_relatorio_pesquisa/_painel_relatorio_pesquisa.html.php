<? if ($this->_ordenacao->temErros()) { ?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_ordenacao->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<? } ?>

<table width="100%" border="0" align="center" cellpadding="4" cellspacing="0" class="rlt_cd">
    <tr>
    	<td class="rlt_cd_Titulo" style="padding: 16px; width: 45%;" valign="top"><div class="maior"><strong><?= $this->_relatorio->obterNome(); ?></strong></div>
		<? if ( @$this->_botaoVoltar['habilitado'] ) { ?>
			<div><a href="<?= @$this->_botaoVoltar['endereco']; ?>"><?= @$this->_botaoVoltar['nome']; ?></a></div>
		<? } ?>
      	</td>

      	<?php
      		$opcoes = array();
      		$opcoes[0] = 'Em branco';
      		$opcoes[1] = 'Nada';
      		$opcoes[2] = 'Um pouco';
      		$opcoes[4] = 'As vezes';
      		$opcoes[8] = 'Muito';

      		$competencias = Competencia::obterArrayCompetenciasParaFormulario();

      		$queryString = '';
      		if(array_key_exists('QUERY_STRING',$_SERVER)){
      			$queryString = $_SERVER['QUERY_STRING'];
      		}
      	?>

      	<td nowrap="nowrap" class="rlt_cd_TituloDireita" style="padding: 16px; width: 52%;">
      		<strong>Filtros da Pesquisa:</strong>
      		<br />
      		<form action="./?<?= $queryString; ?>" enctype="application/x-www-form-urlencoded" method="post" >
	      		<table style="border: 1px #ddd solid;margin: 10px;border-radius: 5px;padding: 5px; width: 98%;" id='filtros'>
	  				<tr>
	  					<td></td>
	  					<?php
	  						foreach ($opcoes as $ok => $ov) {
	  							echo'<td style="text-align: center;">'.$ov.'</td>';
	  						}
	  					?>
	  				</tr>
	  				<?php
	  					foreach ($competencias as $ck => $cv) {
		  				echo'<tr>';
		  					echo'<td style="border-right: 1px #ddd solid;">'.$cv.'</td>';
		  					$lk = end(array_keys($opcoes));
		  					foreach ($opcoes as $ok => $ov) {
		  						if ($key !== $last_key) {
		  							echo'<td style="border-right: 1px #ddd solid; text-align: center;">';
							    } 
							    else {
		  							echo'<td style="text-align: center;">';
							    }

							    $checked = 'checked="checked"';
								if(array_key_exists('atualizar',$_POST)){
									if(array_key_exists('fco',$_POST)){
										if(!array_key_exists($ck.'_'.$ok,$_POST['fco'])){
											$checked = '';
										}
									}
								}
		  						
		  						echo'<input type="checkbox" '.$checked.' name="fco['.$ck.'_'.$ok.']"/></td>';
		  					}
		  				echo'</tr>';
	  					}
	  				?>
	      		</table>
	      		
	      		<input name="inverter" id="inverter" value="Inverter filtros" type="button">
	      		<script type="text/javascript">
					jQuery(function(){
						jQuery('#inverter').click(function(){
							jQuery('#filtros input:checkbox').each(function (){
								this.checked = !this.checked; 
							});
						});
					});
	      		</script>

	      		<input name="atualizar" id="atualizar" value="Atualizar filtros" type="submit" class="botao salvar">
      		</form>
      	</td>

		<?= $this->_ordenacao->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>

		<? if ( $this->_ordenacao->campo('seletor_pesquisa') !== false ) { ?>
		    <td nowrap="nowrap" class="rlt_cd_TituloDireita" style="padding: 16px;"><strong><?= $this->_ordenacao->obterHTML('seletor_pesquisa', Formulario::HTML_LABEL, true); ?></strong><br /><?= $this->_ordenacao->obterHTML('seletor_pesquisa', Formulario::HTML_CAMPO, true); ?></td>
		<? } ?>

		<? if ( $this->_ordenacao->campo('seletor_simulado') !== false ) { ?>
		    <td nowrap="nowrap" class="rlt_cd_TituloDireita" style="padding: 16px;"><strong><?= $this->_ordenacao->obterHTML('seletor_simulado', Formulario::HTML_LABEL, true); ?></strong><br /><?= $this->_ordenacao->obterHTML('seletor_simulado', Formulario::HTML_CAMPO, true); ?></td>
		<? } ?>

		<?= $this->_ordenacao->obterHTML(null, Formulario::HTML_FORM_FIM); ?>
    </tr>
</table>

