<?
if (!defined('CORE_INCLUIDO')) exit();

include_once('modulos/relatorios_pesquisas/_seletor_simulados/_seletor_simulados.php');

Core::incluir('RelListPesquisa', null, true);
Core::incluir('RelCorePesquisa', null, true);
Core::incluir('PesquisaParticipante', null, true);
Core::incluir('PesquisaResposta', null, true);

class Rel_3ComparativoControleFiltros extends RelListPesquisa {
	const GRAFICO_COR_TEXTO = '#ffffff';
	const GRAFICO_CONTROLE_COR = '#3B867F';
	const GRAFICO_FILTRO_COR = '#22b4e5';

	public $orientacao_PDF = ExportadorPDF::PAISAGEM;

	public function __construct () {
		parent::__construct();

		$this->filtros = array();

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_analizadorSimulado1 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorSimulado2 = new AnalizadorSimuladoProvaFloripa();
		$this->_analizadorPesquisa = new RelCorePesquisa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_prepararPainel();

		$this->obterFiltrosPesquisa();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '3_comparativo_controle_filtros.relatorio.html.php';

		include 'modulos/relatorios_pesquisas/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);
		
		$this->obterFiltrosPesquisa();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		include '3_comparativo_controle_filtros.relatorio.html.php';

		include 'modulos/relatorios_pesquisas/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio_pesquisa',
									'pasta' => 'relatorios_pesquisas/',
									'classe' => 'MPainelRelatorioPesquisa',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_pesquisas/_painel_relatorio_pesquisa/_painel_relatorio_pesquisa.html.php' );

		$this->_ordenacao = new Ordenacao_RelatoriosPesquisas('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();
		
		$this->_seletorPesquisas = new MSeletorPesquisas($this->_ordenacao);
		$this->_seletorPesquisas->configurarCampoSeletorPesquisas();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();

		$this->_seletorPesquisas->ajustarPesquisaselecionado();
	}

	protected function obterFiltrosPesquisa(){		
		if(array_key_exists('atualizar',$_POST)){
			if(array_key_exists('fco',$_POST)){
				foreach ($_POST['fco'] as $fcok => $fcov) {
					$ci = explode('_',$fcok);
					$this->filtros['cio'][$ci[0]][] = $ci[1];
				}
			}
		}
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado1->fixarSimulado($this->_seletorSimulados->simulado);
		$this->_analizadorSimulado2->fixarSimulado($this->_seletorSimulados->simulado);

		if ( $this->_seletorPesquisas->pesquisa->obterID() == null )
			return false;

		$this->_analizadorPesquisa->fixarPesquisa($this->_seletorPesquisas->pesquisa);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		$idREDE = null;
		foreach ($this->_dados1 as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados1[$idREDE])) {
			$rede = $this->_dados1[$idREDE];
			unset($this->_dados1[$idREDE]);
			array_push($this->_dados1, $rede);
		}

		$idREDE = null;
		foreach ($this->_dados2 as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados2[$idREDE])) {
			$rede = $this->_dados2[$idREDE];
			unset($this->_dados2[$idREDE]);
			array_push($this->_dados2, $rede);
		}

		$this->gerarGrafico();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sim'.$this->_seletorSimulados->simulado->obterID().'_p'.$this->_seletorPesquisas->pesquisa->obterID();

		$this->_analizadorSimulado1->carregarInscritos(true);
		$this->_analizadorSimulado1->carregarRespostasDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado1->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado1->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado1->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado1->calcularRendimentoPorSerie();
		$this->_analizadorSimulado1->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado1->calcularRendimentoPorSeriePorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado1->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado1->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado1->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados1[] = $dado;
		}

		$this->_analizadorPesquisa->carregarParticipantes();

		$aIDs = array();
		if(count($this->filtros)>0){
			$this->_analizadorPesquisa->carregarItensPesquisa();
			$this->_analizadorPesquisa->carregarRespostasPorItem();

			foreach ($this->_analizadorPesquisa->obterParticipantes() as $op => $ov) {
				$ov->carregar();
				$dadosAluno = $ov->obterAluno();
				$dadosAluno->carregar();
				$aID = $dadosAluno->obterID();
				$dadosTurma = $dadosAluno->obterTurma();
				$dadosSerie = $dadosTurma->obterSerie();
				$ano = $dadosSerie->obterID();

				if($ano != $this->_analizadorSimulado1->simulado->obterSerieAvaliacao()->obterID()){ continue; }

				foreach ($this->_analizadorPesquisa->obterItensPesquisaII() as $iid => $iclass) {
					$iclass->carregarCompetencias();
					$piCompetencias = $iclass->obterCompetencias();

					foreach ($piCompetencias as $pck => $pcv) {
						$cID = $pcv->obterID();

						if(array_key_exists($cID,$this->filtros['cio'])){
							foreach ($this->_analizadorPesquisa->obterRespostasPorItemPorAluno($aID) as $pid => $participantes) {
								$rval = PesquisaResposta::carregarValorPorId($participantes[$iid]);

								//if($rval !== NULL){
									if(array_search($rval, $this->filtros['cio'][$cID]) !== false){
										if(array_search($aID, $aIDs) === false){
											$aIDs[] = $aID;
										}
									}
									elseif($rval === NULL){
										if(array_search($aID, $aIDs) === false){
											$aIDs[] = $aID;
										}
									}
								//}
							}
						}
					}
				}
			}
		}
		else{
			/*foreach ($this->_analizadorPesquisa->obterParticipantes() as $op => $ov) {
				$ov->carregar();
				$dadosAluno = $ov->obterAluno();
				$dadosAluno->carregar();
				$aID = $dadosAluno->obterID();

				$aIDs[] = $aID;
			}*/

			$this->_analizadorPesquisa->carregarItensPesquisa();
			$this->_analizadorPesquisa->carregarRespostasPorItem();

			foreach ($this->_analizadorPesquisa->obterParticipantes() as $op => $ov) {
				$ov->carregar();
				$dadosAluno = $ov->obterAluno();
				$dadosAluno->carregar();
				$aID = $dadosAluno->obterID();
				$dadosTurma = $dadosAluno->obterTurma();
				$dadosSerie = $dadosTurma->obterSerie();
				$ano = $dadosSerie->obterID();

				if($ano != $this->_analizadorSimulado1->simulado->obterSerieAvaliacao()->obterID()){ continue; }

				foreach ($this->_analizadorPesquisa->obterRespostasPorItemPorAluno($aID) as $pid => $participantes) {
					$rval = PesquisaResposta::carregarValorPorId($participantes[$iid]);

					if($rval !== NULL){
						if(array_search($aID, $aIDs) === false){
							$aIDs[] = $aID;
						}
					}
				}
			}
		}

		$this->_analizadorSimulado2->carregarInscritos(true);
		$this->_analizadorSimulado2->limitarInscritosPorAlunos($aIDs);

		$this->_analizadorSimulado2->carregarRespostasDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado2->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado2->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado2->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado2->calcularRendimentoPorSerie();
		$this->_analizadorSimulado2->calcularRendimentoPorInstituicaoPorDisciplina();
		$this->_analizadorSimulado2->calcularRendimentoPorSeriePorDisciplina();

		// SERIE
		foreach($this->_analizadorSimulado2->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado2->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( isset( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado2->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados2[] = $dado;
		}
	}

	protected function gerarGrafico() 
	{		
		require_once ('jpgraph/jpgraph.php');
		require_once ('jpgraph/jpgraph_bar.php');
		 
		$graph = new Graph(1000, 400, $this->_tituloCache.'.png', (int) CACHE_RELATORIOS / 60, false);
		$graph->SetMargin(34, 0, 10, 75);
		$graph->setFrame(false);
		$graph->SetScale('textint', 0, 100, 0, 0);
		$graph->yaxis->scale->ticks->Set(10);
		$graph->yaxis->SetLabelFormat('%d%%');
		$graph->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
		$graph->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
		$graph->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);

		$graph->legend->Pos(0.2,0.996,'left','bottom');
		$graph->legend->SetLayout(LEGEND_HOR);
		$graph->legend->SetFillColor('white');
		$graph->legend->SetFrameWeight(0);
		$graph->legend->SetShadow(false);
		$graph->legend->SetReverse(false);
		$graph->legend->SetFont(FF_ARIAL,FS_NORMAL, 9);

		$labelsX = array();
		$dados1 = array();
		foreach($this->_dados1 as $k => &$d) {
			$labelsX[] = 'Global';
			$dados1[] = $d['rendimento'];

			foreach ( $this->_analizadorSimulado1->nomesDisciplinas as $dID => $dNome ) {
				if (isset($d['rendimento_por_disciplina'][$dID])) {
					$labelsX[] = $dNome;
					$dados1[] = $d['rendimento_por_disciplina'][$dID];
				}
			}
		}

		$dados2 = array();
		foreach($this->_dados2 as $k => &$d) {
			$dados2[] = $d['rendimento'];

			foreach ( $this->_analizadorSimulado2->nomesDisciplinas as $dID => $dNome ) {
				if (isset($d['rendimento_por_disciplina'][$dID])) {
					$dados2[] = $d['rendimento_por_disciplina'][$dID];
				}
			}
		}

		$graph->xaxis->SetTickLabels($labelsX);

		$b1plot = new BarPlot($dados1);
		$b2plot = new BarPlot($dados2);

		$gbplot = new GroupBarPlot(array($b1plot,$b2plot));
		$graph->Add($gbplot);

		$b1plot->SetColor('gray');
		$b2plot->SetColor('gray');
		$gbplot->SetColor('gray');
		$gbplot->SetWeight(1);

		$b1plot->SetFillColor(self::GRAFICO_CONTROLE_COR);
		$b1plot->SetLegend('Controle: '.count($this->_analizadorSimulado1->inscritos).' alunos');
		$b1plot->value->SetColor(self::GRAFICO_COR_TEXTO);
		$b1plot->value->Show();
		$b1plot->value->SetFormat('%d%%');
		$b1plot->SetValuePos('center');
		$b1plot->value->SetFont(FF_ARIAL, FS_BOLD, 12);
		$b1plot->SetWidth(0.6);

		$b2plot->SetFillColor(self::GRAFICO_FILTRO_COR);
		$b2plot->SetLegend('Filtro: '.count($this->_analizadorSimulado2->inscritos).' alunos');
		$b2plot->value->SetColor(self::GRAFICO_COR_TEXTO);
		$b2plot->value->Show();
		$b2plot->value->SetFormat('%d%%');
		$b2plot->SetValuePos('center');
		$b2plot->value->SetFont(FF_ARIAL, FS_BOLD, 12);
		$b2plot->SetWidth(0.6);
		
		$graph->Stroke();
	}
}

?>