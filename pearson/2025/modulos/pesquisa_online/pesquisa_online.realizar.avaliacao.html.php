<script>
	//Remove a aba lateral de Contato
	jQuery(function(){
		jQuery('#contactable').remove();
	});

	document.onreadystatechange = function() { 
        if (document.readyState === "complete") { 
            jQuery('#modal_load').hide();
        } 
    }; 
</script>

<link rel="stylesheet" href="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/swiper/package/css/swiper.css"/>

<style>
/*html, body {
  position: relative;
  height: 100%;
}
body {
  background: #eee;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color:#000;
  margin: 0;
  padding: 0;
}*/

.wrap {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button {
  min-width: 300px;
  min-height: 60px;
  font-family: 'Nunito', sans-serif;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1.3px;
  font-weight: 700;
  color: #FFFFFF;
  background: #07003B;
background: linear-gradient(90deg, rgba(7,0,59) 0%, rgba(139,0,139) 100%);
  border: none;
  border-radius: 1000px;
  box-shadow: 12px 12px 24px rgba(115,111,145);
  transition: all 0.3s ease-in-out 0s;
  cursor: pointer;
  outline: none;
  position: relative;
  padding: 10px;
  }

.button::before {
content: '';
  border-radius: 1000px;
  min-width: calc(300px + 12px);
  min-height: calc(60px + 12px);
  border: 6px solid #8B008B;
 box-shadow: 0 0 60px rgba(143,188,143);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all .3s ease-in-out 0s;
}

.button:hover, .button:focus {
  color: #FFFFFF;
  transform: translateY(-6px);
}

.button:hover::before, .button:focus::before {
  opacity: 1;
}

.button::after {
  content: '';
  width: 30px; height: 30px;
  border-radius: 100%;
  border: 6px solid #8B008B;
  position: absolute;
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: ring 1.5s infinite;
}

.button:hover::after, .button:focus::after {
  animation: none;
  display: none;
}

@keyframes ring {
  0% {
    width: 30px;
    height: 30px;
    opacity: 1;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

.swiper-container {
  width: 95vw;
  /*width: 1450px;*/
  height: 100%;
}
.swiper-slide {
  min-height: 200px;

  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

</style>

<style type="text/css">
div.geral{
	display: block;
	height: auto;
	width: 100%;
}

div.title{
	background-color: #eee;
    border-radius: 10px 10px 0px 0px;
    font-size: 20px;
    margin-bottom: 0;
    padding: 10px;
    font-weight: bold;
}

.crono{
	border-bottom: 1px solid #DEDEDE;
	border-left: 1px solid #DEDEDE;
	border-right: 1px solid #DEDEDE;
    border-radius: 0px 0px 5px 5px;
    color: #555555;
    font-size: 13px;
    margin: 0px auto;
    padding: 5px 5px 2px;
    width: 125px;
    overflow: hidden;
    display:none;
}

img.crono_start, img.crono_stop{
	cursor: pointer;
	display:none;
}

#crono_time{
	display: block;
    float: right;
    margin-left: 5px;
}

.conteudo {
    border: 1px solid #DEDEDE;
    border-radius: 0px 0px 5px 5px;
    margin-top: 0;
    background: #fff;
}

.num_questao{
	border-bottom: 1px solid #DEDEDE;
    border-radius: 5px 0 5px 0;
    border-right: 1px solid #DEDEDE;
    color: #333333;
    font-size: 14px;
    padding: 5px 10px;
    float:left;
}

.crono_questao{
	border-bottom: 1px solid #DEDEDE;
    border-left: 1px solid #DEDEDE;
    border-radius: 0 5px 0 5px;
    float: right;
    font-size: 14px;
    padding: 5px;
    color:#333333;
    display:none;
}

.tbl_conteudo{
	width: 100%;
	border-collapse: collapse; 
	border-spacing: 0;
}

.tbl_conteudo td{
	border-spacing: 0;
	padding: 0;
	line-height: 20px;
}

.botoes{
	padding: 5px;
	border-top: 1px solid #DEDEDE;
}

.resposta_questao {
    padding: 15px;
	width: 71%;
	text-align: right;
}

.questao{
	width: 100%;
}

.questionario_general_start{
	border: 1px solid #DEDEDE;
	border-radius: 0px 0px 5px 5px;
}

.questionario_general_end{
	border: 1px solid #DEDEDE;
	border-radius: 0px 0px 5px 5px;
	background: #fff;
}

#modal_load{
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	background: rgba(150, 150, 150, 0.8);
	width: 100%;
	height: 100%;
	z-index: 999999999999999;
}

.pergunta {
	font-size:30px;
}

.multiplaescolha {
    width: 60px;
    height: 35px;
    font-size: 18px;
    text-align: center;
}

.wrapper {
	border-radius: 0.5rem;
	border: 1px solid #ccc;
	padding: 15px;
	color: #000;
	display: flex;
	font-weight: bold;
	background-color: #ddd;
	font-size: 14px;
	text-align: justify;
	width: 100%;
}

.resposta_questao_valor {
  cursor: pointer;
  appearance: none;
  width: 30px !important;
  height: 30px !important;
  border: 2px solid #8B008B;
  border-radius: 50%;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  outline: none;
}

.resposta_questao_valor:before  {
  content: '';
  width: 15px;
  height: 15px;
  border: 1px solid #8B008B;
  background: #8B008B;
  border-radius: 50%;
  opacity: 0%;
  transition: all 200ms ease-in-out;
  position: absolute;
}

.resposta_questao_valor:checked:before {
  opacity: 100%;
}

.wrapper .resposta_questao_valor:focus {
  box-shadow: 0 0 5px #D8BFD8;
}

.resposta_questao_valor2:before  {
  content: '';
  width: 15px;
  height: 15px;
  border: 1px solid #FFF;
  background: #FFF;
  border-radius: 50%;
  opacity: 0%;
  transition: all 200ms ease-in-out;
  position: absolute;
}

.resposta_questao_valor2:checked:before {
  opacity: 100%;
}

.resposta_questao_valor2:focus {
  box-shadow: 0 0 5px #FFF;
}

#modal_load2{
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	background: rgba(150, 150, 150, 0.3);
	width: 100%;
	height: 100%;
	z-index: 999999999999999;
}

#modal_select{
	background-color: #8B008B;
	padding: 15px;
	border-radius: 10px;
	height: 55%;
	border: 2px solid #EEE;
	width: 35%;
	display: block;
	margin: 10% auto;
	color:#FFF;
}

#modal_select p{
	font-size: 24px;
}

#modal_select ul li p{
	font-size: 22px;
}

</style>

<div id="modal_load">
	<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
loader6.gif" style="width: 15%;display: block;margin: 15% auto;" />
</div>

<div class="geral">
	<div class="title">
		<?php echo$pesquisa['nome']; ?>
	</div>

	<div id="div_btn_start" class="questionario_general_start" style="padding: 20px;">
		<p id="fsalert" style="background: none repeat scroll 0 0 #8B008B;border: 1px dashed #ccc;border-radius: 3px 3px 3px 3px;color: #FFFFFF;font-size: 20px;padding: 10px;text-align: center;">
			Bem-vindo! &#128515;
			<br>
			Se tiver d&#250;vidas, chame o formador!
		</p>
		<table style="width: 100%; background-color: #FFFFFF;">
			<tr style="display: inline-block; align-itens: center; width: 70%;padding-left: 20%">
				<td style=" float: left;">
					<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/5836.jpg" width="500px" height="350px;"/>
				</td>
				<td style="padding-top: 30%">
					<h1 style="text-align: center; font-size: 22px;">
						<?php echo$pesquisa['tinicio']; ?>
					</h1>
				<div id="btn_start" style='cursor:pointer; padding-top: 80px;' class="wrap">
						
						<?php
							$txt_start = 'Iniciar';
							if(isset($pesquisa['todasRespostas'])){
								if(count($pesquisa['todasRespostas'])>0){
									$txt_start = 'Continuar';
								}
							}
						?>
						<button class="button"><?php echo$txt_start; ?> Questionário</button>
						
					</div>
				<td>
				<br>
			<tr>
		</table>
	</div>
	<div id='cnt_questoes' class="conteudo" style="display:none;">
		<div>
			<table class='tbl_conteudo'>
				<tr>
					<td style="width: 33%;">
						<div class='num_questao'>
							N&ordm; <span id='num_questao'>1</span>
						</div>
					</td>
					<td style="width: 33%;">
						<div class="crono" style="">
							<div style="float:left;">
							<img style="float:left;" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'clock.png';?>"/>
								<span id="crono_time">00:00:00</span>
								<input id="tempo-total" name="tempo-total" type="hidden" value="<?php echo$pesquisa['tempoTotalHide']; ?>"/>
							</div>
							<div style="float:right;">
							<img class="crono_start" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_play.png';?>"/>
							<img class="crono_stop" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_pause.png';?>"/>
							</div>
						</div>
					</td>
					<td style="width: 33%;">
						<div class="crono_questao">
							<?php
							
								$num = 1;
								foreach($pesquisa['questoes'] as $sqk => $sqv){
									$idq = $sqv->obterID();
									
									if(!isset($pesquisa['respostas'][$idq])){
										$qTime = '00:00:00';
									}
									else{
										$qTime = $pesquisa['respostas'][$idq]['tempo'];
									}
							?>
								<span style="display:none;" id="crono_questao<?php echo$num; ?>"><?php echo$qTime; ?></span>
							<?php
									$num++;
								}
							?>
						</div>
					</td>
				</tr>
			</table>
		</div>

		<div style="padding: 5px;">
			<!-- <table class='tbl_conteudo_questao'>
				<tr>
					<td> -->
					<div class="swiper-container">
					    <div class="swiper-wrapper">

							<?php

								$num = 1;

								//echo'-><pre>';print_r($pesquisa['todasRespostas']);echo'</pre>';

								foreach($pesquisa['questoes'] as $sqk => $sqv){  
									
									$title = $sqv->obterEnunciado();
									//$title = strip_tags($title); removido para implementar o tinymce
									$sqv->carregar();
				
									$questoesTextos = $sqv->obterOpcoes();
									
									$ans = $sqv->obterTipo();
									$idq = $sqv->obterID();

									$resps = "";

									$qOptionsRespostas = "";
									$qTextosRespostas = "";
									foreach($questoesTextos as $qtk => $qtv){
										$valorqtr = $qtv->obterNumero();

										$checked = '';
										if(array_key_exists($idq, $pesquisa['todasRespostas'])){
											if($valorqtr == $pesquisa['todasRespostas'][$idq]->obterOpcao()){
												$checked = ' checked="checked" ';
											}
										}

										if($sqv->obterTipo() == PesquisaItem::MULTIPLAESCOLHA){
											$valorqtr = MultiplaEscolha::letra($valorqtr);
										}
										
										$textoqtr = $qtv->obterTexto();
										
										$qOptionsRespostas .= "<div class='wrapper'>";
										$qOptionsRespostas .= "<input class='resposta_questao_valor' spellcheck='false' type='radio' name='rqID-".$idq."' id='resposta-".$num."' value=".$valorqtr." ".$checked." />";
										$qOptionsRespostas .= "<label style='padding-top: 8px; padding-left: 7px;' for='resposta-".$num."'>".$valorqtr.' - '.$textoqtr."</label><br>";
										$qOptionsRespostas .= "</div>";
										$qOptionsRespostas .= "<div style='padding-top: 8px;'></div>";
										
										$qTextosRespostas .= $valorqtr." - ".$textoqtr."</br>";
									}							
							
									switch ($ans) {
										case 'ABERTA':
											$resps = "<textarea spellcheck='false' class='sr_ctexto aberta' maxlenght='1020' name='rqID-".$idq."' id='resposta-".$num."'>".$simulado['respostas'][$idq]['valor']."</textarea>";
											break;
										case 'DISCURSIVA':
											//$resps = "<textarea spellcheck='false' class='sr_ctexto discursiva' maxlenght='1020' name='rqID-".$idq."' id='resposta-".$num."'>".$simulado['respostas'][$idq]['valor']."</textarea>";
											$resps = "
												<textarea spellcheck='false' class='sr_ctexto discursiva' maxlenght='1020' name='rqID-".$idq."' id='resposta-".$num."' style='display:none;'>".$simulado['respostas'][$idq]['valor']."</textarea>
												<p>Por favor, consulte o professor para o preenchimento desta quest&#227;o.</p>
											";
											break;
										case 'SOMATORIO':
											$resps = "<input spellcheck='false' class='sr_cvalor somatorio' name='rqID-".$idq."' id='resposta-".$num."' size='1' maxlength='3' value='".$simulado['respostas'][$idq]['valor']."' />";
											break;
										case 'MULTIPLAESCOLHA':
											//$resps = "<input spellcheck='false' class='sr_cvalor multiplaescolha' name='rqID-".$idq."' id='resposta-".$num."'>";
											//$resps .= "<input value='0'> </input>";
											$resps .= '<table style="width: 100%;">';
											$resps .= '<tr>';
											$resps .= '<td style="float: left;width: 45%">';
											$resps .= '</td>';
											$resps .= '<td style="float: right;width: 65%">';
											$resps .= $qOptionsRespostas;
											$resps .= '</td>';
											$resps .= '</tr>';
											$resps .= '</table>';
											break;
									}
									?>

									<div class="swiper-slide">
										<div class='questao' id="questao<?php echo$num; ?>">

											<input id="tempo-<?php echo$num; ?>" name="tempo[<?php echo$num; ?>]" type="hidden" value="<?php echo$pesquisa['respostas'][$idq]['tempo']; ?>"/>
											
											<div class="sr_texto_questao">
												<p style="font-size: 24px"><?php echo $title; ?></p>
											</div>
											<div class="resposta_questao">
												<p></p>
												<div ><?php echo$resps; ?></div>
											</div>
										</div>
									</div>

									<?php
											$num++;
										}
									?>
						</div>
					    <!-- Add Pagination -->
					    	<div class="swiper-pagination"></div>
					</div>
		</div>

		<div class="botoes"> <!-- style="display:none;"> -->
			<table border="0" style=" width: 100%;">
				<tr>
					<td style="text-align: right; width: 49%;padding-right: 15px;">
						<button class="" id="btn_prev" style="width: 125px; height: 55px;" disabled="disabled">
							<label style="font-size: 16px; align-itens: center;">Anterior</label>
							<img style="display: inline-block; align:itens: center" src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_start.png';?>">
						</button>
					</td>
					<td style="padding-left: 15px; width: 36%">
						<button class="sr_pesquisa_retornar" id="btn_next" style="width: 125px; height: 55px;">
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'player_end.png';?>">
							<span style="font-size: 16px;">Pr&oacute;xima</span>
						</button>
					</td>
					<td style="width: 15%">
						<button class="sr_pesquisa_retornar" id="btn_finalizar" style='width: 125px; height: 55px;'>
							<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'sucesso-big.png';?>">
							<label style="font-size: 16px;">Finalizar</label>
						</button>
					</td>
				</tr>
			</table>
		</div>
	</div>

	<div id="div_btn_end" class="questionario_general_end" style="padding: 20px; display:none;">
		<div id="alert_box" class="alert_box alert_box_erro" style="display:none;"></div>
		<div class="sr_right" style="width: 100%; float: left; margin-right: 2%;">
			<!-- <p style="font-size: 18px;margin: 0;">Tempo de Prova: 
				<span id='tempo_realizacao' style="font-weight: bold;"><?php echo$pesquisa['tfim']; ?></span>
			</p>
			<br> -->
			<div style="background-color: #8B008B; margin: 0; font-weight: bold; color: #FFFFFF; height: 30px; border-radius: 4px 4px 4px 4px;">
				<p style="padding-left: 10px; padding-top: 5px; margin: 0; font-size: 18px; ">Quest&#245;es:</p>
			</div>
			<br>
			<?php 
			$num = 1;

			$agrupamento_pordois = array_chunk($pesquisa['questoes'], 20);
			echo '<table>';
			echo '<tr>';
			foreach ($agrupamento_pordois as $chave_dogrupo => $conteudo_dogrupo) { 

				echo '<td style="vertical-align: top;">';
				echo '<ul style="padding: 0 10px 0 0;">';
				
				foreach ($conteudo_dogrupo as $chave_doconteudo => $elemento_doconteudo) {
					$_CADA_id_doelemento = $elemento_doconteudo->ObterID();
					echo '<li class="pendente" style="list-style-type: none; padding-bottom: 6px;">';
					echo "<button id='lquestao$num' class='troca_questao' style='cursor:pointer;font-size: 16px;color: #FFFFFF; width: 160px; height: 35px; border-radius: 5px; border-color: #8B008B ; background-color: #8B008B;'>$num | Pendente</button>";
					echo '</li>';
					$num++;
				}
				
				echo '</ul>';
				echo '</td>';
			?>
			<? }
				echo '</tr>';
				echo '</table>';
			?>
			
		</div>
		<div class="sr_right" style="width: 100%;">
			<div id='msg_fim' style="margin: 0 auto;margin-bottom:10%;margin-top:8%;text-align: center; display:none;">
				<h1 style="font-size: 20px;">Questionário entregue!</h1>
				<p></p>
				<a class="sr_pesquisa" style="cursor:pointer; padding-top: 20px; font-size: 20px; width: 20%; height:46px;" href="<?= Gerenciador_URL::gerarLinkPelaAcao(Core::diretiva('CORE:ACAO:logout')); ?>">Sair</a>
			</div>
			<div style="margin: 0 auto;margin-bottom:10%;margin-top:8%;text-align: center;">
				<a id="btn_end" style="cursor:pointer; width: 55%; height:70px; font-size: 20px; padding-top: 35px;" class="sr_pesquisa">
					<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'stock_task20.png';?>">
					<span>Entregar Questionário</span>
				</a>
			</div> 
			<div style="margin: 0 auto;margin-bottom:10%;margin-top:8%;text-align: center;">
				<a class="sr_pesquisa" id="voltar_prova" style='cursor:pointer; font-size: 20px; width: 55%; height:70px; padding-top: 35px; display: inline-block;'>
					<img src="<?=Core::diretiva('ESTILO:DIRETORIO:media') . 'voltar_start.png';?>">
					<span>Voltar e continuar</span>
				</a>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/swiper/package/js/swiper.min.js"></script>
<script type="text/javascript" src="https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/jquery-stopwatch/jquery.stopwatch.js"></script>

<script type="text/javascript">
	var mySwiper;
	var entregou = false;

	function cronoStart(){
		jQuery('.sr_cvalor').prop('disabled', false);
		jQuery('.sr_ctexto').prop('disabled', false);

		btnFinalizar();

		jQuery('#btn_prev').show();
		jQuery('#pause-mgs').hide();
		jQuery('.crono_stop').show();
		jQuery('.crono_start').hide();
		jQuery('#crono_time').stopwatch().stopwatch('start');

		var currQ = mySwiper.activeIndex;
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('start');
	}

	function cronoStop(){
		jQuery('.sr_cvalor').prop('disabled', true);
		jQuery('.sr_ctexto').prop('disabled', true);

		jQuery('#btn_prev').hide();
		//jQuery('#btn_next').hide();
		//jQuery('#btn_finalizar').hide();
		jQuery('#pause-mgs').show();
		jQuery('.crono_stop').hide();
		jQuery('.crono_start').show();
		jQuery('#crono_time').stopwatch().stopwatch('stop');

		var currQ = mySwiper.activeIndex;
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');

		var crono_time = jQuery('#crono_time').html();
		jQuery('#tempo-total').val(crono_time);
	}

	function btnFinalizar(){
		mySwiper.updateAutoHeight(1);
		var currQ = mySwiper.activeIndex;
		var countQ = jQuery('.swiper-slide').length;

		if(parseInt(currQ) == parseInt(countQ-1)){
			//jQuery('#btn_next').hide();
			jQuery('#btn_finalizar').show();
		}
		else{
			jQuery('#btn_next').show();
			//jQuery('#btn_finalizar').hide();
		}
	}

	function nextQuest(move = false, index = false){
		var countQ = jQuery('.swiper-slide').length;
		currQ = mySwiper.activeIndex;

			jQuery('#btn_next').addClass('sr_pesquisa_retornar');
			jQuery('#btn_next').prop('disabled',false);
			jQuery('#btn_prev').addClass('sr_pesquisa_retornar');
			jQuery('#btn_prev').prop('disabled',false);

		if((currQ+1) >= countQ){
			//jQuery('#btn_next').hide();
			jQuery('#btn_next').removeClass('sr_pesquisa_retornar');
			jQuery('#btn_next').prop('disabled',true);
			return;
		}
		else{
			jQuery('#btn_next').addClass('sr_pesquisa_retornar');
			jQuery('#btn_next').prop('disabled',false);
			jQuery('#btn_prev').addClass('sr_pesquisa_retornar');
			jQuery('#btn_prev').prop('disabled',false);
		}

		if(currQ === index){
			return;
		}

    	jQuery('#crono_questao'+(currQ+1)).hide();
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');
		timeQ = jQuery('#crono_questao'+(currQ+1)).html();
		jQuery('#tempo-'+(currQ+1)).val(timeQ);

		jQuery('#crono_questao'+(currQ+2)).stopwatch().stopwatch('start');
    	jQuery('#crono_questao'+(currQ+2)).show();

		jQuery('#num_questao').html(currQ+2);
		if(move){
			mySwiper.slideNext('150');
		}

		btnFinalizar();
	}

	function prevQuest(move = false, index = false){
		currQ = mySwiper.activeIndex;

			jQuery('#btn_next').addClass('sr_pesquisa_retornar');
			jQuery('#btn_next').prop('disabled',false);
			jQuery('#btn_prev').addClass('sr_pesquisa_retornar');
			jQuery('#btn_prev').prop('disabled',false);

		if((currQ+1) <= 1){
			jQuery('#btn_prev').removeClass('sr_pesquisa_retornar');
			jQuery('#btn_prev').prop('disabled',true);
			return;
		}
		else{
			jQuery('#btn_next').addClass('sr_pesquisa_retornar');
			jQuery('#btn_next').prop('disabled',false);
			jQuery('#btn_prev').addClass('sr_pesquisa_retornar');
			jQuery('#btn_prev').prop('disabled',false);
		}

		if(currQ === index){
			return;
		}   

    	jQuery('#crono_questao'+(currQ+1)).hide();
		jQuery('#crono_questao'+(currQ+1)).stopwatch().stopwatch('stop');
		timeQ = jQuery('#crono_questao'+(currQ+1)).html();
		jQuery('#tempo-'+(currQ+1)).val(timeQ);

		jQuery('#crono_questao'+(currQ)).stopwatch().stopwatch('start');
    	jQuery('#crono_questao'+(currQ)).show();

		jQuery('#num_questao').html(currQ);
		if(move){
			mySwiper.slidePrev('150');
		}

		btnFinalizar();
	}

	jQuery(document).ready(function() { 

    	jQuery('#btn_start').click(function() {
    		jQuery('#div_btn_start').hide();
    		jQuery('#cnt_questoes').show();

    		mySwiper = new Swiper('.swiper-container', {
    		  autoHeight: true,
    		  allowTouchMove: false,
		      pagination: {
		        el: '.swiper-pagination',
		        type: 'progressbar',
		      },
		      on: {
			    slideNextTransitionStart: function () {
	    		  currQ = mySwiper.activeIndex; 
			      nextQuest(false, currQ);
			    },
			    slidePrevTransitionStart: function () {
	    		  currQ = mySwiper.activeIndex; 
			      prevQuest(false, currQ);
			    }
			  }
		    });

    		jQuery('.crono_start').hide();
    		jQuery('.crono_stop').show();
    		jQuery('#crono_time').stopwatch().stopwatch('start');
    		jQuery('#crono_questao1').show();
    		jQuery('#crono_questao1').stopwatch().stopwatch('start');
    	});


    	jQuery('.crono_start').click(function () {
    		cronoStart();
		});

    	jQuery('.crono_stop').click(function () {
    		cronoStop();
		});

    	jQuery('#btn_next').click(function() {
    		nextQuest(true,false);
    	});

    	jQuery('#btn_prev').click(function() {
    		prevQuest(true,false);
    	});   	

    	jQuery('.resposta_questao_valor > textarea').keyup(function(){
			num = jQuery(this).attr('id')+'a<?php echo$pesquisa['aluno']; ?>';
			val = jQuery(this).val();

			if(val != ''){
				window.localStorage.setItem(num, val);
			}
			else{
				window.localStorage.removeItem(num);
			}
    	}).each(function(){
    		num = jQuery(this).attr('id')+'a<?php echo$pesquisa['aluno']; ?>';
    		cache = window.localStorage.getItem(num);
    		if(cache != ''){
				jQuery(this).val(cache);
    		}
    	});
    	jQuery('.resposta_questao_valor > textarea').keyup(function(){
			num = jQuery(this).attr('id')+'a<?php echo$pesquisa['aluno']; ?>';
			val = jQuery(this).val();

			if(val != ''){
				window.localStorage.setItem(num, val);
			}
			else{
				window.localStorage.removeItem(num);
			}		
    	}).each(function(){
    		num = jQuery(this).attr('id')+'a<?php echo$pesquisa['aluno']; ?>';
    		cache = window.localStorage.getItem(num);
    		if(cache != ''){
				jQuery(this).val(cache);
    		}
    	});

    	jQuery('.resposta_questao_valor').change(function(){
			num = jQuery(this).attr('id')+'a<?php echo$pesquisa['aluno']; ?>';
			val = jQuery(this).val();

			jQuery('.wrapper').css("border","");
			
			if (jQuery(this).is(':checked')) { 
				jQuery(this).parent('.wrapper').css('border','3px solid #8B008B');
			}

			if(val != 0){

				window.localStorage.setItem(num, val);
			}
			else{
				window.localStorage.removeItem(num);
			}
    	}).each(function(){
    		num = jQuery(this).attr('id')+'a<?php echo$pesquisa['aluno']; ?>';
    		cache = window.localStorage.getItem(num);			
			
    		if(cache != ''){
				jQuery(this).find('input[value="'+cache+'"]').prop("checked", true).attr('checked','checked');

    		}
    	});
		
    	jQuery('#btn_finalizar').click(function() {
			jQuery('.resposta_questao_valor').each(function(){
				num = jQuery(this).attr('id').replace("resposta-", "");
				val = jQuery(this).is(':checked');

				if(val == true){
					jQuery('#lquestao'+num).parent().removeClass('pendente');
					jQuery('#lquestao'+num).parent().addClass('realizada');
					jQuery('#lquestao'+num).html(num+' | Respondida');
				}
					
			});
	
    		//cronoStop();
			jQuery('#cnt_questoes').hide();
			jQuery('#div_btn_end').show();
    	});

    	 jQuery('.troca_questao').click(function() {
  
    		idV = jQuery(this).attr('id');
    		id = idV.replace('lquestao','');
    		mySwiper.slideTo(id-1, '150');
    		jQuery('#num_questao').html(id);
			jQuery('#cnt_questoes').show();
			jQuery('#div_btn_end').hide();
			btnFinalizar();
    	}); 

    	jQuery('#voltar_prova').click(function() {
    		cronoStart();
    		mySwiper.slideTo(0, '150');
    		jQuery('#num_questao').html(1);
			jQuery('#cnt_questoes').show();
			jQuery('#div_btn_end').hide();
			jQuery('#btn_next').addClass('sr_pesquisa_retornar');
			jQuery('#btn_next').prop('disabled',false);
			btnFinalizar();
    	});

    	window.onbeforeunload = function(){ 
			if(entregou == false){
				jQuery('.alert_box').html('Voc&#234; n&#227;o entregou a avalia&#231;&#227;o! Deseja sair dessa p&#225;gina?').fadeIn('fast');
				return "Você não entregou a avaliação! Deseja sair dessa página?";
			}
		}

    	jQuery('#btn_end').click(function(){
    		jQuery('#modal_load').show();

    		var tempoTotal = jQuery('#tempo-total').val();
			/*if(!tempoTotal){
				jQuery('.alert_box').html('Não foi encontrado o tempo total da avalia&ccedil;&atilde;o on-line.').fadeIn('fast');
				window.scroll(0,0);
				return false;
			}*/

			var rValores = Array();
			jQuery('.questao').each(function(){
				var qId = jQuery(this).attr('id');
				var curr = qId.replace("questao", "");

				var rValor = '';
				var rTagName = jQuery(this).find('#resposta-'+curr).is(':checked');

				var rqID = jQuery('#resposta-'+curr).attr('name');
				var rqID = rqID.replace("rqID-", "");

				if(rTagName === true){
					var rValor = jQuery(this).find('#resposta-'+curr+':checked').val();
				}

				vTmp = new Object();
				vTmp.qid = rqID;
				vTmp.valor = rValor;
				rValores.push(vTmp);
			});

			var dados = JSON.stringify(rValores);
	
			jQuery.ajax({
		        type: "POST",
		        async: true,
		        cache: false,
		        url: "./?m=pesquisa_online&a=responder",
		        data: {d: dados},
		        dataType:'json',
		        //headers: { "Content-type" : "application/json" },
		        error: function(err){
		        	/*if(retorno.indexOf('Sem perm') >= 0){
						alert('Sua sessão de usuário expirou. Por favor faça login novamente.');
						return false;
					}*/

		            window.scroll(0,0);
		            jQuery('.alert_box').html('Houve um erro ao finalizar.').fadeIn('fast');
		            jQuery('#modal_load').hide();
		        },
		        success: function(response){	
					var msg = response.msg;
					
					if(response.ret == '1'){
						localStorage.clear();
						entregou = true;
						jQuery('.alert_box').removeClass('alert_box_erro');
						jQuery('.alert_box').addClass('alert_box_sucesso');

						/*jQuery('#btn_end').hide();
						jQuery('#voltar_prova').hide();
						jQuery('#msg_fim').show();*/
					}

					window.scroll(0,0);
					jQuery('.alert_box').html(msg).fadeIn('fast');
					jQuery('#modal_load').hide();
		        },
		        complete: function(jqXHR,textStatus){
					if(textStatus == 'error' && jqXHR.readyState == 0 && jqXHR.status == 0){
		    			window.scroll(0,0);
						jQuery('.alert_box').html('Os dados n&#227;o foram enviados! Por favor, verifique sua conex&#227;o com a internet e tente novamente.').fadeIn('fast');
		    			jQuery('#modal_load').hide();
					}
					
		        	return;
		        }
		    });
    	});
    });
</script>