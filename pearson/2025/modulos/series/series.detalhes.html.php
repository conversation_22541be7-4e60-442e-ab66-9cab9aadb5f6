<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio"><strong><?= $serie->obterNome(); ?></strong></div>

<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('series', 'detalhar', array('id' => $serie->obterID())); ?>">Informações da série </a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('series', 'detalhar', array('id' => $serie->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_MAIS_INFO)); ?>">Mais informa&ccedil;&otilde;es</a></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<?
if ($this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO)) {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

	<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		  <tr>
			<td><strong>Hist&oacute;rico de registro </strong></td>
			<td align="right"><input type="button" value="Limpar hist&oacute;rico" class="botao novo"></td>
		  </tr>
	  </table>
		
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
		  <td width="10%" align="center" class="lp_ColHeader">Data</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Grupo</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Tipo</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Descri&ccedil;&atilde;o</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Refer&ecirc;ncia</td>
		</tr>
		
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
      <td align="center" nowrap="nowrap" >---</td>
		<td>SÉRIES</td>
		<td>cria&ccedil;&atilde;o</td>
		<td>Série cadastrada</td>
		<td><a href="<?= Gerenciador_URL::gerarLink('series', 'detalhar', array('id' => $serie->obterID())); ?>"><?= $serie->obterNome(); ?></a></td>
    </tr>
	</table>

	</td>
  </tr>
</table>
<?
} else {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

	<div class="vd_BlocoBotoes">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
	  <tr>
		<td><input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('series', 'editar', array('id' => $serie->obterID())); ?>'" value="Editar" class="botao editar"></td>
		<td align="right"><input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('series', 'remover', array('id' => $serie->obterID())); ?>'; }" value="Remover" class="botao remover"></td>
	  </tr>
	</table>
	</div>

	<table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="2" class="titulo">Informações da série </th>
        </tr>
        <tr>
          <th>Nome</th>
          <td><?= $serie->obterNome(); ?></td>
        </tr>
      </table>
	
	</td>
  </tr>
</table>
<?
}
?>