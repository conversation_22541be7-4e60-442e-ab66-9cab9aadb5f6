<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');

class LSeries extends Listagem
{
	protected $_procuraSQL = null;
	
	public function prepararListagem ()
	{
		$this->_prepararBusca();
		
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormulario();


		$this->_iniciarRenderizacao(Modulo::HTML);
			include('series.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_series')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_series')->termoProcurado != null && Core::modulo('procurar_series')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_series')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_series')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}
		
			if ( Core::modulo('procurar_series')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(s_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_series')->letraSelecionada);
			}
			
			if ( is_array($this->_procuraSQL) ) {
				$this->_procuraSQL = ' AND '. implode(' AND ', $this->_procuraSQL);
			}
		}
	}
	
	protected function _preObterDados ()
	{
		$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM series WHERE s_instituicao = %s %s',
			Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ),
			$this->_procuraSQL ) );
			
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}
	
	protected function _obterDados ()
	{
		$rs = Core::registro('db')->query( sprintf('SELECT * FROM series WHERE s_instituicao = %s %s ORDER BY %s %s LIMIT %s',
			Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ),
			$this->_procuraSQL,
			$this->_ordenacao->ordenarPor,
			$this->_ordenacao->tipoOrdem,
			$this->_paginacao->paginador->obterLimitesSQL() ) );
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$serie = new Serie($row['s_id']);
				
				$serie->fixarNome($row['s_nome']);
				$serie->fixarInstituicao( new Instituicao($row['s_instituicao']) );
				
				$this->_dados[] = $serie;
			}
		}
		$rs->free();
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_series'));

		Core::modulo('procurar_series')->prepararProcura('form_procurar_series', 'caixa_procurar_series');
		Core::modulo('procurar_series')->configurarTermo();
		Core::modulo('procurar_series')->configurarOndeProcurar( array('s_nome' => 'Nome da série') );
		Core::modulo('procurar_series')->configurarAlfabeto();
		
		Core::modulo('procurar_series')->carregarProcura();
		
		Core::modulo('procurar_series')->prepararAtalho();
	}
	
	protected function _prepararOrdenacao()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_series'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_series');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('s_nome' => 'Nome'), 's_nome' );
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();	
		
		Core::modulo('ord_series')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_series')->carregarOrdenacao();
		Core::modulo('ord_series')->prepararAtalho(null, 'caixa_ord_series');
		
		if ( Core::modulo('procurar_series')->procuraSolicitada() ) {
			Core::modulo('ord_series')->anexarHTML( Core::modulo('procurar_series')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_series'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormulario()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_series', 'acao' => Gerenciador_URL::gerarLink('series', 'listar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$d) {
			$ids[] = $d->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Remover', 'Tem certeza que deseja REMOVER todos os itens selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();

				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {
					if (is_array($ids)) {
						Core::modulo('solicitado')->aRemoverSeries($ids);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('series', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				}
			}
			catch (Formulario_Exception $e) { }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('séries', $totais);
	}
		
}

?>