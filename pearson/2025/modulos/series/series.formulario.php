<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('series.formulario.exception.php');

class FSeries extends Formulario
{
	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$this->_carregar();

		$this->adicionarCampo( new Campo(array( 'nome' => 'nome',
												'etiqueta' => 'Nome',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 50),
												'html_tamanho_maximo' => 50,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterNome(),
												'html_ordem' => 1
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );

		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Enviar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 2
							  )) );

		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outra série');
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próxima série');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}

	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e)
		{
			throw new FSeries_Exception($e->getMessage());
		}
	}

	public function executar ()
	{
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Série adicionada com sucesso!', 'Adicionando série...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar a série!', 'Adicionando série...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Série editada com sucesso!', 'Editando série...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar a série!', 'Editando série...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}

		return $this->_dados;
	}

	protected function _carregar ()
	{
		$id = null;

		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}

		$this->_dados = new Serie($id);

		if ( $id != null ) {
			if ( $this->_dados->carregar() && $this->_dados->validarInstituicao() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('series', 'listar'), 'Série inválida!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
			//Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('series', 'listar'), 'Série inválida!');
		}
	}

	protected function _adicionar ()
	{
		$this->_dados = Serie::obterNovaSerie($this->_campos['nome']->obter('valor'), Core::registro('instituicao'));

		return ($this->_dados->obterID() != null);
	}

	protected function _editar ()
	{
		$this->_dados->fixarNome($this->_campos['nome']->obter('valor'));

		return $this->_dados->salvar();
	}

}

?>