<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Serie', null, true);

include_once('series.listagem.php');
include_once('series.formulario.php');

class MSeries extends Modulo
{
	protected $_listagem;
	protected $_formulario;

	public function __construct () {
		parent::__construct();

		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		// habilita botão de adicionar nos atalhos do navegador
		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('series', 'nova'), 'Adicionar série...');
		}
	}

	public function aListarSeries () {
		$this->_carregarListagem();
	}

	public function aDetalharSerie ()
	{
		$serie = new Serie( (int) @$_GET['id'] );

		if ( $serie->carregar() && $serie->validarInstituicao() ) {
			$this->_iniciarRenderizacao(Modulo::HTML);
				include('series.detalhes.html.php');
			$this->_finalizarRenderizacao();
		} else {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('series', 'listar'), 'Série inválida!');
		}
	}

	public function aNovaSerie () {
		$this->_formulario = new FSeries( array('nome' => 'form_series', 'acao' => Gerenciador_URL::gerarLink('series', 'nova')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->carregarFormulario();

		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$id = $this->_formulario->executar()->obterID();

				switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
					case 'adicionar':
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('series', 'nova', array('pos_envio' => 'adicionar')) ); break;
					case 'editar_proximo':
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('series', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
					default:
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('series', 'detalhar', array('id' => $id)) );
				}

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e) {}
		}

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('series.formulario.html.php');
		$this->_finalizarRenderizacao();
	}

	public function aProcessarFormulario () {
		return $this->aNovaSerie();
	}

	public function aRemoverSeries ($ids = null)
	{
		if ( $ids != null ) {
			foreach ($ids as $id) {
				$obj = new Serie($id);

				if ( !$obj->carregar() || !$obj->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}

				if ( !$obj->podeRemover() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'não pode ter turmas para poder ser removida;');
				} else {
					if (!$obj->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'série não foi removida;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($obj->obterNome(), 'série removida com sucesso;');
					}
				}
			}

			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo séries...');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('series', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if (isset($_GET['id']) && (int) $_GET['id'] > 0) {
				return $this->aRemoverSeries( array((int) $_GET['id']) );
			}

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}

	private function _carregarListagem () {
		$this->_listagem = new LSeries();

		$this->_listagem->prepararListagem();

		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}

}

?>