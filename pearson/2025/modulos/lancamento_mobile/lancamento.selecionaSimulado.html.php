<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<style>
        :root {
            --primary-color: #07003B;
            --secondary-color: #8B008B;
            --background-color: #f0f2f5;
            --text-color: #2d3748;
            --border-color: #e2e8f0;
            --gradient-start: #07003B;
            --gradient-end: #8B008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

		#menu_pai {
        display: none;

		}
		#id_instituicao_selecionada {
			display: none;
		}

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #ffffff 100%);
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-title {
            font-size: 1.5rem;
            color: var(--primary-color);
            text-align: center;
            margin: 30px 0;
            padding: 0 10px;
            font-weight: 600;
            text-shadow: 1px 1px 2px var(--shadow-color);
        }

        .evaluation-group {
            background: #ffffff;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px var(--shadow-color);
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease;
        }

        .evaluation-group:hover {
            transform: translateY(-5px);
        }

        .evaluation-header {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 1.1rem;
            text-align: center;
            font-weight: 500;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(139,0,139,0.2);
        }

        .evaluation-list {
            display: grid;
            gap: 20px;
            grid-template-columns: 1fr;
        }

        .evaluation-button {
            width: 100%;
            padding: 18px;
            border: 1px solid var(--border-color);
            border-radius: 15px;
            background: white;
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .evaluation-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(139,0,139,0.15);
            border-color: var(--secondary-color);
            background: linear-gradient(to right, #ffffff, #f8f9fa);
        }

        .evaluation-icon {
            width: 48px;
            height: 48px;
            object-fit: contain;
            filter: drop-shadow(2px 2px 2px var(--shadow-color));
        }

        .evaluation-info {
            flex: 1;
            text-align: left;
        }

        .evaluation-name {
            font-size: 1.1rem;
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 6px;
        }

        .evaluation-date {
            font-size: 0.9rem;
            color: var(--text-color);
            opacity: 0.8;
        }

        .back-button {
            display: block;
            width: 100%;
            max-width: 250px;
            margin: 30px auto;
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
        }

        .no-data {
            text-align: center;
            padding: 30px;
            color: var(--text-color);
            font-size: 1.1rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px var(--shadow-color);
            margin: 20px 0;
        }

        @media screen and (min-width: 768px) {
            .container {
                padding: 30px;
            }

            .page-title {
                font-size: 1.8rem;
                margin: 40px 0;
            }

            .evaluation-list {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media screen and (min-width: 1024px) {
            .evaluation-list {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* Animações */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .evaluation-group {
            animation: fadeIn 0.5s ease-out forwards;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Escolha a avaliação para lançar as respostas</h1>
        
        <?php if (count($this->_dados)): ?>
            <?php foreach ($this->_dados as $kd => $vd): ?>
                <?php
                $disc = $vd['disc'];
                $img = $disc == 'MAT' ? 'mt_mini.png' : 'pt_mini.png';
                $disciplina = $disc == 'MAT' ? 'Matemática' : 'Português';
                ?>

                <div class="evaluation-group">
                    <div class="evaluation-header">
                        <?php echo $kd;  ?>
                    </div>
                    <div class="evaluation-list">
                        <?php foreach($vd as $s_nome => $s): ?>
                            <?php if(is_array($s)): ?>
                                <form id="form_seletor_lancamento<?php echo $s_nome; ?>" 
                                      method="post" 
                                      action="./?m=lancamento_mobile&a=lancamento_mobile">
                                    <input type="hidden" name="id_instituicao_selecionada_lancamento" value="<?php echo $_intituicao; ?>" />
                                    <input type="hidden" name="id_simulado_selecionada_lancamento" value="<?php echo $s_nome; ?>" />
                                    <button type="submit" class="evaluation-button">
                                        <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
educacao-online.png" class="evaluation-icon" alt="Ícone avaliação"/>
                                        <div class="evaluation-info">
                                            <div class="evaluation-name"><?php echo $s['nome']; ?></div>
                                            <div class="evaluation-date"><?php echo $s['data']; ?></div>
                                        </div>
                                    </button>
                                </form>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p class="no-data">Não tem nenhum teste com alunos inscritos para lançamento de notas.</p>
        <?php endif; ?>

        <button class="back-button" onclick="history.go(-1)">Voltar</button>
    </div>

    <script>
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        if (isMobile()) {
            document.body.classList.add('mobile');
        }
    </script>
</body>
</html>