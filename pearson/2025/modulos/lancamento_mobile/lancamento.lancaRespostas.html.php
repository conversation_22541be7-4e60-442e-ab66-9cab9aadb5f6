<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Folha de Respostas</title>
    <script src="https://kit.fontawesome.com/544f3b41ce.js" crossorigin="anonymous"></script>
    <script src="modulos/lancamento_mobile/js/modal.js"></script>
    <script src="modulos/lancamento_mobile/js/jquery.modal_box_anular_inscricao_aluno.js"></script>
    <script src="modulos/lancamento_mobile/js/jquery.modal_box_novo_aluno.js"></script>
    <script type="text/javascript">
        var JAVA_URL_BASE = "<?= Core::diretiva('JAVA_URL_BASE'); ?>";     
    </script>
    <link rel="stylesheet" href="modulos/lancamento_mobile/css/avaliacaoOnline.css">
    <style>
        .mtse input {
            text-align: center;
            padding: 10px;
            width: 100%!important;
            height: 50px;
            background-color: white;
            border: 0px;
            display: inline-block;
            /* padding-left: 0; */
        }
    </style>
</head>
<body>
    <input type="hidden" id="id_turma_selecionada_lancamento" name="id_turma_selecionada_lancamento" value="<?php echo$_turma; ?>">
		<input type="hidden" id="id_simulado_selecionada_lancamento" name="id_simulado_selecionada_lancamento" value="<?php echo$_simulado; ?>">
		<input type="hidden" id="lancamento_sendo_lancado" name="lancamento_sendo_lancado" value="1" />

    <div class="school-info">
        <div class="info-row">
            <span class="info-label">Escola:</span>
            <span class="info-value" id="school-name"><?php echo $intituicao_nome; ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Ano/Turma:</span>
            <span class="info-value" id="class-info"><?php echo $ext_serie;?> -<?php echo $turma_nome; ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Avaliação:</span>
            <span class="info-value" id="evaluation-name"><?php echo $simuNome; ?></span>
        </div>
    </div>
    <div style="text-align: center;">
        <img id="waiting" src="https://dyubuzjbgoyjh.cloudfront.net/lancamento/loading_orange.gif" style="width: 100px;"/>
    </div>

    <!-- Template para o accordion (será clonado via JS) -->
    <template id="student-accordion-template">
        <div class="student-accordion-item">
            <div class="student-header">
                <div class="student-info">
                    <span class="student-name"></span>
                    <span class="student-ra"></span>
                </div>
                <div class="expand-icon">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
            <div class="student-content">
                <div class="questions-container">
                    <!-- Questões serão carregadas aqui -->
                </div>
            </div>
        </div>
    </template>

    <div class="mobile-students-container">
        <div class="students-list" id="students-accordion">
        <!-- Alunos serão carregados aqui dinamicamente -->
        </div>
    </div>
    
    <hr style="border: 0; height: 1px; background: #ccc; margin: 20px 0;">
    <div class="action-buttons">
       <!-- <button class="action-button" onclick="openModalTutorial()">
            <i class="fa-solid fa-play"></i>
            Tutorial
        </button>-->
        <button class="action-button" onclick="openModalIncluirAluno()">
            <i class="fa-solid fa-user-plus"></i>
            Inscrever Aluno
        </button>
        <button class="action-button" onclick="openModalAnularInscricaoAluno()">
            <i class="fa-solid fa-ban"></i>
            Anular Inscrição
        </button>
        <button class="action-button" onclick="window.location.href='?m=avaliacao_offline&a=listar'">
            <i class="fa-solid fa-arrow-left"></i>
            Voltar
        </button>
    </div>

    <div class="table-container">
        <table class="response-table" id="questions-table">
            <!-- Table content will be dynamically generated -->
        </table>
    </div>

    <div class="submit-section">
        <button class="back-button" onClick="javascript:window.location='./?m=lancamento_mobile&a=lancamento_mobile&r_referencia=1';">
            TROCAR DE TURMA
        </button>
    </div>

    <!-- Modal do Tutorial -->
    <div class="tutorial-modal">
        <div class="tutorial-modal-content">
            <!-- Botão de fechar -->
            <span class="close-modal" onclick="closeModalTutorial()">&times;</span>
            
            <!-- Container do Slideshow -->
            <div class="slideshow-container">
                <!-- Slides -->
                <div class="slide fade">
                    <div class="numbertext">1 / 3</div>
                    <img src="modulos/principal/slides/ticket.png" alt="Tutorial passo 1">
                    <div class="slide-caption">Como fazer o lançamento - Passo 1</div>
                </div>

                <div class="slide fade">
                    <div class="numbertext">2 / 3</div>
                    <img src="modulos/principal/slides/ticket.png" alt="Tutorial passo 2">
                    <div class="slide-caption">Como fazer o lançamento - Passo 2</div>
                </div>

                <div class="slide fade">
                    <div class="numbertext">3 / 3</div>
                    <img src="modulos/principal/slides/ticket.png" alt="Tutorial passo 3">
                    <div class="slide-caption">Como fazer o lançamento - Passo 3</div>
                </div>

                <!-- Botões de navegação -->
                <a class="prev" onclick="changeSlide(-1)">&#10094;</a>
                <a class="next" onclick="changeSlide(1)">&#10095;</a>
            </div>

            <!-- Indicadores de slide -->
            <div class="dots-container">
                <span class="dot" onclick="currentSlide(1)"></span>
                <span class="dot" onclick="currentSlide(2)"></span>
                <span class="dot" onclick="currentSlide(3)"></span>
            </div>
        </div>
    </div>

    <div id="incluir_aluno" class="modal-box">
        <header> <a href="javascript:closeModalIncluirAluno();" class="js-modal-close close">&times;</a>
            <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/><p style="text-align:left; font-size:18px; opacity:70%"> - INSCREVER ALUNO</p>
        </header>
        <div class="modal-body-novo-aluno">
            <label class="modal-font-novo-aluno" style="padding-bottom:15px"></label>
            </br>
            <p style="margin-left: 30px;font-size: 12px; margin-bottom: 6px;">Digitar o <b style="color: black">e-mail</b> do aluno, exemplos:</p><br>
            <p style="margin-left: 30px;font-size: 12px; color: blue"><b style="color: black">se estadual: </b><EMAIL>,</p>
            <br>
            <p style="margin-left: 30px;font-size: 12px; color: blue">
                <b style="color: black">se municipal: </b>
                <EMAIL>, 123456789;
                <b style="color: black">ou</b> PARANAITA999
            </p>
            </br>
            <div class="mtse">
                <div class="zerosra" style="display: inline-block; width: 100%; margin-top: 10px;">
                    <div class="mtse2" style="width: 50px; display: inline-block; text-align: right"></div>
                    <input class="mtse2" id="matricula_incluir_aluno" type="text" style="font-size: 20px; font-family: Verdana, Geneva, Tahoma, sans-serif;color:rgb(122, 122, 122)"/>
                </div>	    
            </div>
            
            </br>
            <label class="labelalunoincluir" style="font-size: 12px; display: none; margin-left: 30px;">
			Digitar o nome do aluno, exemplos:<br><br><b style="color: black; margin-left: 30px;">se estadual:</b> primeiro nome sem acentuação<br><label style="margin-left: 30px">(TUDO MAIÚSCULO) + E1212121</label> <label style="color: blue">(ex: JOAO E1212121)</label><br><br><b style="color: black; margin-left: 30px">se municipal:</b> nome completo sem acentuação<br><label style="margin-left: 30px">(TUDO MAIÚSCULO)</label> <label style="color: blue">(ex: JOAO SANTOS GONCALVES)</label>
            </label>
            <a class="nomealunoincluir" style="display: none;">
            </br>
            <input id="nome_incluir_aluno" placeholder="Digite o nome do aluno" class="incluiraluno-input" style="width: 100%; margin-top: 10px;text-align: center; font-size: 20px; height: 50px;" />
            <select id="nome_incluir_aluno_existente" class="selecione-input" style="display:inline-block;background-color: #07003B; color: white; font-size: 20px; width: 100%;">		</select>			    
            </a>
        </div>
        
        <footer>
            <a href="javascript:incluirAluno();" class="btn-ok btn-small">&#10004; &nbsp; Incluir</a>
            <a href="javascript:closeModalIncluirAluno();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Cancelar</a>
        </footer>
	</div>

    
    <div id="anular_aluno" class="modal-box">
        <header> <a href="javascript:closeModalAnularInscricaoAluno();" class="js-modal-close close">&times;</a>
        <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
logoava.png" width="160px" height="50px" style="float:left; opacity:70%"/><p style="text-align:left; font-size:18px; opacity:70%"> - ANULAR INSCRI&Ccedil;&Atilde;O</p>

        </header>
        <div class="modal-body-anular-aluno">
            <p class="modal-font-anular-aluno"></p>
            <p>
                <label style="font-weight: bold;font-size: 14px;">Selecione o Aluno:</label>
                <p>
                <select id="anular_aluno_select" class="selecione-input" style="width: 100%; background-color: #07003B; color: white;">
                </select>


        </div>
        <footer>
            <a href="javascript:anularnscricao();" class="btn-ok btn-small">&#10004; &nbsp;Anular Inscri&ccedil;&atilde;o</a>
            <a href="javascript:closeModalAnularInscricaoAluno();" class="btn-notok btn-small js-modal-close-confirmation">&#10006;&nbsp; Cancelar</a>
        </footer>
    </div>

</body>
</html>

<script src="modulos/lancamento_mobile/js/jquery.lancamento.js"></script>
<script src="includes/JavaScript/iMask/jquery.mask.js"></script>
<script src="includes/JavaScript/iMask/jquery.maskMoney.js"></script>
