<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Lote</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .back-button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .back-button:hover {
            background-color: #45a049;
        }
        /* Estilos para o accordion */
        .accordion {
            background-color: #eee;
            color: #444;
            cursor: pointer;
            padding: 18px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 15px;
            transition: 0.4s;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        .accordion-lote {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        .accordion-school {
            background-color: #2196F3;
            color: white;
        }
        .accordion-student {
            background-color: #f1f1f1;
        }
        .active, .accordion:hover {
            opacity: 0.9;
        }
        .panel {
            padding: 0 18px;
            display: none;
            background-color: white;
            overflow: hidden;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 0 0 4px 4px;
        }
        .accordion-container {
            margin-bottom: 15px;
        }
        /* Estilos para os dados do aluno */
        .student-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .student-content {
            margin-bottom: 20px;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
            color: #333;
        }
        .ocr-image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .ocr-container {
            flex: 1;
            min-width: 300px;
        }
        .thumbnail-container {
            flex: 1;
            min-width: 300px;
        }
        .thumbnail {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        /* Estilos para a tabela OCR */
        .ocr-table {
            width: 100%;
            border-collapse: collapse;
        }
        .ocr-table th, .ocr-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .ocr-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .ocr-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #777;
        }
        .error {
            color: red;
            padding: 10px;
            border: 1px solid #ffcccc;
            background-color: #ffeeee;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<?php
$apiScan = Core::$gerenciadorDeDiretivas->obterDiretiva('API_SCAN');
?>

<script>
      const API_SCAN = "<?php echo $apiScan; ?>";
</script>
<body>
    <div class="container">
        <div class="header">
            <h2>Detalhes do Lote: <?php echo $id; ?></h2>
            <button class="back-button" onclick="window.location.href='./?m=lotes&a=listar'">Voltar</button>
        </div>
        <div id="scannedLinksContainer">
            <div class="loading">Carregando dados do lote...</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Obter o ID do lote da URL
            const loteId = <?php echo json_encode($id); ?>;
            fetchLoteById(loteId);
        });

        // Função para formatar dados OCR em tabela HTML
        function formatOcrData(ocrString) {
            try {
                // Tentar converter a string para objeto
                const ocrData = typeof ocrString === 'string' ? JSON.parse(ocrString.replace(/'/g, '"')) : ocrString;
                
                if (!ocrData || typeof ocrData !== 'object') {
                    return `<p>Formato OCR não reconhecido: ${ocrString}</p>`;
                }
                
                // Criar tabela HTML com os dados do OCR
                let tableHtml = `
                    <table class="ocr-table">
                        <thead>
                            <tr>
                                <th>Questão</th>
                                <th>Resposta</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                // Adicionar cada questão e resposta na tabela
                Object.keys(ocrData).sort((a, b) => parseInt(a) - parseInt(b)).forEach(question => {
                    const responses = Array.isArray(ocrData[question]) ? ocrData[question].join(', ') : ocrData[question];
                    tableHtml += `
                        <tr>
                            <td>${question}</td>
                            <td>${responses}</td>
                        </tr>
                    `;
                });
                
                tableHtml += `
                        </tbody>
                    </table>
                `;
                
                return tableHtml;
            } catch (e) {
                console.error("Erro ao processar OCR:", e);
                return `<p>Erro ao processar OCR: ${ocrString}</p>`;
            }
        }

        async function fetchLoteById(loteId) {
            try {
                const scannedLinksContainer = document.getElementById('scannedLinksContainer');
                
                // Fazer requisição à API usando o ID do lote
                c const response = await axios.get(`https://api.escaneamento.avaliarede.com.br/api_scan/avaliare_db_pearson_2025/dados_lote/${loteId}`, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    withCredentials: true,
                    crossdomain: true
                });
                
                const data = response.data;
                console.log('API Response Data:', data);

                if (!data || data.length === 0) {
                    scannedLinksContainer.innerHTML = '<div class="error">Nenhum dado encontrado para este lote.</div>';
                    return;
                }

                // Gerar HTML do accordion
                let htmlContent = `<button class="accordion accordion-lote">Lote: ${loteId}</button><div class="panel">`;

                // Agrupar dados
                const groupedData = data.reduce((acc, scan) => {
                    const schoolYearClassKey = `Escola: ${scan.slg_escola || 'N/A'} - Ano: ${scan.slg_ano || 'N/A'} - Turma: ${scan.slg_turma || 'N/A'}`;
                    if (!acc[schoolYearClassKey]) acc[schoolYearClassKey] = [];
                    acc[schoolYearClassKey].push({
                        studentName: `Aluno RA: ${scan.slg_aluno_ra || 'N/A'}`,
                        ra: scan.slg_aluno_ra,
                        imagePath: scan.slg_path_imagem,
                        rawOcr: scan.slg_raw_ocr,
                        datetime: scan.slg_datetime
                    });
                    return acc;
                }, {});

                // Gerar HTML para dados agrupados
                for (const schoolYearClass in groupedData) {
                    htmlContent += `<div class="accordion-container"><button class="accordion accordion-school">${schoolYearClass}</button><div class="panel">`;
                    groupedData[schoolYearClass].forEach(student => {
                        htmlContent += `
                            <button class="accordion accordion-student">${student.studentName}</button>
                            <div class="panel">
                                <div class="student-info">
                                    <h3>Dados do aluno</h3>
                                    <p><strong>Data/Hora do registro:</strong> ${student.datetime || 'N/A'}</p>
                                </div>
                                
                                <div class="student-content">
                                    <div class="section-title">Dados escaneados:</div>
                                    <div class="ocr-image-container">
                                        <div class="ocr-container">
                                            ${formatOcrData(student.rawOcr)}
                                        </div>
                                        
                                        <div class="thumbnail-container">
                                            <img src="${student.imagePath}" class="thumbnail" alt="Imagem escaneada do aluno">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    htmlContent += `</div></div>`;
                }

                htmlContent += `</div>`;
                scannedLinksContainer.innerHTML = htmlContent;

                // Configurar accordion
                const accordions = document.getElementsByClassName("accordion");
                for (let i = 0; i < accordions.length; i++) {
                    accordions[i].addEventListener("click", function() {
                        this.classList.toggle("active");
                        const panel = this.nextElementSibling;
                        if (panel.style.display === "block") {
                            panel.style.display = "none";
                        } else {
                            panel.style.display = "block";
                        }
                    });
                }

                // Abrir o primeiro accordion automaticamente
                if (accordions.length > 0) {
                    accordions[0].click();
                }

            } catch (error) {
                console.error("Erro ao buscar lote:", error);
                document.getElementById('scannedLinksContainer').innerHTML = 
                    `<div class="error">Erro ao buscar dados do lote: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
