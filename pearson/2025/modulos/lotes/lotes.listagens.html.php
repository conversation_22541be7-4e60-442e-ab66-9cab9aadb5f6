<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> por Professor</title>
    <link rel="stylesheet" href="modulos/lotes/css/lotes.css" />
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .professor-select {
            padding: 8px;
            margin-bottom: 20px;
            width: 100%;
            max-width: 400px;
        }
        .lote-card-link {
            text-decoration: none;
            color: inherit;
        }
        .lote-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #f9f9f9;
            transition: background-color 0.3s;
        }
        .lote-card:hover {
            background-color: #f0f0f0;
        }
        .back-button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .back-button:hover {
            background-color: #45a049;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #777;
        }
        .error {
            color: red;
            padding: 10px;
            border: 1px solid #ffcccc;
            background-color: #ffeeee;
            border-radius: 4px;
        }
        .professor-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f8ff;
            border-left: 4px solid #2196F3;
            border-radius: 4px;
        }
        
        .professor-info h3 {
            margin: 0;
            color: #333;
        }
    </style>
</head>
<?php
$apiScan = Core::$gerenciadorDeDiretivas->obterDiretiva('API_SCAN');
?>

<script>
      const API_SCAN = "<?php echo $apiScan; ?>";
</script>
<body>
    <div class="container">
        <div class="header">
            <h2>Lista de Lotes por Professor</h2>
            <div id="backButtonContainer"></div>
        </div>
        
        <select id="professorSelect" class="professor-select">
            <option value="">Selecione um professor...</option>
            <?php foreach ($this->_obterProfessores() as $professor): ?>
                <option value="<?php echo $professor['id']; ?>">
                    <?php echo htmlspecialchars($professor['nome']); ?> 
                    (<?php echo htmlspecialchars($professor['email']); ?>)
                </option>
            <?php endforeach; ?>
        </select>
        
        <div id="lotesContainer">
            <div class="loading">Selecione um professor para visualizar seus lotes</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Criar botão de voltar
            const backButton = document.createElement('button');
            backButton.textContent = 'Voltar';
            backButton.className = 'back-button';
            backButton.onclick = () => window.location.href = './?m=lotes';
            
            // Inserir botão de voltar no contêiner
            const backButtonContainer = document.getElementById('backButtonContainer');
            backButtonContainer.appendChild(backButton);

            // Adicionar event listener para o select de professores
            const professorSelect = document.getElementById('professorSelect');
            professorSelect.addEventListener('change', function() {
                if (this.value) {
                    const professorText = this.options[this.selectedIndex].text;
                    const professorId = this.value;
                    fetchLotes(professorId, professorText);
                } else {
                    const lotesContainer = document.getElementById('lotesContainer');
                    lotesContainer.innerHTML = '<div class="loading">Selecione um professor para visualizar seus lotes</div>';
                }
            });
        });

        async function fetchLotes(userId, professorText) {
            const lotesContainer = document.getElementById('lotesContainer');
            lotesContainer.innerHTML = '<div class="loading">Carregando lotes...</div>';
            
            try {
                const response = await axios.get(`https://api.escaneamento.avaliarede.com.br/api_scan/avaliare_db_pearson_2025/listar_lotes/${userId}`, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    withCredentials: true,
                    crossdomain: true
                });

                const lotes = response.data;

                // Adicionar header com informações do professor
                let htmlContent = `
                    <div class="professor-info">
                        <h3>Professor selecionado: ${professorText}</h3>
                    </div>
                `;
                
                // Gerar HTML para os lotes
                if (lotes && lotes.length > 0) {
                    lotes.forEach(lote => {
                        htmlContent += `
                            <a href="./?m=lotes&a=detalhar&id=${lote.sl_id}" class="lote-card-link">
                                <div class="lote-card">
                                    <h3>Lote: ${lote.sl_id}</h3>
                                    <p>Data de Criação: ${lote.sl_datetime}</p>
                                    <p>Usuário: ${lote.sl_usuario}</p>
                                </div>
                            </a>
                        `;
                    });
                    lotesContainer.innerHTML = htmlContent;
                } else {
                    lotesContainer.innerHTML = htmlContent + '<div class="loading">Nenhum lote encontrado para este professor</div>';
                }

            } catch (error) {
                console.error("Erro ao buscar lotes:", error);
                lotesContainer.innerHTML = `<div class="error">Erro ao buscar lotes: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
