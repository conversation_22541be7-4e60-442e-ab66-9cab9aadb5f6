<?php
if (!defined('CORE_INCLUIDO')) { exit(); }

// Debugging message removed

class MLotes extends Modulo
{
    public function aListarLotes () 
    {
        $this->_iniciarRenderizacao(Modulo::HTML);
            include('lotes.listagens.html.php');
        $this->_finalizarRenderizacao();
    }
    
    private function _obterProfessores()
    {
        $professores = array();
        
        // Query atualizada para incluir o e-mail através de um LEFT JOIN com a tabela emails
        $rs = Core::registro('db')->query(
            'SELECT u.u_id, u.u_nome, e.e_endereco 
             FROM usuarios u 
             LEFT JOIN emails e ON e.e_id = u.u_email
             WHERE u.u_grupos = \'3\' 
             AND EXISTS (
                SELECT 1 
                FROM scan_lotes sl 
                WHERE sl.sl_usuario = u.u_id
             )
             ORDER BY u.u_nome ASC'
        );
    
        if ($rs->num_rows) {
            while ($row = $rs->fetch_assoc()) {
                $professores[] = array(
                    'id' => $row['u_id'],
                    'nome' => $row['u_nome'],
                    'email' => $row['e_endereco'] ? $row['e_endereco'] : 'Sem e-mail'
                );
            }
        }
        $rs->free();
        
        return $professores;
    }

    public function aDetalharLotes()
    {
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            // Redirect back to list if no valid ID
            header('Location: ./?m=lotes&a=listar');
            exit;
        }
        
        $this->_iniciarRenderizacao(Modulo::HTML);
            include('lotes.detalhe.html.php');
        $this->_finalizarRenderizacao();
    }
}

?>  