<!-- <script src="https://kit.fontawesome.com/544f3b41ce.js" crossorigin="anonymous"></script> -->
<link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>

<script src="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/development-bundle/ui/jquery.ui.core.js" type="text/javascript"></script>
<script src="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/development-bundle/ui/jquery.ui.widget.js" type="text/javascript"></script>
<script src="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/development-bundle/ui/jquery.ui.accordion.js" type="text/javascript"></script>
<link rel="stylesheet" type="text/css" href="includes/JavaScript/jQueryUI/jquery-ui-1.8.22.custom/css/custom-theme/jquery-ui-1.8.22.custom.css" />

<style type='text/css'>
	.geral * {
		font-family: 'Roboto';
	}

	.fa{
		font-family:var(--fa-style-family,"Font Awesome 6 Free") !important;
	}

	.fa-solid{
		font-family: "Font Awesome 6 Free" !important;
	}

	div.geral{
		display: block;
		height: auto;
		width: 100%;
	}

	div.title{
		background-color: #eee;
		border-radius: 10px 10px 0px 0px;
		font-size: 20px;
		margin-bottom: 0;
		padding: 10px;
		font-weight: bold;
	}

	input {
		display: block;
	}
	.crono{
		border-bottom: 1px solid #DEDEDE;
		border-left: 1px solid #DEDEDE;
		border-right: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		color: #555555;
		font-size: 13px;
		margin: 0px auto;
		padding: 5px 5px 2px;
		width: 125px;
		overflow: hidden;
		display:none;
	}

	img.crono_start, img.crono_stop{
		cursor: pointer;
		display:none;
	}

	#crono_time{
		display: block;
		float: right;
		margin-left: 5px;
	}

	.conteudo {
		border: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		margin-top: 0;
		background: #fff;
	}

	.num_questao{
		border-bottom: 1px solid #DEDEDE;
		border-radius: 5px 0 5px 0;
		border-right: 1px solid #DEDEDE;
		color: #333333;
		font-size: 14px;
		padding: 5px 10px;
		float:left;
	}

	.crono_questao{
		border-bottom: 1px solid #DEDEDE;
		border-left: 1px solid #DEDEDE;
		border-radius: 0 5px 0 5px;
		float: right;
		font-size: 14px;
		padding: 5px;
		color:#333333;
		display:none;
	}

	.tbl_conteudo{
		width: 100%;
		border-collapse: collapse; 
		border-spacing: 0;
	}

	.tbl_conteudo td{
		border-spacing: 0;
		padding: 0;
		line-height: 20px;
	}

	.botoes{
		padding: 5px;
		border-top: 1px solid #DEDEDE;
	}

	.resposta_questao {
		padding: 15px;
	}

	.questao{
		width: 100%;
	}

	.questionario_general_start{
		border: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		background: #fff;
	}

	.questionario_general_end{
		border: 1px solid #DEDEDE;
		border-radius: 0px 0px 5px 5px;
		background: #fff;
	}

	#modal_load{
		display: block;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	#modal_load1{
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	#modal_load2{
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	#modal_load3{
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(150, 150, 150, 0.8);
		width: 100%;
		height: 100%;
		z-index: 999999999999999;
	}

	h3{
		padding: 5px 5px 5px 25px !important;
	}

	.btnRec{
		height: 75px;
		width: 240px;
		font-size: 28px !important;
		font-weight: bold;
		color: #fff;
		border-radius: 30px;
		cursor: pointer;
	}

	.btnRecCorGRAVAR{
		background: #f93737;
		border: 1px solid #ae0606;
	}

	.btnRecCorGRAVANDO{
		display: none;
		background: #f96e37;
		border: 1px solid #ae4d06;
	}

	.btnRecCorSALVANDO{
		display: none;
		background: #119fe6;
		border: 1px solid #066fae;
	}

	.btnRecCorERRO{
		display: none;
		background: #e3a40f;
		border: 1px solid #ae8406;
	}

	.btnRecCorGRAVADO{
		display: none;
		background: #20d905;
		border: 1px solid #129921;
	}

	.azul{
		color: #119fe6;
	}

	.verde{
		color: #129921;
	}

	.tblRec {
		border: solid 2px #aaa;
		border-radius: 10px;
	}

	body .timer .endMessage {
		font-size: 50px;
		text-align: center;
		font-weight: bold;
		opacity: 0;
		transition: all .35s ease;
	}

	body .timer .clock-wrapper {
		display: flex;
		justify-content: center;
	}

	body .timer .clock-wrapper span {
		font-size: 100px;
		font-weight: bold;
		transition: all .2s ease;
	}

	body .timer .clock-wrapper span.red {
		transition: all .2s ease;
		color: rgb(233, 19, 19);
		font-size: 180px;
	}

	body .timer .clock-wrapper span.red.hours,
	body .timer .clock-wrapper span.red.minutes,
	body .timer .clock-wrapper span.red.dots {
		width: 0;
		opacity: 0;
		transition: all .3s ease;
	}

	body .timer .clock-wrapper span.dots {
		margin-top: -5px;
	}

	#fsalert{
		font-size: 18px !important; 
		background: none repeat scroll 0 0 #F7EECD;
		border: 1px dashed #8C7416;
		border-radius: 3px 3px 3px 3px;
		color: #896E00;
		font-size: 14px;
		padding: 10px;
		text-align: center;
	}

	.fsalert2{
		width: 22%;
		display: block;
		font-size: 18px;
		margin: 15% auto;
		border-radius: 10px;
		padding: 20px;
		border: 3px solid #ccc !important;
		background: green;
		color: #FFFFFF;
	}

	.fsalert3{
		width: 22%;
		display: block;
		font-size: 18px;
		margin: 15% auto;
		border-radius: 10px;
		padding: 20px;
		border: 3px solid #ccc !important;
		background: #333;
		color: #FFFFFF;
	}

	.fsalert_sucesso {
		border: 4px dashed #558E2F  !important;
	}

	.fsalert_erro {
		border: 4px dashed #EE1111  !important;
	}
	.cssbuttons-io-button {
		background: #228B22;
		color: white;
		font-family: inherit;
		padding: 0.35em;
		padding-left: 1.2em;
		font-size: 17px;
		font-weight: 600;
		border-radius: 0.9em;
		border: none;
		letter-spacing: 0.05em;
		display: flex;
		align-items: center;
		overflow: hidden;
		position: relative;
		height: 2.8em;
		padding-right: 3.3em;
		cursor: pointer;
		}

	.cssbuttons-io-button .icon {
	background: #4CAF50;
	margin-left: 1em;
	position: absolute;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 2.2em;
	width: 2.2em;
	border-radius: 0.7em;
	right: 0.3em;
	transition: all 0.3s;
	}

	.cssbuttons-io-button .icon svg {
	width: 1.1em;
	transition: transform 0.3s;
	color: #FFFFFF;
	}

	.cssbuttons-io-button:hover .icon {
	width: calc(100% - 0.6em);
	}

	.cssbuttons-io-button:hover .icon svg {
	transform: translateX(0.1em);
	}

	.cssbuttons-io-button:active .icon {
	transform: scale(0.95);
	}

	.button {
		padding: 10px 20px;
		margin: auto;
		align-items: center;
		border: none;
		border-radius: 5px;
		cursor: pointer;
		display: flex;
		transition: background-color 0.3s, color 0.3s;
	}

	#sim2 {
		background-color: #4CAF50;
		color: white;
		text-align: center;
		align-items: center
	}

	#sim3 {
		background-color: red;
		color: white;
		text-align: center;
		align-items: center
	}

	/* Efeito de hover nos botoes */
	#sim2:hover {
		background-color: #708090;
		color: #FFFFFF;
	}

	#sim3:hover {
		background-color: #708090;
		color: #FFFFFF;
	}

	.respostas {
		background-color: #eee !important;
		border: 1px solid #aaa;
	}

	.respostas:focus {
		background-color: #B0E0E6 !important;
		border: 1px solid #aaa;
	}

	.progress {
	width: 134.4px;
	height: 24.6px;
	border-radius: 22.4px;
	color: #fff;
	border: 2.2px solid #035700;
	position: relative;
	}

	.progress::before {
	content: "";
	position: absolute;
	margin: 2.2px;
	inset: 0 100% 0 0;
	border-radius: inherit;
	background: currentColor;
	animation: progress-pf82op 1.6s infinite;
	}

	@keyframes progress-pf82op {
	100% {
		inset: 0;
	}
	}

	.custom-loader {
	width:100px;
	height:48px;
	background: 
		radial-gradient(circle closest-side,#035700 90%,#0000) 0%   50%,
		radial-gradient(circle closest-side,#035700 90%,#0000) 50%  50%,
		radial-gradient(circle closest-side,#035700 90%,#0000) 100% 50%;
	background-size:calc(100%/3) 24px;
	background-repeat: no-repeat;
	animation:d3 0.5s infinite linear;
	}
	@keyframes d3 {
		20%{background-position:0%   0%, 50%  50%,100%  50%}
		40%{background-position:0% 100%, 50%   0%,100%  50%}
		60%{background-position:0%  50%, 50% 100%,100%   0%}
		80%{background-position:0%  50%, 50%  50%,100% 100%}
	}
</style>

<div class="geral">
	<div class="title">
		<?php echo $iNome.' - '.$simulado->obterSerieAvaliacao()->obterNome().' - '.$tNome.' ('.$sNome.')'; ?>
	</div>

	<div id="div_btn_start" class="questionario_general_start" style="padding: 20px;">

	<br>

	<div id="modal_load">
		<img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
loader6.gif" style="width: 15%;display: block;margin: 15% auto;" />
	</div>

	<div id="modal_load1">
		&nbsp;
	</div>

	<div id="modal_load2">
		<p class="fsalert fsalert2" style="">
			Lan&ccedil;amento realizado com sucesso!
			<br>
			<br>
			<button id="sim2" class="button">Ok</button>
		</p>
	</div>

	<div id="modal_load3">
		<p class="fsalert fsalert3 fsalert_erro" style="">
			Falha! :(
			<br>
			<span></span>
			<br>
			<button id="sim3" class="button">Ok</button>
		</p>
	</div>
 		
		<form id="formulario">
		<div id="itens" class="itens" style="overflow: hidden;">
			<?php 
				$questoes = $simulado->obterQuestoesPorID();

				foreach($arrInscritos as $aik => $aiv): 
					$iid = $aiv['iid'];
					$respostas = $aiv['resposta'];
					
					$respostasValidas = 0;
					foreach($questoes as $questao){ 
						$rvalor = '';
						$qid = $questao->obterID();
						if(array_key_exists($qid, $respostas)){
							$rvalor = $respostas[$qid];
							if($rvalor != ''){
								$respostasValidas++;
							}
						}			
					}

					$icon = '';
					if($respostasValidas > 0){
						$icon='&#x2713;';
					}
			?>
				<h3 style="font-size: 18px !important;" hiid="<?php echo $iid; ?>"> &#127911; <?php echo '<label style="font-size: 16px; font-weight: light; color: #2F4F4F">'.$aiv['nome'].' - '.$aiv['ra'].' <span id="qtd_'.$iid.'">('.$respostasValidas.'/'.count($questoes).')'.'</span></label> <label class="mudaCor"><span id="ok_'.$iid.'" style="color:green;">'.$icon.'</span></label>' ?></h3>
				<div style="overflow: hidden">
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<th style="width: 7%; border: 1px solid #ccc; font-weight: bold; text-align: center; background-color: #708090; color: #FFFFFF;padding: 10px;">IDENTIFICADOR</th>
							<th style="border: 1px solid #ccc; font-weight: bold; text-align: center; background-color: #708090; color: #FFFFFF;padding: 10px;width: 40%;">ENUNCIADO</th>
							<th style="border: 1px solid #ccc; font-weight: bold; text-align: center; background-color: #708090; color: #FFFFFF;padding: 10px;">A&Uacute;DIO DO ALUNO</th>
							<th style="border: 1px solid #ccc; font-weight: bold; text-align: center; background-color: #708090; color: #FFFFFF;padding: 10px;width: 20%;">CRIT&Eacute;RIOS</th>
							<th style="width: 7%; border: 1px solid #ccc; font-weight: bold; text-align: center; background-color: #708090; color: #FFFFFF;padding: 10px;">NOTA</th>
						</tr>
						<?php 
							$vmax = 0;
							foreach($questoes as $questao){ 
								$qid = $questao->obterID();
								$vmax = $questao->obterPontosQuestao();
								
								$rvalor = '';
								if(array_key_exists($qid, $respostas)){
									//$rvalor = floatval($respostas[$qid]);
									//$rvalor = str_replace('.', ',', $rvalor);
									$rvalor = $respostas[$qid];
								}
							?>
						<tr>				
								<td style="padding: 9px; border: 1px solid #ccc; text-align: center">		
									<?php echo $questao->obterIdentificador();?>			
								</td>
		
							<td style="text-align: center; border: 1px solid #ccc">
								<?php echo $questao->obterEnunciado(); ?>
							</td>

							<td style="width: 200px; padding: 10px; border: 1px solid #ccc; text-align: center">
								<?php 
									$semGravacao = false;
									$pathTMP = str_replace('modulos/lancamento_fluencia', '', realpath(dirname(__FILE__)));

									$audios = glob($pathTMP.'upload/gravacoes/'.CACHE_PREFIXO_SESSION.'_rec_'.$iid.'_'.$qid.'*.ogg');
									if(count($audios)>0){
										foreach($audios as $audio){
											$oggLink = '';
											$oggLink = Gerenciador_URL::gerarLinkIndex(); 
											$oggLink .= 'upload/gravacoes/'.basename($audio);
											echo'<audio controls="true" style="border-radius: 10px; width: 100%;">';
											echo '<source src="'.$oggLink.'" type="audio/ogg">';
											echo'</audio>';
											echo'<br>';
										}
									}	
								?>
							</td>

							<td style="text-align: center; border: 1px solid #ccc">
								<?php echo $questao->obterResolucao(); ?>
							</td>

							<td style="text-align: center; border: 1px solid #ccc; align-items: center;">
								<input vmax="<?php echo $vmax; ?>" id="<?php echo $iid.'_'.$qid; ?>" value="<?php echo $rvalor; ?>" <?php if($semGravacao){echo'disabled=disabled';} ?> class="respostas" type="text" style="width: 50px; height: 30px; font-size: 16px; font-weight: bold; text-align: center;align-items: center; margin: auto; border-radius: 10px 10px 10px 10px;"/>
							</td>
						</tr>
						<?php } ?>
					</table>
				</div>
			<?php 
				endforeach; 
			?>
		</div>
		<br>
			<a style="text-decoration: none; align-items: center; margin: auto; text-align: center; display: flex;">
				<button type="submit" id="btn_lancamento" class="cssbuttons-io-button" style="align-items: center; margin: auto;"> Realizar Lan&ccedil;amento
					<div class="icon">
						<div id="progress" class="progress" style="display:none;"></div>
						<div class="custom-loader" style="display:none;"></div>
						<svg id="setinha" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"></path><path fill="currentColor" d="M16.172 11l-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"></path></svg>
					</div>
				</button>
			</a>
		</form>
	</div>
</div>

<script src="includes/JavaScript/iMask/jquery.maskMoney.js"></script>

<script language="JavaScript">
	let enviando = false;

	window.onbeforeunload = function(){
		if(enviando === true){
			return "Aguarde o processamento do lan&ccedil;amento!";
		}
		//return "Os dados n&atilde;o salvos ser&atilde;o perdidos. Deseja sair dessa p&aacute;gina?";
	}

	jQuery(document).ready(function(){
		jQuery('#modal_load').hide();
		jQuery('#modal_load1').hide();
		jQuery('#modal_load2').hide();
		jQuery('#modal_load3').hide();

		jQuery("#itens").accordion({
			active:false,
			collapsible:true, 
			autoHeight: false, 
			icons: { "header": "ui-icon-plusthick", "activeHeader": "ui-icon-minusthick" },
		});

		jQuery("#formulario").submit(function(event) {
        	event.preventDefault();

			if(enviando === true){
				return;
			}
			enviando = true;

			jQuery('#modal_load').show();
			jQuery('#setinha').hide();
			jQuery('#progress').show();
			jQuery("#btn_lancamento > .icon").css('width','14.7em');

			var notas = new Array();
			index = 0;
			jQuery(".respostas").each(function() {
				var value = jQuery(this).val();
				var id = jQuery(this).attr("id");
				var chg = jQuery(this).attr("changed");

				if(chg){
					nota = new Object();
					nota.v = value;

					// Usando explode para dividir o ID em duas partes
					id_parts = id.split("_");
					nota.i = id_parts[0]; // Valor antes do "_"
					nota.q = id_parts[1]; // Valor depois do "_"

					notas[index++] = nota;
				}				
			});

			notas = JSON.stringify(notas);

			jQuery.ajax({
				url: "./?m=lancamento_fluencia&a=processar_fluencia", 
				async: true,
				cache: false,
				type: "POST",
				data: { data: notas },
				error: function(err, textStatus, errorThrown){
					if(err.readyState == 0 && err.status == 0){
						jQuery('#modal_load3 > p > span').html('Sem conex&atilde;o com a internet!');
					}

					jQuery('#modal_load3').show();
				},
				success: function(response, textStatus, jqXHR) {
					console.log('success');
					console.log(response);
					console.log(textStatus);
					console.log(jqXHR);

					if(response == ''){
						jQuery('#modal_load3').show();
					}
					else if(response.indexOf('Acesso a plataforma') >= 0){
						jQuery('#modal_load3 > p > span').html('Sua sessão expirou!');
						jQuery('#modal_load3').show();
					}
					else if(response.indexOf('Sem perm') >= 0){
						jQuery('#modal_load3 > p > span').html('Sua sessão expirou!');
						jQuery('#modal_load3').show();
					}
					else{
						response = JSON.parse(response);
						if(response.ret == 1){
							jQuery('#modal_load2').show();

							jQuery('h3').each(function(index, el) {
								id = jQuery(this).attr("hiid");
								numTotal = jQuery(this).next().find('.respostas').length;
								numNotEmpty = jQuery(this).next().find('.respostas[value!=""]').length;

								if(numNotEmpty > 0 && numTotal > 0){
									jQuery('#ok_'+id).html('&#x2713;');
									jQuery('#ok_'+id).css('color','green');
									jQuery('#qtd_'+id).html('('+numNotEmpty+'/'+numTotal+')');
								}
							});
						}
						else{
							jQuery('#modal_load3').show();
						}
					}
				},
				complete:  function(response,textStatus){
					console.log('complete');
					console.log(response);
					console.log(textStatus);

					if(textStatus == 'error' && response.readyState == 0 && response.status == 0){
						jQuery('#modal_load3').show();
					}

					jQuery('#modal_load').hide();
					jQuery('#setinha').show();
					jQuery('#progress').hide();
					jQuery("#btn_lancamento > .icon").css('width','2.2em');
				}
			});
   		});

		jQuery('#sim2').click(function() {
			jQuery('#modal_load2').hide();
			jQuery("html, body").animate({ scrollTop: 0 }, "slow");
    		jQuery("html, body").animate({ scrollLeft: 0 }, "slow");
			enviando = false;
		});

		jQuery('#sim3').click(function() {
			jQuery('#modal_load3').hide();
			jQuery("html, body").animate({ scrollTop: 0 }, "slow");
			jQuery("html, body").animate({ scrollLeft: 0 }, "slow");
			enviando = false;
		});

		jQuery(".respostas").change(function() {
			jQuery(this).attr("changed", "true");
		});

		jQuery(".respostas").keyup(function(e) {
            vcur = jQuery(this).val();
			vcur = vcur.replace(',', '.');
			vcur = parseFloat(vcur).toFixed(2);

			vmax = jQuery(this).attr('vmax');
            vmax = parseFloat(vmax).toFixed(2);
			vmax = Number(vmax);

			vmaxShow = parseFloat(vmax).toFixed(2);

			if(vcur > vmax){
				jQuery(this).val(0);
				itemNome = jQuery(this).parent('td').parent('tr').find('td:first').text();
				msg_modal = "<label style='font-size: 20px; text-align: center;'>&#10060; A pontua&ccedil;&atilde;o m&aacute;xima deste<br>item ("+itemNome+") &eacute;:  "+vmaxShow.toString().replace('.',',')+"</label>";
				openModal(msg_modal,"closeModal()");
			}
        });
	});
</script>