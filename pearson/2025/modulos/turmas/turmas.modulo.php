<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Turma', null, true);

include_once('turmas.listagem.php');
include_once('turmas.formulario.php');
include_once('turmas.exportador.php');
include_once('turmas.transferencia.php');

class MTurmas extends Modulo
{
	protected $_listagem;
	protected $_formulario;

	public function __construct ()
	{
		parent::__construct();
				
		// habilita botão de adicionar nos atalhos do navegador
		if ( Core::moduloCarregado('navegador') ) {
			Core::modulo('navegador')->habilitarAtalho('adicionar', true, Gerenciador_URL::gerarLink('turmas', 'nova'), 'Adicionar turma...');
		}
	}

	public function aListarTurmas () {	
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();
		$this->_carregarListagem();
	}
	
	public function aDetalharTurma ()
	{
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		$turma = new Turma( (int) @$_GET['id'] );
		
		if ( $turma->carregar() ) {	// && $turma->validarInstituicao() ) {	
			$turma->obterInstituicao()->carregar();

			$this->_iniciarRenderizacao(Modulo::HTML);
				include('turmas.detalhes.html.php');
			$this->_finalizarRenderizacao();
		} else {
			Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('turmas', 'listar'), 'Turma inválida!');
		}
	}
	
	public function aNovaTurma () {
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		$this->_formulario = new FTurmas( array('nome' => 'form_turmas', 'acao' => Gerenciador_URL::gerarLink('turmas', 'editar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();
		$this->_formulario->carregarFormulario();
		
		if ( $this->_formulario->foiEnviado() ) {
			try
			{
				$this->_formulario->checarFormulario();
				$id = $this->_formulario->executar()->obterID();
				
				switch ( $this->_formulario->acoesPosEnvio->obterAcaoSolicitada() ) {
					case 'adicionar':
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('turmas', 'nova', array('pos_envio' => 'adicionar')) ); break;
					case 'editar_proximo':
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('turmas', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) ); break;
					default:
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $id)) );
				}
				
				Gerenciador_URL::habilitarAtualiacaoReferencia(false);			
				Core::modulo('redirecionador')->redirecionar();
			}
			catch (Formulario_Exception $e) {}
		}
		
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('turmas.formulario.html.php');
		$this->_finalizarRenderizacao();
	}
	
	public function aProcessarFormulario () {
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();
		return $this->aNovaTurma();
	}
	
	public function aRemoverTurmas ($ids = null)
	{
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		if ( $ids != null ) {
			foreach ($ids as $id) {
				$obj = new Turma($id);
				
				if ( !$obj->carregar() || !$obj->validarInstituicao() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterID(), 'id inválido;');
					continue;
				}
				
				if ( !$obj->podeRemover() ) {
					Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'não pode ter alunos para poder ser removida;');
				} else {
					if (!$obj->remover()) {
						Core::modulo('redirecionador')->adicionarFalha($obj->obterNome(), 'turma não foi removida;');
					} else {
						Core::modulo('redirecionador')->adicionarSucesso($obj->obterNome(), 'turma foi removida com sucesso;');
					}
				}
			}
			
			Core::modulo('redirecionador')->fixarMensagem(null, 'Removendo turmas...');
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);			
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('turmas', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		} else {
			// remove via $_GET['id']
			if (isset($_GET['id']) && (int) $_GET['id'] > 0) {
				return $this->aRemoverTurmas( array((int) $_GET['id']) );
			}
			
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::redirecionar(Redirecionador::HEADER, Gerenciador_URL::gerarLinkPelaReferencia());
		}
	}
	
	public function aExportarTurmas ()
	{
		Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		try {
			$exportador = new ExportadorTurmas();
		
			$exportador->exportar();
			
			$this->_iniciarRenderizacao(Modulo::CSV);
				$exportador->obterSaida('turmas.csv');
			$this->_finalizarRenderizacao();
		} catch (Core_Exception $e) {
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);			
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('turmas', 'listar') );
			Core::modulo('redirecionador')->redirecionar();
		}
	}
	
	private function _carregarListagem () {
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		$this->_listagem = new LTurmas();
		
		$this->_listagem->prepararListagem();
		
		$this->_iniciarRenderizacao(Modulo::HTML);
			echo $this->_listagem->obterSaida();
		$this->_finalizarRenderizacao();
	}
	
	// @todo: mudar isso daqui...
	private function _obterListaAlunos (Turma $turma) {
		//Core::modulo('_seletor_instituicoes')->forcaUsoDeInstituicao();

		$alunos = array();
		
		if ( $turma != null ) {
			$rs = Core::registro('db')->query( sprintf(
				  'SELECT * FROM alunos 
				  INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
				  LEFT JOIN emails ON emails.e_id = usuarios.u_email 
				  WHERE a_turma = %s',
				Core::registro('db')->formatarValor( $turma->obterID() ) ) );
				
			if ($rs->num_rows) {
				while ( $row = $rs->fetch_assoc() ) {
					$alunos[] = array('id' => $row['a_id'], 'nome' => $row['u_nome'], 'login' => $row['e_endereco'], 'matricula' => $row['a_matricula']);
				}
			}
			$rs->free();
		}
		
		return $alunos;
	}
	
	public function aTransferenciaAlunos ($ids = null)	{		
		$this->_formulario = new FTransferencia(array('nome' => 'form_transferencia', 'acao' => Gerenciador_URL::gerarLink('turmas', 'transferencia')));
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->prepararEntrada();

		if ($this->_formulario->foiEnviado()) {
			$turmasSel = Core::diretiva('_turma_transferencia.turmasDe');
			if(empty($turmasSel)){
				Core::modulo('redirecionador')->fixarMensagem(null, 'Transferência de Alunos...');
				Core::modulo('redirecionador')->adicionarFalha(null, 'Falha ao obter dados das turmas selecionadas.');

				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('turmas', 'listar'));
				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			$this->_formulario->fixarTurmasDE(explode(',',$turmasSel));

			$this->_formulario->carregarFormulario();

			try{
				Gerenciador_URL::habilitarAtualiacaoReferencia(false);
				Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('turmas', 'listar'));
				Core::modulo('redirecionador')->fixarMensagem(null, 'Transferência de Alunos...');

				$this->_formulario->checarFormulario();
				$this->_formulario->executar();

				Core::modulo('redirecionador')->redirecionarNenhum();
			}
			catch (Formulario_Exception $e)	{ 
				//echo $this->_formulario->obterHTML('turma_para', Formulario::HTML_ERROS, true);
				//throw new FTransferencia_Exception($e->getMessage());
			}
		}
		elseif ($ids != null && is_array($ids)){
			Core::fixarDiretiva('_turma_transferencia.turmasDe', implode(',',$ids));

			$this->_formulario->fixarTurmasDE($ids);
			$this->_formulario->carregarFormulario();

			$this->_iniciarRenderizacao(Modulo::HTML);
				include('turmas.transferencia.html.php');
			$this->_finalizarRenderizacao();
		} 
		else {
			Core::modulo('redirecionador')->fixarMensagem(null, 'Transferência de Alunos...');
			Core::modulo('redirecionador')->adicionarFalha(null, 'É preciso selecionar uma ou mais turmas.');

			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('turmas', 'listar'));
			Core::modulo('redirecionador')->redirecionarNenhum();
		}
	}
}

?>