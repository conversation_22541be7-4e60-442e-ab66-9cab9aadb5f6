<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('ExportadorCVS', 'Exportador/');

class ExportadorTurmas extends ExportadorCVS
{
	
	public function exportar ()
	{
		$this->_dados = array(array('ID', 'Nome da turma', 'ID Série', 'Série', 'Instituição'));

		$inst = '';
		if(Core::registro('instituicao')->obterID()){
			$inst = 'WHERE t_instituicao = '. Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID());
		}
	
		$rs = Core::registro('db')->query(sprintf(
			'SELECT * FROM turmas JOIN series ON series.s_id = turmas.t_serie JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao %s ORDER BY t_nome',
			$inst));
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$this->_dados[] = array($row['t_id'], $row['t_nome'], $row['s_id'], $row['s_nome'], $row['i_nome']);
			}
		}
		$rs->free();
				
		if ( count($this->_dados) < 2 ) {
			Core::modulo('redirecionador')->fixarMensagem('Nenhuma turma encontrada!', 'Exportando turmas...');
			throw new Core_Exception('Sem dados!');
		}
	}
		
}

?>