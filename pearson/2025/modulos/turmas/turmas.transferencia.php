<?php

if (!defined('CORE_INCLUIDO')) { exit(); }

class FTransferencia_Exception extends Formulario_Exception { }

class FTransferencia extends Formulario
{
	public $nomesTurmasSelecionadas = array();
	public $_turmasDE = null;

	public function carregarFormulario ()
	{
		parent::carregarFormulario();

		$serieTurmasSelecionadas = 0;
		$instiTurmasSelecionadas = 0;
		$checkTurmasSelecionadas = array();

		$turmasDE = $this->_turmasDE;
		foreach ($turmasDE as $ktde => $vtde) {
			$rs = Core::registro('db')->query('SELECT t_id,t_nome,t_serie,t_instituicao FROM turmas WHERE t_id = '.$vtde.' LIMIT 1;');
			$qtdTDE = $rs->num_rows;
			if ($qtdTDE){
				while ($row = $rs->fetch_assoc()){
					$this->nomesTurmasSelecionadas[$row['t_id']] = $row['t_nome'];
					$serieTurmasSelecionadas = $row['t_serie'];
					$instiTurmasSelecionadas = $row['t_instituicao'];
					$checkTurmasSelecionadas[] = $row['t_serie'].$row['t_instituicao'];
				}
			}
		}

		$checkTurmasSelecionadas = array_unique($checkTurmasSelecionadas);
		if(count($checkTurmasSelecionadas)>1){
			Gerenciador_URL::habilitarAtualiacaoReferencia(false);
			Redirecionador::fixarEndereco(Gerenciador_URL::gerarLink('turmas', 'listar'));
			Core::modulo('redirecionador')->fixarMensagem(null, 'Transferência de Alunos...');
			Core::modulo('redirecionador')->adicionarFalha(null, 'As turmas selecionadas devem ser da mesma Série e Instituição.');
			Core::modulo('redirecionador')->redirecionarNenhum();

			return false;
		}

		$turmasDisponiveis = array();
		$rs2 = Core::registro('db')->query('SELECT * FROM turmas WHERE t_serie = '.$serieTurmasSelecionadas.' AND t_instituicao = '.$instiTurmasSelecionadas.';');
		$qtdTPARA = $rs2->num_rows;
		if ($qtdTPARA){
			while ($row2 = $rs2->fetch_assoc()){
				$tid = $row2['t_id'];
				if(!in_array($tid, $turmasDE)){
					$turmasDisponiveis[$tid] = '('.$tid.') '.$row2['t_nome'];
				}
			}
		}

		$turmaPARAselecionada = null;
		if (!$this->foiEnviado() && !isset($_POST['post_anterior']) && Core::diretiva('_turma_transferencia.turmaPARAselecionada') != false && array_key_exists(Core::diretiva('_turma_transferencia.turmaPARAselecionada'), $turmasDisponiveis))
			$turmaPARAselecionada = Core::diretiva('_turma_transferencia.turmaPARAselecionada');
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'turma_para',
												'etiqueta' => '(ID) Turma PARA:',
												'valor' => $turmaPARAselecionada,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($turmasDisponiveis)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $turmasDisponiveis,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
						  	)) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Enviar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 3
							  )) );
							  
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e){		
			throw new FTransferencia_Exception($e->getMessage());
		}
	}

	public function fixarTurmasDE ($de){
		$this->_turmasDE = $de;
	}
	
	public function executar ()
	{
		$turmaDE = $this->_turmasDE;
		$turmaPARA = (int) $this->_campos['turma_para']->obter('valor');

		if (is_array($turmaDE) && count($turmaDE) > 0 && is_numeric($turmaPARA) && !empty($turmaPARA) && $turmaPARA > 0){
			$turmaDE = '"'.implode('","',$turmaDE).'"';

			Core::registro('db')->query(sprintf('
				UPDATE alunos 
				SET a_turma = %s
				WHERE a_turma IN(%s)',
				Core::registro('db')->formatarValor($turmaPARA),
				$turmaDE)
			);

			Core::modulo('redirecionador')->adicionarSucesso(null, 'Sucesso na transferência dos alunos das turmas.<br>');
		}
		else{
			Core::modulo('redirecionador')->adicionarSucesso(null, 'Falha ao processar as turmas DE e/ou PARA.');
		}
	}
}

?>