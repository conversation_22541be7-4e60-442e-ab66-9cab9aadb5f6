<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio"><strong><?= $turma->obterNome(); ?></strong></div>

<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $turma->obterID())); ?>">Informações da turma </a></th>
    <td>&nbsp;</td>
    <th <?= $this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO) ? 'class="selecionada"' : ''; ?>><a href="<?= Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $turma->obterID(), $this->seletorVisao->nomeSeletor() => self::VISAO_MAIS_INFO)); ?>">Mais informa&ccedil;&otilde;es</a></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<?
if ($this->seletorVisao->visaoEstaSelecionada(self::VISAO_MAIS_INFO)) {
	$alunos = $this->_obterListaAlunos($turma);
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>
	
		<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		  <tr>
			<td><strong>Alunos</strong></td>
		  </tr>
	  	</table>
		
<?
	if (count($alunos)) {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
		  <td width="25%" class="lp_ColHeader">Nome</td>
		  <td width="25%" class="lp_ColHeader">Login (E-mail)</td>
		  <td class="lp_ColHeader">Matrícula</td>
		</tr>
<?
		foreach ($alunos as $aluno) {
?>
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
      <td><a title="Visualizar detalhes" href="<?= Gerenciador_URL::gerarLink('alunos', 'detalhar', array('id' => $aluno['id'])); ?>"><?= $aluno['nome']; ?></a></td>
		<td><a title="Enviar e-mail" href="mailto: <?= $aluno['login']; ?>"><?= $aluno['login']; ?></a></td>
		<td><?= $aluno['matricula']; ?></td>
    </tr>
<?
		}
?>
	</table>
<?
	} else {
?>
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr class="lp_ColData">
		  <td><em>Sem alunos </em></td>
	    </tr>
	</table>
<?
	}
?>

	<div class="vd_BlocoEspacador">
	<table width="100%" border="0" cellspacing="3" cellpadding="0" style="">
		  <tr>
			<td><strong>Hist&oacute;rico de registro </strong></td>
			<td align="right"><input type="button" value="Limpar hist&oacute;rico" class="botao novo"></td>
		  </tr>
	  </table>
		
	<table width="100%" border="0" cellpadding="3" cellspacing="1" class="lp">
		<tr>
		  <td width="10%" align="center" class="lp_ColHeader">Data</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Grupo</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Tipo</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Descri&ccedil;&atilde;o</td>
		  <td nowrap="nowrap" class="lp_ColHeader">Refer&ecirc;ncia</td>
		</tr>
		
    <tr onMouseOver="this.className='lp_ColDataHover'" onMouseOut="this.className='lp_ColData'" class="lp_ColData">
      <td align="center" nowrap="nowrap" >---</td>
		<td>TURMAS</td>
		<td>cria&ccedil;&atilde;o</td>
		<td>Turma cadastrada</td>
		<td><a href="<?= Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $turma->obterID())); ?>"><?= $turma->obterNome(); ?></a></td>
    </tr>
	</table>
	</div>

	</td>
  </tr>
</table>
<?
} else {
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

	<div class="vd_BlocoBotoes">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
	  <tr>
		<td><input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('turmas', 'editar', array('id' => $turma->obterID())); ?>'" value="Editar" class="botao editar"></td>
		<td align="right"><input type="button" onClick="if (confirm('Tem certeza que deseja REMOVER esse item?')) { window.location='<?= Gerenciador_URL::gerarLink('turmas', 'remover', array('id' => $turma->obterID())); ?>'; }" value="Remover" class="botao remover"></td>
	  </tr>
	</table>
	</div>

	<table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
        <tr>
          <th colspan="4" class="titulo">Informações da turma </th>
        </tr>
        <tr>
          <th>Nome</th>
          <td width="25%"><?= $turma->obterNome(); ?></td>
          <th>Número de alunos</th>
          <td><?= $turma->obterNumeroAlunos(); ?></td>
        </tr>
        <tr>
          <th>Instituição</th>
          <td><?= $turma->obterInstituicao()->obterNome(); ?></td>
        </tr>
      </table>
	
	</td>
  </tr>
</table>
<?
}
?>