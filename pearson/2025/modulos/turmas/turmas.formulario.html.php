<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio">
<?
if ($this->_formulario->obterEstado() == Formulario::ADICIONANDO) {
	echo '<strong>Adicionando turma</strong>';
} else {
	echo '<strong>'. $this->_formulario->obterDados()->obterNome() .'</strong> - Editando turma';
}
?>
</div>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th class="selecionada"><?= $this->_formulario->obterEstado() == Formulario::EDITANDO ? '<a href="'. Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $this->_formulario->obterDados()->obterID())) .'">Informações da turma</a>' : 'Informações da turma' ; ?></th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>
	
	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?> e então: <?= $this->_formulario->acoesPosEnvio->obterSeletorAcoes(true); ?>
	
	<input type="button" onClick="window.location='<?= $this->_formulario->obterEstado() == Formulario::EDITANDO ? Gerenciador_URL::gerarLink('turmas', 'detalhar', array('id' => $this->_formulario->obterDados()->obterID())) : Gerenciador_URL::gerarLink('turmas', 'listar'); ?>'" value="Cancelar" class="botao cancelar">
	</div>

  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
	<tr>
	  <th colspan="4" class="titulo">Informações da turma </th>
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('nome', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="25%"><?= $this->_formulario->obterHTML('nome', Formulario::HTML_CAMPO, true); ?> </td>
	  <th><?= $this->_formulario->obterHTML('serie', Formulario::HTML_LABEL, true); ?> </th>
	  <td><?= $this->_formulario->obterHTML('serie', Formulario::HTML_CAMPO, true); ?> </td>
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('instituicao', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="25%"><?= $this->_formulario->obterHTML('instituicao', Formulario::HTML_CAMPO, true); ?> </td>
	</tr>
  </table>

	</td>
  </tr>
</table>	
<?= $this->_formulario->obterHTML('id', Formulario::HTML_CAMPO, true); ?>
<?= $this->_formulario->acoesPosEnvio->obterCampoEditarEmOrdem(true); ?>
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>