<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('turmas.formulario.exception.php');

class FTurmas extends Formulario
{
	public function carregarFormulario ()
	{
		parent::carregarFormulario();
		
		$this->_carregar();
				
		$this->adicionarCampo( new Campo(array( 'nome' => 'nome',
												'etiqueta' => 'Nome',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::TAM_MAX => 50),
												'html_tamanho_maximo' => 50,
												'html_tipo' => Campo::HTML_TEXTO,
												'html_valor' => $this->_dados->obterNome(),
												'html_ordem' => 1
							  )) );
							  
		$series = Serie::obterArraySeriesParaFormulario();
		$seriesSelecionada = null;

		if ( !$this->foiEnviado() ) {
			if ($this->_dados->obterSerie() != null && array_key_exists($this->_dados->obterSerie()->obterID(), $series))
				$seriesSelecionada = $this->_dados->obterSerie()->obterID();
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'serie',
												'etiqueta' => 'Série',
												'valor' => $seriesSelecionada,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($series)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $series,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 2
							  )) );

		//$instituicoes = array( 'NULA' => 'Nenhuma' );		
		foreach ( Instituicao::obterArrayInstituicoesParaFormulario() as $k => $v ) {
			$instituicoes[$k] = $v;
		}

		$instituicoesSel = null;
		if ( !$this->foiEnviado() && $this->obterEstado() == self::EDITANDO ) {
			if ( $this->_dados->obterInstituicao()->obterID() != null ) {
				$instituicoesSel = $this->_dados->obterInstituicao()->obterID();
			}
		}

		$this->adicionarCampo( new Campo(array( 'nome' => 'instituicao',
												'etiqueta' => 'Instituição',
												'valor' => $instituicoesSel,
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($instituicoes)),
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $instituicoes,
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_ordem' => 3
							  )) );	
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'id',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_tipo' => Campo::HTML_ESCONDIDO,
												'html_valor' => $this->_dados->obterID()
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => 'Enviar',
												'requerimento' => Campo::REQUERIDO,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar',
												'html_ordem' => 3
							  )) );
							  
		$this->acoesPosEnvio->editarAcao('adicionar', 'Adicionar outra turma');		
		$this->acoesPosEnvio->prepararEdicaoEmOrdem('Editar próxima turma');
		$this->acoesPosEnvio->prepararSeletorAcoes();
	}
	
	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();
			
			// checa se a serie selecionada é valida
			$serie = new Serie($this->_campos['serie']->obter('valor'));
			
			if (!$serie->carregar()) {
				$this->_adicionarErro('serie', 'série selecionada é inválida;');
				throw new FTurmas_Exception('Série selecionada é inválida!');
			}
		}
		catch (Formulario_Exception $e)
		{		
			throw new FTurmas_Exception($e->getMessage());
		}
	}
	
	public function executar ()
	{
		if ($this->obterEstado() == self::ADICIONANDO) {
			if ( $this->_adicionar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Turma adicionada com sucesso!', 'Adicionando turma...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao adicionar a turma!', 'Adicionando turma...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		} else {
			if ( $this->_editar() ) {
				Core::modulo('redirecionador')->fixarMensagem('Turma editada com sucesso!', 'Editando turma...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_SUCESSO);
			} else {
				Core::modulo('redirecionador')->fixarMensagem('Ocorreu um erro ao editar a turma!', 'Editando turma...');
				Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			}
		}
		
		Core::fixarDiretiva('_turmas.serie.ultima_selecao', $this->_campos['serie']->obter('valor'));
		
		return $this->_dados;
	}
	
	protected function _carregar ()
	{
		$id = null;
		
		if (!$this->foiEnviado() && isset($_GET['id'])) {
			$id = $_GET['id'];
		} else {
			$id = (isset($_POST['id']) ? $_POST['id'] : null );
		}
		
		$this->_dados = new Turma($id);
		
		if ( $id != null ) {
			if ( $this->_dados->carregar() ) {// && $this->_dados->validarInstituicao() ) {
				$this->fixarEstado(self::EDITANDO);
			} else {
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('turmas', 'listar'), 'Turma inválida!');
			}
		} else {
			$this->fixarEstado(self::ADICIONANDO);
		}
	}
	
	protected function _adicionar ()
	{
		$this->_dados = Turma::obterNovaTurma( $this->_campos['nome']->obter('valor'), new Serie($this->_campos['serie']->obter('valor')), new Instituicao($this->_campos['instituicao']->obter('valor')) );
		
		return ($this->_dados->obterID() != null);
	}
	
	protected function _editar ()
	{	
		$this->_dados->fixarNome( $this->_campos['nome']->obter('valor') );
		$this->_dados->fixarSerie( new Serie($this->_campos['serie']->obter('valor')) );
		$this->_dados->fixarInstituicao(new Instituicao($this->_campos['instituicao']->obter('valor')));
		
		return $this->_dados->salvar();
	}	
}

?>