<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Listagem');

class LTurmas extends Listagem
{
	
	protected $_procuraSQL = null;
	
	public function prepararListagem ()
	{
		$this->_prepararBusca();
	
		$this->_prepararOrdenacao();
		
		$this->_montaProcuraSQL();
		
		$this->_preObterDados();
		
		$this->_prepararPaginacao();
		
		$this->_preparaTituloOrdenacao();
		
		$this->_obterDados();
		
		$this->_prepararFormulario();


		$this->_iniciarRenderizacao(Modulo::HTML);
			include('turmas.listagem.html.php');
		$this->_finalizarRenderizacao();
	}
	
	protected function _montaProcuraSQL ()
	{
		if ( Core::modulo('procurar_turmas')->procuraSolicitada() )
		{
			if ( Core::modulo('procurar_turmas')->termoProcurado != null && Core::modulo('procurar_turmas')->ondeProcurar != null ) {
				$termo = '%'. Core::registro('db')->escape(Core::modulo('procurar_turmas')->termoProcurado) .'%';				
				$this->_procuraSQL[] = sprintf('LCASE(%s) LIKE LCASE(%s)', Core::modulo('procurar_turmas')->ondeProcurar, Core::registro('db')->formatarValor( $termo ) );
			}
		
			if ( Core::modulo('procurar_turmas')->letraSelecionada != null ) {
				$this->_procuraSQL[] = 'UCASE(LEFT(t_nome, 1)) = '. Core::registro('db')->formatarValor(Core::modulo('procurar_turmas')->letraSelecionada);
			}
			
			if ( is_array($this->_procuraSQL) ) {
				$this->_procuraSQL = ' AND '. implode(' AND ', $this->_procuraSQL);
			}
		}
	}
	
	protected function _preObterDados ()
	{
		$inst = '';
		if(Core::registro('instituicao')->obterID()){
			$inst = ' AND t_instituicao = '. Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID());
		}

		$rs = Core::registro('db')->query( sprintf('
			SELECT COUNT(0) AS total FROM turmas JOIN series ON series.s_id = turmas.t_serie WHERE 1=1 %s %s',
			$inst,
			$this->_procuraSQL ) );
			
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$this->_total = $row['total'];
		}
		$rs->free();
	}
	
	protected function _obterDados ()
	{
		$inst = '';
		if(Core::registro('instituicao')->obterID()){
			$inst = ' AND t_instituicao = '. Core::registro('db')->formatarValor(Core::registro('instituicao')->obterID());
		}

		$rs = Core::registro('db')->query( sprintf(
			'SELECT *, (SELECT COUNT(0) FROM alunos WHERE a_turma = t_id) as numero_alunos FROM turmas 
			JOIN series ON series.s_id = turmas.t_serie 
			JOIN instituicoes ON instituicoes.i_id = turmas.t_instituicao 
			WHERE 1=1 %s %s 
			ORDER BY %s %s LIMIT %s', 
			$inst,
			$this->_procuraSQL,
			$this->_ordenacao->ordenarPor,
			$this->_ordenacao->tipoOrdem,
			$this->_paginacao->paginador->obterLimitesSQL() ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$obj = new Turma($row['t_id']);

				$obj->fixarNome($row['t_nome']);
				$obj->fixarNumeroAlunos($row['numero_alunos']);
				$obj->fixarInstituicao( new Instituicao($row['t_instituicao']) );
				$obj->obterInstituicao()->carregar();

				$serie = new Serie($row['s_id']);

				$serie->fixarNome($row['s_nome']);
				$serie->fixarInstituicao( new Instituicao($row['s_instituicao']) );

				$obj->fixarSerie($serie);

				$this->_dados[] = $obj;
			}
		}
		$rs->free();
	}
	
	protected function _prepararBusca ()
	{
		Core::carregarModulo(array('nome' => '_procurar', 'classe' => 'MProcurar', 'guardar_como' => 'procurar_turmas'));

		Core::modulo('procurar_turmas')->prepararProcura('form_procurar_turmas', 'caixa_procurar_turmas');
		Core::modulo('procurar_turmas')->configurarTermo();
		Core::modulo('procurar_turmas')->configurarOndeProcurar( array('t_nome' => 'Nome da turma', 's_nome' => 'Nome da série') );
		Core::modulo('procurar_turmas')->configurarAlfabeto();
		
		Core::modulo('procurar_turmas')->carregarProcura();
		
		Core::modulo('procurar_turmas')->prepararAtalho();
	}
	
	protected function _prepararOrdenacao()
	{
		Core::carregarModulo(array('nome' => '_ordenacao', 'classe' => 'MOrdenacao', 'guardar_como' => 'ord_turmas'));
		
		$this->_ordenacao = new Ordenacao_Padrao('form_ord_turmas');
		$this->_ordenacao->prepararOrdenacao();
		$this->_ordenacao->configurarCampoOrdenarPor( array('t_nome' => 'Nome', 'series.s_nome' => 'Série', 'instituicoes.i_nome' => 'Instituição', 'numero_alunos' => 'Número de alunos'), 't_nome' );
		$this->_ordenacao->configurarCampoTipoOrdem();
		$this->_ordenacao->configurarCampoPorPagina();
		$this->_ordenacao->configurarCampoPagina();	
		
		Core::modulo('ord_turmas')->fixarOrdenacao( $this->_ordenacao, '_ordenacao_padrao.html.php' );
		Core::modulo('ord_turmas')->carregarOrdenacao();
		Core::modulo('ord_turmas')->prepararAtalho(null, 'caixa_ord_turmas');
		
		if ( Core::modulo('procurar_turmas')->procuraSolicitada() ) {
			Core::modulo('ord_turmas')->anexarHTML( Core::modulo('procurar_turmas')->obterHTMLParaAnexar() );
		}
	}
	
	protected function _prepararPaginacao()
	{
		$this->_paginacao = Core::carregarModulo(array('nome' => '_paginacao', 'classe' => 'MPaginacao', 'guardar_como' => 'pag_turmas'));
		
		$pagOrdenado = new Paginador_Ordenado($this->_ordenacao);
		$pagOrdenado->prepararPaginacao($this->_total, $this->_ordenacao->porPagina, $this->_ordenacao->pagina);

		$this->_paginacao->paginar($pagOrdenado);
	}
	
	protected function _prepararFormulario()
	{
		$this->_formulario = new Formulario_De_Acoes(array('nome' => 'form_acao_turmas', 'acao' => Gerenciador_URL::gerarLink('turmas', 'listar')) );
		$this->_formulario->incluirChecadorDeEnvio();
		$this->_formulario->carregarFormulario();
		
		$ids = array();
		foreach ($this->_dados as &$d) {
			$ids[] = $d->obterID();
		}
		$this->_formulario->adicionarMultiplaSelecao($ids);
		$this->_formulario->adicionarBotao('remover', 'Remover', 'Tem certeza que deseja REMOVER todos os itens selecionados?', 'remover');
		$this->_formulario->adicionarBotao('editar_em_ordem', 'Editar em ordem', null, 'editar');
		$this->_formulario->adicionarBotao('transferencia', 'Transferência de Alunos', null, 'editar');
		
		if ($this->_formulario->foiEnviado()) {
			try
			{
				$this->_formulario->checarFormulario();
				
				$ids = $this->_formulario->obterIDsSelecionados();

				if ( $this->_formulario->obterBotaoEnviador() == 'remover' ) {					
					if (is_array($ids)) {
						Core::modulo('solicitado')->aRemoverTurmas($ids);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'editar_em_ordem' ) {
					if (is_array($ids)) {
						$this->_formulario->acoesPosEnvio->fixarProximosIDs($ids);
						Gerenciador_URL::habilitarAtualiacaoReferencia(false);
						Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('turmas', 'editar', array('id' => $this->_formulario->acoesPosEnvio->obterProximoID(), 'proximosids' => $this->_formulario->acoesPosEnvio->obterProximosIDs(true))) );
						Redirecionador::redirecionar(Redirecionador::HEADER);
					}
				} else if ( $this->_formulario->obterBotaoEnviador() == 'transferencia' ) {
					if (is_array($ids)) {
						Core::modulo('solicitado')->aTransferenciaAlunos($ids);
					}
				}
			}
			catch (Formulario_Exception $e) { }
		}
	}
	
	public function _preparaTituloOrdenacao ()
	{
		$totais = $this->_paginacao->paginador->obterLimitesArray();
		$totais[0]++;
		$totais[] = $this->_total;
		
		$this->_ordenacao->configurarTituloDeTotais('turmas', $totais);
	}
		
}

?>