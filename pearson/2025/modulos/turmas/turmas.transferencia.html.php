<? if (!defined('CORE_INCLUIDO')) { exit(); } ?>
<div class="vd_Titulo medio">
<?
if ($this->_formulario->obterEstado() == Formulario::ADICIONANDO) {
	echo '<strong>Transferência de Alunos</strong>';
} 
?>
</div>

<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<table border="0" cellspacing="0" cellpadding="0" id="vd_TabHeader">
  <tr>
  	<td>&nbsp;</td>
    <th class="selecionada">Transferência de Alunos</th>
    <td class="ultimo">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="vd_TabConteudo">
  <tr>
    <td>

<?
if ($this->_formulario->temErros()) {
?>
	<div class="vd_BlocoEspacadorErro" align="center"><?= $this->_formulario->obterHTML(null, Formulario::HTML_ERROS, true); ?></div>
<?
}
?>
	
	<div class="vd_BlocoBotoes" align="center">
	<?= $this->_formulario->obterHTML('enviar', Formulario::HTML_CAMPO, true); ?>
	
	<input type="button" onClick="window.location='<?= Gerenciador_URL::gerarLink('turmas', 'listar'); ?>'" value="Cancelar" class="botao cancelar">
	</div>

  <table width="100%" border="0" cellspacing="0" cellpadding="0" id="vd_Bloco">
	<tr>
	  <th colspan="4" class="titulo">Formulário</th>
	</tr>
	<tr>
	  <th>(IDs) Turmas DE:</th>
	  <td width="75%" colspan="3">
	  	<?php
	  		$arrNTS = $this->_formulario->nomesTurmasSelecionadas;
	  		foreach ($arrNTS as $arrNTSk => $arrNTSv) {
	  			echo '<li>('.$arrNTSk.') '.$arrNTSv.'</li>';
	  		}
	  	?>
	  </td>
	</tr>
	<tr>
	  <th><?= $this->_formulario->obterHTML('turma_para', Formulario::HTML_LABEL, true); ?> </th>
	  <td width="75%" colspan="3"><?= $this->_formulario->obterHTML('turma_para', Formulario::HTML_CAMPO, true); ?> 
	</tr>
  </table>
 	</td>
  </tr>
</table>	
<?= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>