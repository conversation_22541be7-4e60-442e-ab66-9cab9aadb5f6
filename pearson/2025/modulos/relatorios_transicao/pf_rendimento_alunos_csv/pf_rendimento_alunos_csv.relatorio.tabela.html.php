<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => true,
	'nao_quebrar_pagina' => false,
	'quebra_invisivel' => true,
	'forcar_rodape' => true
	//'footer' => '&nbsp;'
);

$tabela['titulo'] = $this->_relatorio->obterNome();
$tabela['descricao'] = sprintf('<strong>SECRETARIA MUNICIPAL DE EDUCA&Ccedil;&Atilde;O DE '.MUNICIPIO.'</strong>');

if(count($this->_dados)>0){
	echo"<style type='text/css'>body {margin-bottom: 0cm; padding-bottom: 0cm;}</style>";
	echo'<div style="margin:0cm auto; padding:0cm 0cm; width:100%; text-align: center; margin-bottom:1cm; ">';

	echo'<table width="100%" border="1" cellspacing="0" cellpadding="0">';

	/*foreach($this->_cabecalho as $ck => $cv){
		echo'<tr>';
		foreach($cv as $ck2 => $cv2){
			echo'<th class="titulo">'.$cv2.'</th>';
		}
		echo'</tr>';
	}*/

	foreach($this->_dados as $dk => $dv){
		echo'<tr>';
		foreach($dv as $dk2 => $dv2){
			echo'<td>'.$dv2.'</td>';
		}
		echo'</tr>';
	}

	echo'</table>';
	echo'<br/><br/>';
	echo'</div>';
}
else{
	echo"<style type='text/css'>body {margin-bottom: 0cm; padding-bottom: 0cm;}</style>";
	echo'<div style="margin:0cm auto; padding:0cm 0cm; width:100%; text-align: center;">';

	echo'<p>Não há dados para visualizar.</p>';
	echo'<br/><br/>';

	echo'</div>';
}

$tabela['extra'] = ob_get_clean(); ob_start();
$relHTML[] = $tabela;
?>