<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('modulos/relatorios_transicao/_detalhe_rendimento/_detalhe_rendimento.analisador.php');

class MDetalheRendimento extends Modulo {
	public function obterNovoDetalheRendimento (AnalisadorDetalheRendimento $analisador) {
		if ( $analisador == null || $analisador->obterTotal() == 0 )
			return null;
		
		$maior = @array_pop(array_keys($analisador->obterRendimentos(false), max($analisador->obterRendimentos(false))));
		$tabela = array();
		foreach ($analisador->obterRendimentos(true) as $k => $v)
			$tabela[] = "['$k', '$v', ". ($k == $maior ? 'true' : 'false') ."]";
			
		return sprintf( ' onmouseover="meu_TabelaDeRendimentoDetalhado.mostrarRendimentoDetalhado(%s, this);" onmouseout="meu_TabelaDeRendimentoDetalhado.esconder();" ',
						 implode_to_javascript($tabela) );
	}

	public function obterSaida() {
		Core::modulo('js')->incluirArquivo('modulos/relatorios_transicao/_detalhe_rendimento/_detalhe_rendimento.js');

		$this->_iniciarRenderizacao(Modulo::HTML);
			include('_detalhe_rendimento.html.php');
		$this->_finalizarRenderizacao();
		
		return parent::obterSaida();		
	}
}

?>