<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class AnalisadorDetalheRendimento {
	const ZERO_A_VINTE = '0% a 20%';
	const VINTE_A_QUARENTA = '21% a 40%';
	const QUARENTA_A_SESSENTA = '41% a 60%';
	const SESSENTA_A_OITENTA = '61% a 80%';
	const OITENTA_A_CEM = '81% a 100%';
	
	protected $_total = 0;
	protected $_rendimentos;
	protected $_desemepenho_individual = array();
	
	public function __construct () {
		$this->_rendimentos = array(self::ZERO_A_VINTE => 0,
									self::VINTE_A_QUARENTA => 0,
									self::QUARENTA_A_SESSENTA => 0,
									self::SESSENTA_A_OITENTA => 0,
									self::OITENTA_A_CEM => 0);
	}
	
	public function adicionarRendimento($rendimento) {
		$rendimento = (int) $rendimento;
		
		if ($rendimento <= 20)
			$this->_rendimentos[self::ZERO_A_VINTE]++;
		else if ($rendimento <= 40)
			$this->_rendimentos[self::VINTE_A_QUARENTA]++;
		else if ($rendimento <= 60)
			$this->_rendimentos[self::QUARENTA_A_SESSENTA]++;
		else if ($rendimento <= 80)
			$this->_rendimentos[self::SESSENTA_A_OITENTA]++;
		else
			$this->_rendimentos[self::OITENTA_A_CEM]++;
			
		$this->_total++;
	}
	
	public function adicionarAoRendimentoIndividual ($id, $desempenho) {
		if (!isset($this->_desemepenho_individual[$id]))
			$this->_desemepenho_individual[$id] = array('total_pontos' => 0, 'rendimento' => 0, 'pontos_questao' => 0);
			
		$this->_desemepenho_individual[$id]['pontos_questao'] += $desempenho['pontos_questao'];
		$this->_desemepenho_individual[$id]['total_pontos'] += $desempenho['pontos'];
		$this->_desemepenho_individual[$id]['rendimento'] += $desempenho['rendimento'] * $desempenho['pontos_questao'];
	}
	
	public function calcularRendimentoIndividual ($id, $adicionar = true) {
		if (isset($this->_desemepenho_individual[$id]) && $this->_desemepenho_individual[$id]['pontos_questao'] > 0) {
			$this->_desemepenho_individual[$id]['rendimento'] /= $this->_desemepenho_individual[$id]['pontos_questao'];
			
			if ($adicionar)
				$this->adicionarRendimento($this->_desemepenho_individual[$id]['rendimento']);
		}
	}
	
	public function obterRendimentoIndividual ($id) {
		if (!isset($this->_desemepenho_individual[$id]))
			return 0;

		return $this->_desemepenho_individual[$id]['rendimento'];
	}
	
	public function obterRendimentos ($formatado = true) {
		if (!$formatado || $this->_total == 0)
			return $this->_rendimentos;
		
		$resultado = array();
		foreach ($this->_rendimentos as $k => $v)
			$resultado[$k] = sprintf('%s (%d %%)', $v, 100 * $v / $this->_total);
			
		return $resultado;
	}
	
	public function obterTotal () {
		return $this->_total;
	}
}
?>