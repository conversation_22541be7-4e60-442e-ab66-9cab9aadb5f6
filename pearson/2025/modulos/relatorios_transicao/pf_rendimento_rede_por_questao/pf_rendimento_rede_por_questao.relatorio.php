<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoRedePorQuestao extends RelatorioListagemTransicao {
	const GRAFICO_COR = '#29476c';
	const GRAFICO_COR_TEXTO = '#ffffff';

	public $orientacao_PDF = ExportadorPDF::RETRATO;

	protected $_questoesPorDisciplina = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_questao' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_questao' => array(),
			'_rede' => false
		);

		$this->_config = array(

		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$pagina_atual = 0;
			$dados_mostrar = array();
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == 999 || !$total) {
					include 'pf_rendimento_rede_por_questao.relatorio.tabela.html.php';
					$dados_mostrar = array();
					$pagina_atual++;
				}
			}

			include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		$total = count($this->_dados);
		$pagina_atual = 0;
		$dados_mostrar = array();
		foreach ( $this->_dados as $k => &$d ) {
			$dados_mostrar[] = $k;
			$total--;

			if (count($dados_mostrar) == 999 || !$total) {
				include 'pf_rendimento_rede_por_questao.relatorio.tabela.html.php';
				$dados_mostrar = array();
				$pagina_atual++;
			}
		}

		include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, ExportadorPDF::RETRATO, $pasta);
	}

	protected function _ajustarParametros () {
		$this->_sugestao['rendimento'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		/*
		// coloca a rede antes
		$idREDE = null;
		foreach ($this->_dados as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados[$idREDE])) {
			$rede = $this->_dados[$idREDE];
			unset($this->_dados[$idREDE]);
			array_unshift($this->_dados, $rede);
		}
		*/

		$this->gerarGrafico();
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_transicao/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			$this->_dados = &$tempCache['dados'];
			$this->_questoesPorDisciplina = &$tempCache['_questoesPorDisciplina'];
			return;
		}


		$this->_analizadorSimulado->carregarInscritos(true);
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		//$this->_analizadorSimulado->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado->calcularRendimentoPorSerie();
		if ( $this->_colunas['rendimento_por_questao'] ) {
			//$this->_analizadorSimulado->calcularRendimentoPorInstituicaoPorQuestao();
			$this->_analizadorSimulado->calcularRendimentoPorSeriePorQuestao();
		}


		// QUESTOES POR DISCIPLINA
		foreach ( $this->_analizadorSimulado->simuladoQuestoesPorID as $qID => &$q) {
			$dID = $q->obterDisciplina()->obterID();

			if (!isset($this->_questoesPorDisciplina[$dID]))
				$this->_questoesPorDisciplina[$dID] = array();

			$this->_questoesPorDisciplina[$dID][] = $qID;
		}


/*
		// ESCOLAS
		foreach($this->_analizadorSimulado->nomesInstituicoes as $instID => $instNome) {
			$dado = $this->_modelo;

			$dado['nome'] = $instNome;

			if (isset($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( $this->_colunas['rendimento_por_questao'] && isset( $this->_analizadorSimulado->rendimentoPorInstituicaoPorQuestao[$instID] ) ) {
				foreach ( $this->_analizadorSimulado->rendimentoPorInstituicaoPorQuestao[$instID] as $qID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados[] = $dado;
		}
*/

		// SERIE
		foreach($this->_analizadorSimulado->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			$dado['rendimento'] = 0;
			if (isset($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR QUESTAO
			if ( $this->_colunas['rendimento_por_questao'] && isset( $this->_analizadorSimulado->rendimentoPorSeriePorQuestao[$sID] ) ) {
				foreach ( $this->_analizadorSimulado->rendimentoPorSeriePorQuestao[$sID] as $qID => $desempenho ) {
					$dado['rendimento_por_questao'][$qID] = 0;
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_questao'][$qID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados[] = $dado;
		}

		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados,
			'_questoesPorDisciplina' => $this->_questoesPorDisciplina
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		$total = count($this->_dados);
		$dados_mostrar = array();
		$pagina_atual = 0;
		foreach ( $this->_dados as $k => &$d ) {
			$dados_mostrar[] = $k;
			$total--;

			if (count($dados_mostrar) == 9999 || !$total) {


				$grafico = new Graph(700, 700, $this->_tituloCache.'_'.$pagina_atual.'.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetMargin(34, 0, 10, 30);
				$grafico->setFrame(false);
				//$grafico->title->Set('REDE');
				//$grafico->SetMarginColor('white');
				$grafico->SetScale('textint', 0, 100, 0, 0);
				$grafico->yaxis->scale->ticks->Set(20);
				$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$grafico->yaxis->SetLabelFormat('%d%%');
				//$grafico->yaxis->title->Set('Quantidade de alunos (%)');
				$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
				$grafico->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);
				$grafico->Set90AndMargin(150, 5, 5, 5);


				$labelsX = $barra = $dados = array();

				foreach($this->_dados as $k => &$d) {
					if (!in_array($k, $dados_mostrar) || !$d['_rede']) continue;

					if(count($d['rendimento_por_questao'])>0){
						foreach( $d['rendimento_por_questao'] as $qID => $rendimento ) {
							$identificador = $this->_analizadorSimulado->simuladoQuestoesPorID[$qID]->obterIdentificador();
							$identificador = str_replace(') ',")\n",$identificador);
							$labelsX[] = $identificador;
							$dados[] = $rendimento;
						}
					}
					else{
						$labelsX[] = '';
						$dados[] = '0';
					}
				}

				$grafico->xaxis->SetTickLabels($labelsX);

				$barra = new BarPlot( $dados );
				$barra->SetFillColor( self::GRAFICO_COR );
				$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
				$barra->value->Show();
				$barra->value->SetFormat('%d%%');
				//$barra->value->SetColor( self::COR_TEXTO );
				$barra->SetValuePos('center');
				$barra->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$barra->SetWidth(0.6);

				$grafico->Add($barra);
				$grafico->Stroke();


				$dados_mostrar = array();
				$pagina_atual++;
			}
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>