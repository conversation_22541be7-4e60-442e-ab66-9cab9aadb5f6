<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome() .'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento da rede por aspecto da produção textual'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );;

}
$tabela['titulo']  = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong>'
);

/*
ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="25%"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Prova') ;?> </th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_questao'] ) {
	foreach ( $this->_analizadorSimulado->nomesQuestoes as $qID => $qNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $qNome ;?> </th>
<?
	}
}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean();*/ ob_start();

if ( count($this->_dados) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'_'. $pagina_atual .'.png" border="0" />';

	/*
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr <?= $d['_rede'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> nowrap="nowrap"><?= @$d['nome']; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_questao'] ) {
			foreach ( $this->_analizadorSimulado->nomesQuestoes as $qID => $qNome ) {
				if (isset( $d['rendimento_por_questao'][$qID] ))
					echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : '') .' align="center">'. $d['rendimento_por_questao'][$qID] .'%</td>';
				else
					echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : '') .'>&nbsp;</td>';
			}
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
	*/
}

$tabela['td'] = ob_get_clean(); ob_start();
?>
	<div style="margin-top: 5px; border: 1px #cccccc solid; padding: 0px;">
		<?
		$discNomes = $this->_analizadorSimulado->nomesDisciplinas;
		ksort($discNomes);

		foreach ( $discNomes as $dID => $dNome ) {
			if (!count($this->_questoesPorDisciplina[$dID])) continue;

			echo ''. $dNome . ': ';
			$questoesTemp = array();
			foreach ( $this->_questoesPorDisciplina[$dID] as $qID)
				$questoesTemp[] = $this->_analizadorSimulado->nomesQuestoes[$qID];

			echo implode(', ', $questoesTemp) . '<br />';
		}
		?>
	</div>
<?
$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>