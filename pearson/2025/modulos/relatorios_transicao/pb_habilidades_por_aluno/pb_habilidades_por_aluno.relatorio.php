<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);
Core::incluir('AnalizadorSimuladoProvinhaBrasil', 'ProvinhaBrasil/', true);
Core::incluir('ProvinhaBrasil', null, true);

class RLPBHabilidadesPorAluno extends RelatorioListagemTransicao {
	const GRAFICO_COR_TEXTO = '#ffffff';

	public $_por_pagina = 28;

	protected $_turmas_mostrar = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_habilidade' => true
		);

		$this->_modelo = array(
			'turma' => null,
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_habilidade' => array(),
			'_inscricao' => null,
			'_turma' => false
		);

		$this->_config = array(
			'professor' => null
		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvinhaBrasil();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

                $this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		if ($this->obterModoVisualizacao() != self::PDF)
			$this->_por_pagina = 20;

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();
		$total_mostrar = 0;
			foreach($this->_turmas_mostrar as $tID => $tNome) {
				$turma = $tNome;
				foreach ( $this->_dados as $k => &$d ) {
					if ($d['turma'] == $turma->obterNome() && $d['rendimento'] != NULL) {
						$total_mostrar++;
					}
				}
			}

			foreach($this->_turmas_mostrar as $tID => $tNome) {
				$turma = $tNome;

				$total = count($this->_dados);
				$dados_mostrar = array();
				$mostrados = 0;
				foreach ( $this->_dados as $k => &$d ) {
					$total--;
					if ($d['turma'] == $turma->obterNome() && $d['rendimento'] != NULL)
						$dados_mostrar[] = $d;

					if (count($dados_mostrar) == 999 || !$total) {
						$mostrados += count($dados_mostrar);
						include 'pb_habilidades_por_aluno.relatorio.tabela.html.php';
						$dados_mostrar = array();
					}
				}
			}

		    include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);
		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		if ($this->obterModoVisualizacao() != self::PDF)
			$this->_por_pagina = 20;

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();
		$total_mostrar = 0;
		foreach($this->_turmas_mostrar as $tID => $tNome) {
			$turma = $tNome;
			foreach ( $this->_dados as $k => &$d ) {
				if ($d['turma'] == $turma->obterNome() && $d['rendimento'] != NULL) {
					$total_mostrar++;
				}
			}
		}

		foreach($this->_turmas_mostrar as $tID => $tNome) {
			$turma = $tNome;

			$total = count($this->_dados);
			$dados_mostrar = array();
			$mostrados = 0;
			foreach ( $this->_dados as $k => &$d ) {
				$total--;
				if ($d['turma'] == $turma->obterNome() && $d['rendimento'] != NULL)
					$dados_mostrar[] = $d;

				if (count($dados_mostrar) == 999 || !$total) {
					$mostrados += count($dados_mostrar);
					include 'pb_habilidades_por_aluno.relatorio.tabela.html.php';
					$dados_mostrar = array();
				}
			}
		}

		include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo,ExportadorPDF::RETRATO, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR &&
			Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			$this->_config['professor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( CarregadorUsuarioEspecifico::obterProfessor() == null )
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_transicao', 'listar'), 'Professor inválido!');
			else
				$this->_config['professor'] = CarregadorUsuarioEspecifico::obterProfessor();
		}

		$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmasComAulas();

		$this->_sugestao['rendimento'] = Ordenacao::ASC;
		$this->_sugestao['nome'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		//if ( $this->_seletorSimulados->simulado->obterID() == null )
		//	return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$prof = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			//$turmasProf = array_keys($prof->obterArrayTurmasComAulas());
			//$turma = new Turma($turmasProf[0]);//$_GET['turma_mostrar']);
		$turma = Core::modulo('_perfil_falso')->_perfil_turma;

		//$turma = new Turma($_GET['turma_mostrar']);
		$turmas = array();
		$turma->carregar();
		$turmas[0] = $turma;
		$this->_turmas_mostrar = $turmas;
		//$this->_turmas_mostrar = $this->_config['professor']->obterArrayTurmaPorSimulado($this->_analizadorSimulado->simulado->obterID());

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		// coloca alunos sem notas depois
		$ids_alunos = array();
		foreach ($this->_dados as $k => &$d) {
			if ($d['rendimento'] == null)
				$ids_alunos[] = $k;
		}

		foreach($ids_alunos as $k) {
			if (isset($this->_dados[$k])) {
				$obj = $this->_dados[$k];
				unset($this->_dados[$k]);
				$this->_dados[] = $obj;
			}
		}

		$this->gerarGrafico();
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
			//	continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_transicao/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao, true);
		//$this->_seletorSimulados->simulado = ProvinhaBrasil::$simulado;
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

		if ( $this->_config['professor'] !== null )
			$this->_tituloCache .= '_pro'.$this->_config['professor']->obterID();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			//$this->_dados = &$tempCache['dados'];
			//return;
		}

        $idTurmas = array();
		$idTurmas[0] = $this->_turmas_mostrar[0]->obterID();
		$this->_analizadorSimulado->carregarInscritosPorInstituicao($this->_turmas_mostrar[0]->obterInstituicao()->obterID());
		$this->_analizadorSimulado->limitarInscritosPorTurmas($idTurmas);
		$this->_analizadorSimulado->carregarRespostasDosInscritos();

		//$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();

		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorHabilidadeDosInscritos();

		foreach ( $this->_analizadorSimulado->inscritos as $iID => $inscricao ) {
			$dado = $this->_modelo;

			$dado['nome'] = @$inscricao->obterAluno()->obterUsuario()->obterNome();
			$dado['turma'] = @$inscricao->obterAluno()->obterTurma()->obterNome();
			$dado['_inscricao'] = $inscricao;

			if ( count($this->_analizadorSimulado->respostas[$iID]) ) {
				$dado['rendimento'] = $this->_analizadorSimulado->rendimentoGlobal[$iID]['total_pontos'];

				$dado['rendimento_por_habilidade'] = $this->_analizadorSimulado->rendimentoPorHabilidade[$iID];
			}			

			$this->_dados[] = $dado;
		}

		// elimina inscritos pra calcular por turma
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		//$this->_analizadorSimulado->calcularRendimentoPorTurmaPorHabilidade();



		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	protected function _ordenarTitulosQuestoesPorTurma ($a, $b) {
		return Ordenacao_Relatorios::ordenarNumero($a['rendimento'], $b['rendimento'], Ordenacao_Relatorios::ASC);
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';


		$proporcaoPorHabilidadePorTurma = array();
		foreach($this->_dados as $k => &$d) {
			if ($d['_inscricao']->obterAluno()->obterPortadorNecessidade())
				continue;

			if (!isset($proporcaoPorHabilidadePorTurma[$d['turma']]))
				$proporcaoPorHabilidadePorTurma[$d['turma']] = array();

			foreach ($this->_analizadorSimulado->nomesHabilidades as $hID => $hNome ) {
				if (isset($d['rendimento_por_habilidade'][$hID])) {
					if (!isset($proporcaoPorHabilidadePorTurma[$d['turma']][$hID]))
						$proporcaoPorHabilidadePorTurma[$d['turma']][$hID] = array(
							ProvinhaBrasil::HAB_ADQUIRIDA => 0,
							ProvinhaBrasil::HAB_EM_AQUISICAO => 0,
							ProvinhaBrasil::HAB_NAO_ADQUIRIDA => 0,
						);

					if ($d['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_EM_AQUISICAO_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d['turma']][$hID][ProvinhaBrasil::HAB_ADQUIRIDA]++;
					elseif ($d['rendimento_por_habilidade'][$hID]['rendimento'] >= ProvinhaBrasil::HAB_NAO_ADQUIRIDA_PORCENTAGEM)
						$proporcaoPorHabilidadePorTurma[$d['turma']][$hID][ProvinhaBrasil::HAB_EM_AQUISICAO]++;
					else
						$proporcaoPorHabilidadePorTurma[$d['turma']][$hID][ProvinhaBrasil::HAB_NAO_ADQUIRIDA]++;
				}
			}
		}

		$proporcaoPorHabilidadePorTurmaAbs = array();
		foreach($proporcaoPorHabilidadePorTurma as $turma => &$proporcoesPorTurma) {
			foreach($proporcoesPorTurma as $hID => &$proporcoes) {
				$proporcaoPorHabilidadePorTurmaAbs[$turma][$hID] = $proporcoes;

				$proporcoes = self::calcularPorcentagemPerfeita($proporcoes);
			}
		}

		foreach($this->_turmas_mostrar as $tID => $tNome) {


				$grafico = new Graph( 600, 800, $this->_tituloCache.'_'.$tID.'.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetMargin(34, 0, 10, 30);
				$grafico->setFrame(false);
				//$grafico->title->Set('REDE');
				//$grafico->SetMarginColor('white');
				$grafico->SetScale('textint', 0, 100, 0, 0);
				$grafico->yaxis->scale->ticks->Set(20);
				$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$grafico->yaxis->SetLabelFormat('%d%%');
				//$grafico->yaxis->title->Set('Quantidade de alunos (%)');
				$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
				$grafico->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);
				$grafico->Set90AndMargin(140, 5, 5, 5);

				$labelsX = $barras = array();
				$dadosProporcoes = array(
					ProvinhaBrasil::HAB_ADQUIRIDA => array(),
					ProvinhaBrasil::HAB_EM_AQUISICAO => array(),
					ProvinhaBrasil::HAB_NAO_ADQUIRIDA => array()
				);

				foreach($proporcaoPorHabilidadePorTurma[$tNome->obterNome()] as $hID => &$proporcoes) {
					$labelsX[] = $this->_analizadorSimulado->nomesHabilidades[$hID];

					$dadosProporcoes[ ProvinhaBrasil::HAB_ADQUIRIDA ][] = $proporcoes[ProvinhaBrasil::HAB_ADQUIRIDA];
					$dadosProporcoes[ ProvinhaBrasil::HAB_EM_AQUISICAO ][] = $proporcoes[ProvinhaBrasil::HAB_EM_AQUISICAO];
					$dadosProporcoes[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ][] = $proporcoes[ProvinhaBrasil::HAB_NAO_ADQUIRIDA];
				}

				if (!count($labelsX))
					continue;

				$grafico->xaxis->SetTickLabels($labelsX);

				$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_ADQUIRIDA ] );
				$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ]->SetFillColor(ProvinhaBrasil::HAB_ADQUIRIDA_COR);

				$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_EM_AQUISICAO ] );
				$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ]->SetFillColor(ProvinhaBrasil::HAB_EM_AQUISICAO_COR);

				$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] = new BarPlot( $dadosProporcoes[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ] );
				$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]->SetFillColor(ProvinhaBrasil::HAB_NAO_ADQUIRIDA_COR);

				/*
				$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ]->value->SetColor( self::GRAFICO_HAB_ADQUIRIDA_TEXTO_COR );
				$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ]->value->SetColor( self::GRAFICO_HAB_EM_AQUISICAO_TEXTO_COR );
				$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]->value->SetColor( self::GRAFICO_HAB_NAO_ADQUIRIDA_TEXTO_COR );
				*/

				foreach ($barras as &$barra) {
					$barra->value->Show();
					$barra->value->SetFormat('%d%%');
					$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
					$barra->SetValuePos('center');
					$barra->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
				}


				// new GroupBarPlot
				$gruposBarras = new AccBarPlot( array(
					$barras[ ProvinhaBrasil::HAB_ADQUIRIDA ],
					$barras[ ProvinhaBrasil::HAB_EM_AQUISICAO ],
					$barras[ ProvinhaBrasil::HAB_NAO_ADQUIRIDA ]
				) );

				$gruposBarras->SetWidth(0.64);

				$grafico->Add($gruposBarras);
				$grafico->Stroke();

		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento',
												 'rendimento_questoes' ),
							 'linhas' => array(),
							 'config' => array( 'professor' => true, 'ordenar_questoes' => false ) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>