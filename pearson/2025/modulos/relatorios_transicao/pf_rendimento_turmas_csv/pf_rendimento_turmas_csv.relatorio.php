<?php
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('ExportadorCVS', 'Exportador/');

class RLPFRendimentoTurmasCSV extends RelatorioListagemTransicao 
{
	public $_cabecalho = array(array(
		'ESCOLA',
		'ANO',
		'TURMA',
		'NOME ALUNO',
		'ESCRITA',
		'FLUÊNCIA LEITORA',
		'MATEMÁTICA',
	));
	public $_dados = array();

	public function __construct () 
	{
		parent::__construct();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);
		$this->_prepararPainel();		
		$this->_obterDados();
		$this->_prepararFormulario();	

		if ($this->obterModoVisualizacao() == self::PDF) { 
			$this->fixarModoVisualizacao(self::NORMAL);
		}

		$this->_autoEsconderComponentes();	
		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();
		include 'pf_rendimento_turmas_csv.relatorio.tabela.html.php';
		include 'modulos/relatorios/relatorio.pf'.$this->obterModoVisualizacao().'.html.php';

		$this->_finalizarRenderizacao();

		if ($this->obterModoVisualizacao() == self::XLS) {
			$exportador = new ExportadorRLPFRendimentoTurmasCSV();
			$exportador->exportar($this->_cabecalho, $this->_dados);
			
			$this->_iniciarRenderizacao($exportador->obterTipoSaida());
				$exportador->obterSaida($relatorio->obterNome().'.csv');
			$this->_finalizarRenderizacao();
		}
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		return false; // não tem saida de pdf para bot.
	}

	protected function _obterDados () 
	{
		/*echo phpversion();
		
		echo'<br>--->+++++++++++<---<br><br>';
		
		echo'->';PRINT_R(get_cfg_var('display_errors'));echo';<br>';
		echo'->';PRINT_R(error_reporting());echo';<br>';
		
		error_reporting(E_ALL);
		ini_set("display_errors", 1);
		
		echo'<br>--->+++++++++++<---<br><br>';
		
		echo'->';PRINT_R(get_cfg_var('display_errors'));echo';<br>';
		echo'->';PRINT_R(error_reporting());echo';<br>';
		
		echo'<br>--->+++++++++++<---<br><br>';*/

		$db = Core::registro('db');

		$escolas = array();
		$r0 = $db->query("SELECT * FROM instituicoes", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$escolas[$rr0['i_id']] = $rr0;
			}
			//echo'2---><pre>';print_r($escolas);echo'</pre><br>---<br>';
		}
		
		$series = array();
		$r0 = $db->query("SELECT * FROM series", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$series[$rr0['s_id']] = $rr0;
			}
			//echo'2---><pre>';print_r($series);echo'</pre><br>---<br>';
		}
		
		$turmas = array();
		$r0 = $db->query("SELECT * FROM turmas", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$turmas[$rr0['t_serie']][$rr0['t_id']] = $rr0;
			}
			//echo'2---><pre>';print_r($turmas);echo'</pre><br>---<br>';
		}
		
		$avaliacoes = array();
		$r0 = $db->query("SELECT * FROM simulados ORDER BY s_nome ASC", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$sid = $rr0['s_id'];
				$avaliacoes[$rr0['s_serie']][$sid] = $rr0;
			}
		}

		foreach ($series as $sk => $serie) {
			$DADOS_LINHA = array();
			$DADOS_LINHA[] = '';
			$DADOS_LINHA[] = $serie['s_nome']; //SERIE
			$DADOS_LINHA[] = '';
			for($i=0; $i<count($avaliacoes[$sk]); $i++){
				if($i==0){
					$DADOS_LINHA[] = 'PORCENTAGEM DE ALUNOS QUE ATINGIU A META';
				}
				else{
					$DADOS_LINHA[] = '';
				}
			}
			$DADOS_LINHA[] = 'META ATINGIDA?';
			$this->_dados[] = $DADOS_LINHA;	

			$DADOS_LINHA = array();
			$DADOS_LINHA[] = '';
			$DADOS_LINHA[] = '';
			$DADOS_LINHA[] = '';
			foreach($avaliacoes[$sk] as $ak => $rr4){
				$DADOS_LINHA[] = $rr4['s_nome']; //AVALIACAO
			}
			$DADOS_LINHA[] = '';
			$this->_dados[] = $DADOS_LINHA;	
			
			foreach ($turmas[$sk] as $tk => $turma) {
				$DADOS_LINHA = array();
				$DADOS_LINHA[] = $escolas[$turma['t_instituicao']]['i_nome']; //ESCOLA
				$DADOS_LINHA[] = $serie['s_nome']; //ANO
				$DADOS_LINHA[] = $turma['t_nome']; //TURMA

				$totalNaMeta = 0;
				$metaSim = 0;
				foreach($avaliacoes[$sk] as $ak => $rr4){
					$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
					$this->_analizadorSimulado->fixarSimulado(new Simulado($ak));
					$this->_analizadorSimulado->carregarInscritos(true);
					$this->_analizadorSimulado->limitarInscritosPorTurmas(array(0=>$tk));
					$this->_analizadorSimulado->carregarRespostasDosInscritos();
					$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
					//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
					$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
					$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();

					$serieNome = $serie['s_nome'];
					$disciplinas = $this->_analizadorSimulado->nomesDisciplinas;
					$questoes = $this->_analizadorSimulado->nomesQuestoes;
					$FK = array_keys($disciplinas);

					$totalInscritos = 0;//count($this->_analizadorSimulado->inscritos);
					$metaAluno = 0;
					$metaSim = 'N&atilde;o';

					foreach ($this->_analizadorSimulado->inscritos as $ik => $iv) {
						$rArr = $iv->obterRespostasArray();
						if(count($rArr)<=0){
							continue;
						}

						if($FK[0] == 1 && count($disciplinas) == 1){ //escrita (qual questão?)
							foreach($questoes as $qid => $qnome){
								if($qnome === '01_lp'){
									$rendimento = $this->_analizadorSimulado->rendimentoPorQuestao[$ik][$qid]['pontos'];
									$resultadosAlunos = round($rendimento);

									/*
										Max: 5 pontos
										Ref: Documento_norteador_-_metas_-_Programa_Cruzada_pela_Educacao_-_2022_versao_final.pdf
										Pag: 5 e 12
									*/
									if($serieNome == '2FASE' && $resultadosAlunos >= 4){//2fase
										$metaAluno++;
									}
									/*
										Max: 5 pontos
										Ref: Documento_norteador_-_metas_-_Programa_Cruzada_pela_Educacao_-_2022_versao_final.pdf
										Pag: 5 e 23
									*/
									elseif($serieNome == '1ANO' && $resultadosAlunos >= 5){//1ano - ortografico
										$metaAluno++;
									}
									/*
										Max: 5 pontos
										Ref: Documento_norteador_-_metas_-_Programa_Cruzada_pela_Educacao_-_2022_versao_final.pdf
										Pag: 5
									*/
									elseif($serieNome == '2ANO' && $resultadosAlunos >= 5){//2ano - alfabetizados com hipótese ortográfica
										$metaAluno++;
									}

									break;
								}
								elseif($qnome === 'GLOBAL'){ //fluencia leitora
									$rendimento = $this->_analizadorSimulado->rendimentoPorQuestao[$ik][$qid]['pontos'];
									$resultadosAlunos = round($rendimento);

									/*
										Max: 6 pontos
										Ref: Documento_norteador_-_metas_-_Programa_Cruzada_pela_Educacao_-_2022_versao_final.pdf
										Pag: 5 e 16
									*/
									if($serieNome == '2FASE' && $resultadosAlunos == 6){//2fase
										$metaAluno++;
									}
									elseif($serieNome == '1ANO' && $resultadosAlunos == 1){//1ano - Leitor Iniciante
										$metaAluno++;
									}
									elseif($serieNome == '2ANO' && $resultadosAlunos == 1){//2ano - Leitor Fluente
										$metaAluno++;
									}

									break;
								}
							}
						}
						elseif($FK[0] == 2){ //matematica
							$rendimento = $this->_analizadorSimulado->rendimentoGlobal[$ik]['rendimento'];
							$resultadosAlunos = round($rendimento);

							/*
								Base de corte
									2° fase 75%
									1° ano 70%
									2° ano 60%
							*/
							if($serieNome == '2FASE' && $resultadosAlunos >= 75){//2fase
								$metaAluno++;
							}
							elseif($serieNome == '1ANO' && $resultadosAlunos >= 70){//1ano - Leitor Iniciante
								$metaAluno++;
							}
							elseif($serieNome == '2ANO' && $resultadosAlunos >= 60){//2ano - Leitor Fluente
								$metaAluno++;
							}
						}

						$totalInscritos++;
					}
					
					$porcentagemTurma = 0;
					if($metaAluno>0){
						$porcentagemTurma = round(($metaAluno/$totalInscritos)*100);
						if($porcentagemTurma >= 85){
							$totalNaMeta++;
						}
					}
					
					$DADOS_LINHA[] = $porcentagemTurma; //RESULTADO
				}

				if($totalNaMeta >= 3){
					$metaSim = 'Sim ('.$totalNaMeta.')';
				}
				else{
					$metaSim = 'N&atilde;o ('.$totalNaMeta.')';
				}
				$DADOS_LINHA[] = $metaSim; //RESULTADO

				$this->_dados[] = $DADOS_LINHA;
			}
			
			$DADOS_LINHA = array();
			
			$DADOS_LINHA[] = '';
			$DADOS_LINHA[] = '';
			$DADOS_LINHA[] = '';
			foreach($avaliacoes[$sk] as $ak => $rr4){
				$DADOS_LINHA[] = ''; //AVALIACAO
			}
			$DADOS_LINHA[] = '';
			$this->_dados[] = $DADOS_LINHA;
		}

		//ECHO'-----><PRE>';
		//PRINT_R($this->_dados);
		//ECHO'</PRE><BR>---<BR>';
		//exit;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML('modulos/relatorios/_painel_relatorio/_painel_relatorio.html.php');

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_'.$this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		Core::modulo('painel_relatorio')->fixarOrdenacao($this->_ordenacao);
		Core::modulo('painel_relatorio')->fixarRelatorio($this->_relatorio);
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();
	}
}

class ExportadorRLPFRendimentoTurmasCSV extends ExportadorCVS
{
	public function exportar($_cabecalho = array(), $_dados = array())
	{	
		$this->_dados = array_merge($_cabecalho, $_dados);
	}
}
?>