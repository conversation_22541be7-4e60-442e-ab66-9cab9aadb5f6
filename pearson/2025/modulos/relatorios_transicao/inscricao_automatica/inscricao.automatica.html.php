

<style>
    #inscricao_automatica_table {
        margin-left: 10px;
        text-align: center;
    }

    .label_geral {
        color: #049cdb;
        font-weight: bold;
        display: block;
        float: left;
        width: 40px;
        font-size: 14px;
    }

    .label_checkbox {
        color: #049cdb;
        font-weight: bold;
        display: block;
        font-size: 14px;
        left: 30px;
        top: -20px;
        position: relative;
    }

    .myButton {
        -moz-box-shadow:inset 0px 1px 0px 0px #dcecfb;
        -webkit-box-shadow:inset 0px 1px 0px 0px #dcecfb;
        box-shadow:inset 0px 1px 0px 0px #dcecfb;
        background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #bddbfa), color-stop(1, #80b5ea));
        background:-moz-linear-gradient(top, #bddbfa 5%, #80b5ea 100%);
        background:-webkit-linear-gradient(top, #bddbfa 5%, #80b5ea 100%);
        background:-o-linear-gradient(top, #bddbfa 5%, #80b5ea 100%);
        background:-ms-linear-gradient(top, #bddbfa 5%, #80b5ea 100%);
        background:linear-gradient(to bottom, #bddbfa 5%, #80b5ea 100%);
        filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#bddbfa', endColorstr='#80b5ea',GradientType=0);
        background-color:#bddbfa;
        -moz-border-radius:6px;
        -webkit-border-radius:6px;
        border-radius:6px;
        border:1px solid #84bbf3;
        display:inline-block;
        cursor:pointer;
        color:#ffffff;
        font-family:Arial;
        font-size:15px;
        font-weight:bold;
        padding:6px 24px;
        text-decoration:none;
        text-shadow:0px 1px 0px #528ecc;
    }
    .myButton:hover {
        background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #80b5ea), color-stop(1, #bddbfa));
        background:-moz-linear-gradient(top, #80b5ea 5%, #bddbfa 100%);
        background:-webkit-linear-gradient(top, #80b5ea 5%, #bddbfa 100%);
        background:-o-linear-gradient(top, #80b5ea 5%, #bddbfa 100%);
        background:-ms-linear-gradient(top, #80b5ea 5%, #bddbfa 100%);
        background:linear-gradient(to bottom, #80b5ea 5%, #bddbfa 100%);
        filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#80b5ea', endColorstr='#bddbfa',GradientType=0);
        background-color:#80b5ea;
    }
    .myButton:active {
        position:relative;
        top:1px;
    }


</style>


<div id="inscricao_automatica" class="modal-box">
    <script type="text/javascript">
        var JAVA_URL_BASE = "<?= Core::diretiva('JAVA_URL_BASE'); ?>";     
    </script>
    <script src="modulos/relatorios/inscricao_automatica/js/inscricao_automatica.js"></script>
    <header> <a href="javascript:closeModalInscricaoAutomatica();" class="js-modal-close close">×</a>
        <h3>Avaliativa - Inscrição automática</h3>
    </header>
    <div id="inscricao_automatica_table">
    <table>
        <tr>
            <td style="width: 200px;cursor: pointer;background-color: #4bbea3; "><a style="color: white;
    text-decoration: none;
    font-size: 17px;
    font-weight: bold;" onclick="adicionarLinha();">Adicionar De/Para</a></td>
            <td style="padding-left: 30px">
            </td>
            <td style="width: 200px;cursor: pointer;background-color: #4bbea3; "><a style="color: white;
    text-decoration: none;
    font-size: 17px;
    font-weight: bold;" onclick="removerLinha();">Remover De/Para</a></td>
        </tr>
        <tr style="height: 20px;"/>

        <tr id="de_para_linha">
            <td>
                <label class="label_geral">De: </label>
                <select class="w3-input" id="inscricao_automatica_de" />
            </td>
            <td style="padding-left: 30px">
            </td>
            <td><label class="label_geral">Para: </label>
                <select class="w3-input" id="inscricao_automatica_para" /></td>
        </tr>
        <tr style="height: 20px;"/>
        <!--<tr>
            <td style="text-align: left;">
                <input type="checkbox"  id="inscricao_automatica_para_todos_texto" value="" />
                <label class="label_checkbox">Inscrever todas em produção texto ?</label>
            </td>
        </tr>-->
    </table>
        <img id="waiting" src="https://dyubuzjbgoyjh.cloudfront.net/lancamento/loading_orange.gif" style="width: 100px;left: 210px;
    position: absolute;
    top: 120px;"/>
        <a href="#" class="myButton">Inscrever</a>
        <br/>
        <br/>
        <br/>
    </div>
</div>
