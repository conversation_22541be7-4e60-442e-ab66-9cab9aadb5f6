<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('CarregadorUsuarioEspecifico', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('Inscricao', null, true);
Core::incluir('Aula', null, true);
Core::incluir('Turma', null, true);
Core::incluir('ProvinhaBrasil', 'ProvinhaBrasil/', true);

class MSeletorSimulados
{
	public $simulado;

	protected $_simulados;
	protected $_selecionado = null;
	protected $_ordenacao;

	public function __construct (Ordenacao &$ordenacao, $auto_preparar = true)	{
		$this->_ordenacao = $ordenacao;

		$this->simulado = new Simulado(null);

		if ($auto_preparar)
			$this->_prepararListaSimulados();
	}

	public function configurarCampoSeletorSimulados ($rel_secao = '') {
		$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios_transicao');//$rel_secao);

		if(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR){
			if(in_array('3', Core::registro('usuario')->obterGrupos()) === true){
				$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();		
				$turmaProf = $professor->obterArrayTurmasComAulas();
				$turmaProf = key($turmaProf);
				$sids = Aula::obterSimuladosPorTurmaID($turmaProf);

				foreach ($simus as $sk => $sv) {
					if(!in_array($sk, $sids)){
						unset($simus[$sk]);
					}
				}
			}
			else{
				$turma = Core::modulo('_perfil_falso')->_perfil_turma;
				$turma->carregar();

				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if($turma->obterSerie()->obterID() !== $simulado->obterSerieAvaliacao()->obterID()){
						unset($simus[$sk]);
					}
				}
			}
		}
		elseif(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO){
			$perfil_falso_user = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			$simus = Inscricao::obterSimuladosPeloAlunoPorSecao($perfil_falso_user,'relatorios_transicao');
		}

		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			if(Core::registro('usuario')->obterGrupo() != '1'){
				if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
					unset($simus[$sk]);
				}

				if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
					unset($simus[$sk]);
				}
			}
		}

		$this->fixarArraySimulados($simus);

		$segundo = null;
		$simuladosFormulario = array(Campo::NULO => '');
		foreach ($this->_simulados as $id => $nome) {
			$simuladosFormulario[$id] = $nome;

			if ($segundo == null)
				$segundo = $id;
		}

		$selecionado = $segundo;
		if (Core::diretiva('_seletor_simulados.selecionado.relatorios_transicao') !== false && isset($this->_simulados[Core::diretiva('_seletor_simulados.selecionado.relatorios_transicao')]))
			$selecionado = Core::diretiva('_seletor_simulados.selecionado.relatorios_transicao');

		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_simulado',
												'etiqueta' => '<strong>Selecione uma prova</strong>',
												'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $simuladosFormulario,
												'valor_pos_erro' => $selecionado,
												'autocomplete_off' => true,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function fixarArraySimulados ($simulados)
	{
		$this->_simulados = $simulados;
	}

	public function ajustarSimuladoSelecionado ()
	{
		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = @$this->_ordenacao->campo('seletor_simulado')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_simulados[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$perfil_falso = Core::modulo('_perfil_falso');
			$perfil_falso_tipo = $perfil_falso->obterTipoPerfilSelecionado();
			$perfil_falso_user = $perfil_falso->obterPerfilSelecionado();
			$simuladoDoUsuario = Core::diretiva('_seletor_simulados.selecionado.relatorios_transicao');

			if($perfil_falso_tipo == MPerfilFalso::ALUNO){
				$this->_simulados = Inscricao::obterSimuladosPeloAlunoPorSecao($perfil_falso_user,'relatorios_transicao');
				if(array_key_exists($simuladoDoUsuario, $this->_simulados)){
					$this->_selecionado = $simuladoDoUsuario;								
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);												
				}
			}
			elseif($perfil_falso_tipo == MPerfilFalso::PROFESSOR){
				if(array_key_exists($simuladoDoUsuario, $this->_simulados)){
					$this->_selecionado = $simuladoDoUsuario;								
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);												
				}
			}
			elseif($perfil_falso_tipo == MPerfilFalso::DIRETOR){
				$escola = $perfil_falso->obterInstituicaoDoPerfil();
				$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios_transicao');
				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if(Core::registro('usuario')->obterGrupo() != '1'){
						if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
							unset($simus[$sk]);
							continue;
						}

						if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
							unset($simus[$sk]);
							continue;
						}

						$turmas = Turma::obterArrayTurmasParaFormularioPorSimuladoIntituicao($escola->obterID(),$sk);				
						if(count($turmas)<=0){
							unset($simus[$sk]);
						}
					}
				}
				
				if(count($simus)>0){
					if(array_key_exists($simuladoDoUsuario, $simus)){
						$this->_selecionado = $simuladoDoUsuario;
					}
					else{
						$this->_selecionado = reset($simus);					
					}
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);
				}
			}
			else{
				$this->_selecionado = $simuladoDoUsuario;
				if($simuladoDoUsuario == null){
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);			
				}
			}
		}

		if($this->_selecionado != null){
			$this->_ordenacao->campo('seletor_simulado')->fixar('valor', $this->_selecionado);
			Core::fixarDiretiva('_seletor_simulados.selecionado.relatorios_transicao', $this->_selecionado);
			$this->simulado = new Simulado($this->_selecionado);
			$this->simulado->carregar();

			$this->direcionarSimulado($this->simulado);
		}
		else{
			Core::fixarDiretiva('_seletor_simulados.selecionado.relatorios_transicao', null);
			$this->simulado = new Simulado(null);
		}
	}

	public function redirecionaParaSimuladoLiberado() {
		$simulados = Simulado::obterSimuladosParaFormularioPorSecao('relatorios_transicao', null);

		foreach($simulados as $ks => $vs){
			$jump = 0;
			$jumpR = 0;
			$cur = time();

			$simuladoLoad = new Simulado($ks);
			$simuladoLoad->carregar();

			$iniV = $simuladoLoad->obterInicioLancamento();
			$extV = $simuladoLoad->obterFimLancamento();

			if($cur >= $iniV && $cur <= $extV){
				$jump = 1;
			}

			if(!$jump && !$jumpR){
				$ha = Simulado::trocaSimulado($ks);
				break;

				return;
			}		
		}

		$msg = 'N&atilde;o h&aacute; nenhuma avalia&#231;&#227;o dispon&iacute;vel para visualiza&ccedil&atilde;o dos relat&oacute;rios.';
		Core::modulo('redirecionador')->fixarMensagem($msg, 'Relat&oacute;rios');
		Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
		Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('principal', 'visaogeral') );
		Core::modulo('redirecionador')->redirecionar();
	}

	public function direcionarSimulado ($simulado) {
		$user_logado_id = Core::registro('autenticador')->obterUsuario()->obterID();
		$usuario = new UsuarioInstituido($user_logado_id);
		$usuario->carregar();

		$id = $simulado->obterID();

		$iniV = $simulado->obterInicioLancamento();
		$extV = $simulado->obterFimLancamento();

		$jump = 0;
		$jumpR = 0;
		$cur = time();
			
		if($cur >= $iniV && $cur <= $extV){
			$jump = 1;
		}

		if($jump && !Core::registro('permissoes')->temPermissao('relatorios.visualizacao.lancamento'))
		{
			MSeletorSimulados::redirecionaParaSimuladoLiberado();

			$msg = 'Voc&ecirc; n&atilde;o pode ver esse relat&oacute;rio porque a avalia&#231;&#227;o est&aacute; em per&iacute;odo de lan&ccedil;amento.';
			Core::modulo('redirecionador')->fixarMensagem($msg, 'Relat&oacute;rios');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('videos', 'player') );
			Core::modulo('redirecionador')->redirecionar();
		}

		/*if($jumpR && !Core::registro('permissoes')->temPermissao('relatorios.visualizacao.lancamento'))
		{
			MSeletorSimulados::redirecionaParaSimuladoLiberado();

			$msg = 'Os dados deste simulado est&atilde;o em processo de valida&ccedil;&atilde;o, os relat&oacute;rios ser&atilde;o liberados a partir de '.date('d/m/Y', $datV).'.';
			Core::modulo('redirecionador')->fixarMensagem($msg, 'Relat&oacute;rios');
			Core::modulo('redirecionador')->fixarImagem(MRedirecionador::IMG_FALHA);
			Redirecionador::fixarEndereco( Gerenciador_URL::gerarLink('videos', 'player') );
			Core::modulo('redirecionador')->redirecionar();
		}*/

		Core::fixarDiretiva('NIVEL_1_ANTERIOR_TMP', Core::diretiva('NIVEL_1_ANTERIOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_2_ANTERIOR_TMP', Core::diretiva('NIVEL_2_ANTERIOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_3_ANTERIOR_TMP', Core::diretiva('NIVEL_3_ANTERIOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_4_ANTERIOR_TMP', Core::diretiva('NIVEL_4_ANTERIOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_5_ANTERIOR_TMP', Core::diretiva('NIVEL_5_ANTERIOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_1_TMP', Core::diretiva('NIVEL_1_'.$id), true);
		Core::fixarDiretiva('NIVEL_2_TMP', Core::diretiva('NIVEL_2_'.$id), true);
		Core::fixarDiretiva('NIVEL_3_TMP', Core::diretiva('NIVEL_3_'.$id), true);
		Core::fixarDiretiva('NIVEL_4_TMP', Core::diretiva('NIVEL_4_'.$id), true);
		Core::fixarDiretiva('NIVEL_5_TMP', Core::diretiva('NIVEL_5_'.$id), true);
		Core::fixarDiretiva('NIVEL_6_TMP', Core::diretiva('NIVEL_6_'.$id), true);
		Core::fixarDiretiva('NIVEL_7_TMP', Core::diretiva('NIVEL_7_'.$id), true);
		Core::fixarDiretiva('NIVEL_8_TMP', Core::diretiva('NIVEL_8_'.$id), true);
		Core::fixarDiretiva('NIVEL_9_TMP', Core::diretiva('NIVEL_9_'.$id), true);
		Core::fixarDiretiva('NIVEL_10_TMP', Core::diretiva('NIVEL_10_'.$id), true);
		Core::fixarDiretiva('NIVEL_1_COR_TMP', Core::diretiva('NIVEL_1_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_2_COR_TMP', Core::diretiva('NIVEL_2_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_3_COR_TMP', Core::diretiva('NIVEL_3_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_4_COR_TMP', Core::diretiva('NIVEL_4_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_5_COR_TMP', Core::diretiva('NIVEL_5_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_6_COR_TMP', Core::diretiva('NIVEL_6_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_7_COR_TMP', Core::diretiva('NIVEL_7_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_8_COR_TMP', Core::diretiva('NIVEL_8_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_9_COR_TMP', Core::diretiva('NIVEL_9_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_10_COR_TMP', Core::diretiva('NIVEL_10_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_1_NOME_TMP', Core::diretiva('NIVEL_1_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_1_TEXTO_TMP', Core::diretiva('NIVEL_1_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_2_NOME_TMP', Core::diretiva('NIVEL_2_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_2_TEXTO_TMP', Core::diretiva('NIVEL_2_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_3_NOME_TMP', Core::diretiva('NIVEL_3_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_3_TEXTO_TMP', Core::diretiva('NIVEL_3_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_4_NOME_TMP', Core::diretiva('NIVEL_4_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_4_TEXTO_TMP', Core::diretiva('NIVEL_4_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_5_NOME_TMP', Core::diretiva('NIVEL_5_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_5_TEXTO_TMP', Core::diretiva('NIVEL_5_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_6_NOME_TMP', Core::diretiva('NIVEL_6_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_6_TEXTO_TMP', Core::diretiva('NIVEL_6_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_7_NOME_TMP', Core::diretiva('NIVEL_7_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_7_TEXTO_TMP', Core::diretiva('NIVEL_7_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_8_NOME_TMP', Core::diretiva('NIVEL_8_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_8_TEXTO_TMP', Core::diretiva('NIVEL_8_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_9_NOME_TMP', Core::diretiva('NIVEL_9_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_9_TEXTO_TMP', Core::diretiva('NIVEL_9_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_10_NOME_TMP', Core::diretiva('NIVEL_10_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_10_TEXTO_TMP', Core::diretiva('NIVEL_10_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_123_TMP', Core::diretiva('NIVEL_COMPACTO_123_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_123_COR_TMP', Core::diretiva('NIVEL_COMPACTO_123_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_123_NOME_TMP', Core::diretiva('NIVEL_COMPACTO_123_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_123_TEXTO_TMP', Core::diretiva('NIVEL_COMPACTO_123_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_45_TMP', Core::diretiva('NIVEL_COMPACTO_45_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_45_COR_TMP', Core::diretiva('NIVEL_COMPACTO_45_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_45_NOME_TMP', Core::diretiva('NIVEL_COMPACTO_45_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_COMPACTO_45_TEXTO_TMP', Core::diretiva('NIVEL_COMPACTO_45_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_NLEITOR_TMP', Core::diretiva('NIVEL_NLEITOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_NLEITOR_COR_TMP', Core::diretiva('NIVEL_NLEITOR_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_NLEITOR_NOME_TMP', Core::diretiva('NIVEL_NLEITOR_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_NLEITOR_TEXTO_TMP', Core::diretiva('NIVEL_NLEITOR_TEXTO_'.$id), true);
		Core::fixarDiretiva('NIVEL_LEITOR_TMP', Core::diretiva('NIVEL_LEITOR_'.$id), true);
		Core::fixarDiretiva('NIVEL_LEITOR_COR_TMP', Core::diretiva('NIVEL_LEITOR_COR_'.$id), true);
		Core::fixarDiretiva('NIVEL_LEITOR_NOME_TMP', Core::diretiva('NIVEL_LEITOR_NOME_'.$id), true);
		Core::fixarDiretiva('NIVEL_LEITOR_TEXTO_TMP', Core::diretiva('NIVEL_LEITOR_TEXTO_'.$id), true);

		ProvinhaBrasil::carregar();
	}

	protected function _prepararListaSimulados () {
		$this->_simulados = array();

		$professor = null;

		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		//else
			//$professor = CarregadorUsuarioEspecifico::obterProfessor();

		if ( $professor != null ) {
			$turmasPossiveis = array();
			//$disciplinasPossiveis = array();
			foreach ( $professor->obterAulas() as $aula ) {
				$turmasPossiveis[] = @$aula->obterTurma()->obterID();
				//$disciplinasPossiveis[] = Core::registro('db')->formatarValor( @$aula->obterDisciplina()->obterID() );
			}

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT simulados.*, series.s_nome AS series_s_nome FROM simulados
				  INNER JOIN turmas ON turmas.t_id = %s
				  INNER JOIN series ON series.s_id = turmas.t_serie
				  WHERE simulados.s_instituicao = %s
				  ORDER BY s_ordem',
				  Core::registro('db')->formatarValor( array_pop($turmasPossiveis) ),
				  //Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(Core::registro('usuario')->obterGrupo() != '1'){
						if(time() >= $row['s_data_inicio_lancamento'] AND time() <= $row['s_data_fim_lancamento']){
							continue;
						}

						if(time() >= $row['s_data_inicio_inscricao'] AND time() <= $row['s_data_fim_inscricao']){
							continue;
						}
					}

					if ( time() < $row['s_data'] )
						continue;

					if ( strstr($row['s_nome'], $row['series_s_nome']) === false ) continue;

					if ( !isset($this->_simulados[$row['s_id']]) )
						$this->_simulados[$row['s_id']] = strftime('%d/%m/%Y', $row['s_data']) . ' | ' .$row['s_nome'];
				}
			}
			$rs->free();
		} else {
			$aluno = null;
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$aluno = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			else
				$aluno = CarregadorUsuarioEspecifico::obterAluno();

			$sqlSimulado = null;
			if ( $aluno != null )
				$sqlSimulado = ' INNER JOIN simulados_inscricoes ON simulados_inscricoes.si_aluno = '. Core::registro('db')->formatarValor( $aluno->obterID() ) .' AND simulados_inscricoes.si_simulado = s_id ';

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT simulados.*, questoes.q_id FROM simulados
				  %s
				  INNER JOIN questoes ON questoes.q_simulado = s_id
				  WHERE s_instituicao = %s AND q_id IS NOT NULL
				  ORDER BY s_data DESC, s_nome ASC',
				  $sqlSimulado,
				  //Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(Core::registro('usuario')->obterGrupo() != '1'){
						if(time() >= $row['s_data_inicio_lancamento'] AND time() <= $row['s_data_fim_lancamento']){
							continue;
						}

						if(time() >= $row['s_data_inicio_inscricao'] AND time() <= $row['s_data_fim_inscricao']){
							continue;
						}
					}

					if ( time() < $row['s_data'] )
						continue;

					if ( !isset($this->_simulados[$row['s_id']]) )
						$this->_simulados[$row['s_id']] = strftime('%d/%m/%Y', $row['s_data']) . ' | ' .$row['s_nome'];
				}
			}
			$rs->free();
		}
	}

}

?>