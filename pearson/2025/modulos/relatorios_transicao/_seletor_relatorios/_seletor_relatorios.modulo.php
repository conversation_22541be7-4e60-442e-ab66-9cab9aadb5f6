<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class MSeletorRelatorios extends Modulo
{
	protected $_relatorios = array();
	protected $_selecionado = null;

	public function obterSaida ()
	{
		$this->_limparSaida(Modulo::HTML);
	
		$this->_iniciarRenderizacao(Modulo::HTML);
			include('_seletor_relatorios.html.php');
		$this->_finalizarRenderizacao();
		
		return parent::obterSaida();
	}

	public function limitarPorGrupo (Relatorio &$relatorio) {
		$this->_selecionado = $relatorio;
	}

	public function prepararSeletor ($limitarPorGrupo = true) {
		$this->_prepararArrayRelatorios($limitarPorGrupo);
	}
	
	protected function _prepararArrayRelatorios ($limitarPorGrupo = true) {
		$this->_relatorios = array();

		$rs = Core::registro('db')->query( 
			  'SELECT * FROM relatorios 
			  INNER JOIN relatorios_grupos ON relatorios_grupos.rg_id = relatorios.r_grupo 
			  LEFT JOIN permissoes ON permissoes.p_id = r_permissao 
			  WHERE r_oculto != "1" AND r_secao = "relatorios_transicao" 
			  ORDER BY rg_ordem ASC, r_ordem ASC, r_nome ASC' );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$relatorio = new Relatorio( $row['r_id'] );
				
				$relatorio->fixarNome( $row['r_nome'] );
				$relatorio->fixarDescricao( $row['r_descricao'] );
				$relatorio->fixarGrupo( $row['r_grupo'] );
				$relatorio->fixarPermissao( $row['r_permissao'], $row['p_nome'] );
				$relatorio->fixarOrdem( $row['r_ordem'] );
				
				$permissao = true;
				if ( Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null ) {
					if (!$relatorio->validarPermissaoParaUsuario(Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()))
						$permissao = false;
				} else if ( !$relatorio->validarPermissao() )
					$permissao = false;
				
				if ( ( $limitarPorGrupo && $relatorio->obterGrupo() != $this->_selecionado->obterGrupo() ) || !$permissao )
					continue;

				$this->_relatorios[] = $relatorio;
			}
		}
		$rs->free();
	}
}

?>