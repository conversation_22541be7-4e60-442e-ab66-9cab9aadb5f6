<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoRedeSeriesPorDisciplina extends RelatorioListagemTransicao {
	const GRAFICO_PROVA_COR = '#2D5C85';//'#303FA7';
	const GRAFICO_PROVA_COR_REDE = '#000000';
	const GRAFICO_PROVA_COR_TEXTO = '#ffffff';

	const GRAFICO_DISCIPLINA_COR = '#3B867F';//'#BD0000';
	const GRAFICO_DISCIPLINA_COR_REDE = '#000000';
	const GRAFICO_DISCIPLINA_COR_TEXTO = '#ffffff';

	protected $_nomesDisciplinas = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_config = array(

		);

		//$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();

		if (count($this->_dados)) {
			foreach ($this->_dados as $dk => $dv){
				$dID = $dv['index'];
				$dNome = $dv['label'];
				include 'pf_rendimento_rede_series_por_disciplina.relatorio.tabela.html.php';
			}
		}

		include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';

		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::RETRATO);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if (count($this->_dados)) {
			foreach ($this->_dados as $dk => $dv){
				$dID = $dv['index'];
				$dNome = $dv['label'];
				include 'pf_rendimento_rede_series_por_disciplina.relatorio.tabela.html.php';
			}
		}
		else{
			return false;
		}

		include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, ExportadorPDF::RETRATO, $pasta);
	}

	protected function _ajustarParametros () {
		$this->_sugestao['nome'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		$this->_obterDadosInscricoes();

		/*if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));*/

		// coloca a rede antes
		$idREDE = null;
		foreach ($this->_dados as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados[$idREDE])) {
			$rede = $this->_dados[$idREDE];
			unset($this->_dados[$idREDE]);
			array_push($this->_dados, $rede);
		}

		//echo"1-><pre>";print_r($this->_dados);echo"</pre><br>---<br>";

		$dataPage = array();
		$nomesDisciplinas = array(0 => 'Global');
		foreach ($nomesDisciplinas as $ndk => $ndv) {
			$dados = array_chunk($this->_dados, 24, true);
			foreach ($dados as $dk => $dv) {
				$dataPageTmp['iddis'] = $ndk;
				$dataPageTmp['index'] = 'd'.$ndk.'_p'.$dk;
				$dataPageTmp['label'] = $ndv;
				$dataPageTmp['parte'] = $dk;
				$dataPageTmp['dados'] = $dv;

				$dataPage[] = $dataPageTmp;
			}					
		}

		//echo"2-><pre>";print_r($dataPage);echo"</pre><br>---<br>";

		$nomesDisciplinasTMP = Disciplina::obterArrayDisciplinasParaFormulario(false, false);
		//ksort($nomesDisciplinasTMP);

		$rpd = array();
		foreach ($nomesDisciplinasTMP as $ndk => $ndv) {
			foreach ($this->_dados as $dk => $dv) {
				if(array_key_exists($ndk, $dv['rendimento_por_disciplina'])){
					$rpdTmp['nome'] = $dv['nome'];
					$rpdTmp['rendimento'] = $dv['rendimento_por_disciplina'][$ndk];
					$rpd[$ndk][] = $rpdTmp;
				}
			}					
		}

		//echo"3-><pre>";print_r($rpd);echo"</pre><br>---<br>";

		foreach ($nomesDisciplinasTMP as $ndk => $ndv) {
			$dados = array_chunk($rpd[$ndk], 24, true);
			foreach ($dados as $dk => $dv) {
				$dataPageTmp['iddis'] = $ndk;
				$dataPageTmp['index'] = 'd'.$ndk.'_p'.$dk;
				$dataPageTmp['label'] = $ndv;
				$dataPageTmp['parte'] = $dk;
				$dataPageTmp['dados'] = $dv;

				$dataPage[] = $dataPageTmp;
			}					
		}

		$nomesDisciplinas += $nomesDisciplinasTMP;

		//echo"4-><pre>";print_r($dataPage);echo"</pre><br>---<br>";

		$this->_dados = $dataPage;

		//echo"5-><pre>";print_r($this->_dados);echo"</pre><br>---<br>";

		$this->gerarGrafico();
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_transicao/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		//$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		//$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			$this->_dados = &$tempCache['dados'];
			$this->_nomesDisciplinas = &$tempCache['_nomesDisciplinas'];
			return;
		}

		$simulados = array_keys(Simulado::obterSimuladosParaFormularioPorSecao('relatorios_transicao'));
		foreach ($simulados as $simuladoID) {
			$simulado = new Simulado($simuladoID);
			$simulado->carregar();

			if(Core::registro('usuario')->obterGrupo() != '1'){
				if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
					continue;
				}

				if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
					continue;
				}
			}

			unset($this->_analizadorSimulado);
			$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
			$this->_analizadorSimulado->fixarSimulado($simulado);
			$this->_analizadorSimulado->carregarInscritos(true);
			$this->_analizadorSimulado->carregarRespostasDosInscritos();
			$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
			$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
			$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
			//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
			$this->_analizadorSimulado->calcularRendimentoPorInstituicao();
			$this->_analizadorSimulado->calcularRendimentoPorSerie();
			if ( $this->_colunas['rendimento_por_disciplina'] ) {
				//$this->_analizadorSimulado->calcularRendimentoPorInstituicaoPorDisciplina();
				$this->_analizadorSimulado->calcularRendimentoPorSeriePorDisciplina();
			}

			foreach ( $this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
				if (!isset($this->_nomesDisciplinas[$dID]))
					$this->_nomesDisciplinas[$dID] = $dNome;
			}

			// SERIE
			foreach($this->_analizadorSimulado->nomesSeries as $sID => $sNome) {
				$dado = $this->_modelo;

				$dado['nome'] = ProvaFloripa::obterSeriePeloNomeSimulado($simulado->obterNome());
				$dado['_rede'] = true;

				if (isset($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']))
					$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']);

				// RENDIMENTO POR DISCIPLINA
				if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado->rendimentoPorSeriePorDisciplina[$sID] ) ) {
					foreach ( $this->_analizadorSimulado->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
						if (isset( $desempenho['rendimento'] ))
							$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
					}
				}

				$this->_dados[] = $dado;
			}
		}

		//asort($this->_nomesDisciplinas, SORT_LOCALE_STRING);

		$this->_nomesDisciplinas = Disciplina::obterArrayDisciplinasParaFormulario(false, false);
		
		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados,
			'_nomesDisciplinas' => $this->_nomesDisciplinas
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';	

		foreach ($this->_dados as $dk => $dv) {
			$grafico = new Graph(650, 840, $this->_tituloCache.'_'.$dv['index'].'.png', (int)CACHE_RELATORIOS/60, false);
			$grafico->SetMargin(34, 0, 10, 30);
			$grafico->setFrame(false);
			$grafico->SetScale('textint', 0, 100, 0, 0);
			$grafico->yaxis->scale->ticks->Set(20);
			$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
			$grafico->yaxis->SetLabelFormat('%d%%');
			$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
			$grafico->xaxis->SetFont(FF_ARIAL,FS_BOLD, 9);
			$grafico->Set90AndMargin(350, 5, 5, 5);

			$labelsX = $barra = $dados = array();

			$dID = $dv['iddis'];
			foreach($dv['dados'] as $k => &$d) {
				//if ($dv['label'] == 'Global') {
					$labelsX[] = wordwrap($d['nome'], 50);
					$dados[] = $d['rendimento'];
				//} elseif (isset($d['rendimento_por_disciplina'][$dID])) {
				//	$labelsX[] = wordwrap($d['nome'], 50);
				//	$dados[] = $d['rendimento_por_disciplina'][$dID];
				//}
			}

			if (count($dados)) {
				$grafico->xaxis->SetTickLabels($labelsX);
				$barra = new BarPlot($dados);
				$barra->SetFillColor(($dv['label'] == 'Global' ? self::GRAFICO_PROVA_COR : self::GRAFICO_DISCIPLINA_COR));
				$barra->value->SetColor(($dv['label'] == 'Global' ? self::GRAFICO_PROVA_COR_TEXTO : self::GRAFICO_DISCIPLINA_COR_TEXTO));
				$barra->value->Show();
				$barra->value->SetFormat('%d%%');
				$barra->SetValuePos('center');
				$barra->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$barra->SetWidth(0.5);
				$grafico->Add($barra);
				$grafico->Stroke();
			}			
		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>