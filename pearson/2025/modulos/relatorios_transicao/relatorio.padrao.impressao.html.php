<? 
if (!defined('CORE_INCLUIDO')) { exit(); }
?>

<? //= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_INICIO, true); ?>
<? /*<div class="vd_BlocoBotoes"><strong><?= $this->_obterLinkBotao(self::BT_VOLTAR); ?></strong></div>*/ ?>

<table width="100%" border="0" cellspacing="1" cellpadding="3" style="margin-bottom: 8px;">
      <tr>
        <td><div class="rlt_painel_Titulo gigante"><?= $this->_relatorio->obterNome(); ?></div>
			<div><?= $this->_textoFiltro; ?></div></td>
<? if ( !isset($esconderTotal) || !$esconderTotal ) { ?>
        <td width="20%" align="right" nowrap="nowrap" class="rlt_painel_Titulo medio">Total: <?= count($this->_dados); ?></td>
<? } ?>
      </tr>
  </table>
  
  <table width="100%" align="center" cellpadding="5" cellspacing="0" class="rltli" style="margin-bottom: 16px;">
		<?= $tabela['td']; ?>
		<?= @Core::modulo('_bloco_notas')->obterSaida(); ?>
  </table>
<? //= $this->_formulario->obterHTML('botao_enviador', Formulario::HTML_CAMPO, true); ?>
<? //= $this->_formulario->obterHTML(null, Formulario::HTML_FORM_FIM); ?>