<?php
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('ExportadorCVS', 'Exportador/');

class RLPFRendimentoGeralCSV extends RelatorioListagemTransicao 
{
	public $_cabecalho = array(array(
		'ESCOLA',
		'SERIE',
		'TURMA',
		'TOTAL DE ALUNOS',
		'NOME ALUNO',
	
		'AVALIA&Ccedil;&Atilde;O',
		'TOTAL DE INSCRITOS',
		'TOTAL ALUNOS QUE FIZERAM',
		'TOTAL ALUNOS QUE N&Atilde;O FIZERAM',
		'QTD QUEST&Otilde;ES',
		'QTD QUEST&Otilde;ES RESPONDIDAS',
		'RESULTADO',
		
		'AVALIA&Ccedil;&Atilde;O',
		'TOTAL DE INSCRITOS',
		'TOTAL ALUNOS QUE FIZERAM',
		'TOTAL ALUNOS QUE N&Atilde;O FIZERAM',
		'QTD QUEST&Otilde;ES',
		'QTD QUEST&Otilde;ES RESPONDIDAS',
		'RESULTADO',
		
		'AVALIA&Ccedil;&Atilde;O',
		'TOTAL DE INSCRITOS',
		'TOTAL ALUNOS QUE FIZERAM',
		'TOTAL ALUNOS QUE N&Atilde;O FIZERAM',
		'QTD QUEST&Otilde;ES',
		'QTD QUEST&Otilde;ES RESPONDIDAS',
		'RESULTADO',
	));
	public $_dados = array();

	public function __construct () 
	{
		parent::__construct();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);
		$this->_prepararPainel();		
		$this->_obterDados();
		$this->_prepararFormulario();	

		if ($this->obterModoVisualizacao() == self::PDF) { 
			$this->fixarModoVisualizacao(self::NORMAL);
		}

		$this->_autoEsconderComponentes();	
		$this->_iniciarRenderizacao(Modulo::HTML);

		$relHTML = array();
		include 'pf_rendimento_geral_csv.relatorio.tabela.html.php';
		include 'modulos/relatorios/relatorio.pf'.$this->obterModoVisualizacao().'.html.php';

		$this->_finalizarRenderizacao();

		if ($this->obterModoVisualizacao() == self::XLS) {
			$exportador = new ExportadorRLPFRendimentoGeralCSV();
			$exportador->exportar($this->_cabecalho, $this->_dados);
			
			$this->_iniciarRenderizacao($exportador->obterTipoSaida());
				$exportador->obterSaida($relatorio->obterNome().'.csv');
			$this->_finalizarRenderizacao();
		}
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		return false; // não tem saida de pdf para bot.
	}

	protected function _obterDados () 
	{
		/*echo phpversion();
		
		echo'<br>--->+++++++++++<---<br><br>';
		
		echo'->';PRINT_R(get_cfg_var('display_errors'));echo';<br>';
		echo'->';PRINT_R(error_reporting());echo';<br>';
		
		error_reporting(E_ALL);
		ini_set("display_errors", 1);
		
		echo'<br>--->+++++++++++<---<br><br>';
		
		echo'->';PRINT_R(get_cfg_var('display_errors'));echo';<br>';
		echo'->';PRINT_R(error_reporting());echo';<br>';
		
		echo'<br>--->+++++++++++<---<br><br>';*/

		$db = Core::registro('db');

		$escolas = array();
		$r0 = $db->query("SELECT * FROM instituicoes", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$escolas[$rr0['i_id']] = $rr0;
			}
			//echo'2---><pre>';print_r($escolas);echo'</pre><br>---<br>';
		}
		
		$series = array();
		$r0 = $db->query("SELECT * FROM series", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$series[$rr0['s_id']] = $rr0;
			}
			//echo'2---><pre>';print_r($series);echo'</pre><br>---<br>';
		}
		
		$turmas = array();
		$r0 = $db->query("SELECT * FROM turmas", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$turmas[$rr0['t_id']] = $rr0;
			}
			//echo'2---><pre>';print_r($turmas);echo'</pre><br>---<br>';
		}
		
		$avaliacoes = array();
		$this->_analizadorSimulado = array();
		$r0 = $db->query("SELECT * FROM simulados ORDER BY s_nome ASC", MYSQLI_USE_RESULT);
		if ($r0->num_rows) {
			while ($rr0 = $r0->fetch_assoc()) {
				$sid = $rr0['s_id'];
				$avaliacoes[$rr0['s_serie']][$sid] = $rr0;

				$this->_analizadorSimulado[$sid] = new AnalizadorSimuladoProvaFloripa();
				$this->_analizadorSimulado[$sid]->fixarSimulado(new Simulado($sid));
				$this->_analizadorSimulado[$sid]->carregarInscritos(true);
				$this->_analizadorSimulado[$sid]->carregarRespostasDosInscritos();
				$this->_analizadorSimulado[$sid]->calcularRendimentoPorQuestaoDosInscritos();
				$this->_analizadorSimulado[$sid]->calcularRendimentoGlobalDosInscritos();
				
				$this->_analizadorSimulado[$sid]->eliminarInscritosQueNaoCompareceram();
				$this->_analizadorSimulado[$sid]->eliminarInscritosComRendimentoNulo();

				/*echo'1---><pre>';print_r($this->_analizadorSimulado[$sid]->nomesDisciplinas);echo'</pre><br>---<br>';
				echo'2---><pre>';print_r($this->_analizadorSimulado[$sid]->nomesQuestoes);echo'</pre><br>---<br>';
				echo'3---><pre>';print_r($this->_analizadorSimulado[$sid]->rendimentoPorQuestao);echo'</pre><br>---<br>';
				echo'4---><pre>';print_r($this->_analizadorSimulado[$sid]->rendimentoGlobal);echo'</pre><br>---<br>';*/
			}
		}

		//exit;

		foreach ($turmas as $tk => $turma) {		
			$alunos = array();
			$r3 = $db->query("SELECT * FROM alunos INNER JOIN usuarios ON alunos.a_usuario = usuarios.u_id WHERE a_turma = ".$tk." ORDER BY u_nome ASC;");
			while($rr3 = $r3->fetch_assoc()){
				$alunos[$rr3['a_id']] = $rr3;
			}

			foreach ($alunos as $ak => $rr3) {
				$DADOS_LINHA = array();
			
				$DADOS_LINHA[] = $escolas[$turma['t_instituicao']]['i_nome']; //ESCOLA
				$DADOS_LINHA[] = $series[$turma['t_serie']]['s_nome']; //SERIE
				$DADOS_LINHA[] = $turma['t_nome']; //TURMA

				$nomeAluno = $rr3['u_nome'];
				$idAluno = $rr3['a_id'];
		
				$DADOS_LINHA[] = count($alunos);//$r3->num_rows; //TOTAL DE ALUNOS
		
				$DADOS_LINHA[] = $nomeAluno; //NOME ALUNO
		
				$aid = '';
				foreach($avaliacoes[$turma['t_serie']] as $ak => $rr4){
					$aid = $rr4['s_id'];

					$avaliacoes[] = $aid;
							
					$inscritos[$aid] = array();
					if(count($alunos)>0){
						$r5 = $db->query("SELECT * FROM simulados_inscricoes WHERE si_simulado = ".$aid." AND si_aluno IN(".implode(',',array_keys($alunos)).");");
						while($rr5 = $r5->fetch_assoc()) {
							$inscritos[$aid][$rr5['si_aluno']] = $rr5['si_id'];
						}
					}
				
					$responderam[$aid] = array();
					if(count($inscritos[$aid])>0){
						$r6 = $db->query("
							SELECT r_inscricao, count(r_inscricao) as qtd_r_inscricao 
							FROM respostas 
							WHERE r_inscricao IN(".implode(',',$inscritos[$aid]).")
							GROUP BY r_inscricao
							HAVING qtd_r_inscricao > 0;"
						);
						while($rr6 = $r6->fetch_assoc()) {
							$responderam[$aid][$rr6['r_inscricao']] = $rr6['qtd_r_inscricao'];
						}
					}
		
					$questoes[$aid] = array();
					$r8 = $db->query("SELECT * FROM questoes WHERE q_simulado = ".$aid.";");
					while($rr8 = $r8->fetch_assoc()) {
						$questoes[$aid][$rr8['q_id']] = $rr8['q_identificador'];
					}
		
					$respondidas[$aid] = 0;
					if(count($inscritos[$aid])>0){
						$r9 = $db->query("
							SELECT r_id 
							FROM respostas 
							WHERE r_inscricao IN(".implode(',',$inscritos[$aid]).")"
						);
						while($rr9 = $r9->fetch_assoc()) {
							$respondidas[$aid] += 1;
						}
					}
				}

				foreach($avaliacoes[$turma['t_serie']] as $ak => $rr555){
					$aid = $rr555['s_id'];

					$DADOS_LINHA[] = $rr555['s_nome']; //AVALIACAO
					$DADOS_LINHA[] = count($inscritos[$aid]); //TOTAL DE INSCRITOS
					$DADOS_LINHA[] = count($responderam[$aid]); //TOTAL ALUNOS QUE FIZERAM
					$DADOS_LINHA[] = count($inscritos[$aid])-count($responderam[$aid]); //TOTAL ALUNOS QUE NÃO FIZERAM
					$DADOS_LINHA[] = count($questoes[$aid]); //QTD QUESTOES
					$DADOS_LINHA[] = $respondidas[$aid]; //QTD QUESTOES RESPONDIDAS

					//dados do aluno
					$resultadosAlunos = 0;
					$idInscricao = $inscritos[$aid][$idAluno];

					$disciplinas = $this->_analizadorSimulado[$aid]->nomesDisciplinas;
					$questoes = $this->_analizadorSimulado[$aid]->nomesQuestoes;
					
					$rendimento = $resultadosAlunos = 0;

					$FK = array_keys($disciplinas);

					if(count($this->_analizadorSimulado[$aid]->rendimentoPorQuestao[$idInscricao])<=0){
						$DADOS_LINHA[] = 'Ausente'; //RESULTADO
						continue;
					}

					if($FK[0] == 1 && count($disciplinas) == 1){ //escrita (qual questão?)
						foreach($questoes as $qid => $qnome){
							if($qnome === '01_lp'){
								$rendimento = $this->_analizadorSimulado[$aid]->rendimentoPorQuestao[$idInscricao][$qid]['pontos'];
								$resultadosAlunos = round($rendimento);
								break;
							}
						}
					}
					elseif($FK[0] == 2){ //matematica
						$rendimento = $this->_analizadorSimulado[$aid]->rendimentoGlobal[$idInscricao]['rendimento'];
						$resultadosAlunos = round($rendimento);
						break;
					}
					else{ //fluencia leitora
						foreach($questoes as $qid => $qnome){
							if($qnome === 'GLOBAL'){
								$rendimento = $this->_analizadorSimulado[$aid]->rendimentoPorQuestao[$idInscricao][$qid]['pontos'];
								$resultadosAlunos = round($rendimento);
								break;
							}
						}
					}

					$DADOS_LINHA[] = $resultadosAlunos; //RESULTADO
				}
		
				$this->_dados[] = $DADOS_LINHA;
			}
		}

		//ECHO'-----><PRE>';
		//PRINT_R($this->_dados);
		//ECHO'</PRE><BR>---<BR>';
		//exit;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML('modulos/relatorios_transicao/_painel_relatorio/_painel_relatorio.html.php');

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_'.$this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		Core::modulo('painel_relatorio')->fixarOrdenacao($this->_ordenacao);
		Core::modulo('painel_relatorio')->fixarRelatorio($this->_relatorio);
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();
	}
}

class ExportadorRLPFRendimentoGeralCSV extends ExportadorCVS
{
	public function exportar($_cabecalho = array(), $_dados = array())
	{	
		$this->_dados = array_merge($_cabecalho, $_dados);
	}
}
?>