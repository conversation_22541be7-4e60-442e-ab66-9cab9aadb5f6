<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoRedeEscolasPorDisciplinaTab extends RelatorioListagemTransicao {
	protected $_disciplinasOrdencao;

	public function __construct () {
		parent::__construct();

		$this->orientacao_PDF = ExportadorPDF::PAISAGEM;

		$this->encondarParametros();

		$this->_disciplinasOrdencao = Disciplina::obterArrayDisciplinasParaFormulario();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		foreach ($this->_disciplinasOrdencao as $dID => $dNome)
			$this->_colunas[ $dID ] = true;

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false,
			'_instituicao' => false
		);

		$this->_config = array(

		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			$total = count($this->_dados);
			$pagina_atual = 0;
			$dados_mostrar = array();
			foreach ( $this->_dados as $k => &$d ) {
				$dados_mostrar[] = $k;
				$total--;

				if (count($dados_mostrar) == 999 || !$total) {
					include 'pf_rendimento_rede_escolas_por_disciplina_tab.relatorio.tabela.html.php';
					$dados_mostrar = array();
					$pagina_atual++;
				}
			}

			include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, ExportadorPDF::PAISAGEM);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		if(count($this->_dados)<=0){
			return false;
		}

		$total = count($this->_dados);
		$pagina_atual = 0;
		$dados_mostrar = array();
		foreach ( $this->_dados as $k => &$d ) {
			$dados_mostrar[] = $k;
			$total--;


		}
		if (count($dados_mostrar) == 999 || !$total) {
			include 'pf_rendimento_rede_escolas_por_disciplina_tab.relatorio.tabela.html.php';
			$dados_mostrar = array();
			$pagina_atual++;
		}

		include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, ExportadorPDF::PAISAGEM, $pasta);
	}

	protected function _ajustarParametros () {

	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		// coloca a rede antes
		$idREDE = null;
		foreach ($this->_dados as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados[$idREDE])) {
			$rede = $this->_dados[$idREDE];
			unset($this->_dados[$idREDE]);
			array_push($this->_dados, $rede);
		}
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			elseif ( is_int( $n ) )
				$retorno = Ordenacao_Relatorios::ordenarNumero(@$a['rendimento_por_disciplina'][$n], @$b['rendimento_por_disciplina'][$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_transicao/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			foreach ($this->_disciplinasOrdencao as $dID => $dNome)
				$itensOrdenacao[ $dID ] = $dNome;

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
		$this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID();

		if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
			$this->_dados = &$tempCache['dados'];
			return;
		}


		$this->_analizadorSimulado->carregarInscritos(true);
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado->calcularRendimentoPorInstituicao();
		$this->_analizadorSimulado->calcularRendimentoPorSerie();
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			$this->_analizadorSimulado->calcularRendimentoPorInstituicaoPorDisciplina();
			$this->_analizadorSimulado->calcularRendimentoPorSeriePorDisciplina();
		}



		// ESCOLAS
		foreach($this->_analizadorSimulado->nomesInstituicoes as $instID => $instNome) {
			$dado = $this->_modelo;

			$dado['nome'] = $instNome;
			$dado['_instituicao'] = true;

			if (isset($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorInstituicao[$instID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado->rendimentoPorInstituicaoPorDisciplina[$instID] ) ) {
				foreach ( $this->_analizadorSimulado->rendimentoPorInstituicaoPorDisciplina[$instID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados[] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'REDE';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados[] = $dado;
		}

		// CACHE
		Core::registro('cache')->save(array(
			'dados' => &$this->_dados
		), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	// apenas tempor?rio
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>