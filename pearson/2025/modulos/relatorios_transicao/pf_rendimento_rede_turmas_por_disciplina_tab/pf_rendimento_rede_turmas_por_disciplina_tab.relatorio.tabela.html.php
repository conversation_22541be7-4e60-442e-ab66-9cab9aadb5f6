<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome() .'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento das turmas na prova(produção textual) e seus aspectos'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );;

}
$tabela['titulo']  = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong>'
);

ob_start();
?>
	<tr>
		<? if ($this->_colunas['instituicao']) { ?><th width="30%"><?= $this->obterBotaoAlterador('instituicao', false, 'Escola') ;?></th><? } ?>
		<? if ($this->_colunas['nome']) { ?><th align="center"><?= $this->obterBotaoAlterador('nome', false, 'Turma') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado->nomesPequenosDisciplinas as $dID => $dNome ) {
		$dNome = wordwrap($dNome, 12, "<br>", true);
?>
		<th align="center" nowrap="nowrap"> <?= $this->obterBotaoAlterador($dID, false, $dNome) ;?> </th>
<?
	}
}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr <?= $d['_rede'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['instituicao']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> nowrap="nowrap"><?= @$d['instituicao']; ?></td><? } ?>
		<? if ($this->_colunas['nome']) { ?><td align="center" <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> nowrap="nowrap"><?= @$d['nome']; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : '') .' align="center">'. $d['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d['_rede'] ? 'bgcolor="#dddddd"' : '') .'>&nbsp;</td>';
			}
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>