<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome() .'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento da rede na prova(produção textual) e seus aspectos'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );;

}
$tabela['titulo']  = $titulo;


$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong>'
);
/*
ob_start();
?>
	<tr>
		<? if ($this->_colunas['nome']) { ?><th width="25%"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th nowrap="nowrap" align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Prova') ;?> </th><? } ?>
<?
if ( count($this->_dados) && $this->_colunas['rendimento_por_disciplina'] ) {
	foreach ( $this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome ) {
?>
		<th align="center" nowrap="nowrap"> <?= $dNome ;?> </th>
<?
	}
}
?>
	</tr>
<?
$tabela['th'] = ob_get_clean();*/ ob_start();

if ( count($this->_dados) ) {
	$tabela['grafico'] = '<img src="upload/graficos/'. $this->_tituloCache .'_'. $pagina_atual .'.png" border="0" />';

	/*
	$i = 0;
	foreach ( $this->_dados as $k => &$d ) {
		if (!in_array($k, $dados_mostrar)) continue;

		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
?>
	<tr <?= $d['_rede'] ? 'style="font-weight: normal;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome']) { ?><td <?= $d['_rede'] ? 'bgcolor=""' : ''; ?> nowrap="nowrap"><?= @$d['nome']; ?></td><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><td <?= $d['_rede'] ? 'bgcolor=""' : ''; ?> align="center"><?= @$d['rendimento']; ?>%</td><? } ?>
<?
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			foreach ( $this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome ) {
				if (isset( $d['rendimento_por_disciplina'][$dID] ))
					echo '<td '. ($d['_rede'] ? 'bgcolor=""' : '') .' align="center">'. $d['rendimento_por_disciplina'][$dID] .'%</td>';
				else
					echo '<td '. ($d['_rede'] ? 'bgcolor=""' : '') .'>&nbsp;</td>';
			}
		}
?>
	</tr>
<?
		$i++;
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
	*/
}

//if ($this->obterModoVisualizacao() == self::PDF)
//	$tabela['extra'] = '<style>body{ margin-top: 100px; }</style>';

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>