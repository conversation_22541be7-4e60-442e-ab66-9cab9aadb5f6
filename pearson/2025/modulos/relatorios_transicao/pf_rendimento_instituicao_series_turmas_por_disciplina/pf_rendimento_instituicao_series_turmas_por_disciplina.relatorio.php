<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('RelatorioListagemTransicao', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('Professor', null, true);

class RLPFRendimentoInstituicaoSeriesTurmasPorDisciplina extends RelatorioListagemTransicao {
	const GRAFICO_PROVA_COR = '#2D5C85';
	const GRAFICO_PROVA_COR_REDE = '#18334a';
	const GRAFICO_PROVA_COR_TEXTO = '#ffffff';

	const GRAFICO_DISCIPLINA_COR = '#3B867F';
	const GRAFICO_DISCIPLINA_COR_REDE = '#204b47';
	const GRAFICO_DISCIPLINA_COR_TEXTO = '#ffffff';

	const GRAFICO_PROPORCAO_1_PROVA_COR = '#b32b02';
	const GRAFICO_PROPORCAO_2_PROVA_COR = '#f1501f';
	const GRAFICO_PROPORCAO_3_PROVA_COR = '#f4d826';
	const GRAFICO_PROPORCAO_4_PROVA_COR = '#8be37d';
	const GRAFICO_PROPORCAO_5_PROVA_COR = '#539e48';

	const GRAFICO_PROPORCAO_1_DISCIPLINA_COR = '#b32b02';
	const GRAFICO_PROPORCAO_2_DISCIPLINA_COR = '#f1501f';
	const GRAFICO_PROPORCAO_3_DISCIPLINA_COR = '#f4d826';
	const GRAFICO_PROPORCAO_4_DISCIPLINA_COR = '#8be37d';
	const GRAFICO_PROPORCAO_5_DISCIPLINA_COR = '#539e48';

	const GRAFICO_COR_TEXTO = '#ffffff';

	public $orientacao_PDF = ExportadorPDF::PAISAGEM;

	protected $_nomesDisciplinas = array();

	protected $_dadosProp = array();
	protected $_modeloProp = array();

	public function __construct () {
		parent::__construct();

		$this->encondarParametros();

		$this->_colunas = array(
			'nome' => true,
			'rendimento' => true,
			'rendimento_por_disciplina' => true
		);

		$this->_modelo = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_modeloProp = array(
			'nome' => null,
			'rendimento' => null,
			'rendimento_por_proporcao' => array(),
			'rendimento_por_proporcao_por_disciplina' => array(),
			'_rede' => false
		);

		$this->_config = array(
			'diretor' => null
		);

		$this->_analizadorSimulado = new AnalizadorSimuladoProvaFloripa();
	}

	public function prepararRelatorio (Relatorio &$relatorio)
	{
		parent::prepararRelatorio($relatorio);

		$this->_ajustarParametros();

		$this->_prepararPainel();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
			$relHTML = array();

			include 'pf_rendimento_instituicao_series_turmas_por_disciplina.relatorio.tabela.html.php';

			include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDF(null, $this->orientacao_PDF);
	}

	public function prepararRelatorioParaPacotePDF (Relatorio &$relatorio, $pasta, $nomeArquivo = null)
	{
		parent::prepararRelatorio($relatorio, null, false);

		$this->fixarModoVisualizacao(self::PDF);

		$this->_ajustarParametros();

		$this->_obterDados();

		$this->_prepararFormulario();

		$this->_autoEsconderComponentes();

		$this->_iniciarRenderizacao(Modulo::HTML);
		$relHTML = array();

		include 'pf_rendimento_instituicao_series_turmas_por_disciplina.relatorio.tabela.html.php';

		include 'modulos/relatorios_transicao/relatorio.pf'. $this->obterModoVisualizacao() .'.html.php';
		$this->_finalizarRenderizacao();

		$this->_prepararSaidaPDFParaPacote($nomeArquivo, $this->orientacao_PDF, $pasta);
	}

	protected function _ajustarParametros () {
		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::DIRETOR &&
				Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$this->_config['diretor'] = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		else {
			if ( Core::registro('usuario') == null)
				Core::modulo('redirecionador')->redirecionarPorInvalidez(Gerenciador_URL::gerarLink('relatorios_transicao', 'listar'), 'Diretor inv&aacute;lido!');
			else
				$this->_config['diretor'] = Core::registro('usuario');
		}

		$this->_sugestao['nome'] = Ordenacao::ASC;
	}

	protected function _obterDados () {
		if ( $this->_seletorSimulados->simulado->obterID() == null )
			return false;

		if($this->_seletorSimulados->simulado->obterSecaoRels() !== 'relatorios_transicao')
			return false;

		$this->_analizadorSimulado->fixarSimulado($this->_seletorSimulados->simulado);

		$this->_obterDadosInscricoes();

		if (count($this->_sugestao))
			$this->_ordenacao->multiplaOrdenacao = $this->_sugestao;

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dados) )
			usort($this->_dados, array($this, '_ordenarDados'));

		// coloca a rede antes
		$idREDE = null;
		foreach ($this->_dados as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dados[$idREDE])) {
			$rede = $this->_dados[$idREDE];
			unset($this->_dados[$idREDE]);
			array_push($this->_dados, $rede);
		}

		if ( count($this->_ordenacao->multiplaOrdenacao) && count($this->_dadosProp) )
			usort($this->_dadosProp, array($this, '_ordenarDados'));

		// coloca a rede antes
		$idREDE = null;
		foreach ($this->_dadosProp as $k => &$d) {
			if ($d['_rede'])
				$idREDE = $k;
		}

		if (isset($this->_dadosProp[$idREDE])) {
			$rede = $this->_dadosProp[$idREDE];
			unset($this->_dadosProp[$idREDE]);
			array_push($this->_dadosProp, $rede);
		}

		$this->gerarGrafico();
		$this->gerarGraficoProp();
	}

	protected function _ordenarDados ($a, $b) {
		$retorno = 0;

		foreach ( $this->_ordenacao->multiplaOrdenacao as $n => $tipo ) {
			//if ( !isset($this->_dados[0][$n]) )
				//continue;

			if ( $n == 'rendimento' )
				$retorno = Ordenacao_Relatorios::ordenarNumero($a[$n], $b[$n], $tipo);
			else
				$retorno = @Ordenacao_Relatorios::ordenarString($a[$n], $b[$n], $tipo);

			if ( $retorno )
				break;
		}

		return $retorno;
	}

	protected function _prepararPainel ()
	{
		Core::carregarModulo( array('nome' => '_painel_relatorio',
									'pasta' => 'relatorios_transicao/',
									'classe' => 'MPainelRelatorio',
									'guardar_como' => 'painel_relatorio'
									) );

		Core::modulo('painel_relatorio')->fixarTemplateHTML( 'modulos/relatorios_transicao/_painel_relatorio/_painel_relatorio.html.php' );

		$this->_ordenacao = new Ordenacao_Relatorios('painel_rel_' . $this->_relatorio->obterID());
		$this->_ordenacao->prepararOrdenacao();

		if (!count($this->_sugestao)) {
			$itensOrdenacao = array(
				'nome' => 'Nome',
				'rendimento' => 'Rendimento'
			);

			$itensOrdenacaoPadroes = array( array('rendimento') );

			$this->_autoConfigurarMultiplaOrdenacao($itensOrdenacao, $itensOrdenacaoPadroes);
		}

		$this->_seletorSimulados = new MSeletorSimulados($this->_ordenacao);
		$this->_seletorSimulados->configurarCampoSeletorSimulados();

		Core::modulo('painel_relatorio')->fixarOrdenacao( $this->_ordenacao );
		Core::modulo('painel_relatorio')->fixarRelatorio( $this->_relatorio );
		Core::modulo('painel_relatorio')->prepararSeletorRelatorios();
		Core::modulo('painel_relatorio')->carregarPainel();

		$this->_seletorSimulados->ajustarSimuladoSelecionado();
	}

	protected function _obterDadosInscricoes () {
		// CACHE
        $this->_tituloCache = CACHE_PREFIXO.'_rel'.$this->_relatorio->obterID().'_sug'.$this->_id_sugestao.'_sim'.$this->_seletorSimulados->simulado->obterID().'_ins'.Core::modulo('_perfil_falso')->obterIDInstituicaoDoPerfil();

        //if( ($tempCache = Core::registro('cache')->load($this->_tituloCache)) !== false ) {
        //	$this->_dados = &$tempCache['dados'];
        //	$this->_dadosProp = &$tempCache['dadosProp'];
        //	$this->_nomesDisciplinas = &$tempCache['_nomesDisciplinas'];
        //	return;
        //}


		$this->_analizadorSimulado->carregarInscritos();
		$this->_analizadorSimulado->carregarRespostasDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoPorQuestaoDosInscritos();
		$this->_analizadorSimulado->calcularRendimentoGlobalDosInscritos();
		$this->_analizadorSimulado->eliminarInscritosQueNaoCompareceram();
		//$this->_analizadorSimulado->eliminarInscritosComRendimentoNulo();
		$this->_analizadorSimulado->calcularRendimentoPorTurma();
		$this->_analizadorSimulado->calcularRendimentoPorSerie();
		$this->_analizadorSimulado->calcularRendimentoPorTurmaPorProporcao();
		$this->_analizadorSimulado->calcularRendimentoPorSeriePorProporcao();
		$this->_analizadorSimulado->calcularRendimentoPorTurmaPorProporcaoPorDisciplina();
		$this->_analizadorSimulado->calcularRendimentoPorSeriePorProporcaoPorDisciplina();
		if ( $this->_colunas['rendimento_por_disciplina'] ) {
			$this->_analizadorSimulado->calcularRendimentoPorTurmaPorDisciplina();
			$this->_analizadorSimulado->calcularRendimentoPorSeriePorDisciplina();
		}

		foreach ( $this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
			if (!isset($this->_nomesDisciplinas[$dID]))
				$this->_nomesDisciplinas[$dID] = $dNome;
		}

		// RENDIMENTO POR DISCIPLINA
		// TURMAS
		foreach($this->_analizadorSimulado->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modelo;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);

			if (isset($this->_analizadorSimulado->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorTurma[$tID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado->rendimentoPorTurmaPorDisciplina[$tID] ) ) {
				foreach ( $this->_analizadorSimulado->rendimentoPorTurmaPorDisciplina[$tID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados[] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado->nomesSeries as $sID => $sNome) {
			$dado = $this->_modelo;

			$dado['nome'] = 'ANO';
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']);

			// RENDIMENTO POR DISCIPLINA
			if ( $this->_colunas['rendimento_por_disciplina'] && isset( $this->_analizadorSimulado->rendimentoPorSeriePorDisciplina[$sID] ) ) {
				foreach ( $this->_analizadorSimulado->rendimentoPorSeriePorDisciplina[$sID] as $dID => $desempenho ) {
					if (isset( $desempenho['rendimento'] ))
						$dado['rendimento_por_disciplina'][$dID] = round($desempenho['rendimento']);
				}
			}

			$this->_dados[] = $dado;
		}


		// PROPORÇÃO DE RENDIMENTO
		// TURMAS
		foreach($this->_analizadorSimulado->nomesTurmas as $tID => $tNome) {
			$dado = $this->_modeloProp;

			$dado['nome'] = ProvaFloripa::obterTurmaComNome($tNome);

			if (isset($this->_analizadorSimulado->rendimentoPorTurma[$tID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorTurma[$tID]['rendimento']);

			if (isset($this->_analizadorSimulado->rendimentoPorTurmaPorProporcao[$tID]))
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado->rendimentoPorTurmaPorProporcao[$tID]->obterProporcoesPorcentagens();

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] )) {
				foreach ( $this->_analizadorSimulado->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dadosProp[] = $dado;
		}

		// SERIE
		foreach($this->_analizadorSimulado->nomesSeries as $sID => $sNome) {
			$dado = $this->_modeloProp;

			$dado['nome'] = ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );
			$dado['_rede'] = true;

			if (isset($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']))
				$dado['rendimento'] = round($this->_analizadorSimulado->rendimentoPorSerie[$sID]['rendimento']);

			if (isset($this->_analizadorSimulado->rendimentoPorSeriePorProporcao[$sID]))
				$dado['rendimento_por_proporcao'] = $this->_analizadorSimulado->rendimentoPorSeriePorProporcao[$sID]->obterProporcoesPorcentagens();

			// PROPORCAO POR DISCIPLINA
			if (isset( $this->_analizadorSimulado->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] )) {
				foreach ( $this->_analizadorSimulado->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] as $dID => $proporcao ) {
					$dado['rendimento_por_proporcao_por_disciplina'][$dID] = $proporcao->obterProporcoesPorcentagens();
				}
			}

			$this->_dadosProp[] = $dado;
		}

		// CACHE
		//Core::registro('cache')->save(array(
		//	'dados' => &$this->_dados,
		//	'dadosProp' => &$this->_dadosProp,
		//	'_nomesDisciplinas' => $this->_nomesDisciplinas
		//), $this->_tituloCache,	array(), CACHE_RELATORIOS);
	}

	protected function gerarGrafico() {
		if (!count($this->_dados))
			return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		$nomesDisciplinas = $this->_nomesDisciplinas;
		$nomesDisciplinas[-1] = 'Global';
		foreach ( $nomesDisciplinas as $dID => $dNome ) {

				$labelsX = $barra = $dados = array();

				foreach($this->_dados as $k => &$d) {
					if ($dID == -1) {
						$labelsX[] = wordwrap($d['nome'], 20);
						$dados[] = $d['rendimento'];
					} elseif (isset( $d['rendimento_por_disciplina'][$dID] )) {
						$labelsX[] = wordwrap($d['nome'], 20);
						$dados[] = $d['rendimento_por_disciplina'][$dID];
					}
				}

				$grafico = new Graph(800, 180, $this->_tituloCache.'_'.$dID.'_1.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetMargin(34, 0, 20, 30);
				$grafico->setFrame(false);
				$grafico->title->SetFont(FF_ARIAL,FS_BOLD, 9);
				$grafico->title->Set($dNome);
				//$grafico->SetMarginColor('white');
				$grafico->SetScale('textint', 0, 100, 0, 0);
				$grafico->yaxis->scale->ticks->Set(20);
				$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
				$grafico->yaxis->SetLabelFormat('%d%%');
				//$grafico->yaxis->title->Set('Quantidade de alunos (%)');
				$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
				$grafico->xaxis->SetFont(FF_ARIAL,FS_NORMAL, 9);
				//$grafico->Set90AndMargin(140, 5, 5, 5);

				if (count($dados)) {
					$grafico->xaxis->SetTickLabels($labelsX);

					$barra = new BarPlot( $dados );

					$coresBarras = array_fill(0, count($dados), ($dID == -1 ? self::GRAFICO_PROVA_COR : self::GRAFICO_DISCIPLINA_COR));
					$coresBarras[count($dados) - 1] = ($dID == -1 ? self::GRAFICO_PROVA_COR_REDE : self::GRAFICO_DISCIPLINA_COR_REDE);

					$barra->SetFillColor( $coresBarras );
					$barra->value->SetColor( ($dID == -1 ? self::GRAFICO_PROVA_COR_TEXTO : self::GRAFICO_DISCIPLINA_COR_TEXTO) );
					$barra->value->Show();
					$barra->value->SetFormat('%d%%');
					//$barra->value->SetColor( self::COR_TEXTO );
					$barra->SetValuePos('center');
					$barra->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
					$barra->SetWidth(0.6);

					$grafico->Add($barra);
					$grafico->Stroke();
				}

		}
	}

	protected function gerarGraficoProp() {
		if (!count($this->_dadosProp))
			return;

		return;

		require_once 'jpgraph/jpgraph.php';
		require_once 'jpgraph/jpgraph_bar.php';

		$nomesDisciplinas = $this->_analizadorSimulado->nomesDisciplinas;
		$nomesDisciplinas[-1] = 'Global';
		foreach ( $nomesDisciplinas as $dID => $dNome ) {


				$grafico = new Graph( 700 , count($this->_dadosProp) * 40 + 26, $this->_tituloCache.'_'.$dID.'_0.png', (int) CACHE_RELATORIOS / 60, false);
				$grafico->SetMargin(34, 0, 10, 30);
				$grafico->setFrame(false);
				//$grafico->SetMarginColor('white');
				$grafico->SetScale('textint', 0, 100, 0, 0);
				$grafico->yaxis->scale->ticks->Set(100);
				$grafico->yaxis->SetFont(FF_ARIAL,FS_NORMAL, 8);
				//$grafico->yaxis->title->Set('Quantidade de alunos (%)');
				$grafico->yaxis->title->SetFont(FF_ARIAL,FS_BOLD);
				$grafico->yaxis->HideLabels();
				$grafico->xaxis->SetFont(FF_ARIAL,FS_NORMAL, 9);
				$grafico->Set90AndMargin(150, 5, 5, 30);

				$grafico->legend->Pos(0.2,0.990,'left','bottom');
				$grafico->legend->SetLayout(LEGEND_HOR);
				$grafico->legend->SetFillColor('white');
				$grafico->legend->SetFrameWeight(0);
				$grafico->legend->SetShadow(false);
				$grafico->legend->SetReverse(true);
				$grafico->legend->SetFont(FF_ARIAL,FS_NORMAL, 9);

				$txt = new Text('Rendimento:');
				$txt->SetFont(FF_ARIAL,FS_NORMAL, 9);
				$txt->SetColor('black');
				$txt->SetPos(0.15, 0.99999,'center','bottom');
				$txt->SetBox('white','white');
				$grafico->AddText($txt);

				$labelsX = $barras = array();
				$dadosProporcoes = array(
					ProporcaoDeDesempenho::PROPORCAO_1 => array(),
					ProporcaoDeDesempenho::PROPORCAO_2 => array(),
					ProporcaoDeDesempenho::PROPORCAO_3 => array(),
					ProporcaoDeDesempenho::PROPORCAO_4 => array(),
					ProporcaoDeDesempenho::PROPORCAO_5 => array()
				);

				foreach($this->_dadosProp as $k => &$d) {
					if ($dID == -1) {
						$labelsX[] = $d['nome'] . ' ('. $d['rendimento'] .'%)'; //wordwrap($d['nome'], 20);
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_1 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho::PROPORCAO_1];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_2 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho::PROPORCAO_2];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_3 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho::PROPORCAO_3];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_4 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho::PROPORCAO_4];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_5 ][] = $d['rendimento_por_proporcao'][ProporcaoDeDesempenho::PROPORCAO_5];
					} elseif (isset( $d['rendimento_por_proporcao_por_disciplina'][$dID] )) {
						$labelsX[] = $d['nome'] . ' ('. $d['rendimento'] .'%)'; //wordwrap($d['nome'], 20);
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_1 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho::PROPORCAO_1];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_2 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho::PROPORCAO_2];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_3 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho::PROPORCAO_3];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_4 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho::PROPORCAO_4];
						$dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_5 ][] = $d['rendimento_por_proporcao_por_disciplina'][$dID][ProporcaoDeDesempenho::PROPORCAO_5];
					}
				}

				$grafico->xaxis->SetTickLabels($labelsX);

				$barras[ ProporcaoDeDesempenho::PROPORCAO_1 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_1 ] );
				$barras[ ProporcaoDeDesempenho::PROPORCAO_1 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_1_PROVA_COR : self::GRAFICO_PROPORCAO_1_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho::PROPORCAO_2 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_2 ] );
				$barras[ ProporcaoDeDesempenho::PROPORCAO_2 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_2_PROVA_COR : self::GRAFICO_PROPORCAO_2_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho::PROPORCAO_3 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_3 ] );
				$barras[ ProporcaoDeDesempenho::PROPORCAO_3 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_3_PROVA_COR : self::GRAFICO_PROPORCAO_3_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho::PROPORCAO_4 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_4 ] );
				$barras[ ProporcaoDeDesempenho::PROPORCAO_4 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_4_PROVA_COR : self::GRAFICO_PROPORCAO_4_DISCIPLINA_COR);

				$barras[ ProporcaoDeDesempenho::PROPORCAO_5 ] = new BarPlot( $dadosProporcoes[ ProporcaoDeDesempenho::PROPORCAO_5 ] );
				$barras[ ProporcaoDeDesempenho::PROPORCAO_5 ]->SetFillColor($dID == -1 ? self::GRAFICO_PROPORCAO_5_PROVA_COR : self::GRAFICO_PROPORCAO_5_DISCIPLINA_COR);


				$barras[ ProporcaoDeDesempenho::PROPORCAO_1 ]->SetLegend(ProporcaoDeDesempenho::PROPORCAO_1);
				$barras[ ProporcaoDeDesempenho::PROPORCAO_2 ]->SetLegend(ProporcaoDeDesempenho::PROPORCAO_2);
				$barras[ ProporcaoDeDesempenho::PROPORCAO_3 ]->SetLegend(ProporcaoDeDesempenho::PROPORCAO_3);
				$barras[ ProporcaoDeDesempenho::PROPORCAO_4 ]->SetLegend(ProporcaoDeDesempenho::PROPORCAO_4);
				$barras[ ProporcaoDeDesempenho::PROPORCAO_5 ]->SetLegend(ProporcaoDeDesempenho::PROPORCAO_5);


				foreach ($barras as &$barra) {
					$barra->value->Show();
					$barra->value->SetFormat('%d%%');
					$barra->value->SetColor( self::GRAFICO_COR_TEXTO );
					$barra->SetValuePos('center');
					$barra->value->SetFont(FF_ARIAL,FS_NORMAL, 8);
				}


				// new GroupBarPlot
				$gruposBarras = new AccBarPlot( array(
					$barras[ ProporcaoDeDesempenho::PROPORCAO_1 ],
					$barras[ ProporcaoDeDesempenho::PROPORCAO_2 ],
					$barras[ ProporcaoDeDesempenho::PROPORCAO_3 ],
					$barras[ ProporcaoDeDesempenho::PROPORCAO_4 ],
					$barras[ ProporcaoDeDesempenho::PROPORCAO_5 ]
				) );

				$gruposBarras->SetWidth(0.64);

				$grafico->Add($gruposBarras);
				$grafico->Stroke();

		}
	}

	// apenas temporário
	private function encondarParametros() {
		return false;

		$parametros = array( 'colunas' => array( 'nome',
												 'rendimento' ),
							 'linhas' => array(),
							 'config' => array('diretor' => true) );

		echo chunk_split(base64_encode(serialize( $parametros )));

		exit();
	}
}

?>