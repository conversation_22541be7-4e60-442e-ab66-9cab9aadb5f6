<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => array(),
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$titulo = $this->_relatorio->obterNome() .'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );


if($this->_seletorSimulados->simulado->obterProdTxt()){
	$titulo ='Rendimento das turmas na prova(produção textual) e seus aspectos'.'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );;

}
$tabela['titulo']  = $titulo;
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
	%s',
	Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome()
);


ob_start();

echo '<div align="'. ($this->obterModoVisualizacao() == self::PDF ? 'left' : 'center') .'">';

$dID = -1;
echo '<img style="margin: 3px;" src="upload/graficos/'. $this->_tituloCache .'_'. $dID .'_1.png" border="0" />';

$i = 1;
foreach ($this->_nomesDisciplinas as $dID => $dNome) {
	echo '<img style="margin: 3px;" src="upload/graficos/'. $this->_tituloCache .'_'. $dID .'_1.png" border="0" />';
	if (++$i % 3 ==0)
		echo '<br />';
}

echo '</div>';

$tabela['extra'] = ob_get_clean();
$relHTML[] = $tabela;
?>