<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => false
);

$tabela['titulo'] = $this->_relatorio->obterNome() .'<br />'. ProvaFloripa::obterSeriePeloNomeSimulado( $this->_seletorSimulados->simulado->obterNome() );
$tabela['descricao'] = sprintf(
	'<strong>SECRETARIA MUNICIPAL DE EDUCAÇÃO DE '.MUNICIPIO.'</strong>'
);

ob_start();
?>
	<tr>
		<th rowspan="2">&nbsp;</th>
		<th>&nbsp;</th>
		<th style="text-align: center;" colspan="2">Relativo</th>
		<th style="text-align: center;" colspan="2">Absoluto</th>
		<th>&nbsp;</th>
	</tr>
	<tr>
		<th>Fase</th>
		<th>Presentes</th>
		<th>Ausentes</th>
		<th>Presentes</th>
		<th>Ausentes</th>
		<th>Total</th>
	</tr>
<?
$tabela['th'] = ob_get_clean(); ob_start();

if ( count($this->_dados) ) {
	$i = 0;
	foreach ( $this->_dados as $k => &$d2 ) {//&$d ) {
		//foreach ( $d as $k2 => &$d2 ) {
			//if (!in_array($k2, $dados_mostrar)) continue;

			if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
				echo $tabela['th'];
?>
<?php for ($i=1; $i <= $this->_fases; $i++){ ?>
	<tr <?= $k === '_rede' ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<? if ($this->_colunas['nome'] && $i === 1) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> nowrap="nowrap" rowspan="<?= @count($this->_fases); ?>"><?= @$d2[$i]['nome']; ?></td><? } ?>
		<? if ($this->_colunas['fase']) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$i; ?></td><? } ?>
		<? if ($this->_colunas['presentes_rel']) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d2[$i]['presentes_rel']; ?>% </td><? } ?>
		<? if ($this->_colunas['ausentes_rel']) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d2[$i]['ausentes_rel']; ?>% </td><? } ?>
		<? if ($this->_colunas['presentes_abs']) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d2[$i]['presentes_abs']; ?> </td><? } ?>
		<? if ($this->_colunas['ausentes_abs']) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d2[$i]['ausentes_abs']; ?> </td><? } ?>
		<? if ($this->_colunas['total']) { ?><td <?= $k === '_rede' ? 'bgcolor="#dddddd"' : ''; ?> align="center"><?= @$d2[$i]['total']; ?> </td><? } ?>
	</tr>
<?php } ?>	
<?
			$i++;
		//}
	}
} else {
	echo $tabela['th'];
?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
<?
}

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>