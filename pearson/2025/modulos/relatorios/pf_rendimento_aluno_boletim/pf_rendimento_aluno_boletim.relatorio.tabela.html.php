<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
    'td' => null,
    'th' => null,
    'extra' => null,
    'titulo' => null,
    'descricao' => null,
    'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
    'nao_quebrar_pagina' => $this->obterModoVisualizacao() == self::PDF ? true : false,
    'footer' => '&nbsp;'
);

$mun_logo = Core::diretiva('BOLETIM_V2:municipio:logo');
$projeto = Core::diretiva('BOLETIM_V2:municipio:projeto');

//---------------------------------------------------------------------------------------------------------------------------------------------

$ciclo = 0;
foreach ($this->_dados as $dk => $dv) {
    $ciclo++;

    echo"<style type='text/css'>body {margin-top: 0cm; margin-bottom: 0cm; padding-bottom: 0cm;}</style>";
    echo'<div style="margin:0cm auto; padding:0cm 0cm; width:19cm; height: 146mm;">';

    echo'<TABLE width="100%" BORDER="0" align="center">
        <TR>
            <TD width="90%" style="line-height: 3.4mm;">
                <p style="color:#888888; font-size: 3.7mm; margin: 0;">
                <strong>'.$this->_escola->obterNome().'</strong><br />
                <strong>Aluno: </strong> '.$dv['nome_aluno'].'
                </p>
            </TD>
            <TD>
                <img style="width:55mm;" align="right" src="'.$mun_logo.'">
            </TD>
        </TR>
    </TABLE>';


    $serie = ProvaFloripa::obterSeriePeloNomeSimulado($this->_seletorSimulados->simulado->obterNome());
    $avaliacao = explode(" ", $serie);
    if(stristr($serie, 'MATERNAL') !== FALSE || stristr($serie, 'JARDIM') !== FALSE || stristr($serie, 'BERCÁRIO') !== FALSE || stristr($serie, 'BERÇÁRIO') !== FALSE || stristr($serie, 'BERCARIO') !== FALSE){
        $av_serie = $serie;
    }
    elseif(stristr($serie, 'ESPECIAL') === FALSE && stristr($serie, 'TERMO') === FALSE && stristr($serie, 'EJA') === FALSE){
        $av_serie = $avaliacao[0].' '.$avaliacao[1].' - '.$avaliacao[2].' '.$avaliacao[3];
    }
    else{
        $av_serie = $avaliacao[0].' '.$avaliacao[1].' '.$avaliacao[2].' - '.$avaliacao[3].' '.$avaliacao[4];
    }

    echo'<div style="padding-bottom: 0.5%; line-height: 88%; font-weight: bold; font-size: 4.2mm; color: #888888;" align="center" class="rlt_painel_Titulo gigante">
            <p style="margin-top: 1%; margin-bottom: 0.5%; text-align: center;font-size: 5.2mm; color: #888888;">'.$projeto.'</p>
            '.$this->_relatorio->obterNome().' - '.$av_serie.' - '.$dv['nome_turma'].'
        </div>';

    echo'<div style="" align="center">
            <img src="upload/graficos/'.$dv['grafico_aluno'].'" border="0" />
        </div>';

    echo'<table style="width:100%;" class="rltl" cellspacing="0" cellpadding="2" align="center">
            <tr style="background-color: #f6f6f6; color: #737373; font-weight: bold; white-space: nowrap;">';
                echo'<th width="25%" style="font-size: 2.5mm;border: 1px solid #eaeaea; color: #434343;background-color: #f6f6f6;">Nome</th>';
                echo'<th nowrap="nowrap" align="center" style="font-size: 2.5mm;border: 1px solid #eaeaea; color: #434343;background-color: #f6f6f6;">Prova</th>';
                
                foreach ($this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
                    echo'<th align="center" nowrap="nowrap" style="font-size: 2.5mm;border: 1px solid #eaeaea; color: #434343;background-color: #f6f6f6;">'.$dNome.'</th>';
                }

                // ALUNO
                echo'<tr class="colData">';
                    echo'<td nowrap="nowrap" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.$dv['nome_aluno'].'</td>';
                    echo'<td align="center" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.$dv['rendimento_aluno'].'%</td>';

                    foreach ($this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
                        if (isset($dv['rendimento_aluno_por_disciplina'][$dID]))
                            echo '<td align="center" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.$dv['rendimento_aluno_por_disciplina'][$dID].'%</td>';
                        else
                            echo '<td align="center">Ausente</td>';
                    }
                echo'</tr>';

                // TURMA
                echo'<tr style="font-weight: bold;" class="colData">';
                    echo'<td bgcolor="#eeeeee" nowrap="nowrap" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">TURMA</td>';
                    echo'<td bgcolor="#eeeeee" align="center" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.$dv['rendimento_turma'].'%</td>';

                    foreach ($this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
                        if (isset($dv['rendimento_turma_por_disciplina'][$dID]))
                            echo '<td bgcolor="#eeeeee" align="center" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.$dv['rendimento_turma_por_disciplina'][$dID].'%</td>';
                        else
                            echo '<td bgcolor="#eeeeee">&nbsp;</td>';
                    }
                echo'</tr>';

                // ESCOLA
                echo'<tr style="font-weight: bold;" class="colData">';
                    echo'<td bgcolor="#dddddd" nowrap="nowrap" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">ESCOLA</td>';
                    echo'<td bgcolor="#dddddd" align="center" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.round($dv['rendimento_escola']['rendimento']).'%</td>';

                    foreach ($this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
                        if (isset($dv['rendimento_escola_por_disciplina'][$dID]['rendimento']))
                            echo '<td bgcolor="#dddddd" align="center" style="font-size: 2.5mm;border: 1px solid #DDDDDD; color: #434343;">'.round($dv['rendimento_escola_por_disciplina'][$dID]['rendimento']).'%</td>';
                        else
                            echo '<td bgcolor="#dddddd">&nbsp;</td>';
                    }
                echo'</tr>';
    echo'   </tr>
        </table>';

    echo"<br>";

    //---------------------------------------------------------------------------------------------------------------------------------------------

    echo'<table style="width: 100%; background-color:#fff;" class="rltl" cellspacing="0" cellpadding="2" align="center">';
    foreach ($this->_analizadorSimulado->nomesDisciplinas as $dID => $dNome) {
        if (!count($this->_questoesPorDisciplina[$dID])) continue;
        echo"<tr>";
            echo'<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; font-weight: bold; background-color:#ddd; color: #434343;" align="left" nowrap="nowrap">'.$dNome.'</td>';
        
        $arryaMaiorQueDez = false;
        foreach($this->_questoesPorDisciplina[$dID] as $key => $qID) {
            if(count($this->_questoesPorDisciplina[$dID]) == 20 && $key == 10 ){
                $arryaMaiorQueDez = true;
                echo"</tr>";
                break;
            }
            echo'<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; font-weight: bold; background-color:#ddd; color: #434343;" align="center" nowrap="nowrap">'.$this->_analizadorSimulado->nomesQuestoes[$qID].'</td>';
        }

        echo'<tr><td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white;"></td>';
        foreach($this->_questoesPorDisciplina[$dID] as $key => $qID) {
            if(count($this->_questoesPorDisciplina[$dID]) == 20 && $key == 10 ){
                $arryaMaiorQueDez = true;
                echo"</tr>";
                break;
            }

            if ($dv['rendimento_aluno_por_questao'][$qID] != 100 &&  $dv['rendimento_aluno_por_questao'][$qID] != -1 && $dv['rendimento_aluno_por_questao'][$qID] != 0 && $dv['rendimento_aluno_por_questao'][$qID] !== 'A')
                echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'parcial.gif' .'" border="0" style="" /></td>';
            elseif (isset( $dv['rendimento_aluno_por_questao'][$qID] ) && $dv['rendimento_aluno_por_questao'][$qID] === 'A')
                echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_anulada.gif' .'" border="0" style="" /></td>';
            elseif (isset( $dv['rendimento_aluno_por_questao'][$qID] ) && $dv['rendimento_aluno_por_questao'][$qID] == 100)
                echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_correta.gif' .'" border="0" style="" /></td>';
            elseif (isset( $dv['rendimento_aluno_por_questao'][$qID] ) && $dv['rendimento_aluno_por_questao'][$qID] == -1)
                echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_anulada.gif' .'" border="0" style="" /></td>';
            else
                echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_errada.gif' .'" border="0" style="" /></td>';
        }

        if($arryaMaiorQueDez){
            echo'<tr><td style="font-size: 2.5mm;border: 1px solid #DDDDDD; font-weight: bold; color: #434343;" align="center" nowrap="nowrap"></td>';
                for($i = 10; $i <= 19 ; $i++) {
                    echo'<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; font-weight: bold; color: #434343;" align="center" nowrap="nowrap">'.$this->_analizadorSimulado->nomesQuestoes[$this->_questoesPorDisciplina[$dID][$i]].'</td>';
                }
            echo'</tr>';
            echo'<tr><td style="font-size: 2.5mm;border: 1px solid #DDDDDD; font-weight: bold; color: #434343;" align="center" nowrap="nowrap"></td>';
            for($i = 10; $i <= 19 ; $i++) {
                if ($dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]] != 100 &&  $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]] != -1 && $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]]!= 0  && $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]] !== 'A')
                    echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'parcial.gif' .'" border="0" /></td>';
                elseif (isset( $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]] ) &&  $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]]  === 'A')
                    echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_anulada.gif' .'" border="0" style="" /></td>';
                elseif (isset( $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]] ) &&  $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]]  == 100)
                    echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_correta.gif' .'" border="0" style="" /></td>';
                elseif (isset( $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]] ) &&  $dv['rendimento_aluno_por_questao'][$this->_questoesPorDisciplina[$dID][$i]]  == -1)
                    echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_anulada.gif' .'" border="0" style="" /></td>';
                else
                    echo '<td style="font-size: 2.5mm;border: 1px solid #DDDDDD; background-color: white; color: #434343;" align="center"><img src="'. Core::diretiva('ESTILO:DIRETORIO:media') . 'questao_errada.gif' .'" border="0" style="" /></td>';

            }
        }
        echo"</tr>";
    }
    echo'</table>';

    //---------------------------------------------------------------------------------------------------------------------------------------------

    echo'<div class="minusculo" style="display:block;">    
            <div style="padding-top: 0.5%; padding-left: 1%;">
                <table align="center" cellpadding="1" cellspacing="0" class="rlt_tabela_extra" style="width: 100%;">
                    <tr>
                        <td width="2%" style="height:2%;"> <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
questao_correta.gif" border="0" style="" /> </td>
                        <td nowrap="nowrap" style="font-size:2.5mm; color: #434343;">Questão correta</td>
                        <td width="3%"> </td>
                        <td width="2%" style="height:2%;"> <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
questao_errada.gif" border="0" style="" /> </td>
                        <td nowrap="nowrap" style="font-size:2.5mm; color: #434343;">Questão errada</td>
                        <td width="3%"> </td>
                        <td width="2%" style="height:2%;"> <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
parcial.gif" border="0" style="" /> </td>
                        <td nowrap="nowrap" style="font-size:2.5mm; color: #434343;">Questão parcialmente correta</td>
                        <td width="3%"> </td>
                        <td width="2%" style="height:2%;"> <img src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
questao_anulada.gif" border="0" style="" /> </td>
                        <td nowrap="nowrap" style="font-size:2.5mm; color: #434343;">Questão em branco ou anulada pelo aluno </td>
                    </tr>
                </table>
            </div>
        </div>';

    echo'</div>';

    if($this->obterModoVisualizacao() == self::PDF && $ciclo == 2){
        echo'<div style="page-break-after: always;"></div>';
        $ciclo = 0;
    }
    else{
        echo'<hr style="margin-top: 3mm; color: #fff;background-color: #fff;border-color: #fff; border-top: 1px dashed #eee;">';
    }
}

$tabela['extra'] = ob_get_clean(); ob_start();
$relHTML[] = $tabela;
?>