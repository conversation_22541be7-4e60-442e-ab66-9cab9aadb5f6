<?
if (!defined('CORE_INCLUIDO')) exit();

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'grafico' => null,
	'titulo' => null,
	'descricao' => null,
	'tamanho' => '900px',
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false
);

$tabela['titulo'] = ProvaFloripa::obterSeriePeloNomeSimulado( $simulados[ $simuladoID ] );

if ($j) {
	$tabela['nao_quebrar_pagina'] = true;
	$tabela['quebra_invisivel'] = true;
}

if ($j == count($simuladosComDados) - 1) {
	$tabela['descricao'] = sprintf(
		'<strong>SECRETARIA DE ESTADO DE EDUCAÇÃO DE '.MUNICIPIO.'</strong><br />
		%s',
		Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterNome()
	);

	$tabela['titulo'] = $this->_relatorio->obterNome() .'<br /><br /><br /><br />'. $tabela['titulo'];
}


ob_start();
?>

	<tr>
		<? if ($this->_colunas['nome']) { ?><th align="center"><?= $this->obterBotaoAlterador('nome', false, 'Nome') ;?></th><? } ?>
		<? if ($this->_colunas['rendimento']) { ?><th align="center"> <?= $this->obterBotaoAlterador('rendimento', false, 'Global') ;?> </th><? } ?>
		<?
		// Cache de disciplinas para evitar consultas repetitivas
		$disciplinasSimulado = $this->_obterDisciplinasPorSimulado($simuladoID);
		foreach ($disciplinasSimulado as $disciplinaID => $dNome) {
			?>
			<th align="center" > <?= $dNome ;?> </th>
			<?
		}
		?>
	</tr>
	<br>

<?


if ( count($this->_dadosRendTurmas) ) {
	$i = 0;
	foreach ( $this->_dadosRendTurmas as $k => &$d ) {
		if ($d['_simulado_id'] != $simuladoID) continue;      	
	
		if ( $i == 0 || ($this->_ordenacao->repetirCabecalho > 0 && $i % $this->_ordenacao->repetirCabecalho == 0) )
			echo $tabela['th'];
		
		if($d['rendimento'] !== NULL){
			
		
		?>
        
		<tr <?= $d['_rede'] || $d['_instituicao'] ? 'style="font-weight: bold;"' : ''; ?> onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
			<? if ($this->_colunas['nome']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> nowrap="nowrap"><?= @$d['nome'] == 'PROFESSORA THEREZINHA DO MENINO JESUS SILVEIRA CAMPOS SIRERA' ? 'PROF THEREZINHA DO MENINO JESUS S. C. SIRERA' : @$d['nome']  ?> </td><? } ?>
			<? if ($this->_colunas['rendimento']) { ?><td <?= $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : ''); ?> align="center"><?= @$d['rendimento']; ?>%</td><? } ?>
			<?
			// Usar cache de disciplinas para evitar consultas repetitivas
		    foreach ($disciplinasSimulado as $disciplinaID => $dNome) {
				$bgColor = $d['_rede'] ? 'bgcolor="#dddddd"' : ($d['_instituicao'] ? 'bgcolor="#eeeeee"' : '');

				if (isset($d['rendimento_por_disciplina'][$disciplinaID])) {
					echo '<td '. $bgColor .' align="center">'. $d['rendimento_por_disciplina'][$disciplinaID] .'%</td>';
				} else {
					echo '<td '. $bgColor .' align="center">&nbsp;</td>';
				}
			}
			?>
		</tr>
		<?$i++;
		}
		
	}
} else {
	echo $tabela['th'];
	?>
	<tr onMouseOver="this.className='colDataHover'" onMouseOut="this.className='colData'" class="colData">
		<td colspan="99" align="center">Sem dados!</td>
	</tr>
	<?
}

$tabela['td'] = ob_get_clean();
$relHTML[] = $tabela;
?>