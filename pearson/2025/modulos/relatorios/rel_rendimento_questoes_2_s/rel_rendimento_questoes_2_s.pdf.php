<?
if (!defined('CORE_INCLUIDO')) exit();

//A4 - Altura - 29,7 cm, metade é 14.85 cm

$tabela = array(
	'td' => null,
	'th' => null,
	'extra' => null,
	'titulo' => null,
	'descricao' => null,
	'usar_largura_maxima' => $this->obterModoVisualizacao() == self::PDF ? true : false,
	'nao_quebrar_pagina' => $this->obterModoVisualizacao() == self::PDF ? true : false,
);

$titulo = $this->_relatorio->obterNome();
$descricao = '<strong>SECRETARIA DE ESTADO DE EDUCA&#199;&#195;O DE '.MUNICIPIO.'</strong><br />';
$descricao .= !empty($this->_dadosBasicos['escola']) ? $this->_dadosBasicos['escola'].'<br />' : '';
$descricao .= $this->_dadosBasicos['avaliacao'].'';
$descricao .= !empty($this->_dadosBasicos['turma']) ? ' - '.$this->_dadosBasicos['turma'].'' : '';

$this->_dadosQuestoes = array_chunk($this->_dadosQuestoes, 10);
foreach ($this->_dadosQuestoes as $dqk => $dqv) {
	$this->_dadosQuestoes[$dqk] = array_chunk($dqv, 2);
}

//echo"---><pre>";print_r($this->_dadosQuestoes);echo"</pre><br>---<br>";

foreach ($this->_dadosQuestoes as $dqk => $divDez) {
	if(count($divDez)<=0){continue;}

	echo'<div style="padding-bottom: 10px; line-height: 88%" class="medio">';
		echo $descricao;
	echo'</div>';
	echo'<div style="padding-bottom: 15px; line-height: 88%; font-weight: bold;" align="center" class="rlt_painel_Titulo gigante">';
		echo $titulo;
	echo'</div>';

	echo'<TABLE width="100%" align="center" border=0 style="width:190mm;height:250mm;">';

	foreach ($divDez as $ddk1 => $divDois) {
		if(count($divDois)<=0){continue;}

		echo'<TR>';
		foreach ($divDois as $ddk2 => $ddv) {
			if(count($ddv)<=0){continue;}

			echo'<TD>';

			echo'<p style="font-size:9px;margin:0;">'.$ddv['num'].') '.$ddv['nome'].' - '.$ddv['disciplina'].'</p>';
		    if(!empty($ddv['img'])){
		    	echo'<p style="text-align:center;margin:0;"><img src="'.$ddv['img'].'" style="width:90mm;"/></p>';
		    }
		    else{
		    	echo'<p>Sem dados!</p>';
		    }
				
			echo'</TD>';
		}
		echo'</TR>';
	}

	echo'</TABLE>';

	echo'<div style="page-break-after:always;"></div>';
}

$tabela['extra'] = ob_get_clean(); ob_start();
$relHTML[] = $tabela;
?>