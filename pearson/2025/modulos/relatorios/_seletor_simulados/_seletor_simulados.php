<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('CarregadorUsuarioEspecifico', null, true);
Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('Inscricao', null, true);
Core::incluir('Aula', null, true);
Core::incluir('Turma', null, true);

class MSeletorSimulados
{
	public $simulado;
	public $relatorio;

	protected $_simulados;
	protected $_selecionado = null;
	protected $_ordenacao;

	public function __construct (Ordenacao &$ordenacao, $auto_preparar = true)	{
		$this->_ordenacao = $ordenacao;

		$this->simulado = new Simulado(null);

		if ($auto_preparar)
			$this->_prepararListaSimulados();
	}

	public function configurarCampoSeletorSimulados ($rel_secao = '') {
		$dataRealizacao = null;
		if ( Core::diretiva('IDS_RELS_GABARITOS_OFFLINE') !== false )
			$dataRealizacao = Core::diretiva('IDS_RELS_GABARITOS_OFFLINE');

		$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios');//$rel_secao);

		if(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR){
			if(in_array('3', Core::registro('usuario')->obterGrupos()) === true){
				$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();		
				$turmaProf = $professor->obterArrayTurmasComAulas();
				$turmaProf = key($turmaProf);
				$sids = Aula::obterSimuladosPorTurmaID($turmaProf);

				foreach ($simus as $sk => $sv) {
					if(!in_array($sk, $sids)){
						unset($simus[$sk]);
					}
				}
			}
			else{
				$turma = Core::modulo('_perfil_falso')->_perfil_turma;
				$turma->carregar();

				//print_r($turma);
				//print_r($simus);

				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if($turma->obterSerie()->obterID() != $simulado->obterSerieAvaliacao()->obterID()){
						unset($simus[$sk]);
					}
				}
				//print_r($simus);
				//echo'<br>';echo'<br>';echo'<br>';echo'<br>';echo'<br>';echo'<br>';
			}
		}
		elseif(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO){
			$perfil_falso_user = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			$simus = Inscricao::obterSimuladosPeloAlunoPorSecao($perfil_falso_user,'relatorios');
		}
		//print_r($simus);
		//echo'<br>';echo'<br>';echo'<br>';echo'<br>';echo'<br>';echo'<br>';

		/* echo'1---><pre>';print_r($dataRealizacao);echo'</pre><br>+++<br>';
		echo'2---><pre>';print_r(Core::registro('usuario')->obterGrupo());echo'</pre><br>+++<br>';
		echo'3---><pre>';print_r($this->relatorio);echo'</pre><br>+++<br>';

		echo'4---><pre>';print_r($dataRealizacao);echo'</pre><br>+++<br>';
		echo'5---><pre>';print_r(explode(',', $dataRealizacao));echo'</pre><br>+++<br>';
		echo'6---><pre>';print_r($simus);echo'</pre><br>+++<br>';

		echo'<br>========================================<br>';
		echo'7---><pre>';var_dump(Core::registro('usuario')->obterGrupo() == '2' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false);echo'</pre><br>+++<br>';
		echo'7---><pre>';var_dump(Core::registro('usuario')->obterGrupo() == '5' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false);echo'</pre><br>+++<br>';
		echo'7---><pre>';var_dump(Core::registro('usuario')->obterGrupo() == '3' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false);echo'</pre><br>+++<br>';
		echo'<br>========================================<br>'; */

		$bimestreRecente = null;
		if($this->relatorio == 181 || $this->relatorio == 182 || $this->relatorio == 183){
			$bimestreRecente = Simulado::obterMaiorBimestre();
		}

		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			if($bimestreRecente != null){
				if($simulado->obterBimestre() != $bimestreRecente){
					unset($simus[$sk]);
					continue;
				}

				if($simulado->obterSerieAvaliacao()->obterID() == 1 || $simulado->obterSerieAvaliacao()->obterID() == 2){
					unset($simus[$sk]);
					continue;
				}
			}

			if(Core::registro('usuario')->obterGrupo() == '1' || Core::registro('usuario')->obterGrupo() == '12'){
				//nao faz nada
			}
			elseif(Core::registro('usuario')->obterGrupo() == '2' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false){
				//nao faz nada
			}
			elseif(Core::registro('usuario')->obterGrupo() == '5' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false){
				//nao faz nada
			}
			elseif(Core::registro('usuario')->obterGrupo() == '3' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false){
				//nao faz nada
			}
			else{
				if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
					unset($simus[$sk]);
				}

				if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
					unset($simus[$sk]);
				}
						
				if(time() <= $simulado->obterDataRealizacaoFim()){
					unset($simus[$sk]);
				}
			}
		}

		/* echo'1---><pre>';print_r($dataRealizacao);echo'</pre><br>+++<br>';
		echo'2---><pre>';print_r(Core::registro('usuario')->obterGrupo());echo'</pre><br>+++<br>';
		echo'3---><pre>';print_r($this->relatorio);echo'</pre><br>+++<br>';

		echo'4---><pre>';print_r($dataRealizacao);echo'</pre><br>+++<br>';
		echo'5---><pre>';print_r(explode(',', $dataRealizacao));echo'</pre><br>+++<br>';

		echo'6---><pre>';print_r($simus);echo'</pre><br>+++<br>'; */

		//print_r($simus);
		//echo'<br>';echo'<br>';echo'<br>';echo'<br>';echo'<br>';echo'<br>';
		//exit;

		#debugar ate aqui pra entender pq ele está removendo a avaliação disponivel do sistema.

		$this->fixarArraySimulados($simus);

		$segundo = null;
		$simuladosFormulario = array();//Campo::NULO => '');
		foreach ($this->_simulados as $id => $nome) {
			$simuladosFormulario[$id] = $nome;

			if ($segundo == null)
				$segundo = $id;
		}

		$selecionado = $segundo;
		if ( Core::diretiva('_seletor_simulados.selecionado.relatorios') !== false && isset($this->_simulados[Core::diretiva('_seletor_simulados.selecionado.relatorios')]) )
			$selecionado = Core::diretiva('_seletor_simulados.selecionado.relatorios');

		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_simulado',
												'etiqueta' => '<strong>Selecione uma prova</strong>',
												'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($simuladosFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $simuladosFormulario,
												'valor_pos_erro' => $selecionado,
												'autocomplete_off' => true,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function fixarArraySimulados ($simulados)
	{
		$this->_simulados = $simulados;
	}

	public function fixarRelatorio ($relatorio)
	{
		$this->relatorio = $relatorio;
	}

	public function ajustarSimuladoSelecionado ()
	{
		$dataRealizacao = null;
		if ( Core::diretiva('IDS_RELS_GABARITOS_OFFLINE') !== false )
			$dataRealizacao = Core::diretiva('IDS_RELS_GABARITOS_OFFLINE');

		//echo'<br><br>++++++++++++<br><br>';print_r($this->_simulados);

		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = @$this->_ordenacao->campo('seletor_simulado')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_simulados[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$perfil_falso = Core::modulo('_perfil_falso');
			$perfil_falso_tipo = $perfil_falso->obterTipoPerfilSelecionado();
			$perfil_falso_user = $perfil_falso->obterPerfilSelecionado();
			$simuladoDoUsuario = Core::diretiva('_seletor_simulados.selecionado.relatorios');

			if($perfil_falso_tipo == MPerfilFalso::ALUNO){
				$this->_simulados = Inscricao::obterSimuladosPeloAlunoPorSecao($perfil_falso_user,'relatorios');
				if(array_key_exists($simuladoDoUsuario, $this->_simulados)){
					$this->_selecionado = $simuladoDoUsuario;								
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);												
				}
			}
			elseif($perfil_falso_tipo == MPerfilFalso::PROFESSOR){
				//print('sdojghsdghuighdghdidhfi555555555555555');
				//print_r($this->_simulados);

				if(array_key_exists($simuladoDoUsuario, $this->_simulados)){
					$this->_selecionado = $simuladoDoUsuario;								
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);												
				}
			}
			elseif($perfil_falso_tipo == MPerfilFalso::DIRETOR){
				$escola = $perfil_falso->obterInstituicaoDoPerfil();
				$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios');
				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if(Core::registro('usuario')->obterGrupo() == '1' || Core::registro('usuario')->obterGrupo() == '12'){
						//nao faz nada
					}
					elseif(Core::registro('usuario')->obterGrupo() == '2' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false){
						//nao faz nada
					}
					elseif(Core::registro('usuario')->obterGrupo() == '5' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false){
						//nao faz nada
					}
					elseif(Core::registro('usuario')->obterGrupo() == '3' && array_search($this->relatorio, explode(',', $dataRealizacao)) !== false){
						//nao faz nada
					}
					else{
						if((time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento())){
							unset($simus[$sk]);
							continue;
						}

						if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
							unset($simus[$sk]);
							continue;
						}
						
						if(time() <= $simulado->obterDataRealizacaoFim()){
							unset($simus[$sk]);
							continue;
						}

						$turmas = Turma::obterArrayTurmasParaFormularioPorSimuladoIntituicao($escola->obterID(),$sk);				
						if(count($turmas)<=0){
							unset($simus[$sk]);
						}
					}
				}
				
				if(count($simus)>0){
					if(array_key_exists($simuladoDoUsuario, $simus)){
						$this->_selecionado = $simuladoDoUsuario;
					}
					else{
						$this->_selecionado = reset($simus);					
					}
				}
				else{
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);
				}
			}
			else{
				$this->_selecionado = $simuladoDoUsuario;
				if($simuladoDoUsuario == null){
					reset($this->_simulados);
					$this->_selecionado = key($this->_simulados);			
				}
			}
		}

		if($this->_selecionado != null){
			$this->_ordenacao->campo('seletor_simulado')->fixar('valor', $this->_selecionado);
			Core::fixarDiretiva('_seletor_simulados.selecionado.relatorios', $this->_selecionado);
			$this->simulado = new Simulado($this->_selecionado);
			$this->simulado->carregar();
		}
		else{
			Core::fixarDiretiva('_seletor_simulados.selecionado.relatorios', null);
			$this->simulado = new Simulado(null);
		}
	}

	protected function _prepararListaSimulados () {
		$this->_simulados = array();

		$professor = null;

		if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
			$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
		//else
			//$professor = CarregadorUsuarioEspecifico::obterProfessor();

		if ( $professor != null ) {
			$turmasPossiveis = array();
			//$disciplinasPossiveis = array();
			foreach ( $professor->obterAulas() as $aula ) {
				$turmasPossiveis[] = @$aula->obterTurma()->obterID();
				//$disciplinasPossiveis[] = Core::registro('db')->formatarValor( @$aula->obterDisciplina()->obterID() );
			}

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT simulados.*, series.s_nome AS series_s_nome FROM simulados
				  INNER JOIN turmas ON turmas.t_id = %s
				  INNER JOIN series ON series.s_id = turmas.t_serie
				  WHERE simulados.s_instituicao = %s
				  ORDER BY s_ordem',
				  Core::registro('db')->formatarValor( array_pop($turmasPossiveis) ),
				  //Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(Core::registro('usuario')->obterGrupo() != '1' && Core::registro('usuario')->obterGrupo() != '11'){
						if(time() >= $row['s_data_inicio_lancamento'] AND time() <= $row['s_data_fim_lancamento']){
							continue;
						}

						if(time() >= $row['s_data_inicio_inscricao'] AND time() <= $row['s_data_fim_inscricao']){
							continue;
						}
					}

					if ( time() < $row['s_data'] )
						continue;

					if ( strstr($row['s_nome'], $row['series_s_nome']) === false ) continue;

					if ( !isset($this->_simulados[$row['s_id']]) )
						$this->_simulados[$row['s_id']] = strftime('%d/%m/%Y', $row['s_data']) . ' | ' .$row['s_nome'];
				}
			}
			$rs->free();
		} else {
			$aluno = null;
			if ( Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::ALUNO && Core::modulo('_perfil_falso')->obterPerfilSelecionado() != null )
				$aluno = Core::modulo('_perfil_falso')->obterPerfilSelecionado();
			else
				$aluno = CarregadorUsuarioEspecifico::obterAluno();

			$sqlSimulado = null;
			if ( $aluno != null )
				$sqlSimulado = ' INNER JOIN simulados_inscricoes ON simulados_inscricoes.si_aluno = '. Core::registro('db')->formatarValor( $aluno->obterID() ) .' AND simulados_inscricoes.si_simulado = s_id ';

			$rs = Core::registro('db')->query( sprintf(
				  'SELECT simulados.*, questoes.q_id FROM simulados
				  %s
				  INNER JOIN questoes ON questoes.q_simulado = s_id
				  WHERE s_instituicao = %s AND q_id IS NOT NULL
				  ORDER BY s_data DESC, s_nome ASC',
				  $sqlSimulado,
				  //Core::registro('db')->formatarValor( ProvaFloripa::$instituicao->obterID() ) ) );
				  Core::registro('db')->formatarValor($this->_selecionado ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if(Core::registro('usuario')->obterGrupo() != '1' && Core::registro('usuario')->obterGrupo() != '11'){
						if(time() >= $row['s_data_inicio_lancamento'] AND time() <= $row['s_data_fim_lancamento']){
							continue;
						}

						if(time() >= $row['s_data_inicio_inscricao'] AND time() <= $row['s_data_fim_inscricao']){
							continue;
						}
					}

					if ( time() < $row['s_data'] )
						continue;

					if ( !isset($this->_simulados[$row['s_id']]) )
						$this->_simulados[$row['s_id']] = strftime('%d/%m/%Y', $row['s_data']) . ' | ' .$row['s_nome'];
				}
			}
			$rs->free();
		}
	}
}

#=====================================================================================================================>

class MSeletorSimuladosPeriodos
{
	public $periodo;

	protected $_periodos;
	protected $_selecionado = null;
	protected $_ordenacao;

	public function __construct (Ordenacao &$ordenacao)	{
		$this->_ordenacao = $ordenacao;

		$this->periodo = null;
	}

	public function configurarCampoSeletorPeriodos ($rel_secao = '') {
		$simus = Simulado::obterSimuladosParaFormularioPorSecao('relatorios');

		if(Core::modulo('_perfil_falso')->obterTipoPerfilSelecionado() == MPerfilFalso::PROFESSOR){
			if(in_array('3', Core::registro('usuario')->obterGrupos()) === true){
				$professor = Core::modulo('_perfil_falso')->obterPerfilSelecionado();		
				$turmaProf = $professor->obterArrayTurmasComAulas();
				$turmaProf = key($turmaProf);
				$sids = Aula::obterSimuladosPorTurmaID($turmaProf);

				foreach ($simus as $sk => $sv) {
					if(!in_array($sk, $sids)){
						unset($simus[$sk]);
					}
				}
			}
			else{
				$turma = Core::modulo('_perfil_falso')->_perfil_turma;
				$turma->carregar();


				//print('sdhfhsdishfsjhfsdjk');

				foreach ($simus as $sk => $sv) {
					$simulado = new Simulado($sk);
					$simulado->carregar();

					if($turma->obterSerie()->obterID() !== $simulado->obterSerieAvaliacao()->obterID()){
						unset($simus[$sk]);
					}
				}
			}
		}

		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			if(Core::registro('usuario')->obterGrupo() != '1'){
				if(time() >= $simulado->obterInicioLancamento() AND time() <= $simulado->obterFimLancamento()){
					unset($simus[$sk]);
				}

				if(time() >= $simulado->obterDataInicioInscricoes() AND time() <= $simulado->obterDataFimInscricoes()){
					unset($simus[$sk]);
				}
			}
		}

		$periodos = array();
		foreach ($simus as $sk => $sv) {
			$simulado = new Simulado($sk);
			$simulado->carregar();

			$aplicacao = $simulado->obterBimestre();
			$lancamentoi = $simulado->obterInicioLancamento(true,'%d/%m/%Y');//'%d/%m/%Y %H:%M'
			$lancamentof = $simulado->obterFimLancamento(true,'%d/%m/%Y');//'%d/%m/%Y %H:%M'

			$periodos[$aplicacao] = $lancamentoi.' - '.$lancamentof;
		}

		$this->fixarArrayPeriodos($periodos);

		$segundo = null;
		$periodosFormulario = array(Campo::NULO => '');
		foreach ($this->_periodos as $id => $nome) {
			$periodosFormulario[$id] = $nome;

			if ($segundo == null)
				$segundo = $id;
		}

		$selecionado = $segundo;
		if ( Core::diretiva('_seletor_periodos.selecionado.relatorios') !== false && isset($this->_periodos[Core::diretiva('_seletor_periodos.selecionado.relatorios')]) )
			$selecionado = Core::diretiva('_seletor_periodos.selecionado.relatorios');

		$this->_ordenacao->adicionarCampo( new Campo(array( 'nome' => 'seletor_periodos',
												'etiqueta' => '<strong>Selecione um per&iacute;odo</strong>',
												'valor' => $selecionado,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::POSSIBILIDADES => array_keys($periodosFormulario)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $periodosFormulario,
												'valor_pos_erro' => $selecionado,
												'componente' => new JAlteradorDeFormulario(time(), '1', $this->_ordenacao->info('nome'), true, 'onchange="%s"')
							  )), null );
	}

	public function fixarArrayPeriodos ($periodos)
	{
		$this->_periodos = $periodos;
	}

	public function ajustarPeriodoSelecionado ()
	{
		$this->_selecionado = null;

		if($this->_ordenacao->foiEnviado()){
			$this->_selecionado = @$this->_ordenacao->campo('seletor_periodos')->obter('valor');
			if ($this->_selecionado == Campo::NULO || !isset($this->_periodos[$this->_selecionado])){
				$this->_selecionado = null;
			}
		}
		else{
			$this->_selecionado = Core::diretiva('_seletor_periodos.selecionado.relatorios');
		}

		if($this->_selecionado != null){
			$this->_ordenacao->campo('seletor_periodos')->fixar('valor', $this->_selecionado);
			Core::fixarDiretiva('_seletor_periodos.selecionado.relatorios', $this->_selecionado);
			$this->periodo = $this->_selecionado;
		}
		else{
			Core::fixarDiretiva('_seletor_periodos.selecionado.relatorios', null);
			$this->periodo = reset(array_keys($this->_periodos));
		}
	}
}

?>