<?
/**
 * @package Core
 * @subpackage Core
 */

/**
 * Faz o redireionamento HTTP de diversas formas
 * 
 * @package Core
 * @subpackage Core
 */
class Redirecionador
{
	const HEADER = 1;
	const JAVASCRIPT = 2;
	const META = 3;

	static private $_tempoDeEspera = 5;
	static private $_endereco = './';
	static private $_tipoDeRedirecionamento = self::META;
	static private $_finalizarAoRedirecionar = false;
	
	static public function redirecionar ($tipo = null, $endereco = null)
	{
		if ( $tipo != null )
			self::fixarTipoDeRedirecionamento($tipo);
		
		if ( $endereco != null )
			self::fixarEndereco($endereco);
		
		$retorno = null;
		
		switch (self::$_tipoDeRedirecionamento) {
			case Redirecionador::HEADER:
				header('Location: ' . self::$_endereco);
				break;
			case Redirecionador::JAVASCRIPT:
				$retorno = sprintf( '<script language="JavaScript">window.location="%s";</script>', self::$_endereco);
				break;
			case Redirecionador::META:
			default:
				$retorno = '<meta http-equiv="refresh" content="'. self::$_tempoDeEspera .';URL='. self::$_endereco .'">';
				break;
		}
		
		if (self::$_finalizarAoRedirecionar)
			Core::finalizar();
		
		return $retorno;
	}
	
	static public function post ($endereco = null, $dados = null)
	{
		if ( $endereco != null )
			self::fixarEndereco($endereco);

		if ( $dados == null )
			$dados = &$_POST;

		$ch = curl_init();

		if ($ch === false)
			return self::redirecionar(self::HEADER, $endereco);

		curl_setopt($ch, CURLOPT_URL, $endereco);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $dados);
		curl_setopt($ch, CURLOPT_COOKIE, session_name() .'='. session_id());

		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		curl_setopt($ch, CURLOPT_CAINFO, '/etc/apache2/ssl/avaliarede.com.br.ca-bundle');
		curl_setopt($ch, CURLOPT_CAPATH, '/etc/apache2/ssl/avaliarede.com.br.ca-bundle');

		if (self::$_finalizarAoRedirecionar)
			Core::finalizar(false);
			
		curl_exec($ch);
		curl_close($ch);
		
		exit();
	}
	
	static public function postSemRedirecionar ($endereco = null, $dados = null, $time_out = 10)
	{
		if ( $endereco != null )
			self::fixarEndereco($endereco);

		if ( $dados == null )
			$dados = &$_POST;

		$ch = @curl_init();

		if ($ch === false)
			return false;

		curl_setopt($ch, CURLOPT_URL, $endereco);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $dados);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_MAXREDIRS, 0);
		curl_setopt($ch, CURLOPT_TIMEOUT, $time_out); 

		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		curl_setopt($ch, CURLOPT_CAINFO, '/etc/apache2/ssl/avaliarede.com.br.ca-bundle');
		curl_setopt($ch, CURLOPT_CAPATH, '/etc/apache2/ssl/avaliarede.com.br.ca-bundle');

		echo curl_exec($ch);

		curl_close($ch);

		return true;
	}
	
	static public function fixarTempoDeEspera ($seg = 5)
	{
		self::$_tempoDeEspera = (int) $seg;
	}
	
	static public function fixarEndereco ($end = './')
	{
		self::$_endereco = $end;
	}
	
	static public function fixarTipoDeRedirecionamento ($red = Redirecionador::META)
	{
		self::$_tipoDeRedirecionamento = $red;
	}
	
	static public function finalizarAoRedirecionar ($finalizar = false)
	{
		self::$_finalizarAoRedirecionar = (bool) $finalizar;
	}
	
	static public function obterTempoDeEspera ()
	{
		return self::$_tempoDeEspera;
	}
	
	static public function obterEndereco ()
	{
		return self::$_endereco;
	}
	
	static public function obterTipoDeRedirecionamento ()
	{
		return self::$_tipoDeRedirecionamento;
	}
}

?>