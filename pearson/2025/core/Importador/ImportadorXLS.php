<?php
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

require_once 'PHPExcel.php';
require_once 'PHPExcel/Reader/Excel5.php';
require_once 'PHPExcel/IOFactory.php';

class ImportadorXLS
{
	protected $_dados = null;
	protected $_arquivo = null;
	protected $_qtd_cols = 0;
	protected $_qtd_lines = 0;
	protected $_ctn_check = array();
	
	public function upload ($nomeArquivo) {
		set_time_limit(0);
		
		$dir = Core::diretiva('CORE:DIRETORIO:upload','upload/').uniqid(md5(rand())).substr($nomeArquivo[0]['nome'],-4);
		@chmod(Core::diretiva('CORE:DIRETORIO:upload','upload/'), 0777);

		if (is_uploaded_file($nomeArquivo[0]['nome_temporario']) && 
			move_uploaded_file($nomeArquivo[0]['nome_temporario'], $dir) !== false ) {
			$this->_arquivo = $dir;
			@chmod($dir, 0777);
			$this->importar($dir);
			return true;
		}

		return false;
	}

	public function remover ($nomeArquivo) {
		if (@unlink($nomeArquivo)){
			return true;
		}

		return false;
	}

	public function &obterNomeArquivo () {
		return $this->_arquivo;
	}

	public function &obterQtdColunas () {
		return $this->_qtd_cols;
	}

	public function &obterQtdLinhas () {
		return $this->_qtd_lines;
	}

	public function &obterCtnCheck () {
		return $this->_ctn_check;
	}

	public function importar ($nomeArquivo) {
		set_time_limit(0);
		$this->_dados = null;
		
		$dir = $nomeArquivo;

		$this->importarImgs($dir);

exit(1);

		$objReader = new PHPExcel_Reader_Excel5();
		$objReader->setReadDataOnly(true); //com isso o sistema não pega link em celulas.
		$objPHPExcel = $objReader->load($dir);
		$objWorksheet = $objPHPExcel->getActiveSheet();

		$highestColumn = $objWorksheet->getHighestColumn();
		$highestColumnIndex = PHPExcel_Cell::columnIndexFromString($highestColumn);

		$this->_dados = $objWorksheet;
		$this->_qtd_cols = $highestColumnIndex;
		$this->_qtd_lines = $objWorksheet->getHighestRow();

		$rtc = $this->_qtd_lines;

		for ($row = 1; $row <= $rtc; ++$row) {
			for ($col = 0; $col <= $highestColumnIndex; ++$col) {
		  		$texto = $objWorksheet->getCellByColumnAndRow($col, ($row+1))->getFormattedValue();
		  		
		  		if(mb_detect_encoding($texto) == 'UTF-8'){
		  			$texto = utf8_decode($texto);
		  		}
		  	
		  		$this->_ctn_check[$row][$col] = $texto;
		  	}
		}

		if($this->_dados !== null){
			return true;
		}

		return false;
	}
	
	public function importarImgs ($nomeArquivo) {	
		$reader = new PHPExcel_Reader_Excel5();
		$reader->setReadDataOnly(false);
		$PHPExcel = $reader->load($nomeArquivo);
		$worksheet = $PHPExcel->getActiveSheet();

echo"<pre>";print_r($worksheet->getDrawingCollection());echo"</pre>";

		foreach ($worksheet->getDrawingCollection() as $drawing) {
			if ($drawing instanceof PHPExcel_Worksheet_MemoryDrawing) {
				ob_start();
				call_user_func(
					$drawing->getRenderingFunction(),
					$drawing->getImageResource()
				);
				$imageContents = ob_get_contents();
				ob_end_clean();

echo"<br><img>";print($imageContents);echo"</img><br>";				
			}
		}
	}

	public function &obterDados () {
		return $this->_dados;
	}

	public function letras($index = 0)
	{
		$letras = 0;
		$tam = 0;
		$div = 0;
		$sub = 0;
		$exibe = 0;
	
		$letras = Array("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");

		$tam = count($letras);
		
		if($index > $tam)
		{
			$div = ($index/$tam);
			settype($div, "integer");
			$sub = $tam*$div;
			$sub = ($index-$sub);
			$div = $div-1;
			$sub = $sub-1;
			if($div < 0) $div = 0;
			if($sub < 0) $sub = 0;
			$exibe = $letras[$div].$letras[$sub];
			
			return $exibe;
		}
		
		$sub = $index-1;
		if($sub < 0) $sub = 0;
		return $letras[$sub];
	}
}

?>