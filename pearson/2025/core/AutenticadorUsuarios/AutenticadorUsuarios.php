<?
/**
 * @package Core
 * @subpackage GerênciamentoDeUsuários
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Classe pra fazer autenticação de usuário
 * 
 * @package Core
 * @subpackage GerênciamentoDeUsuários
 */
class AutenticadorUsuarios
{
	/**
	 * Guarda informações básicas sobre o usuário
	 * @var Usuario
	 */
	protected $_usuario;
	
	/**
	 * Guarda configurações internas da classe
	 * Configurações padrões:
	 * <code>
	 * array(
	 * 'habilitar_cookies' => true, // habilitar o uso de cookies pra fazer autologin
	 * 'habilitar_autologin' => false, // habilitar autologin, usado no login via formulário
	 * 'expiracao_cookies' => 30, // duração dos cookies em dias
	 * 'diferenciar_letras' => false, // ativa ou desativa o login case-sensitive
	 * 'regExp_l' => '/[^a-z0-9_-s@.]/i', // preg_match pra validar os logins
	 * 'regExp_s' => '/[^a-z0-9]/i' // preg_match pra validar as senhas
	 * );
	 * </code>
	 * 
	 * @var array
	 */
	protected $_config;

	/**
	 * Construtor padrão
	 * @see Autenticacao::$_config
	 * @param array $config opcional Configurações especificas pra usar (parametros possíveis: Autenticacao::$_config)
	 * @return void
	 */
	public function __construct ($config = array())
	{
		// prepara configurações padrões
		$this->_config = array(
			'habilitar_cookies' => true,
			'habilitar_autologin' => false,
			'expiracao_cookies' => 1,
			'diferenciar_letras' => false,
			'regExp_l' => '/[^a-z0-9_-s@.]/i',
			'regExp_s' => '/[^a-z0-9]/i'
		);
		
		// atualiza a configuração padrão
		$this->_config = array_merge($this->_config, $config);
		$this->_usuario = new Usuario(null);
		$this->_usuario->fixarEstadoDeLogin(false);
	}
	
	/**
	 * Tenta autenticar usuário
	 * @param string $login opcional
	 * @param string $senha opcional
	 * @return bool
	 * 
	 * @todo Retornar vários tipos de exceção pra saber exatamente que erro ocorreu e fatorar melhor...
	 */
	public function checaLogin ($login = '', $senha = '')
	{
		@setcookie('id', '', time()-1);
		@setcookie('s', '', time()-1);
		@setcookie('l', '', time()-1);

		$id = null;
		$viaFormulario = false;			
		$gravarLog = false;
                
		if (!empty($login) && !empty($senha)) {
			$viaFormulario = true;
			
			// se diferenciar maiusculo de minisculo, deixar tudo minusculo
			if (!$this->_config['diferenciar_letras'])
				$login = strtolower($login);
			
			// senha sempre minusculo
			//$senha = strtolower($senha);
			
			// checa se o login e a senha estão de acordo
			if ( !$this->_sintaxeCorreta($login, $senha) ) {
				return false;
			}
		}
		
		// se o login não é via formulário tenta logar usando dados do SESSION e dos COOKIES
		if (!$viaFormulario) {
			if (isset($_SESSION['id']) && isset($_SESSION['s']) && isset($_SESSION['l'])) {
				$id = $_SESSION['id'];
				$senha = $_SESSION['s'];
				$login = $_SESSION['l'];
			} /*else if ($this->_config['habilitar_cookies']) {
				if (isset($_COOKIE['id']) && isset($_COOKIE['s']) && isset($_COOKIE['l'])) {
					$id = $_COOKIE['id'];
					$senha = $_COOKIE['s'];
					$login = $_COOKIE['l'];
				}
			}*/
			
			if (!$this->_config['diferenciar_letras'])
				$login = strtolower($login);

			// checa se o login e a senha estão de acordo
			if ( !$this->_sintaxeCorreta($login, $senha) ) {
				$this->_usuario->fixarEstadoDeLogin(false);
				$this->_usuario->fixarID(null);
				return false;
			}
		} else {
			$id = $this->_obterIDUsuarioPeloLogin($login, $this->_config['diferenciar_letras']);
			if ( $id !== false ) {
				$senha = md5($senha);
			} else {
				// usuario não encontrado
				$this->_usuario->fixarEstadoDeLogin(false);
				$this->_usuario->fixarID(null);
				return false;
			}
		}
				
		// caso não tenha informações suficientes pra fazer o login, retorna falha
		if (!isset($id) || !isset($senha)) {
			$this->_usuario->fixarEstadoDeLogin(false);
			$this->_usuario->fixarID(null);
			
			return false;
		}

		// tenta realmente fazer o login checando no banco de dados
		if ( Filtrador::natural($id) && $this->_avaliarLogin($id, $senha, $login) ) {		
			// guarda informações sobre o usuario
			$this->_usuario->fixarEstadoDeLogin(true);
			$this->_usuario->fixarID($id);
            #$this->_usuario->userOnChat();
			
			// atualiza SESSION
			$_SESSION['id'] = $id;
			$_SESSION['s'] = $senha;
			$_SESSION['l'] = $login;

			// guarda cookies caso estejam ativos
			/*$duracaoCookies = time() + (60 * 60 * 24 * $this->_config['expiracao_cookies']);
			if ($this->_config['habilitar_cookies'] && $this->_config['habilitar_autologin'] && $viaFormulario) {
				setcookie('id', $id, $duracaoCookies);
				setcookie('s', $senha, $duracaoCookies);
				setcookie('l', $login, $duracaoCookies);
			} else if ($viaFormulario && !$this->_config['habilitar_autologin']) {
				@setcookie('id', '', $duracaoCookies * -1);
				@setcookie('s', '', $duracaoCookies * -1);
				@setcookie('l', '', $duracaoCookies * -1);
			}*/
			
			return true;
		}
		
		$this->_usuario->fixarEstadoDeLogin(false);
		$this->_usuario->fixarID(null);
			
		return false;
	}
	
	/**
	 * Tenta deslogar o usuário
	 * @return bool
	 * 
	 * @todo retornar exceção caso usuário não esteja logado
	 */
	public function logOut ()
	{
		// remove cookies
		@setcookie('id', '', time()-1);
		@setcookie('s', '', time()-1);
		@setcookie('l', '', time()-1);
		
		// remove SESSION
		@session_unset();
		@session_destroy();
		@session_regenerate_id();
		
		$this->_usuario->fixarEstadoDeLogin(false);
		$this->_usuario->fixarID(null);
		
		return true;
	}
	
	/**
	 * Habilita o autologin. Só tem efeito se os cookies estão habilitados
	 * @param bool $habilitar opcional
	 */
	public function autoLogar ($habilitar = false)
	{
		$this->_config['habilitar_autologin'] = (bool) $habilitar;
	}
	
	public function &obterUsuario ()
	{
		return $this->_usuario;
	}
	
	protected function _obterIDUsuarioPeloLogin ($login, $diferenciarLetras = false) {
		$id = false;
		
		$loginSQL = $diferenciarLetras ? 'u_login' : 'LCASE(u_login)';
		
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT u_id FROM usuarios WHERE BINARY %s = %s LIMIT 0,1', 
			  $loginSQL,
			  Core::registro('db')->formatarValor( $login ) ) );
			
		if ($rs->num_rows == 1) {
			$row = $rs->fetch_assoc();
			$id = $row['u_id'];
		}
		$rs->free();
		
		return $id;
	}
	
	protected function _avaliarLogin ($id, $senha, $login) {
		$loginSQL = ($this->_config['diferenciar_letras'] ? 'BINARY u_login' : 'LCASE(u_login)');
	
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT u_id FROM usuarios WHERE u_id = %s AND %s = %s AND BINARY u_senha = %s LIMIT 0,1',
			   Core::registro('db')->formatarValor( $id ),
			   $loginSQL,
			   Core::registro('db')->formatarValor( $login ),
			   Core::registro('db')->formatarValor( $senha ) ) );

		return ($rs->num_rows == 1);
	}
	
	/**
	 * Checa se a sintaxe está de acordo
	 * @param string $login
	 * @param string $senha
	 * @return bool
	 */
	protected function _sintaxeCorreta ($login, $senha)
	{		
		if(strpos($login, '@') === false){
			return true;
		}
		else{
			if ( Filtrador::expRegular($login, $this->_config['regExp_l']) || Filtrador::expRegular($senha, $this->_config['regExp_s'])) {
				return false;
			} else {
				return true;
			}
		}
	}
}
?>