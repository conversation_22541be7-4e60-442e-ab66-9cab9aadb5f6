<?
class Timer {
	const TOTAL = 'total';
	
	public static $precisao = 5;
	protected static $contadores = array();
	protected static $contando = array();

	public static function iniciar($nome) {
		self::$contadores[$nome] = microtime(true);
		self::$contando[$nome] = true;
	}

	public static function parar($nome) {
		self::$contadores[$nome] = microtime(true) - self::$contadores[$nome];
		self::$contando[$nome] = false;
	}
	
	public static function remover($nome) {
		unset(self::$contadores[$nome]);
		unset(self::$contando[$nome]);
	}
	 
	public static function mostrar($nome = null, $retornar = false) {
		$saida = '';

		if ($nome != null) {
			if (!isset(self::$contadores[$nome]))
				return;
				
			if (self::$contando[$nome])
				self::parar($nome);

			$saida = '<div>Timer: <strong>'. $nome .'</strong>: '. substr(self::$contadores[$nome], 0, self::$precisao) .' segundos<br /></div>';
		} else {
			if ( count(self::$contadores) )
				$saida .= '<div><strong>Timer: </strong></div>';
				
			$i = 0;
			foreach (self::$contadores as $nome => $tempo) {
				if (self::$contando[$nome])
					self::parar($nome);
					
				$porcentagem = '';
				if ($nome == self::TOTAL)
					$porcentagem = ' (100 %)';
				else if (isset(self::$contadores[self::TOTAL]) && self::$contadores[self::TOTAL] > 0)
					$porcentagem = ' ('. substr(100 * $tempo / self::$contadores[self::TOTAL], 0, 5) . ' %)';

				$saida .= '<div style="padding-left: '. 12 * ++$i .'px"><strong>'. $nome .'</strong>: '. substr(self::$contadores[$nome], 0, self::$precisao) . ' segundos'. $porcentagem .'</div>';
			}
		}

		if ($retornar)
			return $saida;
		else
			echo $saida;
	}
}
?>