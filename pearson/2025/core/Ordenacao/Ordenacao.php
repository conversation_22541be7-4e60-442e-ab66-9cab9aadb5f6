<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('Exception.php');

abstract class Ordenacao extends Formulario
{
	const ASC = 'ASC';
	const DESC = 'DESC';

	protected $_camposParaSalvar = array();

	public function __construct ($nome, $acao = null, $metodo = Formulario::POST)
	{
		if ( is_null($acao) )
			$acao = Gerenciador_URL::gerarLinkPelaEntrada();
		
		parent::__construct( array('nome' => $nome, 'acao' => $acao, 'metodo' => $metodo) );
	}

	public function prepararOrdenacao ()
	{	
		$this->carregarFormulario();
	}
	
	public function executarPedidoDeOrdenacao ()
	{
		$this->checarFormulario();
	}
	
	public function checarFormulario ()
	{
		try
		{
			parent::checarFormulario();
		}
		catch (Formulario_Exception $e)
		{			
			throw new Ordenacao_Exception($e->getMessage());
		}
	}
	
	public function forcarValoresDeOrdenacao($ordenacao) {
		if (!is_array($ordenacao))
			return false;
			
		$ordenacao_atual = $this->_carregaArrayOrdenacoesDoBancoDeDados();
		
		if (is_array($ordenacao)) {
			foreach($ordenacao as $k => $v)
				$ordenacao_atual[$k] = $v;
		}
			
		return $this->_atualizaArrayOrdenacoesNoBancoDeDados($ordenacao_atual);
	}
	
	public function obterValoresDeOrdenacao() {
		return $this->_carregaArrayOrdenacoesDoBancoDeDados();
	}
	
	protected function _adicionarCampoParaSalvar ($nomeCampo) {
		$this->_camposParaSalvar[] = $nomeCampo;
	}
	
	protected function _atualizaOrdenacoesNoBancoDeDados ()
	{
		$ordenacao = array();

		foreach ($this->_campos as $n => $c) {
			if ( in_array($n, $this->_camposParaSalvar) )
				$ordenacao[$n] = $c->obter('valor');
		}

		return $this->_atualizaArrayOrdenacoesNoBancoDeDados($ordenacao);
	}

	protected function _carregaOrdenacoesDoBancoDeDados () {
		$ordenacao = $this->_carregaArrayOrdenacoesDoBancoDeDados();
		
		if (is_array($ordenacao)) {
			foreach ($ordenacao as $n => $v) {
				if (in_array($n, $this->_camposParaSalvar) && isset($this->_campos[$n]) ) {
					if ( $this->_campos[$n]->obterArgumento(Campo::POSSIBILIDADES) === false || (is_array( $this->_campos[$n]->obterArgumento(Campo::POSSIBILIDADES) ) && in_array($v, $this->_campos[$n]->obterArgumento(Campo::POSSIBILIDADES))) )
						$this->_campos[$n]->fixar('valor', $v);						
				}
			}
		}
	}
	
	protected function _ordenacaoCadastradaNoBancoDeDados ()
	{
		$rs = Core::registro('db')->query( sprintf("
			SELECT o_nome FROM ordenacao WHERE o_nome = '%s' AND o_usuario = '%u'",
			$this->_info['nome'],
			Core::registro('usuario')->obterID()) );
		
		return $rs->num_rows > 0;
	}
	
	protected function _atualizaArrayOrdenacoesNoBancoDeDados ($ordenacao)
	{
		if (!is_array($ordenacao))
			return false;

		if ( count($ordenacao) )
			$ordenacao = "'". @chunk_split(base64_encode(serialize($ordenacao))) ."'";
		else
			$ordenacao = 'NULL';
		
		if ( !$this->_ordenacaoCadastradaNoBancoDeDados() ) {
			Core::registro('db')->query( sprintf("INSERT INTO ordenacao(o_nome, o_ordenacao, o_usuario) VALUES ('%s', %s, '%u')", $this->_info['nome'], $ordenacao, Core::registro('usuario')->obterID()) );
		} else {
			Core::registro('db')->query( sprintf("UPDATE ordenacao SET o_ordenacao = %s WHERE o_nome = '%s' AND o_usuario = '%u'", $ordenacao, $this->_info['nome'], Core::registro('usuario')->obterID()) );
		}
	}
	
	protected function _carregaArrayOrdenacoesDoBancoDeDados ()
	{
		$rs = Core::registro('db')->query( sprintf("
			SELECT * FROM ordenacao WHERE o_nome = '%s' AND o_usuario = '%u'",
			$this->_info['nome'],
			Core::registro('usuario')->obterID()) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$ordenacao = @unserialize(base64_decode($row['o_ordenacao']));
			
			return $ordenacao;
		}
		$rs->free();
		
		return null;
	}
}

?>