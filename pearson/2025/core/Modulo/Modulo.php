<?
/**
 * @package Core
 * @subpackage Core
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('SeletorVisao.php');

/**
 * Modulo abstrato
 * 
 * @package Core
 * @subpackage Core
 * 
 * @abstract 
 */
abstract class Modulo
{
	const ARQUIVO = 'arquivo';
	const HTML = 'HTML';
	const AJAX_TEXTO = 'AJAX_TEXTO';
	const AJAX_JSON = 'AJAX_JSON';
	const AJAX_JAVASCRIPT = 'AJAX_JAVASCRIPT';
	const CUSTOMIZADO = 'customizado';
	
	const VISAO_INFO = 'info';
	const VISAO_MAIS_INFO = 'mais_info';
	
	public $seletorVisao;
	
	protected $_tipoSaida = self::HTML;
	protected $_conteudo = array();

	public function __construct ()
	{		
		$this->seletorVisao = new SeletorVisao();
		$this->seletorVisao->adicionarVisao(self::VISAO_INFO, true);
		$this->seletorVisao->adicionarVisao(self::VISAO_MAIS_INFO);
		$this->seletorVisao->verificarVisaoSolicitada();
	}
	
	public function fixarTipoDeSaida ($tipoSaida)
	{		
		$this->_tipoSaida = $tipoSaida;
	}
	
	public function obterTipoDeSaida ()
	{
		return $this->_tipoSaida;
	}
	
	public function obterSaida ()
	{
		return @$this->_conteudo[$this->_tipoSaida];
	}
	
	final public function tentarExecutarAcoes ($listaAcoes)
	{
		foreach ($listaAcoes as $acao)
		{
			if (method_exists($this, $acao)) {
				$this->$acao();
				return true;
			} else {
				continue;
			}
		}
		
		return false;
	}	

	protected function _iniciarRenderizacao ($tipoSaida = null)	{
		if ($tipoSaida != null)
			$this->_tipoSaida = $tipoSaida;

		if (!isset($this->_conteudo[$this->_tipoSaida]))
			$this->_conteudo[$this->_tipoSaida] = '';

		ob_start();
	}

	protected function _finalizarRenderizacao()	{
		$this->_conteudo[$this->_tipoSaida] .= ob_get_clean();
	}

	protected function _limparSaida ($tipoSaida = null)
	{
		if ($tipoSaida == null)
			$tipoSaida = $this->_tipoSaida;

		$this->_conteudo[$tipoSaida] = null;
	}
}

?>