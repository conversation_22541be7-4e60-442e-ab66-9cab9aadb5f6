<?
if (!defined('CORE_INCLUIDO')) { exit(); }

abstract class ModuloIndex extends Modulo
{
	const ANEXO_CSS = 'css';
	const ANEXO_JS = 'js';
	const ANEXO_META = 'meta';

	protected $_header = array();
	protected $_tituloSite;
	protected $_anexos;
	protected $_componentes;
	protected $_charset = 'iso-8859-1';

	public function __construct ($titulo = null)
	{
		parent::__construct();
		
		if ($titulo === null)
			$this->_tituloSite = Core::diretiva('CORE:HTML:titulo_site');
		else
			$this->_tituloSite = $titulo;

		$this->_anexos = array();

		$this->_componentes = array( 'cabecalho' => true,
									 'solicitado' => true,
									 'rodape' => true,
									 'redirecionador' => false,
                                     'chat' => false
								   );
	}
	
	public function carregarComponentesDoSistema() {}

	public function prepararSaida() {
		$this->enviarHeader();

		switch ( $this->obterTipoDeSaida() ) {
			case self::HTML:
				$this->_prepararHTML();				
				break;
			default:
				echo Core::modulo('solicitado')->obterSaida($this->obterTipoDeSaida());
		}
	}
	
	public function injetarHTML($conteudo) {
		if ( !isset($this->_conteudo[ self::HTML ]) )
			$this->_conteudo[ self::HTML ] = $conteudo;
		else
			$this->_conteudo[ self::HTML ] .= $conteudo;
	}

	public function fixarTipoDeSaida($tipo = null, $dados = array()) {
		if ($tipo != null)
			parent::fixarTipoDeSaida($tipo);

		switch ($tipo) {
			case self::ARQUIVO:
				if ( !isset($dados['mime']) )
					$dados['mime'] = 'application/x-unknown';

				$this->_header = array('tipo' => $tipo, 'nome_arquivo' => @$dados['nome_arquivo'], 'mime' => $dados['mime']);
				break;
			case self::AJAX_JSON:
			case self::AJAX_JAVASCRIPT:
			case self::AJAX_TEXTO:
				if ( !isset($dados['charset']) )
					$dados['charset'] =  $this->_charset;

				$this->_header = array('tipo' => $tipo, 'charset' => $dados['charset']);
				break;
			case self::CUSTOMIZADO:
				if ( !isset($dados['header']) )
					$dados['header'] = array();

				$this->_header = array('tipo' => $tipo, 'header' => $dados['header']);
				break;
			default:
				$this->_header = array('tipo' => self::HTML);
		}
	}
	
	public function obterCharset () {
		return $this->_charset;
	}
	
	public function fixarCharset ($charset) {
		$this->_charset = $charset;
	}
	
	public function fixarTitulo ($titulo) {
		$this->_tituloSite = $titulo;
	}

	public function anexarAoTitulo ($texto, $separador = ' - ') {
		$this->_tituloSite .= $separador . $texto;
	}

	public function anexarAoHeader ($endereco, $tipo, $forcarMaisDeUmaInclusao = false)	{
		if ($forcarMaisDeUmaInclusao && array_key_exists($endereco, $this->_anexos))
			$this->_anexos[] = array('endereco' => $endereco, 'tipo' => $tipo);
	
		$this->_anexos[$endereco] = array('endereco' => $endereco, 'tipo' => $tipo);
	}
	
	public function fixarEstadoDoComponente($nome, $valor = true) {
		$this->_componentes[$nome] = $valor;
	}

	protected function _prepararHTML() {
		// anexa folha de estilos
		$this->anexarAoHeader(Core::diretiva('ESTILO:DIRETORIO:estilo') . Core::diretiva('ESTILO:ARQUIVO:css'), MIndex::ANEXO_CSS);

		// carrega o cabeçalho
		if ($this->_componentes['cabecalho']) {
			Core::carregarModulo(array('nome' => '_cabecalho', 'classe' => 'MCabecalho', 'guardar_como' => 'cabecalho'));
			
			Core::modulo('cabecalho')->carregarCabecalho($this->_componentes['cabecalho_painel']);
		}

		// carrega o rodapé
		if ($this->_componentes['rodape']) {
			Core::carregarModulo(array('nome' => '_rodape', 'classe' => 'MRodape', 'guardar_como' => 'rodape'));
			Core::modulo('rodape')->carregarRodape();
		}

        if ($this->_componentes['chat']) {
            //Core::carregarModulo(array('nome' => '_chat', 'classe' => 'MChat', 'guardar_como' => 'chat'));
            //Core::modulo('chat')->carregarChat();
        }
	}
	
	protected function enviarHeader() {
		if ( !isset($this->_header['tipo']) )
			$this->fixarTipoDeSaida();

		switch ($this->_header['tipo']) {
			case self::ARQUIVO:
				header('Content-type: ' . $this->_header['mime']);
				header('Content-Disposition: attachment; filename="'. $this->_header['nome_arquivo'] .'"');
				break;
			case self::AJAX_JAVASCRIPT:
				header('Content-type: text/javascript; charset='. $this->_header['charset']);
				break;
			case self::AJAX_JSON:
				header('Content-type: text/json; charset='. $this->_header['charset']);
				break;
			case self::AJAX_TEXTO:
				header('Content-type: text/html; charset='. $this->_header['charset']);
				break;
			case self::CUSTOMIZADO:
				foreach($this->_header['header'] as $header)
					header($header);
				break;
			default:
				header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); 
				header('Last-Modified: ' . gmdate("D, d M Y H:i:s") . ' GMT');
				header('Cache-Control: no-store, no-cache, must-revalidate'); 
				header('Cache-Control: post-check=0, pre-check=0', FALSE); 
				header('Pragma: no-cache');
				header('X-XSS-Protection: 0');
		}
	}
	
	protected function _montarAnexos() {
		foreach ($this->_anexos as $anexo) {
			switch ($anexo['tipo']) {
				case MIndex::ANEXO_CSS:
					printf('<link href="%s" rel="stylesheet" type="text/css" />', $anexo['endereco']);
					break;
				case MIndex::ANEXO_JS:
					printf('<script src="%s" type="text/javascript"></script>', $anexo['endereco']);
					break;
				case MIndex::ANEXO_META:
				default:
					echo $anexo['endereco'];
					break;
			}

			echo "\n";
		}
	}
}

?>
