<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class SeletorVisao {
	protected $_nomeSeletor;
	protected $_visoes = array();
	protected $_visaoSelecionada;
	protected $_visaoPadrao;
	protected $_visaoForcada = null;
	
	public function __construct ($nomeSeletor = 'visao') {
		$this->nomeSeletor($nomeSeletor);
	}
	
	public function adicionarVisao ($nome, $padrao = false) {
		if ( !in_array($nome, $this->_visoes) )
			$this->_visoes[] = $nome;

		if ( $padrao )
			$this->_visaoPadrao = $nome;
	}

	public function verificarVisaoSolicitada () {
		$this->_visaoSelecionada = @$_GET[ $this->nomeSeletor() ];
		
		if ( !in_array($this->_visaoSelecionada, $this->_visoes) )
			$this->_visaoSelecionada = $this->_visaoPadrao;
	}
	
	public function visaoSelecionada () {
		if ( $this->_visaoForcada == null )
			return $this->_visaoSelecionada;
		else
			return $this->_visaoForcada;
	}
	
	public function visaoEstaSelecionada ($visao) {
		return ($visao == $this->visaoSelecionada());
	}

	public function nomeSeletor ($novoNome = null) {
		if ( $novoNome != null )
			$this->_nomeSeletor = $novoNome;
		else
			return $this->_nomeSeletor;
	}
	
	public function forcarVisao ($nomeVisao) {
		$this->_visaoForcada = $nomeVisao;
	}
}

?>