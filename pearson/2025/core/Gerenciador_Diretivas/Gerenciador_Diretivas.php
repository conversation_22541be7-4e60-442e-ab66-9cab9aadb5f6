<?
/**
 * @package Core
 * @subpackage Core
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Carrega diretivas/configurações de banco de dados, xml, ou que for.
 * Por enquanto só carrega do banco de dados
 * @todo extrair uma classe abstract e depois extender como Gerenciador_Diretivas_DB, Gerenciador_Diretivas_XML, etc...
 Essa classe está mtoooooo mal feita, refazer quando puder...
 */
class Gerenciador_Diretivas
{
	protected $_usuario = null;
	
	private $_diretivas;
	private $_recarregarDiretivas = true;

	public function carregarDiretivas ()
	{
		if (!$this->_recarregarDiretivas)
			return false;

		$rs = Core::registro('db')->query( sprintf(
			  'SELECT d_nome, d_valor, d_usuario FROM diretivas WHERE %s',
			  $this->_obterSQLUsuario() ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$this->_diretivas[$row['d_nome']] = $row['d_valor'];
			}
		}
		$rs->free();
		
		$this->_recarregarDiretivas = false;
		
		return true;
	}
	
	/**
	 * Obtem o valor de uma diretiva, pelo seu nome
	 * @param string $nome Nome da diretiva
	 * @return mixed
	 */
	public function obterDiretiva ($nome)
	{
		$this->carregarDiretivas();
		
		if ( isset($this->_diretivas[$nome]) ) {
			return $this->_diretivas[$nome];
		} else {
			return false;
		}
	}
	
	public function obterDiretivaPeloID ($id, Usuario &$usuario = null)
	{
		$sqlUsuario = ' AND d_usuario IS NULL';
		if ($usuario != null)
			$sqlUsuario = ' AND d_usuario = ' . Core::registro('db')->formatarValor( $usuario->obterID() );
		
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT d_nome, d_valor, d_definicao, d_usuario FROM diretivas WHERE d_id = %s %s',
			   Core::registro('db')->formatarValor((int) $id),
			   $sqlUsuario ) );
			   
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			return array('nome' => $row['d_nome'], 'valor' => $row['d_valor'], 'definicao' => $row['d_definicao']);
		}
		$rs->free();
		
		return false;
	}
	
	public function obterIDDiretivaPeloNome ($nome)
	{
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT d_id, d_usuario FROM diretivas WHERE d_nome = %s AND (%s)',
			   Core::registro('db')->formatarValor($nome),
			   $this->_obterSQLUsuario() ) );
			   
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			return $row['d_id'];
		}
		$rs->free();
		
		return false;
	}
	
	public function fixarUsuario (Usuario &$usuario) {
		$this->_usuario = $usuario;
	}

	/**
	 * Remove uma diretiva; remove da classe e também do local onde fica armazenada
	 * @param string $nome Nome da diretiva
	 * @return mixed
	 * 
	 * @todo retornar exceção em caso de erro
	 */
	public function removerDiretiva ($nome)
	{
		unset($this->_diretivas[$nome]);
		
		Core::registro('db')->query( sprintf(
			'DELETE FROM diretivas WHERE d_nome = %s AND (%s)',
			Core::registro('db')->formatarValor($nome),
			$this->_obterSQLUsuario() ) );
		
		return Core::registro('db')->affected_rows;
	}

	public function removerDiretivaSimples ($nome)
	{
		unset($this->_diretivas[$nome]);
		
		Core::registro('db')->query(sprintf(
			'DELETE FROM diretivas WHERE d_nome = %s',
			Core::registro('db')->formatarValor($nome)
		));
		
		return Core::registro('db')->affected_rows;
	}

	/**
	 * Remove uma diretiva; remove da classe e também do local onde fica armazenada
	 * @param string $id Id da diretiva
	 * @return mixed
	 * 
	 * @todo retornar exceção em caso de erro
	 */
	public function removerDiretivaPeloID ($id, Usuario &$usuario)
	{		
		Core::registro('db')->query( 
			sprintf('DELETE FROM diretivas WHERE d_id = %s AND %s',
			Core::registro('db')->formatarValor((int) $id),
			$this->_obterSQLUsuario($usuario) ) );

		return Core::registro('db')->affected_rows;
	}

	/**
	 * Edita uma diretiva; edita na classe e também no local onde fica armazenada
	 * Insere no local de armazenamento caso não exista
	 * @param string $nome Nome da diretiva
	 * @param mixed $valor Novo valor
	 * @param bool $adicionar opcional adicionar ao DB caso nao tenha cadastrada
	 * @param bool $fundamental opcional Se for fundamental então não consulta no local de armazenamento
	 * @return bool
	 */
	public function editarDiretiva ($nome, $valor, $adicionar = false, $fundamental = false)
	{
		if ($fundamental) {
			$this->_diretivas[$nome] = $valor;
			return true;
		}
		
		$fazerUpdate = true;
		
		if ( $this->obterIDDiretivaPeloNome($nome) === false ) {
			if ($adicionar) {
				$this->adicionarDiretiva($nome, $valor);
			} else {
				$fazerUpdate = false;
			}			
		}
		
		if ($fazerUpdate) {
			Core::registro('db')->query( sprintf(
				'UPDATE diretivas SET d_valor = %s WHERE d_nome = %s AND (%s)',
				Core::registro('db')->formatarValor($valor),
				Core::registro('db')->formatarValor($nome),
				$this->_obterSQLUsuario() ) );
		}

		$this->_diretivas[$nome] = $valor;
		
		return true;
	}

	public function editarDiretivaPeloID ($id, $nome, $valor, Usuario &$usuario = null)
	{
		Core::registro('db')->query( sprintf(
			'UPDATE diretivas SET d_nome = %s, d_valor = %s WHERE d_id = %s AND %s',
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($valor),
			Core::registro('db')->formatarValor((int) $id),
			$this->_obterSQLUsuario($usuario) ) );
		
		return Core::registro('db')->affected_rows;
	}

	/**
	 * Adiciona uma diretiva; adiciona na classe e também no local onde fica armazenada
	 * @param string $nome Nome da diretiva
	 * @param mixed $valor Novo valor
	 * @param bool $fundamental opcional Se for fundamental então não consulta no local de armazenamento
	 * @return mixed
	 */
	public function adicionarDiretiva ($nome, $valor, $fundamental = false)
	{
		$this->_diretivas[$nome] = $valor;
		
		if ($fundamental)
			return true;
			
		$sqlUsuario = 'NULL';
		if ( $this->_usuario != null )
			$sqlUsuario = Core::registro('db')->formatarValor( $this->_usuario->obterID() );

		Core::registro('db')->query( sprintf(
			'INSERT INTO diretivas (d_valor, d_nome, d_usuario) VALUES (%s, %s, %s)',
			Core::registro('db')->formatarValor($valor),
			Core::registro('db')->formatarValor($nome),
			$sqlUsuario ) );

		return Core::registro('db')->insert_id;
	}
	
	public function adicionarDiretivaDoSistema ($nome, $valor, $definicao)
	{
		$this->_diretivas[$nome] = $valor;

		Core::registro('db')->query( sprintf(
			'INSERT INTO diretivas (d_valor, d_nome, d_definicao) VALUES (%s, %s, %s)',
			Core::registro('db')->formatarValor($valor),
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($definicao) ) );

		return Core::registro('db')->insert_id;
	}
	
	public function adicionarDiretivaDoSistemaSimples ($nome, $valor)
	{
		$this->_diretivas[$nome] = $valor;

		Core::registro('db')->query(sprintf(
			'INSERT INTO diretivas (d_valor, d_nome) VALUES (%s, %s)',
			Core::registro('db')->formatarValor($valor),
			Core::registro('db')->formatarValor($nome)
		));

		return Core::registro('db')->insert_id;
	}

	public function editarDiretivaDoSistemaPeloID ($id, $nome, $valor, $definicao)
	{
		Core::registro('db')->query( sprintf(
			'UPDATE diretivas SET d_nome = %s, d_valor = %s, d_definicao = %s WHERE d_id = %s',
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($valor),
			Core::registro('db')->formatarValor($definicao),
			Core::registro('db')->formatarValor((int) $id)));
		
		return Core::registro('db')->affected_rows;
	}
	
	/**
	 * Força o recarregamento das diretivas do local de armazenamento
	 */
	public function forcarRecarregamento ()
	{		
		$this->_recarregarDiretivas = true;
	}
	
	/**
	 * Checa se a diretiva já está cadastrada
	 * @return mixed
	 * 
	 * @param string $nome Nome da diretiva
	 */
	public function diretivaEstaCadastrada ($nome, Usuario &$usuario = null)
	{
		$sqlUsuario = ' AND d_usuario IS NULL';
		if ($usuario != null)
			$sqlUsuario = ' AND d_usuario = ' . Core::registro('db')->formatarValor( $usuario->obterID() );
		
		$rs = Core::registro('db')->query( sprintf('SELECT d_id FROM diretivas WHERE d_nome = %s %s',
			Core::registro('db')->formatarValor($nome),
			$sqlUsuario));
			
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			return $row['d_id'];
		}
		$rs->free();
		
		return false;
	}
	
	protected function _obterSQLUsuario (Usuario &$usuario = null) {
		$sqlUsuario = ' d_usuario ';
		
		if ( $usuario == null ) {
			$sqlUsuario .= ' IS NULL ';
			
			if ( $this->_usuario != null && $this->_usuario->obterID() != null )
				$sqlUsuario .= ' OR d_usuario = ' . Core::registro('db')->formatarValor( $this->_usuario->obterID() );
		} else {
			if ( $usuario->obterID() == null )
				$sqlUsuario .= ' IS NULL ';
			else
				$sqlUsuario .= ' = ' . Core::registro('db')->formatarValor( $usuario->obterID() );
		}
		
		return $sqlUsuario;
	}
	
	static public function removerDiretivaPeloIDUsuario ($id)
	{		
		Core::registro('db')->query( 
			sprintf('DELETE FROM diretivas WHERE d_usuario = %s',
			Core::registro('db')->formatarValor((int) $id) ) );

		return Core::registro('db')->affected_rows;
	}

	/* Sem Usuario */

	public function carregarDiretivasSemUsuario ()
	{
		$rs = Core::registro('db')->query('SELECT d_nome, d_valor FROM diretivas');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$this->_diretivas[$row['d_nome']] = $row['d_valor'];
			}
		}
		$rs->free();
		
		return true;
	}

	public function obterIDDiretivaPeloNomeSemUsuario ($nome)
	{
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT d_id FROM diretivas WHERE d_nome = %s',
			   Core::registro('db')->formatarValor($nome)) );
			   
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			return $row['d_id'];
		}
		$rs->free();
		
		return false;
	}

	public function editarDiretivaSemUsuario ($nome, $valor, $adicionar = false, $fundamental = false)
	{
		if ($fundamental) {
			$this->_diretivas[$nome] = $valor;
			return true;
		}
		
		$fazerUpdate = true;
		
		if ( $this->obterIDDiretivaPeloNomeSemUsuario($nome) === false ) {
			if ($adicionar) {
				$this->adicionarDiretivaSemUsuario($nome, $valor);
			} else {
				$fazerUpdate = false;
			}			
		}
		
		if ($fazerUpdate) {
			Core::registro('db')->query( sprintf(
				'UPDATE diretivas SET d_valor = %s WHERE d_nome = %s',
				Core::registro('db')->formatarValor($valor),
				Core::registro('db')->formatarValor($nome)) );
		}

		$this->_diretivas[$nome] = $valor;
		
		return true;
	}

	public function adicionarDiretivaSemUsuario ($nome, $valor, $fundamental = false)
	{
		$this->_diretivas[$nome] = $valor;
		
		if ($fundamental)
			return true;

		Core::registro('db')->query( sprintf(
			'INSERT INTO diretivas (d_valor, d_nome) VALUES (%s, %s)',
			Core::registro('db')->formatarValor($valor),
			Core::registro('db')->formatarValor($nome)) );

		return Core::registro('db')->insert_id;
	}
}

?>
