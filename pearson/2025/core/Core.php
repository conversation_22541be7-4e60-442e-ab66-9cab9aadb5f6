<?
/**
 * @package Core
 * @subpackage Core
 */

define('CORE_INCLUIDO', true, false);

require_once('Exception.php');
require_once('FuncoesGlobais.php');
require_once('Modulo/Modulo.php');
require_once('Gerenciador_Diretivas/Gerenciador_Diretivas.php');

/**
 * Principal classe do Core. Responsável principalmente pelo carregamento de modulos
 *
 * @package Core
 * @subpackage Core
 *
 * @final
 * @static
 */
final class Core
{
	static public $gerenciadorDeDiretivas;
	static public $deslogarAoFinalizar = false;

	static private $_registro = array();
	static private $_modulos = array();

	static public function registrar ($nome, &$objeto)
	{
		self::$_registro[$nome] = $objeto;

		return true;
	}

	static public function &registro ($nome)
	{
		try
		{
			if ( !array_key_exists($nome, self::$_registro) )
			{
				throw new Core_Exception('Registro "'. $nome .'" não encontrado!', Core_Exception::E_FATAL);
			}

			return self::$_registro[$nome];
		}
		catch (Core_Exception $e)
		{
			echo $e;
			return false;
		}
	}

	static public function fixarDiretiva ($nome, $valor, $fundamental = false)
	{
		return self::$gerenciadorDeDiretivas->editarDiretiva($nome, $valor, true, $fundamental);
	}

	static public function diretiva ($nome)
	{
		return self::$gerenciadorDeDiretivas->obterDiretiva($nome);
	}

	static public function &carregarModulo ($infoModulo, $sobreCarregar = false)
	{
		if (!isset($infoModulo['guardar_como']) || empty($infoModulo['guardar_como']))
			$infoModulo['guardar_como'] = $infoModulo['nome'];

		if ( !$sobreCarregar && array_key_exists($infoModulo['guardar_como'], self::$_modulos) )
			return $infoModulo;

		if ( !isset($infoModulo['pasta']) )
			$infoModulo['pasta'] = '';

		try
		{
			if ( !class_exists($infoModulo['classe']) ) {
				if ( !@include( sprintf('%s%s%s/%s.modulo.php', Core::diretiva('CORE:DIRETORIO:modulos'), $infoModulo['pasta'], $infoModulo['nome'], $infoModulo['nome']) ) )
				{
					throw new Core_Exception('Modulo "'. $infoModulo['nome'] .'" não encontrado!', Core_Exception::E_FATAL);
				}
			}

			if (! self::$_modulos[$infoModulo['guardar_como']] = new $infoModulo['classe'] )
			{
				throw new Core_Exception('Erro ao criar modulo "'. $infoModulo['nome'] .'"!', Core_Exception::E_FATAL);
			}

			return self::$_modulos[$infoModulo['guardar_como']];
		} catch (Core_Exception $e)
		{
			echo $e;
			return false;
		}
	}

	static public function &modulo ($nome)
	{
		try
		{
			if ( !array_key_exists($nome, self::$_modulos) )
			{
				throw new Core_Exception('Modulo "'. $nome .'" não encontrado!', Core_Exception::E_FATAL);
			}

			return self::$_modulos[$nome];
		}
		catch (Core_Exception $e)
		{
			echo $e;
			return false;
		}
	}

	static public function moduloCarregado ($nome)
	{
		if ( array_key_exists($nome, self::$_modulos) )
		{
			return true;
		}

		return false;
	}

	static public function incluir ($nome, $dir = null, $pessoal = false)
	{
		if ( $dir == null )
			$dir = $nome . '/';

		if ($pessoal)
			$dir = 'includes/' . $dir;

		require_once($dir . $nome . '.php');
	}

	static public function iniciar ()
	{
		@ob_start();

		self::$gerenciadorDeDiretivas = new Gerenciador_Diretivas();
	}

	static public function iniciar_session ($session_name = NULL)
	{
		@setcookie('id', '', time()-1);
		@setcookie('s', '', time()-1);
		@setcookie('l', '', time()-1);
		
		if ($session_name != NULL)
			@session_name($session_name);
			@session_cache_expire(60*24);
			@session_start();
        }

	static public function finalizar ($exit = true)
	{
		@Gerenciador_URL::atualizarReferencia();

		if ( self::$deslogarAoFinalizar && isset(self::$_registro['autenticador']) )
			@Core::registro('autenticador')->logOut();

		@ob_end_flush();

		@session_write_close();

		if ($exit)
			@exit();
	}
}

?>