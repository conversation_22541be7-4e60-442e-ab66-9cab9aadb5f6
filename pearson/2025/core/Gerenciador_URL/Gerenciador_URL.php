<?
/**
 * @package Core
 * @subpackage Core
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Cuida de toda a parte que envolve URLs, principalmente geração e analize de links/urls
 * 
 * @package Core
 * @subpackage Core
 */
class Gerenciador_URL
{
	static protected $_index = './';
	static protected $_indexTemporario;
	static protected $_extra = array();
	static protected $_entrada = null;
	static protected $_atualizarReferencia = true;

	static public function gerarLink ($modulo = null, $acao = null, $parametros = array())
	{
		$parametrosPadrao = array();
		if (!is_null($modulo)) { $parametrosPadrao[Core::diretiva('CORE:PARAMETRO:modulo')] = $modulo; }
		if (!is_null($acao)) { $parametrosPadrao[Core::diretiva('CORE:PARAMETRO:acao')] = $acao; }
	
		$parametros = array_merge($parametrosPadrao, self::$_extra, $parametros);
	
		$link = self::$_index . Gerenciador_URL::_gerarParametros($parametros);
		
		return $link;
	}
	
	static public function gerarLinkCustomizado ($parametros = array())
	{
		$parametros = array_merge(self::$_extra, $parametros);
	
		$link = self::$_index . Gerenciador_URL::_gerarParametros($parametros);
		
		return $link;
	}
	
	static public function gerarLinkPelaReferencia ($parametros = array())
	{
		if (!isset($_SESSION['referencia'])) {
			$_SESSION['referencia'] = array();
		}
		
		// pequeno fix pra não ficar redirecionando pro mesmo lugar...
		if ( isset($_REQUEST['r_referencia']) && $_REQUEST['r_referencia'] == 1 ) {
			return self::gerarLinkIndex($parametros);
		}
		
		$parametros = array_merge($_SESSION['referencia'], self::$_extra, $parametros, array('r_referencia' => 1));
	
		$link = self::$_index . Gerenciador_URL::_gerarParametros($parametros);
		
		return $link;
	}
	
	static public function gerarLinkIndex ()
	{
		$link = self::$_index . Gerenciador_URL::_gerarParametros(self::$_extra);
		
		return $link;
	}
	
	static public function gerarLinkPelaAcao ($acao, $parametros = array())
	{
		$rs = Core::registro('db')->query("SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '{$acao}'");
		if ($rs->num_rows) {			
			$row = $rs->fetch_assoc();
			return Gerenciador_URL::gerarLink($row['m_nome'], $row['a_nome'], $parametros);
		}
		$rs->free();
		
		return null;
	}
	
	static public function gerarArrayLinkPelaAcao ($acao, $parametros = array())
	{
		$rs = Core::registro('db')->query("SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '{$acao}'");
		if ($rs->num_rows) {			
			$row = $rs->fetch_assoc();
			$link = Gerenciador_URL::gerarLink($row['m_nome'], $row['a_nome'], $parametros);
			$parametros[Core::diretiva('CORE:PARAMETRO:modulo')] = $row['m_nome'];
			$parametros[Core::diretiva('CORE:PARAMETRO:acao')] = $row['a_nome'];
			return array($link, $parametros);
		}
		$rs->free();
		
		return null;
	}
	
	static public function gerarLinkPeloModulo ($modulo, $parametros = array())
	{
		$rs = Core::registro('db')->query("SELECT modulos.m_nome FROM modulos WHERE m_id = '{$modulo}'");
		if ($rs->num_rows) {			
			$row = $rs->fetch_assoc();
			return Gerenciador_URL::gerarLink($row['m_nome'], null, $parametros);
		}
		$rs->free();
		
		return null;
	}
	
	static public function gerarLinkPelaEntrada ($parametros = array())
	{
		return self::$_index . self::_gerarParametros( array_merge(self::$_entrada, $parametros) );
	}
	
	static public function fixarIndex ($index)
	{
		self::$_index = $index;
	}
	
	static public function autoFixarIndexCompleto ($completo = true)
	{
		if ( $completo ) {
			$httpS = ( isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on' ) ? 'https' : 'http';
			$endereco = $httpS.'://'. $_SERVER['HTTP_HOST'];
			
			$arquivo = explode('/', $_SERVER['SCRIPT_NAME']);
			$arquivo = @$arquivo[ count($arquivo) - 1 ];
			
			$pasta = str_replace($arquivo, '', $_SERVER['SCRIPT_NAME']);
			if ( substr($pasta, 0, 1) != '/' )
				$pasta = '/' . $pasta;
			if ( substr($pasta, -1, 1) != '/' )
				$pasta .= '/';
			
			$endereco .= $pasta;
			
			if ( substr(self::$_index, -1, 1) != '/' )
				$endereco .= $arquivo;
		
			self::$_indexTemporario = self::$_index;
			self::$_index = $endereco;
		} else {
			if (isset(self::$_indexTemporario))
				self::$_index = self::$_indexTemporario;
		}
	}
	
	static public function fixarParametrosExtras ($parametros, $sobrepor = true)
	{
		if ($sobrepor) {
			self::$_extra = $parametros;
		} else {
			self::$_extra = array_merge(self::$_extra, $parametros);
		}		
	}
	
	static public function parametrosLinearParaArray ($parametros = null, $separador = '&')
	{
		$temp = array();
		if (!empty($parametros)) {
			$parametros = explode($separador, $parametros);
			foreach ($parametros as $p) {
				$p = explode('=', $p);
				$temp[$p[0]] = $p[1];
			}
		}
		return $temp;
	}
	
	static public function obterParametrosPadroes ()
	{
		self::fixarEntrada();
		
		$parametrosPadrao = array();
		
		if ( isset(self::$_entrada[Core::diretiva('CORE:PARAMETRO:modulo')]) && !empty(self::$_entrada[Core::diretiva('CORE:PARAMETRO:modulo')]) ) {
			$parametrosPadrao[Core::diretiva('CORE:PARAMETRO:modulo')] = self::$_entrada[Core::diretiva('CORE:PARAMETRO:modulo')];
		}
		if ( isset(self::$_entrada[Core::diretiva('CORE:PARAMETRO:acao')]) && !empty(self::$_entrada[Core::diretiva('CORE:PARAMETRO:acao')]) ) {
			$parametrosPadrao[Core::diretiva('CORE:PARAMETRO:acao')] = self::$_entrada[Core::diretiva('CORE:PARAMETRO:acao')];
		}
		
		return $parametrosPadrao;
	}
	
	static public function obterParametro ($nome)
	{
		self::fixarEntrada();
		
		if ( isset(self::$_entrada[$nome]) && !empty(self::$_entrada[$nome]) ) {
			return self::$_entrada[$nome];
		}
		
		return null;
	}
	
	static public function obterArrayDeParametros ($parametros = array())
	{
		$arrayFinal = array();
		foreach ($parametros as $p) {
			$arrayFinal[$p] = self::obterParametro($p);
		}
		
		return $arrayFinal;
	}
	
	static public function sobreporParametros ($parametros)
	{
		self::fixarEntrada();
		
		self::$_entrada = array_merge(self::$_entrada, $parametros);
	}
	
	static public function fixarEntrada ($entrada = null)
	{
		if (is_null($entrada)) {
			if ( is_null(self::$_entrada) ) {
				self::$_entrada = &$_GET;
			}
		} else {
			self::$_entrada = $entrada;
		}
	}
	
	static public function obterEntrada ()
	{
		return self::$_entrada;
	}
	
	static public function habilitarAtualiacaoReferencia ($atualizar = true)
	{
		self::$_atualizarReferencia = $atualizar;
	}
	
	static public function atualizarReferencia ()
	{
		if (self::$_atualizarReferencia) {
			$_SESSION['referencia'] = self::obterEntrada();
		}
	}

	static public function fixarRedirecionamento ($link = '', $msg = '')
	{
		$_SESSION['redirecionamento'] = $link;
		$_SESSION['mensagem'] = $msg;
	}
	
	static private function _gerarParametros ($parametros)
	{
		$link = '';
		$linkFinal = '';
		
		foreach ($parametros as $k => $v) {
			if (!empty($k)) {
				$link .= $k . '=' . $v . '&';
			} else {
				$linkFinal .= '#' . $v;
			}
		}
		
		$link .= $linkFinal;
		
		if (!empty($link)) {
			$link = rtrim('?' . $link, '&');
		}
		
		return $link;
	}
}

?>