<?
/**
 * @package Core
 * @subpackage Core
 */
if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Permite que sejam gerados e admistrados Logs
 * 
 * @package Core
 * @subpackage Core
 * 
 * @todo extrair classe abstrata e depois extender fazendo: Log_DB, Log_XML, Log_TXT, etc...
 */
class Log {
	const LOGIN = 'LOGIN';
	const DESCONHECIDO = 'DESCONHECIDO';
	
	static private $tabela = 'logs';

	static public function logar ($log)
	{
		$valores = array();
		$valores['l_id'] = 'NULL';
		$valores['l_tipo'] = "'{$log['tipo']}'";
		$valores['l_valor'] = 'NULL';
		$valores['l_valor_extendido'] = 'NULL';
		$valores['l_valor_inteiro'] = 'NULL';

		$referencia = $log['referencia'];
		if(empty($referencia)){ $referencia = 0; }

		$valores['l_referencia'] = "'{$referencia}'";
		$valores['l_data'] = 'UNIX_TIMESTAMP( )';
		$valores['l_session'] = '"'.session_id().'"';
		
		$valores['l_cookie'] = 'NULL';
		$valores['l_metas'] = 'NULL';

		if($log['tipo'] == 'LOGIN') {
			$valores['l_cookie'] = '"'.base64_encode(json_encode($_COOKIE)).'"';
			$valores['l_metas'] = '"'.base64_encode(json_encode($_SERVER)).'"';
		}
		
		self::_ajustarValor($log['valor'], $valores);
		
		Core::registro('db')->query( sprintf("INSERT INTO %s VALUES (%s)", self::$tabela, implode(', ', $valores)) );

		if (Core::registro('db')->affected_rows == 1) {
			return true;
		}
		
		return false;
	}
	
	// TODO: permitir uma melhor flexibilidade nos modificadores, por exemplo pra pegar todos os logs até certa data
	static public function obterLog ($mod = array())
	{
		$logs = array();
		
		$rs = Core::registro('db')->query( sprintf("SELECT * FROM %s WHERE %s ORDER BY l_data DESC", self::$tabela, implode(' AND ', self::_ajustarModificadores($mod))) );
		if ($rs->num_rows) {			
			while ($row = $rs->fetch_assoc()) {
				$log = array();
				$log['id'] = $row['l_id'];
				$log['tipo'] = $row['l_tipo'];
				$log['valor'] = $row['l_valor'] . $row['l_valor_extendido'] . $row['l_valor_inteiro'];
				$log['referencia'] = $row['l_referencia'];
				$log['data'] = $row['l_data'];
				$logs[] = $log;
			}
		}
		$rs->free();
		
		return $logs;
	}
	
	static public function limpar ($mod)
	{		
		Core::registro('db')->query( sprintf("DELETE FROM %s WHERE %s", self::$tabela, implode(' AND ', self::_ajustarModificadores($mod))) );
		return Core::registro('db')->affected_rows;
	}
	
	static private function _ajustarValor ($valor, &$valores)
	{
		if (is_bool($valor) || is_int($valor) || is_float($valor)) {
			$valores['l_valor_inteiro'] = "'{$valor}'";
		} else if (strlen($valor) > 255) {
			$valores['l_valor_extendido'] = "'{$valor}'";
		} else {
			$valores['l_valor'] = "'{$valor}'";
		}
	}
	
	static private function _ajustarModificadores (&$mod)
	{
		$valores = array();
		
		if (isset($mod['id'])) { $valores[] = "l_id = '{$mod['id']}'"; }
		if (isset($mod['tipo'])) { $valores[] = "l_tipo = '{$mod['tipo']}'"; }		
		if (isset($mod['referencia'])) { $valores[] = "l_referencia = '{$mod['referencia']}'"; }
		if (isset($mod['data'])) { $valores[] = "l_data = '{$mod['data']}'"; }
		
		if (isset($mod['valor'])) {
			if (is_bool($mod['valor']) || is_int($mod['valor']) || is_float($mod['valor'])) {
				$valores[] = "l_valor_inteiro = '{$mod['valor']}'";
			} else if (strlen($mod['valor']) > 255) {
				$valores[] = "l_valor_extendido = '{$mod['valor']}'";
			} else {
				$valores[] = "l_valor = '{$mod['valor']}'";
			}
		}
		
		return $valores;
	}
}
?>