<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class ControladorAcoesPosEnvio
{
	protected $_formulario;

	protected $_acoes = array();
	protected $_acaoPadrao = null;
	protected $_acaoSolicitada = null;
	
	protected $_ativarRedirecionamentoRapido = false;
	
	protected $_proximosIDs = array();
	
	protected $_nomeCampoSeletorAcoes;
	protected $_nomeCampoRedirecionamentoRapido;
	protected $_nomeCampoEditarEmOrdem;

	public function __construct (Formulario &$formulario) {
		$this->_formulario = $formulario;
		$this->_nomeCampoSeletorAcoes = '_acao_pos_envio_' . $this->_formulario->info('nome');
		$this->_nomeCampoRedirecionamentoRapido = '_redirecionamento_rapido_' . $this->_formulario->info('nome');
		$this->_nomeCampoEditarEmOrdem = '_proximos_ids_' . $this->_formulario->info('nome');
	}
	
	public function adicionarAcao ($nome, $descricao = null) {
		$this->_acoes[$nome] = $descricao;
	}
	
	public function editarAcao ($nome, $descricao = null) {
		if (isset($this->_acoes[$nome]))
			$this->_acoes[$nome] = $descricao;
		else
			$this->adicionarAcao($nome, $descricao);
	}
	
	public function removerAcao ($nome) {
		if (isset($this->_acoes[$nome]))
			unset($this->_acoes[$nome]);
	}

	public function obterAcaoSolicitada () {
		return $this->_acaoSolicitada;
	}
	
	public function verificarAcaoSelecionada () {
		if ( $this->_formulario == null || $this->_formulario->campo($this->_nomeCampoSeletorAcoes) === false ) {
			$this->_acaoSolicitada = $this->_acaoPadrao;
		} else {
			$this->_acaoSolicitada = $this->_formulario->campo($this->_nomeCampoSeletorAcoes)->obter('valor');
			
			if (!array_key_exists($this->_acaoSolicitada, $this->_acoes))
				$this->_acaoSolicitada = $this->_acaoPadrao;
		}
	}
	
	public function redirecionarRapido () {
		if ( $this->_formulario == null || $this->_formulario->campo($this->_nomeCampoRedirecionamentoRapido) === false )
			return $this->_ativarRedirecionamentoRapido;
		else
			return ($this->_formulario->campo($this->_nomeCampoRedirecionamentoRapido)->obter('valor') == 1);
	}

	public function definirAcaoPadrao ($nome) {
		$this->_acaoPadrao = $nome;
	}
	
	public function ativarRedirecionamentoRapido ($ativar = true) {
		$this->_ativarRedirecionamentoRapido = (bool) $ativar;
	}

	public function prepararSeletorAcoes () {
		if ($this->_formulario == null)
			return;
		
		$acaoSel = null;		
		//if ( !$this->_formulario->foiEnviado() && isset($_GET['acaoposenvio']) && array_key_exists($_GET['acaoposenvio'], $this->_acoes) )
			//$acaoSel = $_GET['acaoposenvio'];

		//if ( $acaoSel == null )
			$acaoSel = $this->_acaoPadrao;

		$this->_formulario->adicionarCampo( new Campo(array('nome' => $this->_nomeCampoSeletorAcoes,
															'valor' => $acaoSel,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_MENU,
															'html_valor' => $this->_acoes,
															'html_tamanho_tipo' => Campo::HTML_TAM_NULO
												)) );
	}
	
	public function prepararCampoRedirecionamentoRapido () {
		if ($this->_formulario == null)
			return;

		$this->_formulario->adicionarCampo( new Campo(array('nome' => $this->_nomeCampoRedirecionamentoRapido,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::NATURAL,
															'html_tipo' => Campo::HTML_ESCONDIDO,
															'html_valor' => ($this->_ativarRedirecionamentoRapido ? 1 : 0)
							  				)) );
	}

	public function prepararEdicaoEmOrdem ($descricao = 'Editar próximo item') {
		if ($this->_formulario == null)
			return;

		if ( !count($this->_proximosIDs) && !$this->_formulario->foiEnviado() && isset($_GET['proximosids']) && strlen(trim($_GET['proximosids'])) ) {
			$this->_proximosIDs = explode(';', trim($_GET['proximosids']));
		}

		$this->_formulario->adicionarCampo( new Campo(array('nome' => $this->_nomeCampoEditarEmOrdem,
															'requerimento' => Campo::OPCIONAL,
															'tipo' => Campo::TEXTO,
															'html_tipo' => Campo::HTML_ESCONDIDO,
															'valor' => (count($this->_proximosIDs) ? implode(';', $this->_proximosIDs) : null)
											)) );

		$this->_proximosIDs = explode(';', $this->_formulario->campo($this->_nomeCampoEditarEmOrdem)->obter('valor'));

		for ($i = 0; $i < count($this->_proximosIDs); $i++) {
			if ( !Filtrador::natural($this->_proximosIDs[$i], true) )
				unset($this->_proximosIDs[$i]);
		}

		if ( count($this->_proximosIDs) ) {
			$this->adicionarAcao('editar_proximo', $descricao);
			$this->definirAcaoPadrao('editar_proximo');
		}
	}
	
	public function fixarProximosIDs ($proximosIDs) {
		if ( is_array($proximosIDs) )
			$this->_proximosIDs = $proximosIDs;
	}
	
	public function obterProximoID () {
		if (count($this->_proximosIDs))
			return array_shift($this->_proximosIDs);
		else
			return null;
	}
	
	public function obterProximosIDs ($formatado = false) {
		if (!$formatado)
			return $this->_proximosIDs;
		else
			return implode(';', $this->_proximosIDs);
	}

	public function obterCampoRedirecionamentoRapido ($html = false) {
		if ( $this->_formulario == null || $this->_formulario->campo($this->_nomeCampoRedirecionamentoRapido) === false )
			return;
			
		if (!$html)
			return $this->_formulario->campo($this->_nomeCampoRedirecionamentoRapido);
		else
			return $this->_formulario->obterHTML($this->_nomeCampoRedirecionamentoRapido, Formulario::HTML_CAMPO, true);
	}
	
	public function obterCampoEditarEmOrdem ($html = false) {
		if ( $this->_formulario == null || $this->_formulario->campo($this->_nomeCampoEditarEmOrdem) === false || !count($this->_proximosIDs) )
			return;
			
		if (!$html)
			return $this->_formulario->campo($this->_nomeCampoEditarEmOrdem);
		else
			return $this->_formulario->obterHTML($this->_nomeCampoEditarEmOrdem, Formulario::HTML_CAMPO, true);
	}

	public function obterSeletorAcoes ($html = false) {
		if ( $this->_formulario == null || $this->_formulario->campo($this->_nomeCampoSeletorAcoes) === false )
			return;
			
		if (!$html)
			return $this->_formulario->campo($this->_nomeCampoSeletorAcoes);
		else
			return $this->_formulario->obterHTML($this->_nomeCampoSeletorAcoes, Formulario::HTML_CAMPO, true);
	}
	
	public function obterNomeSeletorAcoes () {
		return $this->_nomeCampoSeletorAcoes;
	}
	
	public function obterNomeRedirecionadorRapido () {
		return $this->_nomeCampoRedirecionamentoRapido;
	}
	
	public function obterNomeEditarEmOrdem () {
		return $this->_nomeCampoEditarEmOrdem;
	}
	
}

?>