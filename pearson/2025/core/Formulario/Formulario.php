<?
if (!defined('CORE_INCLUIDO')) { exit(); }

// @TODO: todas essas classes pra lidar com formulário podem ser bem mais divididas e melhoradas; foi feito assim por questões de prazo de entrega

include_once('Formulario_Exception.php');
include_once('Validador_Formulario.php');
include_once('Campo.php');
include_once('ControladorAcoesPosEnvio.php');

abstract class Formulario
{
	const HTML_LABEL = 0;
	const HTML_FORM_INICIO = 1;
	const HTML_FORM_FIM = 2;
	const HTML_CAMPO = 3;
	const HTML_ERROS = 4;
	
	const POST = 'post';
	const GET = 'get';
	
	const NORMAL = 'application/x-www-form-urlencoded';
	const MULTIPART = 'multipart/form-data';
	
	const ERROS_SIMPLES = 0;
	const ERROS_COMPLETOS = 1;
	
	const ADICIONANDO = 1;
	const EDITANDO = 2;
	const REMOVENDO = 3;
	const OUTRO = 3;

	public $acoesPosEnvio;
	protected $_anexoHTMLAoFimDoFormulario = '';

	protected $_validador;
	protected $_campos = array();
	protected $_info;
	protected $_carregado = false;
	protected $_entrada;
	protected $_erros = array();
	protected $_incluirChecadorDeEnvio = false;
	protected $_nomeCampoChecadorDeEnvio;
	protected $_estado;
	protected $_dados;

	public function __construct ($info = array())
	{
		$this->_info = array('nome' => 'form_sem_nome',
							 'acao' => '',
							 'metodo' => self::POST,
							 'tipo_encriptacao' => self::NORMAL,
							 'tipo_erros' => self::ERROS_COMPLETOS,
							 'componente' => null
		);
		
		$this->_info = array_merge($this->_info, $info);
		
		$this->_nomeCampoChecadorDeEnvio = '_enviado_' . $this->_info['nome'];
		
		$this->fixarEstado(self::ADICIONANDO);
		
		$this->_validador = new Validador_Formulario($this);
		
		$this->acoesPosEnvio = new ControladorAcoesPosEnvio($this);
		$this->acoesPosEnvio->adicionarAcao('retornar', 'Retornar');
		$this->acoesPosEnvio->adicionarAcao('adicionar', 'Adicionar outro item');
		$this->acoesPosEnvio->definirAcaoPadrao(@$_GET['pos_envio'] == 'adicionar' ? 'adicionar' : 'retornar');
	}

	public function carregarFormulario (&$entrada = null)
	{
		$this->prepararEntrada($entrada);
		
		$this->_criarChecadorDeEnvio();
			
		return true;
	}

	public function checarFormulario ()
	{
		try
		{
			$this->_validador->validar($this->_campos);

			$this->acoesPosEnvio->verificarAcaoSelecionada();
		}
		catch (Validador_Formulario_Exception $e)
		{
			$this->_erros = $this->_validador->obterErros();

			foreach ($this->_erros as $k => $v) {
				if ( array_key_exists($k, $this->_campos) && !is_null($this->_campos[$k]->obter('valor_pos_erro')) ) {
					$this->_campos[$k]->fixar('valor', $this->_campos[$k]->obter('valor_pos_erro'));
					unset($this->_erros[$k]);
				}
			}

			if (count($this->_erros)) {
				throw new Formulario_Exception('Foram encontrados erros ao executar a validação do formulário!');
			}
		}
	}
	
	public function adicionarCampo (&$campo)
	{
		$this->_campos[$campo->obter('nome')] = $campo;
		
		$this->_ajustarValor( $campo->obter('nome') );
	}
	
	public function campo ($nome)
	{
		if ( array_key_exists($nome, $this->_campos) ) {
			return $this->_campos[$nome];
		}
		
		return false;
	}
	
	public function info ($nome)
	{
		if ( array_key_exists($nome, $this->_info) ) {
			return $this->_info[$nome];
		}
		
		return false;
	}
	
	public function fixarInfo ($nome, $valor)
	{
		if ( array_key_exists($nome, $this->_info) ) {
			$this->_info[$nome] = $valor;
		}
	}
	
	public function temErros ()
	{
		return count($this->_erros) != 0;
	}
	
	public function incluirChecadorDeEnvio ()
	{
		$this->_incluirChecadorDeEnvio = true;
	}
	
	public function obterNomeChecadorDeEnvio ()
	{
		return $this->_nomeCampoChecadorDeEnvio;
	}
	
	public function foiEnviado ()
	{
		if ($this->_incluirChecadorDeEnvio)
		{
			try {
				$this->_criarChecadorDeEnvio();
				$this->_validador->validarCampo($this->_campos[$this->_nomeCampoChecadorDeEnvio], array(Validador_Formulario::IGN_DEPENDENCIAS, Validador_Formulario::IGN_REGISTRO_ERROS));
				return true;
			} catch (Validador_Formulario_Exception $e) {}
		}
		
		return false;
	}
	
	public function prepararEntrada (&$entrada = null)
	{
		if (!isset($this->_entrada)) {
			if ( $entrada == null )
				$this->_entrada = ($this->_info['metodo'] == Formulario::POST ? $_POST : $_GET );
			else
				$this->_entrada = $entrada;
		}
	}
	
	public function prepararEntradaArquivos ()
	{
		if ( !is_array($_FILES) || !count($_FILES) || !isset($this->_entrada) )
			return ;

		foreach ( $_FILES as $nome => $arquivo ) {
			if ( !isset($this->_entrada[$nome]) )
				$this->_entrada[$nome] = array();
				
			if ( !is_array($arquivo['name']) ) {
				$arquivo['name'] = array($arquivo['name']);
				$arquivo['type'] = array($arquivo['type']);
				$arquivo['tmp_name'] = array($arquivo['tmp_name']);
				$arquivo['error'] = array($arquivo['error']);
				$arquivo['size'] = array($arquivo['size']);
			}
			
			foreach ( $arquivo['error'] as $i => $erro ) {
				$this->_entrada[$nome][] = array( 'nome' => $arquivo['name'][$i],
												  'nome_temporario' => $arquivo['tmp_name'][$i],
												  'tipo' => $arquivo['type'][$i],
												  'erro' => $arquivo['error'][$i],
												  'tamanho' => $arquivo['size'][$i] );
			}
		}
	}
	
	public function &obterEntrada () {
		return $this->_entrada;
	}
	
	public function &obterDados () {
		return $this->_dados;
	}
	
	public function obterEstado () {
		return $this->_estado;
	}
	
	public function fixarEstado ($estado) {
		$this->_estado = $estado;
	}
	
	public function adicionarAoFinalDoFormulario($html) {
		$this->_anexoHTMLAoFimDoFormulario .= $html;
	}
	
	// @todo: essa é o principal método que precisa ser fatorado
	public function obterHTML ($nome, $tipoTAG = Formulario::HTML_CAMPO, $completo = false, $tagsExtras = array(), $itemEmQuestao = null)
	{
		$html = '';
	
		if ( ($tipoTAG == Formulario::HTML_CAMPO || $tipoTAG == Formulario::HTML_LABEL) && ($campo = $this->campo($nome)) === false ) {
			return $html;
		}
	
		switch ($tipoTAG) {
			case Formulario::HTML_FORM_INICIO:
				$tags = array('name' => $this->_info['nome'],
							  'id' => $this->_info['nome'],
							  'action' => $this->_info['acao'],
							  'enctype' => $this->_info['tipo_encriptacao'],
							  'method' => $this->_info['metodo'],
							  );
				
				$html = $this->_tagsToHTML( array_merge($tags, $tagsExtras) );
				
				if ($completo) {
					$html = '<form '. $html .'>';
				}
				
				break;
				
			case Formulario::HTML_FORM_FIM:
				$html = '</form>';

				if ($this->_incluirChecadorDeEnvio)
					$html = $this->obterHTML($this->_nomeCampoChecadorDeEnvio, Formulario::HTML_CAMPO, true) . $html;
				
				if ( strlen($this->_anexoHTMLAoFimDoFormulario) )
					$html = $this->_anexoHTMLAoFimDoFormulario . $html;

				break;
				
			case Formulario::HTML_ERROS:
				if ( count($this->_erros) ) {
					$html = $this->_gerarErrosHTML();
				}
				
				break;

			case Formulario::HTML_LABEL:
				$html = $campo->obter('etiqueta');
				$tags = array('for' => $campo->nomeHTML());
				
				if ( $campo->obter('requerimento') == Campo::REQUERIDO ) {
					$tags['class'] = 'lb_requerido';
					
					if (array_key_exists($nome, $this->_erros)) {
						$tags['class'] = 'lb_requerido_erros';
					}
				} else {
					$tags['class'] = 'lb_opcional';
					
					if (array_key_exists($nome, $this->_erros)) {
						$tags['class'] = 'lb_opcional_com_erro';
					}
				}
				
				if ($completo) {
					$html = '<label '. $this->_tagsToHTML( array_merge($tags, $tagsExtras) ) .'>' . $html . '</label>';
				}
				break;
		
			case Formulario::HTML_CAMPO:
				$tags = array('name' => $campo->nomeHTML(),
							  'id' => $campo->nomeHTML(),
							  );
							  
				if ( !$campo->obter('html_ativo') ) {
					$tags['disabled'] = 'disabled';
				}
				
				if ( $campo->obter('html_somente_leitura') ) {
					$tags['readonly'] = '1';
				}

				switch ($campo->obter('html_tipo')) {
					case Campo::HTML_SENHA:
					case Campo::HTML_ARQUIVO:
					case Campo::HTML_TEXTO:
						if ( $campo->obter('valor') == null && !$this->foiEnviado() ) {
							$tags['value'] = $campo->obter('html_valor');
						} else {
							$tags['value'] = $campo->obter('valor');
						}
						
						if ($campo->obter('html_tamanho_tipo') == Campo::HTML_TAM_CARACTERES) {
							$tags['size'] = $campo->obter('html_tamanho');
						} else if ($campo->obter('html_tamanho_tipo') != Campo::HTML_TAM_NULO) {
							$tags['style'] =  'width: '. $campo->obter('html_tamanho') . $campo->obter('html_tamanho_tipo') .';';
						}
										
						$tags['maxlength'] = $campo->obter('html_tamanho_maximo');
						$tags['type'] = $campo->obter('html_tipo');
						if ( $campo->obter('html_ordem') != null )
							$tags['tabindex'] = $campo->obter('html_ordem');
						break;
					case Campo::HTML_AREA_TEXTO:
						if ($campo->obter('html_tamanho_tipo') == Campo::HTML_TAM_CARACTERES) {
							$tags['cols'] = $campo->obter('html_tamanho');
						} else if ($campo->obter('html_tamanho_tipo') != Campo::HTML_TAM_NULO) {
							$tags['style'] =  'width: '. $campo->obter('html_tamanho') . $campo->obter('html_tamanho_tipo') .';';
						}
					
						$tags['rows'] = $campo->obter('html_linhas');
						if ( $campo->obter('html_ordem') != null )
							$tags['tabindex'] = $campo->obter('html_ordem');
						break;
					case Campo::HTML_ESCONDIDO:
						if ( $campo->obter('valor') == null ) {
							$tags['value'] = $campo->obter('html_valor');
						} else {
							$tags['value'] = $campo->obter('valor');
						}
						$tags['type'] = $campo->obter('html_tipo');
						break;
					case Campo::HTML_GRUPO_SELECAO:
					case Campo::HTML_CAIXA_SELECAO:
						if ( !$campo->obter('html_array') ) {
							$tags['value'] = $campo->obter('html_valor');
							if ( $campo->obter('valor') == $campo->obter('html_valor')) {
								$tags['checked'] = 'checked';
							}
							$tags['type'] = $campo->obter('html_tipo');
							if ( $campo->obter('html_ordem') != null )
								$tags['tabindex'] = $campo->obter('html_ordem');
						}
						break;
					case Campo::HTML_BOTAO:
					case Campo::HTML_ENVIO:
						if ($campo->obter('html_tamanho_tipo') != Campo::HTML_TAM_CARACTERES && $campo->obter('html_tamanho_tipo') != Campo::HTML_TAM_NULO) {
							$tags['style'] =  'width: '. $campo->obter('html_tamanho') . $campo->obter('html_tamanho_tipo') .';';
						}
					
						$tags['value'] = $campo->obter('etiqueta');
						$tags['type'] = $campo->obter('html_tipo');
						$tags['class'] = $campo->obter('html_classe');
						if ( $campo->obter('html_ordem') != null )
							$tags['tabindex'] = $campo->obter('html_ordem');						
						break;
					case Campo::HTML_MENU:
						if ($campo->obter('html_tamanho_tipo') != Campo::HTML_TAM_CARACTERES && $campo->obter('html_tamanho_tipo') != Campo::HTML_TAM_NULO) {
							$tags['style'] =  'width: '. $campo->obter('html_tamanho') . $campo->obter('html_tamanho_tipo') .';';
						}
					
						$tags['size'] = $campo->obter('html_linhas');
						if ( $campo->obter('html_ordem') != null )
							$tags['tabindex'] = $campo->obter('html_ordem');
						if ( $campo->obter('html_varias_selecoes') ) {
							$tags['multiple'] = 'multiple';
						}
						if ( $campo->obter('autocomplete_off') ) {
							$tags['autocomplete'] = 'off';
						}
						break;
				}
				
				$html = $this->_tagsToHTML( array_merge($tags, $tagsExtras) );
				
				if ( $campo->obter('componente') != null ) {
					if ( is_object($campo->obter('componente')) )
						$html .= @$campo->obter('componente')->obterHTML();
					else
						$html .= $campo->obter('componente');
				}
				
				if ($completo) {
					switch ($campo->obter('html_tipo')) {
						case Campo::HTML_AREA_TEXTO:						
							$html = '<textarea '. $html .'>';
							if ( $campo->obter('valor') == null ) {
								$html .= $campo->obter('html_valor');
							} else {
								$html .= $campo->obter('valor');
							}
							$html .= '</textarea>';
							break;
						case Campo::HTML_MENU:				
							$html = '<select '. $html .'>';
							foreach ($campo->obter('html_valor') as $k => $v) {
								$tempTag = array('value' => $k);
								if ( !$campo->obter('html_array') || $campo->obter('html_varias_selecoes') ) {									
									if ( in_array((string) $k, (!is_array($campo->obter('valor')) ? array($campo->obter('valor')) : $campo->obter('valor'))) ) {
										$tempTag['selected'] = 'selected';
									}
								} else{
									$valorTemp = $campo->obter('valor');
									if ( isset($valorTemp[$itemEmQuestao]) && $k == $valorTemp[$itemEmQuestao] ) {
										$tempTag['selected'] = 'selected';
									}
								}
								
								$html .= '<option '. $this->_tagsToHTML($tempTag) .' >'. $v .'</option>';
							}
							$html .= '</select>';
							break;
						case Campo::HTML_GRUPO_SELECAO:
						case Campo::HTML_CAIXA_SELECAO:
							if ( $campo->obter('html_array') ) {
								$htmlOriginal = $html;
								$html = array();
								foreach ($campo->obter('html_valor') as $v) {
									$tempTag = array('value' => $v, 'type' => $campo->obter('html_tipo'));
									
									if ( in_array($v, (!is_array($campo->obter('valor')) ? array() : $campo->obter('valor'))) ) {
										$tempTag['checked'] = 'checked';
									}
									
									$html[] = array('<input '. $htmlOriginal .' '. $this->_tagsToHTML($tempTag) .' />', $v);
								}
							} else {
								$html = '<input '. $html .' />';
							}

							break;
						default:
							$html = '<input '. $html .' />';
					}
				}
				
				break;
		}
		
		return $html;
	}
	
	protected function _criarChecadorDeEnvio ()
	{
		if ($this->_incluirChecadorDeEnvio && !isset($this->_campos[$this->_nomeCampoChecadorDeEnvio])) {
			$this->adicionarCampo( new Campo(array( 'nome' => $this->_nomeCampoChecadorDeEnvio,
													'requerimento' => Campo::REQUERIDO,
													'tipo' => Campo::NATURAL,
													'argumentos' => array(Campo::IGUAL => 1),
													'html_tipo' => Campo::HTML_ESCONDIDO,
													'html_valor' => '1'
								  )) );
		}
	}
	
	protected function _adicionarErro ($nome, $descricao)
	{
		if (empty($descricao)) {
			return false;
		}
		
		if (!empty($nome)) {
			$this->_erros[$nome] = $descricao;
		} else {
			$this->_erros[] = $descricao;
		}
	}
	
	protected function _gerarErrosHTML ()
	{
		$err = array();
		foreach ($this->_erros as $nome => $erro) {
			$temp = $erro;
			if (array_key_exists($nome, $this->_campos)) {
				$temp = '<strong>' . $this->_campos[$nome]->obter('etiqueta') . ':</strong> ' . $temp;
			}
			$err[] = $temp;
		}
		
		$err = implode('<br />', $err);
		
		Core::carregarModulo(array('nome' => '_alerta', 'classe' => 'MAlerta', 'guardar_como' => 'alerta_erros_formulario'));
		Core::modulo('alerta_erros_formulario')->prepararAlerta('Alerta!', $err);
		
		return Core::modulo('alerta_erros_formulario')->obterSaida();
	}
	
	protected function _ajustarValor ($nomeCampo)
	{
		if ($this->_campos[$nomeCampo]->obter('valor_constante'))
			return true;
	
		if ( array_key_exists($nomeCampo, $this->_entrada) )
		{
			$valor = null;
			if ( !is_array($this->_entrada[$nomeCampo]) ) {
				$valor = trim($this->_entrada[$nomeCampo]);
				if ( strlen($valor) < 1 )
					$valor = null;
			} else {
				$valor = $this->_entrada[$nomeCampo];
			}

			if ( $valor != null && $this->_campos[$nomeCampo]->obter('tipo') == Campo::REAL )
				$valor = str_replace(',', '.', $valor);

			$this->_campos[$nomeCampo]->fixar( 'valor', $valor );
		}
		else if ( $this->_campos[$nomeCampo]->obter('html_tipo') == Campo::HTML_MENU 
				  || ($this->_campos[$nomeCampo]->obter('html_tipo') == Campo::HTML_CAIXA_SELECAO && $this->_campos[$nomeCampo]->obter('html_array'))
				  || ($this->_campos[$nomeCampo]->obter('html_tipo') == Campo::HTML_GRUPO_SELECAO && $this->_campos[$nomeCampo]->obter('html_array')) )
		{
			if ( $this->_campos[$nomeCampo]->obter('html_tipo') == null ) {
				$this->_campos[$nomeCampo]->fixar('valor', array());
			}			
		}
	}
	
	private function _tagsToHTML ($tags = array())
	{
		$html = ' ';
		foreach ($tags as $k => $v) {
			$html .= $k .'="'. $v . '" ';
		}
		return $html;
	}
}

?>