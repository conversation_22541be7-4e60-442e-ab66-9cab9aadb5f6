<?

include_once('Validador_Formulario_Exception.php');

class Validador_Formulario
{
	const IGN_DEPENDENCIAS = 1;
	const IGN_REQUERIMENTO = 2;
	const IGN_TIPO = 3;
	const IGN_ARGUMENTOS = 4;
	const IGN_REGISTRO_ERROS = 5;

	protected $_tags = array();
	protected $_camposValidados = array();
	protected $_erros = array();
	protected $_formulario;

	public function __construct (Formulario &$formulario)
	{
		$this->_formulario = $formulario;	
	}
	
	public function validar ($campos, $tags = array(), $agruparErros = true)
	{
		$temErros = false;
		
		$this->_tags = $tags;
	
		foreach ($campos as &$campo)
		{
			if ( !$this->_campoValidado($campo->obter('nome')) ) {
				try {
					$this->validarCampo($campo);
				} catch (Validador_Formulario_Exception $e) {
					if ($agruparErros) {
						$temErros = true;
					} else {
						throw $e;
					}
				}
			}
		}
		
		if ( $temErros ) {
			throw new Validador_Formulario_Exception('Foram encontrados erros ao validar o formulário;');
		}
	}
	
	public function temErros ()
	{
		if ( count($this->_erros) ) {
			return true;
		}
		
		return false;
	}
	
	public function obterErros ()
	{
		return $this->_erros;
	}
	
	public function validarCampo ($campo, $tags = array())
	{
		if (!isset($this->_tags))
			$this->_tags = $tags;
	
		$nome = $campo->obter('nome');
		
		if (!in_array(self::IGN_DEPENDENCIAS, $this->_tags)) {
			if ( !$this->_dependenciasValidadas($campo) ) {
				$this->_camposValidados[$nome] = true;					
				return true;
			}
		}
		
		$valor = is_array($campo->obter('valor')) ? $campo->obter('valor') : array($campo->obter('valor'));
		$requerimentoValidado = $this->_validarRequerimento($campo, $valor);
		
		if (!in_array(self::IGN_REQUERIMENTO, $this->_tags)) {
			// se for um campo opcional e estiver vaziu, faz como se ele estivese validado
			if ( !$requerimentoValidado && $campo->obter('requerimento') == Campo::OPCIONAL ) {
				$this->_camposValidados[$nome] = false;
				return true;
			}
		}
		
		if ( $requerimentoValidado && 
			 (!in_array(self::IGN_TIPO, $this->_tags) && $this->_validarTipo($campo, $valor)) &&
			 (!in_array(self::IGN_ARGUMENTOS, $this->_tags) && $this->_validarArgumentos($campo, $valor)) )
		{			
			$this->_camposValidados[$nome] = true;
		} else {
			$this->_camposValidados[$nome] = false;
			throw new Validador_Formulario_Exception('Foram encontrados erros ao validar o campo '. $nome .';');
		}
		
		return true;
	}
	
	protected function _validarRequerimento ($campo, &$valor)
	{		
		if ( !in_array(self::IGN_REGISTRO_ERROS, $this->_tags) ) {
			$nome = $campo->obter('nome');
		
			foreach ($valor as &$v) {
				if ( !array_key_exists($nome, $this->_formulario->obterEntrada()) ) {
					if ($campo->obter('requerimento') != Campo::OPCIONAL) {
						// erro: campo requerido não encontrado!
						$this->_erros[$nome] = 'não encontrado;';
					}
					return false;
				}
				
				if ( empty($v) ) {
					if ($campo->obter('requerimento') != Campo::OPCIONAL) {
						// erro: campo requerido precisa ser preenchido!
						$this->_erros[$nome] = 'precisa ser preenchido;';
					}
					return false;
				} else if ($campo->obter('tipo') == Campo::ARQUIVO && @$v['erro'] == UPLOAD_ERR_NO_FILE) {
					if ( $campo->obter('requerimento') != Campo::OPCIONAL ) {
						$this->_erros[$nome] = 'precisa ser preenchido;';
					}
					return false;
				}
			}
		}
		
		return true;
	}
	
	protected function _validarTipo ($campo, &$valor)
	{
		$nome = $campo->obter('nome');
	
		switch ($campo->obter('tipo'))
		{
			case Campo::TEXTO:
				foreach ($valor as &$v) {
					if ( !Filtrador::texto($v) ) {
						// erro: não é texto
						$this->_erros[$nome] = 'precisa ser um texto;';
						return false;
					}
				}
				break;
			case Campo::INTEIRO:
				foreach ($valor as &$v) {
					if ( !Filtrador::inteiro($v, true) ) {
						// erro: não é inteiro
						$this->_erros[$nome] = 'precisa ser um número inteiro;';
						return false;
					}
				}
				break;
			case Campo::NATURAL:
				foreach ($valor as &$v) {
					if ( !Filtrador::natural($v, true) ) {
						// erro: não é natural
						$this->_erros[$nome] = 'precisa ser um número natural;';
						return false;
					}
				}
				break;
			case Campo::REAL:
				foreach ($valor as &$v) {
					if ( !Filtrador::real($v, true) ) {
						// erro: não é real
						$this->_erros[$nome] = 'precisa ser um número real;';
						return false;
					}
				}
				break;
			case Campo::EMAIL:
				foreach ($valor as &$v) {
					if ( !Filtrador::email($v) ) {
						// erro: não é e-mail
						$this->_erros[$nome] = 'precisa ser um e-mail válido;';
						return false;
					}
				}
				break;
			case Campo::DATA:
				if ( !array_key_exists(Campo::PADRAO_DATA, $campo->obter('argumentos')) ) {
					$this->_erros[$nome] = 'padrão de data não informado;';
					return false;
				}
				break;
			case Campo::ARQUIVO:
				foreach ($valor as &$v) {
					if ( $v['erro'] != UPLOAD_ERR_OK || $v['tamanho'] <= 0 ) {
						// erro: não é um arquivo válido
						$this->_erros[$nome] = 'precisa ser um arquivo válido;';
						return false;
					}
				}
				break;
			default:
				// erro: tipo do campo é desconhecido
				$this->_erros[$nome] = 'tipo do campo é desconhecido;';
				return false;
				break;
		}
		
		return true;
	}
	
	protected function _validarArgumentos ($campo, &$valor)
	{
		$nome = $campo->obter('nome');
	
		foreach ($campo->obter('argumentos') as $arg_k => $arg_v) {
			if ( $campo->obter('tipo') != Campo::ARQUIVO ) {
				switch ($arg_k)
				{
					case Campo::TAM_MAX:
						foreach ($valor as &$v) {
							if ( strlen($v) > $arg_v ) {
								// erro: maior que o valor permitido
								$this->_erros[$nome] = sprintf('pode ter no máximo %s caracteres;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::TAM_MIN:
						foreach ($valor as &$v) {
							if ( strlen($v) < $arg_v ) {
								// erro: menor que o valor permitido
								$this->_erros[$nome] = sprintf('precisa ter no mínimo %s caracteres;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::TAM_IGUAL:
						foreach ($valor as &$v) {
							if ( strlen($v) != $arg_v ) {
								// erro: tamanho diferente do valor permitido
								$this->_erros[$nome] = sprintf('precisa ter exatamente %s caracteres;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::IGUAL:
						foreach ($valor as &$v) {
							if ( $v != $arg_v ) {
								// erro: diferente do valor necessário
								$this->_erros[$nome] = sprintf('precisa ser igual a <i>%s</i>;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::POSSIBILIDADES:
						foreach ($valor as &$v) {
							if ( !in_array($v, $arg_v) ) {
								// erro: diferente do valor possíveis
								$this->_erros[$nome] = sprintf('é diferente de todos os valores aceitáveis;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::MIN_SELECOES:
						if ( count($valor) < $arg_v ) {
							// erro: precisa selecionar ao menos x itens
							$this->_erros[$nome] = sprintf('precisa selecionar no mínimo %s item(s);', $arg_v);
							return false;
						}
						break;
					case Campo::MAX_SELECOES:
						if ( count($valor) > $arg_v ) {
							// erro: precisa selecionar no máximo x itens
							$this->_erros[$nome] = sprintf('precisa selecionar no máximo %s item(s);', $arg_v);
							return false;
						}
						break;
					case Campo::NUM_SELECOES:
						if ( count($valor) != $arg_v ) {
							// erro: precisa selecionar x itens
							$this->_erros[$nome] = sprintf('precisa selecionar %s item(s);', $arg_v);
							return false;
						}
						break;
					case Campo::PADRAO_DATA:
						foreach ($valor as &$v) {
							if ( !Filtrador::data($arg_v, $v) ) {
								// erro: fora do padrão
								$this->_erros[$nome] = 'data inválida;';
								return false;
							}
						}
						break;
					case Campo::MAIOR_OU_IGUAL_QUE:
					case Campo::MAIOR_QUE:
						foreach ($valor as &$v) {
							if ( $arg_k == Campo::MAIOR_QUE ) {
								if ( $v <= $arg_v ) {
									// erro: menor ou igual
									$this->_erros[$nome] = sprintf('precisa ser maior que <i>%s</i>;', $arg_v);
									return false;
								}
							} else {
								if ( $v < $arg_v ) {
									// erro: menor
									$this->_erros[$nome] = sprintf('precisa ser maior ou igual a <i>%s</i>;', $arg_v);
									return false;
								}
							}						
						}
						break;
					case Campo::MENOR_OU_IGUAL_QUE:
					case Campo::MENOR_QUE:
						foreach ($valor as &$v) {
							if ( $arg_k == Campo::MENOR_QUE ) {
								if ( $v >= $arg_v ) {
									// erro: maior ou igual
									$this->_erros[$nome] = sprintf('precisa ser menor que <i>%s</i>;', $arg_v);
									return false;
								}
							} else {
								if ( $v > $arg_v ) {
									// erro: maior
									$this->_erros[$nome] = sprintf('precisa ser menor ou igual a <i>%s</i>;', $arg_v);
									return false;
								}
							}
						}
						break;
					case Campo::MAIOR_OU_IGUAL_QUE_CAMPO:
					case Campo::MAIOR_QUE_CAMPO:
						$valorCampo = null;
						$formato = '%d/%m/%Y'; // formato padrão pra data...
						
						switch ($campo->obter('tipo')) {
							case Campo::DATA:
								// tenta encontrar o formato
								$argumentos = $campo->obter('argumentos');
								if ( array_key_exists(Campo::PADRAO_DATA, $argumentos) ) {
									$formato = $argumentos[Campo::PADRAO_DATA];
								}
								
								$valorCampo = (int) strptime_real($formato, $this->_formulario->campo($arg_v)->obter('valor'));
								
								break;
							default:
								$valorCampo = (int) $this->_formulario->campo($arg_v)->obter('valor');
						}
						
						foreach ($valor as $v)
						{
							switch ($campo->obter('tipo')) {
								case Campo::DATA:
									$v = strptime($formato, $v);
									break;
							}
							
							if ( $arg_k == Campo::MAIOR_QUE_CAMPO ) {
								if ( $v <= $valorCampo ) {
									// erro: menor ou igual
									$this->_erros[$nome] = sprintf('precisa ser maior que o campo <i>%s</i>;', $this->_formulario->campo($arg_v)->obter('etiqueta'));
									return false;
								}
							} else {
								if ( $v < $valorCampo ) {
									// erro: menor
									$this->_erros[$nome] = sprintf('precisa ser maior ou igual ao campo <i>%s</i>;', $this->_formulario->campo($arg_v)->obter('etiqueta'));
									return false;
								}
							}
						}
						break;
					case Campo::MENOR_OU_IGUAL_QUE_CAMPO:
					case Campo::MENOR_QUE_CAMPO:
						$valorCampo = null;
						$formato = '%d/%m/%Y'; // formato padrão pra data...
						
						switch ($campo->obter('tipo')) {
							case Campo::DATA:
								// tenta encontrar o formato
								$argumentos = $campo->obter('argumentos');
								if ( array_key_exists(Campo::PADRAO_DATA, $argumentos) ) {
									$formato = $argumentos[Campo::PADRAO_DATA];
								}
								
								$valorCampo = (int) strptime_real($formato, $this->_formulario->campo($arg_v)->obter('valor'));
								
								break;
							default:
								$valorCampo = (int) $this->_formulario->campo($arg_v)->obter('valor');
						}
						
						foreach ($valor as $v)
						{
							switch ($campo->obter('tipo')) {
								case Campo::DATA:
									$v = strptime_real($formato, $v);
									break;
							}
						
							if ( $arg_k == Campo::MENOR_QUE_CAMPO ) {
								if ( $v >= $valorCampo ) {
									// erro: maior ou igual
									$this->_erros[$nome] = sprintf('precisa ser menor que o campo <i>%s</i>;', $this->_formulario->campo($arg_v)->obter('etiqueta'));
									return false;
								}
							} else {
								if ( $v > $valorCampo ) {
									// erro: maior
									$this->_erros[$nome] = sprintf('precisa ser menor ou igual ao campo <i>%s</i>;', $this->_formulario->campo($arg_v)->obter('etiqueta'));
									return false;
								}
							}
							
						}
						break;
					case Campo::DEPENDE:
						// já foi checado na hora de validar o campo
						break;
					default:
						// erro: argumento desconhecido
						$this->_erros[$nome] = 'argumento de validação desconhecido;';
						return false;
						break;
				}
			} else {
				switch ($arg_k)
				{
					case Campo::TAM_MAX:
						foreach ($valor as &$v) {
							if ( $v['tamanho'] > $arg_v ) {
								// erro: maior que o valor permitido
								$this->_erros[$nome] = sprintf('pode ter no máximo %d KB;', ($arg_v / 1024));
								return false;
							}
						}
						break;
					case Campo::TAM_MIN:
						foreach ($valor as &$v) {
							if ( $v['tamanho'] < $arg_v ) {
								// erro: menor que o valor permitido
								$this->_erros[$nome] = sprintf('precisa ter no mínimo %d KB;', ($arg_v / 1024));
								return false;
							}
						}
						break;
					case Campo::TAM_IGUAL:
						foreach ($valor as &$v) {
							if ( $v['tamanho'] != $arg_v ) {
								// erro: tamanho diferente do valor permitido
								$this->_erros[$nome] = sprintf('precisa ter exatamente %d KB;', ($arg_v / 1024));
								return false;
							}
						}
						break;
					case Campo::ARQUIVO_EXTENSAO:
						if (!is_array($arg_v))
							$arg_v = array( strtolower($arg_v) );

						foreach ($valor as &$v) {
							$extensao = explode('.', $v['nome']);
							$extensao = $extensao[count($extensao) - 1];
						
							if ( !in_array(strtolower($extensao), $arg_v) ) {
								// erro: tamanho diferente do valor permitido
								$this->_erros[$nome] = sprintf('extensão precisa ser igual a <i>%s</i>;', implode(' ou ', $arg_v));
								return false;
							}
						}
						break;
					case Campo::IMG_LARGURA_MAX:
						foreach ($valor as &$v) {
							$imagem = @getimagesize($v['nome_temporario']);
							
							if ( !is_array($imagem) || $imagem[0] == 0 || $imagem[0] > $arg_v ) {
								// erro: largura maior que o permitido
								$this->_erros[$nome] = sprintf('largura máxima da imagem é %s px;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::IMG_ALTURA_MAX:
						foreach ($valor as &$v) {
							$imagem = @getimagesize($v['nome_temporario']);
							
							if ( !is_array($imagem) || $imagem[1] == 0 || $imagem[1] > $arg_v ) {
								// erro: altura maior que o permitido
								$this->_erros[$nome] = sprintf('altura máxima da imagem é %s px;', $arg_v);
								return false;
							}
						}
						break;
					case Campo::DEPENDE:
						// já foi checado na hora de validar o campo
						break;
					default:
						// erro: argumento desconhecido
						$this->_erros[$nome] = 'argumento de validação desconhecido;';
						return false;
						break;
				}
			}
		}
		
		return true;
	}
	
	private function _dependenciasValidadas ($campo)
	{
		$nome = $campo->obter('nome');
		$precisaValidar = true;
		
		if ( ($dependencias = $campo->obterArgumento(Campo::DEPENDE)) !== false) {
			foreach ($dependencias as $dNome) {
				if ( !$this->_campoValidado($dNome) && $dNome != $nome ) {
					$this->validarCampo( $this->_formulario->campo($dNome) );
				}
				
				if ( !$this->_camposValidados[$dNome] ) {
					$precisaValidar = false;
				}
			}
		}
		
		return $precisaValidar;
	}
	
	private function _campoValidado ($nome)
	{
		return (array_key_exists($nome, $this->_camposValidados));
	}
}

?>