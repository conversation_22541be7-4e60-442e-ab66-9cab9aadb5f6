<?

include_once('Campo_Exception.php');

class Campo
{
	const REQUERIDO = 0;
	const OPCIONAL = 1;
	
	const TEXTO = 0;
	const INTEIRO = 1;
	const NATURAL = 2;
	const REAL = 3;
	const EMAIL = 4;
	const DATA = 5;
	const ARQUIVO = 6;
	
	const HTML_TEXTO = 'text';
	const HTML_AREA_TEXTO = 'textarea';
	const HTML_SENHA = 'password';
	const HTML_CAIXA_SELECAO = 'checkbox';
	const HTML_GRUPO_SELECAO = 'radio';
	const HTML_MENU = 'select';
	const HTML_ENVIO = 'submit';
	const HTML_RESETADOR = 'reset';
	const HTML_ARQUIVO = 'file';
	const HTML_ESCONDIDO = 'hidden';
	const HTML_BOTAO = 'button';
	const HTML_IMAGEM = 'image';
	
	const HTML_TAM_PORCENTAGEM = '%';
	const HTML_TAM_PIXEL = 'px';
	const HTML_TAM_CARACTERES = '';
	const HTML_TAM_NULO = 'NULO';
	
	const DEPENDE = 1;
	const TAM_MAX = 2;
	const TAM_MIN = 3;
	const TAM_IGUAL = 4;
	const IGUAL = 5;
	const MIN_SELECOES = 6;
	const MAX_SELECOES = 7;
	const NUM_SELECOES = 8;
	const POSSIBILIDADES = 9;
	const PADRAO_DATA = 10;
	const MAIOR_QUE_CAMPO = 11;
	const MAIOR_OU_IGUAL_QUE_CAMPO = 12;
	const MENOR_QUE_CAMPO = 13;
	const MENOR_OU_IGUAL_QUE_CAMPO = 14;
	const MAIOR_QUE = 15;
	const MAIOR_OU_IGUAL_QUE = 16;
	const MIN = 16;
	const MENOR_QUE = 17;	
	const MENOR_OU_IGUAL_QUE = 18;
	const MAX = 18;
	const ARQUIVO_EXTENSAO = 19;
	const IMG_LARGURA_MAX = 20;
	const IMG_ALTURA_MAX = 21;
	
	const NULO = 'NULO';
	const NULO_ZERO = 0;
	
	protected $_campo;
	
	static private $_ordem = 0;

	public function __construct ($config)
	{
		//self::_subirOrdem();
	
		$this->_campo = array('nome' => '',
							  'valor' => null,
							  'etiqueta' => '',
							  'requerimento' => self::OPCIONAL,
							  'tipo' => self::INTEIRO,
							  'argumentos' => array(),
							  'html_valor' => null,
							  'html_tamanho' => 99,
							  'html_tamanho_tipo' => self::HTML_TAM_PORCENTAGEM,
							  'html_tamanho_maximo' => '',
							  'html_tipo' => self::HTML_TEXTO,
							  'html_ordem' => null/*self::$_ordem*/,
							  'html_array' => false,
							  'html_varias_selecoes' => false,
							  'html_ativo' => true,
							  'html_classe' => null,
							  'html_somente_leitura' => false,
							  'html_linhas' => 1,
							  'componente' => null,
							  'valor_pos_erro' => null,
							  'valor_constante' => false
							  );
				
		$this->_campo = array_merge($this->_campo, $config);
		
		// corrige alguns padrões
		if ( ($this->_campo['html_tipo'] == self::HTML_ENVIO || $this->_campo['html_tipo'] == self::HTML_BOTAO) && !isset($config['html_tamanho_tipo']) ) {
			$this->_campo['html_tamanho_tipo'] = self::HTML_TAM_NULO;
		}
	}
	
	public function nomeHTML ()
	{
		return $this->_campo['nome'] . ( $this->_campo['html_array'] == true ? '[]' : '' );
	}
	
	public function obter ($info)
	{
		if ( array_key_exists($info, $this->_campo) ) {
			return $this->_campo[$info];
		}
		
		return false;
	}
	
	public function fixar ($info, $valor)
	{
		if ( array_key_exists($info, $this->_campo) ) {
			$this->_campo[$info] = $valor;
			
			return true;
		}
		
		return false;
	}
	
	public function obterArgumento ($tipo)
	{
		if ( array_key_exists($tipo, $this->_campo['argumentos']) ) {
			return $this->_campo['argumentos'][$tipo];
		}
		
		return false;
	}
	
	public function adicionarArgumento ($tipo, $valor) {
		@$this->_campo['argumentos'][$tipo] = $valor;
	}
	
	static private function _subirOrdem ()
	{
		self::$_ordem++;
	}
}

?>