<?
if (!defined('CORE_INCLUIDO')) { exit(); }

include_once('Formulario_De_Acoes.php');

abstract class Listagem extends Modulo
{
	protected $_dados = array();
	protected $_total = 0;
	protected $_ordenacao;
	protected $_paginacao;
	protected $_formulario;

	abstract protected function _obterDados();

	protected function obterBotaoAlterador ($nome, $centrado = false, $label = null, $spacer = true)
	{
		if ( $this->_ordenacao->campo('ordenar_por') === false )
			return ($label == null ? $nome : $label);
		
		if (is_null($label)) {
			$label = @$this->_ordenacao->campo('ordenar_por')->obter('html_valor');
			$label = @$label[$nome];
		}

		$onclick = $this->_ordenacao->obterBotaoAlterador($nome);
		if ( !empty($onclick) )
			$retorno = '<a href="javascript: ' . $onclick . '">' . $label . '</a>';
		else
			$retorno = $label;
		
		if ($this->_ordenacao->campo('ordenar_por')->obter('valor') == $nome)
			$img = ($this->_ordenacao->campo('tipo_ordem')->obter('valor') == 'DESC' ? 'arrow_down.gif' : 'arrow_up.gif' );
		else
			$img = 'spacer.gif';

		if ( $centrado && $spacer )
			$retorno = '<img width="14" height="8" src="'. Core::diretiva('ESTILO:DIRETORIO:media') .'spacer.gif" border="0" /> ' . $retorno;

		if ($img != 'spacer.gif' || ($img == 'spacer.gif' && $spacer))
			$retorno .= ' <img width="14" height="8" src="'. Core::diretiva('ESTILO:DIRETORIO:media') . $img .'" border="0" />';

		return $retorno;
	}
}

?>