<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('JAlteradorDeFormulario', 'JavaScript/JAlteradorDeFormulario/', true);
Core::incluir('JChecadorDeCampos', 'JavaScript/JChecadorDeCampos/', true);
Core::incluir('JConfirmadorDeAcoesSobreCaixasSelecao', 'JavaScript/JConfirmadorDeAcoes/', true);

class Formulario_De_Acoes extends Formulario
{
	protected $_acoes = array();
	protected $_temMultiplaSelecao = false;
	protected $_selecaoObrigatoria = true;

	public function adicionarMultiplaSelecao ($ids, $selecao_obrigatoria = true)
	{
		$this->adicionarCampo( new Campo(array( 'nome' => 'selecionar_todos',
												'etiqueta' => 'Selecionar todos',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'argumentos' => array(Campo::IGUAL => 1),
												'html_valor' => '1',
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'componente' => new JChecadorDeCampos(array('ids[]'), 'onclick="%s"', $selecao_obrigatoria)
							  )) );
							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'ids',
												'etiqueta' => 'IDs',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::NATURAL,
												'html_valor' => $ids,
												'html_tipo' => Campo::HTML_CAIXA_SELECAO,
												'html_array' => true
							  )) );
							  
		$this->_temMultiplaSelecao = true;
		$this->_selecaoObrigatoria = $selecao_obrigatoria;
	}
	
	public function adicionarAcao ($nome, $label, $confirmacao = null)
	{
		$this->_acoes[$nome] = array('label' => $label, 'confirmacao' => $confirmacao);
	}
	
	public function adicionarBotaoEnviar ($label = 'ok')
	{							  
		$this->adicionarCampo( new Campo(array( 'nome' => 'enviar',
												'etiqueta' => $label,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_ENVIO,
												'html_classe' => 'botao salvar'
							  )) );
	}
	
	public function adicionarBotao ($nome, $label, $mensagem = null, $classe = 'salvar')
	{
		if (!isset($this->_campos['botao_enviador'])) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'botao_enviador',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO
								  )) );
		}
	
		$this->adicionarCampo( new Campo(array( 'nome' => $nome,
												'etiqueta' => $label,
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'html_tipo' => Campo::HTML_BOTAO,
												'html_classe' => 'botao '. $classe
							  )) );

		if ( $this->_temMultiplaSelecao && $this->_selecaoObrigatoria )
			$this->_campos[$nome]->fixar('componente', new JConfirmadorDeAcoesSobreCaixasSelecao($mensagem, 'onclick="this.form.botao_enviador.value=\''. $nome .'\'; %s"', 'ids[]'));
		else
			$this->_campos[$nome]->fixar('componente', new JAlteradorDeFormulario('botao_enviador', $nome, $this->_info['nome'], true, 'onclick="%s"'));
	}
	
	public function obterBotaoParaLink ($nome, $acao = 'onclick="%s"')
	{
		if (!isset($this->_campos['botao_enviador'])) {
			$this->adicionarCampo( new Campo(array( 'nome' => 'botao_enviador',
													'requerimento' => Campo::OPCIONAL,
													'tipo' => Campo::TEXTO,
													'html_tipo' => Campo::HTML_ESCONDIDO
								  )) );
		}

		$componente = null;

		if ( $this->_temMultiplaSelecao && $this->_selecaoObrigatoria )
			$componente = new JConfirmadorDeAcoesSobreCaixasSelecao($mensagem, sprintf($acao, 'this.form.botao_enviador.value=\''. $nome .'\'; %s'), 'ids[]');
		else
			$componente = new JAlteradorDeFormulario('botao_enviador', $nome, $this->_info['nome'], true, $acao);
			
		return @$componente->obterHTML();
	}
	
	public function obterBotaoEnviador ()
	{
		if ( isset($this->_campos['botao_enviador']) ) {
			return $this->_campos['botao_enviador']->obter('valor');
		}
		
		return null;
	}
	
	public function obterAcaoSelecionada ()
	{
		return $this->_campos['acao']->obter('valor');
	}
	
	public function obterIDsSelecionados ()
	{
		return $this->_campos['ids']->obter('valor');
	}
	
	public function checarFormulario ()
	{	
		parent::checarFormulario();
	}
	
	/*
	public function prepararCampoAcao ()
	{
		$acoes = array();
		$acoesComConfirmacao = array();
		
		foreach ($this->_acoes as $k => $a) {
			$acoes[$k] = $a['label'];
			
			if ($a['confirmacao'] != null) {
				$acoesComConfirmacao[] = array($k, $a['confirmacao']);
			}
		}
		
		$this->adicionarCampo( new Campo(array( 'nome' => 'acao',
												'etiqueta' => 'Ação',
												'requerimento' => Campo::OPCIONAL,
												'tipo' => Campo::TEXTO,
												'argumentos' => array(Campo::NUM_SELECOES => 1, Campo::POSSIBILIDADES => array_keys($acoes)),
												'html_tamanho_tipo' => Campo::HTML_TAM_NULO,
												'html_tipo' => Campo::HTML_MENU,
												'html_valor' => $acoes,
												'componente' => (count($acoesComConfirmacao) ? new JSComponente_Confirmador_Acoes($acoesComConfirmacao, 'onchange="%s"') : null)
							  )) );
	}
	*/
	
}

?>