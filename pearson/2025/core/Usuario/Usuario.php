<?
/**
 * @package Core
 * @subpackage GerênciamentoDeUsuários
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Classe que representa um usuário do sistema
 * 
 * @package Core
 * @subpackage GerênciamentoDeUsuários
 */
class Usuario
{
	protected $_dados = array();
	
	private $_logado = false;
	
	
	public function __construct ($id, $logado = false)
	{
		$this->_dados = array(	'id' => null,
								'login' => null,
								'senha' => null,
								'grupos' => array() );
		
		$this->fixarID($id);
		$this->fixarEstadoDeLogin($logado);
	}
	
	static public function &obterNovoUsuario ($login, $senha, $grupos = array())
	{
		if ( empty($login) || empty($senha) ) {
			return null;
		}
	
		Core::registro('db')->query( sprintf('INSERT INTO usuarios (u_login, u_senha, u_grupos) VALUES (%s, %s, %s)', 
			Core::registro('db')->formatarValor($login),
			Core::registro('db')->formatarValor( md5($senha) ),
			Core::registro('db')->formatarValor( implode(';', $grupos) ) ) );

		$obj = new Usuario(Core::registro('db')->insert_id);
		
		$obj->fixarLogin($login);
		$obj->fixarSenha( null );
		$obj->fixarGrupos($grupos);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ($this->_dados['id'] != null) {
			$senha = ($this->_dados['senha'] != null ? 'u_senha = '. Core::registro('db')->formatarValor( md5($this->_dados['senha']) ) .',' : '');
		
			Core::registro('db')->query( sprintf('UPDATE usuarios SET u_login = %s, %s u_grupos = %s WHERE u_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['login']),
				$senha,
				Core::registro('db')->formatarValor( implode(';', $this->_dados['grupos']) ),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$retorno = Core::registro('db')->errno == 0;
			
			// atualiza a sessão caso esteja logado
			if ( Core::registro('usuario')->obterID() == $this->_dados['id'] && $retorno && $this->_dados['senha'] != null ) {
				Core::registro('autenticador')->checaLogin($this->_dados['login'], $this->_dados['senha']);
			}
				
			return $retorno;
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('DELETE FROM usuarios WHERE u_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			$retorno = (Core::registro('db')->errno == 0);
			
			if ($retorno)
				@Gerenciador_Diretivas::removerDiretivaPeloIDUsuario( $this->_dados['id'] );

			$this->fixarID(null);

			return $retorno;
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM usuarios WHERE u_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarLogin($row['u_login']);
				$this->fixarSenha( null );
				$this->fixarGrupos( explode(';', $row['u_grupos']) );
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarLogin ($login)
	{
		$this->_dados['login'] = $login;
	}
	
	public function obterLogin ()
	{
		return $this->_dados['login'];
	}
	
	public function fixarSenha ($senha = null)
	{
		$this->_dados['senha'] = $senha;
	}
	
	public function obterSenha ()
	{
		return $this->_dados['senha'];
	}
	
	public function fixarGrupos ($grupos)
	{
		if (!is_array($grupos))
			$grupos = array($grupos);
		
		$this->_dados['grupos'] = $grupos;
	}
	
	public function obterGrupos ()
	{
		return $this->_dados['grupos'];
	}
	
	public function fixarGrupo ($grupo)
	{
		$this->_dados['grupos'] = array($grupo);
	}
	
	public function obterGrupo ()
	{
		return (isset($this->_dados['grupos'][0]) ? $this->_dados['grupos'][0] : null);
	}
	
	public function fixarEstadoDeLogin ($logado = false)
	{
		$this->_logado = (bool) $logado;
	}
	
	public function estaLogado ()
	{
		return $this->_logado;
	}
	
	static public function obterIDPeloLogin ($login)
	{
		$login = strtolower($login);
		
		$rs = Core::registro('db')->query( sprintf('SELECT u_id FROM usuarios WHERE BINARY LCASE(u_login) = %s',
			Core::registro('db')->formatarValor($login) ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			
			return $row['u_id'];
		}
		$rs->free();
		
		return false;
	}

    public function verificaLogadoChat ()
    {
        /* $id = '';
        $occ = '';
        $off = '';
        $session = '';

        $rs = Core::registro('db')->query('SELECT * FROM chat_usuarios WHERE u_id = '.$this->_dados['id']);

        if ($rs->num_rows) {
            $row = $rs->fetch_assoc();
            $id = $row['u_id'];
            $occ = $row['u_ocupado'];
            $off = $row['u_offline'];
            $session = $row['u_session'];
        }
        $rs->free();

        if (!file_exists('/tmp/sess_'.$session)) {
            $this->userOffChat();
        }

        if($off == 'S'){
            return 'off';
        }
        elseif($occ == 'S'){
            return 'occ';
        }
        elseif($id == $this->_dados['id']){
            return 'onn';
        } */

        return false;
    }

    public function userOnChat(){
        /* if($this->verificaLogadoChat() === false){
            $ses_id = session_id();
            Core::registro('db')->query('INSERT INTO chat_usuarios (u_id, u_session) VALUES ('.$this->_dados['id'].', "'.$ses_id.'")');
        } */
		return true;
    }

    public function userOffChat(){
        /* Core::registro('db')->query('DELETE FROM chat_usuarios WHERE u_id = '.$this->_dados['id']);
        Core::registro('db')->query('DELETE FROM chat_group_user WHERE usuario = '.$this->_dados['id']); */
		return true;
    }
}

?>