<?
/**
 * @package Core
 * @subpackage GerênciamentoDeUsuários
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * @package Core
 * @subpackage GerênciamentoDeUsuários
 */
class Permissoes
{
	protected $_usuario; // id do usuário para checar permissões
	protected $_grupo; // grupo do usuário para checar permissões
	protected $_publico; // é um usuário não registrado?
	protected $_permissoes; // array de permissões
	
	public function __construct () {
		$this->_usuario = 0;
		$this->_grupo = 0;
		$this->_publico = false;
		$this->_permissoes = array();
	}
	
	public function checarPermissoesPara (Usuario &$usuario) {
		if ( $usuario == null )
			return false;
		
		if ( $usuario->estaLogado() ) {
			$this->_usuario = (int) $usuario->obterID();
			$this->_grupo = (int) $usuario->obterGrupo();
			$this->_publico = false;
		} else {
			$this->_usuario = 0;
			$this->_grupo = 0;
			$this->_publico = true;
		}		

		return true;
	}
	
	// metodo meio gambiarra pra usar perfil falso nos relatórios
	public function usuarioTemporarioTemPermissao($nome, Usuario &$usuario) {
		$usuario_bak = $this->_usuario;
		$grupo_bak = $this->_grupo;
		$publico_bak = $this->_publico;
		
		$this->_usuario = (int) $usuario->obterID();
		$this->_grupo = (int) $usuario->obterGrupo();
		$this->_publico = false;
		
		$retorno = $this->temPermissao($nome);
		
		$this->_usuario = $usuario_bak;
		$this->_grupo = $grupo_bak;
		$this->_publico = $publico_bak;
		
		return $retorno;
	}

	public function temPermissao ($nome) {
		if ( isset($this->_permissoes[$nome]) ) {
			if ( $this->_publico && $this->_permissoes[$nome]['publico'] ) {
				return true;
			} else if ( !in_array($this->_usuario, $this->_permissoes[$nome]['usuarios_negados']) && 
							!in_array($this->_grupo, $this->_permissoes[$nome]['grupos_negados']) ) {
				if ( in_array($this->_usuario, $this->_permissoes[$nome]['usuarios']) || 
						in_array($this->_grupo, $this->_permissoes[$nome]['grupos']) )
					return true;
			}
		}
		
		return false;
	}
	
	public function carregarPermissoes () {		
		$grupos = $this->_obterTodosOsGrupos();
		
		$rs = Core::registro('db')->query('SELECT p_nome, p_acesso_publico, p_grupos, p_grupos_negados, p_usuarios, p_usuarios_negados FROM permissoes');
		if ( $rs->num_rows ) {
			while ( $row = $rs->fetch_assoc() )
			{				
				$this->_permissoes[ $row['p_nome'] ] = array(   'grupos' => array(),
																'usuarios' => array(),
																'grupos_negados' => array(),
																'usuarios_negados' => array(),
																'publico' => (bool) $row['p_acesso_publico']
															 );
				
				if ( $row['p_grupos'] == '*' )
					$row['p_grupos'] = $grupos;
				else
					$row['p_grupos'] = explode(',', $row['p_grupos']);
				
				if ( $row['p_grupos_negados'] == '*' )
					$row['p_grupos_negados'] = $grupos;
				else
					$row['p_grupos_negados'] = explode(',', $row['p_grupos_negados']);

				$row['p_usuarios'] = explode(',', $row['p_usuarios']);
				$row['p_usuarios_negados'] = explode(',', $row['p_usuarios_negados']);
					
				$this->_permissoes[ $row['p_nome'] ]['grupos'] = $row['p_grupos'];
				$this->_permissoes[ $row['p_nome'] ]['usuarios'] = $row['p_usuarios'];
				$this->_permissoes[ $row['p_nome'] ]['grupos_negados'] = $row['p_grupos_negados'];
				$this->_permissoes[ $row['p_nome'] ]['usuarios_negados'] = $row['p_usuarios_negados'];
			}
		}
		$rs->free();
	}

	private function _obterTodosOsGrupos ()
	{
		$grupos = array();

		$rs = Core::registro('db')->query('SELECT g_id FROM grupos');
		if ( $rs->num_rows ) {			
			while ( $row = $rs->fetch_assoc() ) {
				$grupos[] = $row['g_id'];
			}	
		}
		$rs->free();

		return $grupos;
	}
}

?>