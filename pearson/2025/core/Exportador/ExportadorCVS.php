<?
if (!defined('CORE_INCLUIDO')) { exit(); }

abstract class ExportadorCVS
{
	protected $_dados;
	protected $_separador = ';';
	protected $_duplo = "\"";
	
	abstract public function exportar ();
	
	public function obterSaida ($nomeArquivo = 'saida.csv')
	{
		Core::modulo('index')->fixarTipoDeSaida(Modulo::ARQUIVO, array('nome_arquivo' => $nomeArquivo, 'mime' => 'text/csv'));
	
		$csv = '';
	
		if ( !is_array($this->_dados) )
			echo $csv;

		foreach ($this->_dados as $linha) {
		
			$escreverSeparador = false;
			
			foreach ($linha as $elemento) {
				$elemento = str_replace("\"", "\"\"", $elemento);
				
				if ($escreverSeparador) {
					$csv .= $this->_separador;
				}
				
				$csv .= $this->_duplo . $elemento . $this->_duplo;
				
				$escreverSeparador = true;
			}
			
			$csv .= "\n";
		}
		
		echo $csv;
	}
	
	public function obterTipoSaida() {
		return Modulo::ARQUIVO;		
	}
}

?>