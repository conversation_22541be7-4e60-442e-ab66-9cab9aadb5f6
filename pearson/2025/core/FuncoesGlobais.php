<?
if (!defined('CORE_INCLUIDO')) { exit(); }

// gera um numero randomico
function randomico ($preciso = false, $tamanho = 32) {
	return substr( md5( uniqid(microtime(), $preciso) ), 0, $tamanho);
}

// strptime pareceu problematica no linux, então aqui está uma versão melhor
function strptime_real ($formato, $data)
{
	$ordem = array();
	$partes = explode('%', $formato);

	if (empty($formato) || !is_array($partes) || !count($partes)) {
		return false;
	}
	
	foreach ($partes as $k => $v) {
		if ($k == 0) continue;
		$ordem[$v[0]] = $k;
	}
	
	$regex = preg_replace('(%d|%m|%H|%M|%S)', '([0-9]{2})', $formato);
	$regex = str_replace('%Y', '([0-9]{4})', $regex);
	
	preg_match('#^'. $regex .'$#', $data, $matches);
	
	$dados = array('H' => 0, 'M' => 0, 'S' => 0, 'm' => 1, 'd' => 1, 'Y' => 1970);
	
	foreach ($ordem as $k => $v) {
		if (isset($matches[$v]))
			$dados[$k] = $matches[$v];
	}
	
	return mktime($dados['H'], $dados['M'], $dados['S'], $dados['m'], $dados['d'], $dados['Y']);
}

function implode_to_javascript($array) {
	$retorno = '[';
	
	$i = 0;
	foreach($array as $a) {
		if ( substr($a, 0, 1) == '[' && substr($a, -1, 1) == ']' )
			$retorno .= $a;
		else
			$retorno .= "'$a'";
		
		if (++$i != count($array))
			$retorno .= ",";
	}
	
	return $retorno .= ']';
}
?>