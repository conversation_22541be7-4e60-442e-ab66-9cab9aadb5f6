<?
/**
 * @package Core
 * @subpackage Geral
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Aplica vários tipos de válidações em dados
 *
 * @package Core
 * @subpackage Geral
 * 
 * @static 
 */
class Filtrador
{
	/**
	 * Pra saber que tipo de função usar quando for validado por expressão regular
	 */
	const PREG_MATCH = 0;

	/**
	 * Aplica uma validação usando expressões regulares
	 * @static 
	 * @param string $valor Valor pra validar
	 * @param string $exp Expressão regular
	 * @param bool $acertos opcional Se deve ou não retornar o número de acertos
	 * @param int $tipo opcional Tipo de função pra validar
	 * @return mixed
	 */
	static public function expRegular ($valor, $exp, $acertos = false, $tipo = Filtrador::PREG_MATCH)
	{
		switch ($tipo) {
			case self::PREG_MATCH:
				if ($acertos) {
					preg_match($exp, $valor, $acertos);
					return $acertos;
				} else {
					return preg_match($exp, $valor);
				}
				break;
			default:
				return false;
		}
	}
	
	static public function inteiro ($valor, $considerarStrings = true)
	{
		if ( is_int($valor) )
			return true;
		
		if ( $considerarStrings && is_string($valor) && ltrim($valor, '0') === (string)(int) $valor )
			return true;
		
		return false;
	}
	
	static public function natural ($valor, $considerarStrings = true)
	{
		if ( self::inteiro($valor, $considerarStrings) && $valor >= 0 ) {
			return true;
		}
		
		return false;
	}
	
	static public function real ($valor, $considerarStrings = true)
	{
		if ( is_float($valor) ) {
			return true;
		}
		
		if ( $considerarStrings && is_numeric($valor) ) {
			return true;
		}
		
		return false;
	}
	
	static public function texto ($valor)
	{
		if ( is_string($valor) ) {
			return true;
		}
		
		return false;
	}
	
	static public function email ($valor)
	{
		//if ( preg_match('/^(([A-Za-z0-9]+_+)|([A-Za-z0-9]+\-+)|([A-Za-z0-9]+\.+)|([A-Za-z0-9]+\++))*[A-Za-z0-9]+@((\w+\-+)|(\w+\.))*\w{1,63}\.[a-zA-Z]{2,6}$/', $valor) )
		
		if ( preg_match('/\\A(?:^([a-z0-9][a-z0-9_\\-\\.\\+]*)@([a-z0-9][a-z0-9\\.\\-]{0,63}\\.(com|org|net|biz|info|name|net|pro|aero|coop|museum|[a-z]{2,4}))$)\\z/i', $valor) )
			return true;

		return false;
	}
	
	static public function data ($formato, $data)
	{
		return $data == strftime($formato, strptime_real($formato, $data));
	}
	
	static public function vaziu ($valor)
	{
		$valor = trim($valor);
		
		return empty($valor);
	}
	
	static public function md5Valido ($valor, $verificarTamanho = false) {
		if ( self::vaziu($valor) )
			return false;
			
		if ($verificarTamanho)
			return !self::expRegular ($valor, '/[^a-z0-9]/i') && (strlen($valor) <= 32);
		else
			return !self::expRegular ($valor, '/[^a-z0-9]/i');		
	}
}

?>