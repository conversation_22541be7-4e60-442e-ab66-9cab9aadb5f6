<?php
/**
 * @package Core
 * @subpackage BancoDeDados
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

include_once 'DB/DB.php';

/**
 * Classe pra acesso ao banco de dados MySQL
 * 
 * @package Core
 * @subpackage BancoDeDados
 */
class mySQL_DB extends mysqli implements DB
{
    private $debugMYSQL = false;
    private $debugRedis = false;
    private $start = 0;
    private $_config = array();
    private $redis = null;
    private $redisAvailable = false;
    private $cacheExpireTime = 3600; // 1 hora

    public function __construct($arrayConfig = null, $autoConectar = true)
    {
        $this->debugMYSQL = debugMYSQL;
        $this->debugRedis = debugRedis;

        $this->start = microtime(true);

        // prepara configurações padrões
        $this->_config = array(
            'host' => 'localhost',
            'username' => 'root',
            'passwd' => null,
            'dbname' => null,
            'port' => '3306',
            'socket' => null
        );
        
        // atualiza a configuração padrão
        $this->_config = array_merge($this->_config, $arrayConfig);
        
        // Conecta ao Redis
        try {
            $this->redis = new Redis();
            $connected = $this->redis->connect('127.0.0.1', 6379, 5);
            
            if ($connected) {
                $authenticated = $this->redis->auth('avRede@red1sIn2025t2oUseRD$');
                if ($authenticated) {
                    $this->redisAvailable = true;
                    $this->logRedisDebug("Conexão com Redis estabelecida com sucesso", [
                        'database' => $this->_config['dbname']
                    ]);
                } else {
                    $this->logRedisDebug("Falha na autenticação com Redis", [
                        'database' => $this->_config['dbname']
                    ]);
                }
            } else {
                $this->logRedisDebug("Falha na conexão com Redis", [
                    'database' => $this->_config['dbname']
                ]);
            }

            // Verifica se o Redis está operacional
            if ($this->isRedisOperational()) {
                $this->logRedisDebug("Redis está operacional", [
                    'database' => $this->_config['dbname']
                ]);
            } else {
                $this->logRedisDebug("Redis não está operacional", [
                    'database' => $this->_config['dbname']
                ]);
            }
        } catch (Exception $e) {
            $this->redisAvailable = false;
            $this->logRedisDebug("Erro na conexão com Redis: " . $e->getMessage(), [
                'database' => $this->_config['dbname'],
                'error' => $e->getMessage()
            ]);
        }

        if ($autoConectar) {
            return $this->conectar();
        }
        
        return true;
    }
    
    public function __destruct()
    {
        $this->desconectar();
    }

    public function conectar()
    {
        try
        {
            @parent::connect($this->_config['host'], $this->_config['username'], $this->_config['passwd'], 
                            $this->_config['dbname'], $this->_config['port'], $this->_config['socket']);
        
            if (mysqli_connect_errno())
            {
                $this->logErr('','Erro ao conectar com o banco de dados! - '.Core_Exception::E_FATAL);
                throw new Core_Exception('Erro ao conectar com o banco de dados!', Core_Exception::E_FATAL);
            }
            
            return true;
        }
        catch (Core_Exception $e)
        {
            echo $e;
            return false;
        }
    }
    
    public function desconectar()
    {
        try {
            if ($this->redisAvailable && $this->redis) {
                $this->redis->close();
            }
        } catch (Exception $e) {
            $this->logRedisDebug("Erro ao fechar conexão Redis: " . $e->getMessage(), [
                'database' => $this->_config['dbname']
            ]);
        }
        
        @parent::close();
        @$this->close();
    }
    
    /**
     * Verifica se o Redis está disponível e operacional
     * @return bool True se o Redis estiver funcionando corretamente, False caso contrário
     */
    private function isRedisOperational() {
        if (!$this->redisAvailable || !$this->redis) {
            return false;
        }
        
        try {
            // Tenta uma operação simples para verificar se o Redis está respondendo
            $pingResult = $this->redis->ping();
            return ($pingResult === '+PONG' || $pingResult === true);
        } catch (Exception $e) {
            $this->logRedisDebug("Redis não está operacional: " . $e->getMessage(), [
                'database' => $this->_config['dbname']
            ]);
            $this->redisAvailable = false;
            return false;
        }
    }
    
    public function query($query)
    {
        $this->logRedisDebug("Executando query: $query", [
            'database' => $this->_config['dbname'],
            'query' => $query
        ]);
    
        try
        {
            // Verifica cache apenas para queries SELECT
            if ($this->isRedisOperational() && 
                stripos(trim($query), 'SELECT') === 0) {
                
                // Normaliza a query para gerar chave de cache consistente
                $normalizedQuery = $this->normalizeQuery($query);
                $cacheKey = "query[".$this->_config['dbname']."]:" . md5($normalizedQuery);
                
                $this->logRedisDebug("Verificando cache com a chave: $cacheKey", [
                    'database' => $this->_config['dbname'],
                    'query' => $query,
                    'normalized_query' => $normalizedQuery,
                    'cache_key' => $cacheKey
                ]);
                
                try {
                    $cachedResult = $this->redis->get($cacheKey);
        
                    if ($cachedResult) {
                        $this->logRedisDebug("Cache encontrado - retornando dados do Redis", [
                            'database' => $this->_config['dbname'],
                            'query' => $query,
                            'cache_key' => $cacheKey
                        ]);
                        
                        $resultData = json_decode($cachedResult, true);
                        
                        if ($resultData !== null) {
                            $redisResult = new RedisResult($resultData, $cacheKey, $query);
                            
                            $this->logRedisDebug("RedisResult criado com sucesso", [
                                'database' => $this->_config['dbname'],
                                'query' => $query,
                                'cache_key' => $cacheKey,
                                'row_count' => count($resultData)
                            ]);
                            
                            return $redisResult;
                        } else {
                            $this->logRedisDebug("Erro ao decodificar JSON do cache", [
                                'database' => $this->_config['dbname'],
                                'query' => $query,
                                'cache_key' => $cacheKey
                            ]);
                        }
                    } else {
                        $this->logRedisDebug("Cache não encontrado", [
                            'database' => $this->_config['dbname'],
                            'query' => $query,
                            'cache_key' => $cacheKey
                        ]);
                    }
                } catch (Exception $e) {
                    $this->logRedisDebug("Erro ao verificar cache: " . $e->getMessage(), [
                        'database' => $this->_config['dbname'],
                        'query' => $query,
                        'error' => $e->getMessage()
                    ]);
                }
            }
    
            // Executa a query no MySQL (se não há cache ou não é SELECT)
            $rs = parent::query($query);
            
            if ($rs) {
                $this->logRedisDebug("Query executada com sucesso via MySQL", [
                    'database' => $this->_config['dbname'],
                    'query' => $query,
                    'result_type' => ($rs instanceof mysqli_result) ? 'result_set' : 'boolean'
                ]);
                
                // Log para debug MySQL
                if($this->debugMYSQL) {
                    // Código de log existente
                }
            } else {
                $this->logRedisDebug("ERRO na execução da query", [
                    'database' => $this->_config['dbname'],
                    'query' => $query,
                    'error' => $this->error
                ]);
            }
    
            return $rs;
        }
        catch (Core_Exception $e)
        {
            $this->logRedisDebug("ERRO na execução da query", [
                'database' => $this->_config['dbname'],
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            echo $e;
            return false;
        }
        catch (Exception $e)
        {
            $this->logRedisDebug("Exceção geral na execução da query", [
                'database' => $this->_config['dbname'],
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            
            // Em caso de exceção geral, tenta executar a query diretamente no MySQL
            try {
                return parent::query($query);
            } catch (Exception $mysqlException) {
                $this->logRedisDebug("ERRO na execução da query MySQL após falha Redis", [
                    'database' => $this->_config['dbname'],
                    'query' => $query,
                    'error' => $mysqlException->getMessage()
                ]);
                return false;
            }
        }
    }

    public function escape($valor = null)
    {
        $valor = $this->real_escape_string($valor);
        
        $valor = str_replace('%', '\%', $valor);
        $valor = str_replace('_', '\_', $valor);
        
        return $valor;
    }
    
    public function formatarValor($valor = null, $index = 0)
    {
        if (is_array($valor) && array_key_exists($index, $valor)) {
            $valor = $valor[$index];
        }
        
        if (strlen($valor)) {
            $valor = $this->real_escape_string($valor); // Escape special characters
            return "'{$valor}'";
        } else {
            return 'NULL';
        }
    }

    private function logErr($query = '', $error = '')
    {
        $f = __DIR__.'/../../log/db_err_'.date('d_m_Y').'.log';
        $fp = fopen($f, 'a');

        $mgu = memory_get_usage();
        $mgpu = memory_get_peak_usage();
        $dbt = debug_backtrace();
        $time_elapsed_secs = microtime(true) - $this->start;

        $duration = microtime(true) - $this->start;
        $hours = (int)($duration/60/60);
        $minutes = (int)($duration/60)-$hours*60;
        $seconds = $duration-$hours*60*60-$minutes*60;

        fwrite($fp, "------------------------------\n");
        fwrite($fp, 'Time: '.date('H:i:s')."\n");
        fwrite($fp, 'Query: '.$query."\n");
        fwrite($fp, 'Error: '.$error."\n");
        fwrite($fp, 'memory_get_usage: '.$mgu."\n");
        fwrite($fp, 'memory_get_peak_usage: '.$mgpu."\n");
        fwrite($fp, 'seconds: '.(number_format((float)$seconds, 2, '.', '')).".\n");
        fwrite($fp, 'exec_time: '.($time_elapsed_secs)." seconds.\n");
        fwrite($fp, "------------------------------\n");
        fclose($fp);
    }
    
    /**
     * Registra mensagens de debug específicas para o Redis de forma segura
     * @param string $message Mensagem a ser registrada
     * @param array $data Dados adicionais para o log
     */
    private function logRedisDebug($message, $data = []) {
        if ($this->debugRedis) {
            try {
                $logFile = __DIR__.'/../../log/redis_debug_'.date('d_m_Y').'.log';
                $fp = @fopen($logFile, 'a');
                
                if ($fp) {
                    // Formata os dados para o log em JSON
                    $logData = [
                        'timestamp' => date('Y-m-d H:i:s'),
                        'message' => $message,
                        'database' => isset($data['database']) ? $data['database'] : ($this->_config['dbname'] ?? 'unknown')
                    ];
                    
                    // Adiciona outros dados disponíveis
                    foreach ($data as $key => $value) {
                        if (!isset($logData[$key])) {
                            $logData[$key] = $value;
                        }
                    }
                    
                    // Converte para JSON e trata erros de codificação
                    $jsonData = @json_encode($logData);
                    if ($jsonData === false) {
                        // Se falhar, tente remover caracteres que podem causar problemas
                        array_walk_recursive($logData, function(&$value) {
                            if (is_string($value)) {
                                $value = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
                                $value = preg_replace('/[\x00-\x1F\x7F]/u', '', $value);
                            }
                        });
                        $jsonData = @json_encode($logData);
                        
                        // Se ainda falhar, use um formato simples
                        if ($jsonData === false) {
                            $jsonData = '{"timestamp":"' . date('Y-m-d H:i:s') . '","message":"' . 
                                        str_replace('"', '\"', $message) . '","error":"JSON encoding failed"}';
                        }
                    }
                    
                    @fwrite($fp, $jsonData . "\n");
                    @fclose($fp);
                }
            } catch (Exception $e) {
                // Falha silenciosamente - não podemos logar que falhou ao logar
            }
        }
    }

    /**
     * Normaliza uma query SQL removendo espaços desnecessários e quebras de linha
     * 
     * @param string $query A query SQL a ser normalizada
     * @return string Query normalizada
     */
    private function normalizeQuery($query) {
        // Remove quebras de linha e tabs
        $query = str_replace(["\r\n", "\r", "\n", "\t"], ' ', $query);
        
        // Remove comentários SQL (-- comentário e /* comentário */)
        $query = preg_replace('/--.*$/m', '', $query);
        $query = preg_replace('/\/\*.*?\*\//s', '', $query);
        
        // Remove espaços múltiplos e substitui por um único espaço
        $query = preg_replace('/\s+/', ' ', $query);
        
        // Remove espaços no início e fim
        $query = trim($query);
        
        // Remove espaços antes e depois de caracteres especiais (mas preserva strings)
        $query = preg_replace('/\s*([(),=<>!])\s*/', '$1', $query);
        
        // Converte para minúsculas para comparação consistente
        $query = strtolower($query);
        
        return $query;
    }

    /**
     * Limpa todo o cache Redis
     * 
     * @return bool Sucesso da operação
     */
    public function flushAll() {
        if (!$this->isRedisOperational()) {
            $this->logRedisDebug("flushAll(): Redis não está operacional", [
                'database' => $this->_config['dbname']
            ]);
            return false;
        }
        
        try {
            $flushResult = $this->redis->flushAll();
            
            if ($flushResult) {
                $this->logRedisDebug("flushAll(): Cache Redis limpo com sucesso", [
                    'database' => $this->_config['dbname'],
                    'flush_result' => $flushResult
                ]);
                return true;
            } else {
                $this->logRedisDebug("flushAll(): Falha ao limpar cache Redis", [
                    'database' => $this->_config['dbname'],
                    'flush_result' => $flushResult
                ]);
                return false;
            }
        } catch (Exception $e) {
            $this->logRedisDebug("flushAll(): Erro ao executar flushAll: " . $e->getMessage(), [
                'database' => $this->_config['dbname'],
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}

/**
 * Classe para simular o resultado do mysqli usando dados do Redis
 * Implementa a interface de um objeto mysqli_result de forma simplificada
 */
class RedisResult implements Iterator {
    private $data = [];
    private $position = 0;
    public $num_rows = 0;
    public $field_count = 0;
    private $fieldNames = [];
    private $cacheKey = '';
    private $queryInfo = '';
    private $currentRowIndex = 0;

    /**
     * Construtor
     * 
     * @param array $data Os dados do resultado
     * @param string $cacheKey A chave usada no cache Redis
     * @param string $queryInfo Informações sobre a query original
     */
    public function __construct($data, $cacheKey = '', $queryInfo = '') {
        $this->data = $data;
        $this->num_rows = count($this->data);
        $this->position = 0;
        $this->currentRowIndex = 0;
        $this->cacheKey = $cacheKey;
        $this->queryInfo = $queryInfo;
        
        // Determina os campos com base na primeira linha (se houver)
        if ($this->num_rows > 0) {
            $this->fieldNames = array_keys($this->data[0]);
            $this->field_count = count($this->fieldNames);
        }
        
        $this->logDebug("RedisResult criado", [
            'cache_key' => $this->cacheKey,
            'query' => $this->queryInfo,
            'row_count' => $this->num_rows,
            'field_count' => $this->field_count
        ]);
    }

    /**
     * Retorna a próxima linha como um array associativo
     */
    public function fetch_assoc() {
        if ($this->currentRowIndex >= $this->num_rows) {
            return null;
        }
        
        $row = $this->data[$this->currentRowIndex];
        $this->currentRowIndex++;
        
        return $row;
    }

    /**
     * Retorna a próxima linha como um array com índices numéricos, associativos ou ambos
     */
    public function fetch_array($resultType = MYSQLI_BOTH) {
        if ($this->currentRowIndex >= $this->num_rows) {
            return null;
        }
        
        $row = $this->data[$this->currentRowIndex];
        $this->currentRowIndex++;
        
        if ($resultType == MYSQLI_ASSOC) {
            return $row;
        } elseif ($resultType == MYSQLI_NUM) {
            return array_values($row);
        } else { // MYSQLI_BOTH
            $numericRow = array_values($row);
            $combinedRow = [];
            
            foreach ($row as $key => $value) {
                $combinedRow[$key] = $value;
            }
            
            $i = 0;
            foreach ($numericRow as $value) {
                $combinedRow[$i] = $value;
                $i++;
            }
            
            return $combinedRow;
        }
    }

    /**
     * Retorna a próxima linha como um array com índices numéricos
     */
    public function fetch_row() {
        if ($this->currentRowIndex >= $this->num_rows) {
            return null;
        }
        
        $row = array_values($this->data[$this->currentRowIndex]);
        $this->currentRowIndex++;
        
        return $row;
    }

    /**
     * Move o ponteiro interno para a linha especificada
     */
    public function data_seek($offset) {
        if ($offset >= 0 && $offset < $this->num_rows) {
            $this->currentRowIndex = $offset;
            $this->position = $offset;
            return true;
        }
        
        return false;
    }

    /**
     * Libera os recursos do resultado
     */
    public function free() {
        $this->data = [];
        $this->position = 0;
        $this->currentRowIndex = 0;
        $this->num_rows = 0;
        $this->field_count = 0;
        $this->fieldNames = [];
    }
    
    /**
     * Alias para free()
     */
    public function free_result() {
        return $this->free();
    }
    
    /**
     * Alias para free()
     */
    public function close() {
        return $this->free();
    }

    // Implementação da interface Iterator
    public function rewind() {
        $this->position = 0;
    }

    public function current() {
        return isset($this->data[$this->position]) ? $this->data[$this->position] : null;
    }

    public function key() {
        return $this->position;
    }

    public function next() {
        ++$this->position;
    }

    public function valid() {
        return isset($this->data[$this->position]);
    }
    
    /**
     * Registra mensagens de debug de forma segura
     */
    private function logDebug($message, $data = []) {
        if (defined('debugRedis') && debugRedis) {
            try {
                $logFile = __DIR__.'/../../log/redis_result_'.date('d_m_Y').'.log';
                $fp = @fopen($logFile, 'a');
                
                if ($fp) {
                    $logData = [
                        'timestamp' => date('Y-m-d H:i:s'),
                        'message' => $message,
                        'cache_key' => isset($data['cache_key']) ? $data['cache_key'] : $this->cacheKey,
                        'query_info' => isset($data['query_info']) ? $data['query_info'] : $this->queryInfo
                    ];
                    
                    foreach ($data as $key => $value) {
                        if (!isset($logData[$key])) {
                            $logData[$key] = $value;
                        }
                    }
                    
                    $jsonData = @json_encode($logData);
                    if ($jsonData === false) {
                        $jsonData = '{"timestamp":"' . date('Y-m-d H:i:s') . '","message":"' . 
                                    str_replace('"', '\"', $message) . '","error":"JSON encoding failed"}';
                    }
                    
                    @fwrite($fp, $jsonData . "\n");
                    @fclose($fp);
                }
            } catch (Exception $e) {
                // Falha silenciosamente
            }
        }
    }
}