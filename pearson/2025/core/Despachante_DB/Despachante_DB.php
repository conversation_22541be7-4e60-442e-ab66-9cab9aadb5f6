<?
/**
 * @package Core
 * @subpackage Modulos
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

include('Despachante/Despachante.php');

/**
 * Adiciona a funcionalidade de carregar automaticamente os caminhos possíveis registrados em um banco de dados
 * 
 * @package Core
 * @subpackage Modulos
 */
class Despachante_DB extends Despachante
{
	/**
	 * Construtor padrão.
	 * Não tem necessidade de passar parametros pois ele pega automaticamente do Core.
	 */
	public function __construct ()
	{		
		parent::__construct(array(Despachante::MODULO => Core::diretiva('CORE:PARAMETRO:modulo'), Despachante::ACAO => Core::diretiva('CORE:PARAMETRO:acao')));
	}
	
	/**
	 * Gera os caminhos automaticamente, pegando dados do banco de dados
	 * 
	 * @todo retornar exceção com o erro especifico
	 */
	public function carregarCaminhos ()
	{
		$rs = Core::registro('db')->query('
			SELECT modulos.m_nome, modulos.m_classe, modulos.m_padrao, 
				   acoes.a_nome, acoes.a_funcao, acoes.a_padrao, 
				   permissoes.p_nome 
			FROM modulos 
			JOIN acoes ON acoes.a_modulo = modulos.m_id 
			LEFT JOIN permissoes ON permissoes.p_id = acoes.a_permissao' );

		if ($rs->num_rows) {			
			while ($row = $rs->fetch_assoc())
			{
				$modulo = array();
				$acao = array();
				
				$modulo['nome'] = strtolower($row['m_nome']);
				$modulo['classe'] = $row['m_classe'];
				$modulo['padrao'] = ($row['m_padrao'] == 'S' ? true : false);
				
				$acao['nome'] = strtolower($row['a_nome']);
				$acao['funcao'] = $row['a_funcao'];
				$acao['padrao'] = ($row['a_padrao'] == 'S' ? true : false);
			
				$this->adicionarCaminho($modulo, $acao);
				
				$this->_caminhos[$row['m_nome']]['acoes'][$row['a_nome']]['permissao'] = $row['p_nome'];
			}
		}
		$rs->free();

		return $this->_caminhos;
	}
	
	/**
	 * Baseado em uma entrada, retorna uma array mais especifica com info sobre o modulo e ação despachado
	 * Cuida automaticamente de carregar a permissão pro modulo/ação 
	 * @param array $entrada
	 * @return array
	 * 
	 * @todo retorna token ou exceção
	 */
	public function despachar (&$entrada)
	{
		$caminho = parent::despachar($entrada);
		
		if (!is_null($caminho)) {			
			$caminho['guardar_como'] = 'solicitado';
			
			return $caminho;
		} else {
			// @todo: isso tem que jogar uma exception...
			echo 'Nenhuma ação selecionada para ser carregada!'; exit();
		}
	}
}

?>
