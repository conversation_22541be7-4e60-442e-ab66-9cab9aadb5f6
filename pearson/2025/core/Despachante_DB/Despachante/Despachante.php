<?
/**
 * @package Core
 * @subpackage Modulos
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Baseado em caminhos possíveis essa classe acha o modulo e ação mais provaveis através de parametros
 * 
 * @package Core
 * @subpackage Modulos
 */
class Despachante
{
	/**
	 * Usado pra indicar qual variavel representa o modulo
	 */
	const MODULO = 0;
	
	/**
	 * Usado pra indicar qual variavel representa a ação
	 */
	const ACAO = 1;
	
	/**
	 * Possíveis caminhos
	 * @var array
	 */
	protected $_caminhos = array();
	
	/**
	 * Parametros passados
	 * @var array
	 */
	protected $_parametros = array();

	/**
	 * Construtor padrão
	 * Exemplo:
	 * <code>
	 * new Despachante(array(Despachante::MODULO => 'm', Despachante::ACAO => 'a'));
	 * </code>
	 * 
	 * @param array $parametros
	 */
	public function __construct ($parametros)
	{
		$this->_parametros = $parametros;
	}
	
	/**
	 * Adiciona um caminho possível
	 * @param array $modulo
	 * @param array $acao
	 * 
	 * @todo $modulo e $acao devem virar Tokens, pra todo Core falar a "mesma lingua"
	 */
	public function adicionarCaminho ($modulo, $acao)
	{
		if ( !isset($this->_caminhos[$modulo['nome']]) ) {
			$this->_caminhos[$modulo['nome']] = array('nome' => $modulo['nome'], 'classe' => $modulo['classe'], 'padrao' => $modulo['padrao'], 'acoes' => array());
		}
		
		$this->_caminhos[$modulo['nome']]['acoes'][$acao['nome']] = array('nome' => $acao['nome'], 'funcao' => $acao['funcao'], 'padrao' => $acao['padrao']);
	}
	
	/**
	 * Retorna uma array com o modulo e ação resultado, baseado na $entrada passada
	 * @param array $entrada
	 * @return array
	 * 
	 * @todo retornar token
	 */
	public function despachar (&$entrada)
	{
		$entrada = array_map('strtolower', $entrada);
		return $this->_gerarCaminhoFinal($entrada);
	}
	
	/**
	 * Gera uma array com o caminho final, baseado numa entrada
	 * @param array $entrada
	 * @return array
	 * 
	 * @todo retornar token
	 */
	private function _gerarCaminhoFinal ($entrada)
	{
		$moduloSolicitado = null;
		$acaoSolicitada = null;
		$caminho = null;
		
		if ( array_key_exists($this->_parametros[Despachante::MODULO], $entrada) && $this->_moduloRegistrado($entrada[$this->_parametros[Despachante::MODULO]]) ) {
			$moduloSolicitado = $entrada[$this->_parametros[Despachante::MODULO]];
		} else {
			$moduloSolicitado = $this->_obterModuloPadrao();
		}
		
		if ( $this->_moduloRegistrado($moduloSolicitado) ) {
			$caminho = $this->_caminhos[$moduloSolicitado];
			
			if ( array_key_exists($this->_parametros[Despachante::ACAO], $entrada) && $this->_acaoRegistrada($entrada[$this->_parametros[Despachante::ACAO]], $moduloSolicitado)) {
				$acaoSolicitada = $entrada[$this->_parametros[Despachante::ACAO]];
			} else {
				$acaoSolicitada = $this->_obterAcaoPadrao($moduloSolicitado);
			}

			if ( $this->_acaoRegistrada($acaoSolicitada, $moduloSolicitado) ) {
				$caminho['acao'] = $this->_caminhos[$moduloSolicitado]['acoes'][$acaoSolicitada];
			} else {
				$caminho['acao'] = null;
			}
			
			unset($caminho['acoes']);
			
			return $caminho;
		}

		return null;
	}
	
	/**
	 * Checa se o modulo existe (está registrado) nos caminhos possíveis
	 * @param string $nome
	 * @return bool
	 */
	private function _moduloRegistrado ($nome)
	{
		return array_key_exists($nome, $this->_caminhos);
	}
	
	/**
	 * Checa se ação existe (está registrado) nos caminhos possíveis
	 * @param string $nomeAcao
	 * @param string $nomeModulo
	 * @return bool
	 */
	private function _acaoRegistrada ($nomeAcao, $nomeModulo)
	{
		if ($this->_moduloRegistrado($nomeModulo)) {
			return array_key_exists($nomeAcao, $this->_caminhos[$nomeModulo]['acoes']);
		}
		
		return false;
	}
	
	/**
	 * Retorna o modulo padrão registrado nos caminhos possíveis
	 * @return array
	 */
	private function _obterModuloPadrao ()
	{
		foreach ($this->_caminhos as $c) {
			if ($c['padrao']) {
				return $c['nome'];
			}
		}
		
		return null;
	}
	
	/**
	 * Retorna ação padrão de um modulo registrado nos caminhos possíveis
	 * @param string $nomeModulo
	 * @return array
	 */
	private function _obterAcaoPadrao ($nomeModulo)
	{
		foreach ($this->_caminhos[$nomeModulo]['acoes'] as $a) {
			if ($a['padrao']) {
				return $a['nome'];
			}
		}
		
		return null;
	}
}

?>