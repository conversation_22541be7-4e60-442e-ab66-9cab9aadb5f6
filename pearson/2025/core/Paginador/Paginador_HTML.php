<?
include_once('Paginador/Paginador.php');

class Paginador_HTML extends Paginador
{
	protected $_entradaLink = array();

	public function __construct ($config = array())
	{
		parent::__construct($config);
		
		$this->_pag['html'] = array();
		
		$this->fixarLink($_GET);
	}
	
	public function prepararPaginacao ($total, $por_pagina, $pagina_atual, $delta = null)
	{
		parent::prepararPaginacao($total, $por_pagina, $pagina_atual, $delta);
		
		if ($this->precisaDePaginacao())
		{		
			if ( $this->mostrarInicio() ) {
				$this->_pag['html'][] = array('nome' => '&laquo; Primeira', 'url' => $this->_gerarLink(1), 'pagina' => 1);
			}
			
			if ( !$this->primeiraPagina() ) {
				$this->_pag['html'][] = array('nome' => '&lt;', 'url' => $this->_gerarLink($this->_pag['pagina_atual'] - 1), 'pagina' => ($this->_pag['pagina_atual'] - 1));
			}
			
			foreach($this->_pag['paginas'] as $p) {
				$this->_pag['html'][] = array('nome' => $p, 'url' => $this->_gerarLink($p), 'pagina' => $p);
			}
			
			if ( !$this->ultimaPagina() ) {
				$this->_pag['html'][] = array('nome' => '&gt;', 'url' => $this->_gerarLink($this->_pag['pagina_atual'] + 1), 'pagina' => ($this->_pag['pagina_atual'] + 1));
			}
			
			if ( $this->mostrarFim() ) {
				$this->_pag['html'][] = array('nome' => '&raquo; Última', 'url' => $this->_gerarLink($this->_pag['total_paginas']), 'pagina' => $this->_pag['total_paginas']);
			}
		}
		else
		{
			$this->_pag['html'] = array();
		}
	}
	
	public function fixarLink ($link)
	{
		$this->_entradaLink = array_merge($this->_entradaLink, $link);
	}
	
	public function obterLink ()
	{
		return $this->_entradaLink;
	}
	
	private function _gerarLink ($pagina)
	{
		return Gerenciador_URL::gerarLinkCustomizado( array_merge($this->_entradaLink, array(Core::diretiva('LISTAGEM:PARAMETRO:pagina') => $pagina)) );
	}
}

?>