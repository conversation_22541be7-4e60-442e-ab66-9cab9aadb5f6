<?
include_once('Paginador/Paginador.php');

class Paginador_Ordenado extends Paginador
{
	// não pode ser int...
	const PRIMEIRA = 'primeira';
	const ANTERIOR = 'anteriror';
	const PROXIMA = 'proxima';
	const ULTIMA = 'ultima';

	protected $_ordenacao;
	protected $_linksCriados = array();

	public function __construct (Ordenacao $ordenacao, $config = array())
	{
		parent::__construct($config);
		
		$this->_ordenacao = $ordenacao;
		
		$this->_pag['html'] = array();
	}
	
	public function prepararPaginacao ($total, $por_pagina, $pagina_atual, $delta = null)
	{
		parent::prepararPaginacao($total, $por_pagina, $pagina_atual, $delta);
		
		if ($this->precisaDePaginacao())
		{
			$url = ( $this->mostrarInicio() ? $this->_gerarLink(1) : null );
			$this->_pag['html'][] = array('nome' => self::PRIMEIRA, 'url' => $url, 'pagina' => 1);
			
			$url = ( !$this->primeiraPagina() ? $this->_gerarLink($this->_pag['pagina_atual'] - 1) : null );
			$this->_pag['html'][] = array('nome' => self::ANTERIOR, 'url' => $url, 'pagina' => ($this->_pag['pagina_atual'] - 1));
			
			foreach($this->_pag['paginas'] as $p)
			{
				$this->_pag['html'][] = array('nome' => $p, 'url' => $this->_gerarLink($p), 'pagina' => $p);
			}
			
			$url = ( !$this->ultimaPagina() ? $this->_gerarLink($this->_pag['pagina_atual'] + 1) : null );
			$this->_pag['html'][] = array('nome' => self::PROXIMA, 'url' => $url, 'pagina' => ($this->_pag['pagina_atual'] + 1));

			$url = ( $this->mostrarFim() ? $this->_gerarLink($this->_pag['total_paginas']) : null );
			$this->_pag['html'][] = array('nome' => self::ULTIMA, 'url' => $url, 'pagina' => $this->_pag['total_paginas']);
		}
		else
		{
			$this->_pag['html'] = array();
		}
	}
	
	public function &obterOrdenacao ()
	{
		return $this->_ordenacao;
	}
	
	private function _gerarLink ($pagina)
	{
		if ( $this->_pag['pagina_atual'] == $pagina ) {
			return null;
		}
	
		$nome = 'pagOrd_' . $pagina;
	
		if (!in_array($pagina, $this->_linksCriados)) {
			$this->_linksCriados[] = $pagina;			
			$this->_ordenacao->adicionarBotaoAlterador($nome, Core::diretiva('LISTAGEM:PARAMETRO:pagina'), $pagina);			
		}
		
		return 'javascript: ' . $this->_ordenacao->obterBotaoAlterador($nome);
	}
}

?>