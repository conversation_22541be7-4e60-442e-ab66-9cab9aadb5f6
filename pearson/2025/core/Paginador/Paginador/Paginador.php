<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class Paginador
{

	protected $_pag;
	protected $_config;
	
	public function __construct ($config = array())
	{
		$this->_pag = array();
		$this->_config = array('expandir' => true);
		
		$this->_config = array_merge($this->_config, $config);
	}
	
	public function prepararPaginacao ($total, $por_pagina, $pagina_atual, $delta = null)
	{
		if ($delta == null) {
			$delta = Core::diretiva('LISTAGEM:PAGINACAO:delta');
		}
		
		$delta = (int) $delta;
	
		$this->_pag['total'] = (int) $total;
		$this->_pag['por_pagina'] = (int) $por_pagina;		
		if ($this->_pag['por_pagina'] < 1) { $this->_pag['por_pagina'] = 1; }
		$this->_pag['total_paginas'] = ceil($this->_pag['total'] / $this->_pag['por_pagina']);				
		if ($pagina_atual > $this->_pag['total_paginas']) { $pagina_atual = $this->_pag['total_paginas']; }
		if ($pagina_atual < 1) { $pagina_atual = 1; }
		$this->_pag['pagina_atual'] = $pagina_atual;
		$this->_pag['delta'] = $delta > 1 ? $delta - 1 : 0;
		
		$this->_montarPaginacao();
	}
	
	public function obterLimitesSQL ($array = false)
	{
		$tmp = array($this->_pag['pagina_atual'] == 1 ? 0 : ($this->_pag['pagina_atual'] - 1) * $this->_pag['por_pagina'], $this->_pag['por_pagina']);
		if ( !$array ) {
			return ' ' . $tmp[0] . ', ' . $tmp[1] . ' ';
		}
		
		return $tmp;
	}
	
	public function obterLimitesArray ()
	{
		$temp = array( $this->_pag['pagina_atual'] == 1 ? 0 : ($this->_pag['pagina_atual'] - 1) * $this->_pag['por_pagina'],
					  ($this->_pag['pagina_atual'] == 1 ? 0 : ($this->_pag['pagina_atual'] - 1) * $this->_pag['por_pagina']) + $this->_pag['por_pagina']
					 );

		if ($temp[1] > $this->_pag['total'])
		{
			$temp[1] = $this->_pag['total'];
		}
		
		return $temp;
	}
	
	public function obterNumeroPaginas ()
	{
		return $this->_pag['total_paginas'];
	}
	
	public function obterPaginacao ()
	{
		return $this->_pag;
	}
	
	public function primeiraPagina ()
	{
		if ($this->_pag['pagina_atual'] == 1) { return true; } else { return false; }
	}
	
	public function ultimaPagina ()
	{
		if ($this->_pag['pagina_atual'] == $this->_pag['total_paginas']) { return true; } else { return false; }
	}
	
	public function mostrarFim ($realmente_necessario = true)
	{
		if (!$realmente_necessario) {
			if ($this->_pag['pagina_atual'] != $this->_pag['total_paginas'] && $this->_pag['total_paginas'] > 1) { return true; } else { return false; }
		} else {
			if ( !in_array($this->_pag['total_paginas'], $this->_pag['paginas']) ) { return true; } else { return false; }
		}
	}
	
	public function mostrarInicio ($realmente_necessario = true)
	{
		if (!$realmente_necessario) {
			if ($this->_pag['pagina_atual'] != 1 && $this->_pag['total_paginas'] > 1) { return true; } else { return false; }
		} else {
			if ( !in_array(1, $this->_pag['paginas']) ) { return true; } else { return false; }
		}
	}
	
	public function precisaDePaginacao ()
	{
		if ($this->_pag['total_paginas'] > 1) { return true; } else { return false; }
	}
	
	protected function _montarPaginacao ()
	{
		$this->_pag['paginas'] = array();
		
		if ($this->_pag['total_paginas'] > (2 * $this->_pag['delta'] + 1)) {
			if ($this->_config['expandir']) {
				if (($this->_pag['total_paginas'] - $this->_pag['delta']) <= $this->_pag['pagina_atual']) {
					$__tempExpAnterior = $this->_pag['pagina_atual'] - ($this->_pag['total_paginas'] - $this->_pag['delta']);
				} else {
					$__tempExpAnterior = 0;
				}
				for ($i = $this->_pag['pagina_atual'] - $this->_pag['delta'] - $__tempExpAnterior; $__tempExpAnterior; $__tempExpAnterior--, $i++) {
					$this->_pag['paginas'][] = $i;
				}
			}

			$__tempExpPosterior = 0;
            for ($i = $this->_pag['pagina_atual'] - $this->_pag['delta']; ($i <= $this->_pag['pagina_atual'] + $this->_pag['delta']) && ($i <= $this->_pag['total_paginas']); $i++) {
				if ($i < 1) {
					++$__tempExpPosterior;
					continue;
				}
				$this->_pag['paginas'][] = $i;
            }

            if ($this->_config['expandir'] && $__tempExpPosterior) {
                for ($i = $this->_pag['pagina_atual'] + $this->_pag['delta'] + 1; $__tempExpPosterior; $__tempExpPosterior--, $i++) {
					$this->_pag['paginas'][] = $i;
				}
            }
        } else {
            for ($i = 1; $i <= $this->_pag['total_paginas']; $i++) {
				$this->_pag['paginas'][] = $i;
            }
        }
    }
	
}

?>