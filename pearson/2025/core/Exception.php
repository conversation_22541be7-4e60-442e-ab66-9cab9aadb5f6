<?
/**
 * @package Core
 * @subpackage TratamentoDeErros
 */

if (!defined('CORE_INCLUIDO')) { exit(); }

/**
 * Responsável pelo gerênciamento e tratamento de exceções. Exceção mais baixo nível do Core.
 * 
 * @package Core
 * @subpackage TratamentoDeErros
 */
class Core_Exception extends Exception
{
	const E_FATAL = 0; // quando o código não pode continuar
	const E_AVISO = 1; // quando o código pode continuar, mas erros aleatórios podem acontecer
	const E_MENSAGEM = 2; // usado pra debug apenas

	static protected $_debug = false;
	
	private $detalhes = null;
	
	public function __construct ($mensagem, $codigo = Core_Exception::E_MENSAGEM, $detalhes = null)
	{
		$this->detalhes = $detalhes;
		parent::__construct($mensagem, $codigo);
	}

	public function __toString ()
	{
		$retorno = sprintf('<fieldset><legend><strong>%s</strong></legend>', $this->_codErroParaNome());
		$retorno .= $this->message;
		
		if (Core_Exception::estaDebugando() && !is_null($this->detalhes)) {
			$retorno .= '<br /><br /><strong>Detalhes: ' . $this->detalhes . '</strong>';
		}
		
		if (Core_Exception::estaDebugando()) {
			$retorno .= '<br /><br />' . str_replace("\n", '<br />', $this->getTraceAsString());
		}
		$retorno .= '</fieldset>';
		
		switch ($this->code)
		{
			case Core_Exception::E_FATAL:
				echo $retorno;
				exit;
				break;
			case Core_Exception::E_AVISO:
				
				break;
			case Core_Exception::E_MENSAGEM:
				if (!Core_Exception::estaDebugando()) {
					$retorno = '';
				}
				break;
			default:
					
		}
		
		return $retorno;
	}
	
	static public function estaDebugando ()
	{
		return Core_Exception::$_debug;
	}
	
	static public function debugando ($debugando = false)
	{
		self::$_debug = $debugando;
		
		if ( self::$_debug )
			error_reporting(E_ALL);
	}
	
	private function _codErroParaNome ()
	{
		$nome = '';
		switch ($this->code)
		{
			case Core_Exception::E_FATAL:
				$nome = 'Erro Fatal';
				break;
			case Core_Exception::E_AVISO:
				$nome = 'Aviso';
				break;
			case Core_Exception::E_MENSAGEM:
				$nome = 'Mensagem';
				break;
			default:
				$nome = 'Desconhecido';
				break;
		}
		return $nome;
	}
}


function exceptionNaoTratada ($e) {
	echo $e;
}
//set_exception_handler('exceptionNaoTratada');

?>