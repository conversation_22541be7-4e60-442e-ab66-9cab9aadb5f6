<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Aluno', null, true);
Core::incluir('Professor', null, true);

class CarregadorUsuarioEspecifico
{
	static protected $_professor;
	static protected $_aluno;

	static public function &obterProfessor () {
		if ( !isset( self::$_professor ) ) {
			self::$_professor = Professor::obterIDProfessorPeloUsuario(Core::registro('usuario'));
			
			if ( self::$_professor !== false ) {
				self::$_professor = new Professor(self::$_professor);
				
				if ( !self::$_professor->carregar(Core::registro('usuario')) )
					self::$_professor = null;
			} else {
				self::$_professor = null;
			}
		}

		return self::$_professor;
	}
	
	static public function &obterAluno () {
		if ( !isset( self::$_aluno ) ) {
			self::$_aluno = Aluno::obterIDAlunoPeloUsuario(Core::registro('usuario'));
		
			if ( self::$_aluno !== false ) {
				self::$_aluno = new Aluno(self::$_aluno);
				
				if ( !self::$_aluno->carregar(Core::registro('usuario')) )
					self::$_aluno = null;
			} else {
				self::$_aluno = null;
			}
		}

		return self::$_aluno;
	}
	
	static public function ehProfessor () {
		return Professor::obterIDProfessorPeloUsuario(Core::registro('usuario'));
	}
	
	static public function ehAluno () {
		return Aluno::obterIDAlunoPeloUsuario(Core::registro('usuario'));
	}
}

?>