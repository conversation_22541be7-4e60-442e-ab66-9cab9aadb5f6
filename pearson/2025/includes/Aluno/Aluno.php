<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Turma', null, true);
Core::incluir('CursoVestibular', null, true);
Core::incluir('Inscricao', null, true);
Core::incluir('PesquisaParticipante', null, true);

class Aluno
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'usuario' => null,
								'matricula' => null,
								'turma' => null,
								'numero_chamada' => null,
								'portador_necessidade' => null,
								'cor' => 1  );
		
		$this->fixarID($id);
	}	
	
	static public function &obterNovoAluno (UsuarioInstituido &$usuario)
	{
		Core::registro('db')->query( sprintf('INSERT INTO alunos(a_usuario) VALUES (%s)',
			Core::registro('db')->formatarValor($usuario->obterID()) ) );

		$obj = new Aluno(Core::registro('db')->insert_id);
		
		$obj->fixarUsuario($usuario);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf(
				'UPDATE alunos SET 
				a_usuario = %s, a_matricula = %s, a_turma = %s, a_numero_chamada = %s, a_portador_necessidade = %s, a_cor = %s 
				WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['usuario']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['matricula']),
				Core::registro('db')->formatarValor($this->_dados['turma']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['numero_chamada']),
				Core::registro('db')->formatarValor($this->_dados['portador_necessidade'] ? 1 : 0 ),
				Core::registro('db')->formatarValor($this->_dados['cor']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM alunos WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$retorno = Core::registro('db')->errno == 0;
			
			if ($retorno) {
				@$this->_dados['usuario']->remover();
				
				@Inscricao::removerInscricoesPeloAluno($this);
				@PesquisaParticipante::removerParticipacoesPeloAluno($this);
			}
			
			$this->fixarID(null);
			
			return $retorno;
		}
		
		return false;
	}
	
	public function carregar (UsuarioInstituido &$usuario = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM alunos WHERE a_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				if ( $usuario == null ) {
					$usuario = new UsuarioInstituido($row['a_usuario']);
					$usuario->carregar();
				}
				$this->fixarUsuario($usuario);
				
				$turma = new Turma($row['a_turma']);
				$turma->carregar();
				$this->fixarTurma($turma);
				
				$this->fixarMatricula($row['a_matricula']);
				$this->fixarNumeroChamada($row['a_numero_chamada']);
				$this->fixarPortadorNecessidade($row['a_portador_necessidade']);
				$this->fixarCor($row['a_cor']);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function &obterUsuario ()
	{
		return $this->_dados['usuario'];
	}
	
	public function fixarUsuario (UsuarioInstituido &$usuario)
	{
		$this->_dados['usuario'] = $usuario;
	}
	
	public function &obterTurma ()
	{
		return $this->_dados['turma'];
	}
	
	public function fixarTurma (Turma &$turma)
	{
		$this->_dados['turma'] = $turma;
	}
	
	public function obterMatricula ()
	{
		return $this->_dados['matricula'];
	}
	
	public function fixarMatricula ($matricula)
	{
		$this->_dados['matricula'] = $matricula;
	}
	
	public function obterNumeroChamada ()
	{
		return $this->_dados['numero_chamada'];
	}
	
	public function fixarNumeroChamada ($numero_chamada)
	{
		$this->_dados['numero_chamada'] = $numero_chamada;
	}
	
	public function fixarPortadorNecessidade ($portador_necessidade) {
		$this->_dados['portador_necessidade'] = $portador_necessidade;
	}
	
	public function obterPortadorNecessidade () {
		return $this->_dados['portador_necessidade'];
	}

	public function fixarCor ($cor) {
		$this->_dados['cor'] = $cor;
	}
	
	public function obterCor () {
		return $this->_dados['cor'];
	}
	
	public function obterCorNome () {
		$cor = false;
		$rs = Core::registro('db')->query('SELECT ac_cor FROM alunos_cores WHERE ac_id='.$this->_dados['cor'].' LIMIT 1;');
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				$cor = $row['ac_cor'];
			}
			$rs->free();
		}

		return $cor;
	}

	public function podeRemover ()
	{
		return true;
	}
	
	public function validarInstituicao ()
	{
		if ( $this->_dados['id'] != null ) {
			return $this->_dados['usuario']->validarInstituicao();
		}
		
		return false;
	}
	
	static public function obterIDAlunoPeloUsuario (UsuarioInstituido &$usuario)
	{
		if ( $usuario != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT a_id FROM alunos WHERE a_usuario = %s', 
				Core::registro('db')->formatarValor( $usuario->obterID() ) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				return $row['a_id'];
			}
			$rs->free();
		}
		
		return false;
	}
	
	static public function obterArrayAlunosPelaTurma (Turma $turma, $alunos = array())
	{		
		if ( $turma != null && $turma->obterID() != null ) {			
			$rs = Core::registro('db')->query( sprintf('
				SELECT a_id, u_nome FROM alunos 
				INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
				WHERE a_turma = %s 
				ORDER BY u_nome ASC', 
				Core::registro('db')->formatarValor( $turma->obterID() ) ) );
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
					$alunos[$row['a_id']] = $row['u_nome'];
			}
			$rs->free();
		}
		
		return $alunos;
	}
	
	static public function obterArrayAlunosPelaTurmaESecao (Turma $turma, $alunos = array(), $secao = '')
	{		
		if ( $turma != null && $turma->obterID() != null ) {			
			$rs = Core::registro('db')->query( sprintf('
				SELECT a_id, u_nome FROM alunos 
				INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
				INNER JOIN simulados_inscricoes ON si_aluno = a_id
				INNER JOIN simulados ON si_simulado = s_id
				WHERE s_secao_rels=%s AND a_turma = %s 
				ORDER BY u_nome ASC', 
				Core::registro('db')->formatarValor( $secao ),
				Core::registro('db')->formatarValor( $turma->obterID() ) ) );
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
					$alunos[$row['a_id']] = $row['u_nome'];
			}
			$rs->free();
		}
		
		return $alunos;
	}
	
	static public function obterAlunoPelaMatricula ($matricula)
	{
		if ($matricula != null) {			
			$rs = Core::registro('db')->query(sprintf('SELECT a_id FROM alunos WHERE a_matricula = %s', 
				Core::registro('db')->formatarValor($matricula)));
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				return $row['a_id'];
			}
			$rs->free();
		}
		
		return false;
	}

	public static function obterArrayCores(){
		$cores = array();

		$rs = Core::registro('db')->query('SELECT * FROM alunos_cores ORDER BY ac_id ASC');
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				$cores[$row['ac_id']] = $row['ac_cor'];
			}
		}
		$rs->free();

		return $cores;
	}
}

?>