<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Telefone', null, true);
Core::incluir('Estado', 'Endereco/', true);

class Endereco
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'lagradouro' => null,
								'numero' => null,
								'complemento' => null,
								'bairro' => null,
								'pais' => null,
								'cidade' => null,
								'estado' => null,
								'cep' => null,								
								'telefones' => array() );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoEndereco ($dados = array())
	{	
		Core::registro('db')->query( sprintf("INSERT INTO enderecos() VALUES ()") );

		$obj = new Endereco(Core::registro('db')->insert_id);	
		
		return $obj;
	}
	
	public function salvar ()
	{
		if (!is_null($this->_dados['id'])) {
			Core::registro('db')->query( sprintf('UPDATE enderecos SET e_lagradouro = %s, e_numero = %s, e_complemento = %s, e_bairro = %s, e_pais = %s, e_cidade = %s, e_estado = %s, e_cep = %s WHERE e_id = %s', 
				Core::registro('db')->formatarValor($this->obter('lagradouro')),
				Core::registro('db')->formatarValor($this->obter('numero')),
				Core::registro('db')->formatarValor($this->obter('complemento')),
				Core::registro('db')->formatarValor($this->obter('bairro')),
				Core::registro('db')->formatarValor($this->obter('pais')),
				Core::registro('db')->formatarValor($this->obter('cidade')),
				Core::registro('db')->formatarValor($this->obter('estado')),
				Core::registro('db')->formatarValor($this->obter('cep')),
				Core::registro('db')->formatarValor($this->_dados['id'])) );
			
			$retorno = (Core::registro('db')->errno == 0);
			
			foreach ($this->_dados['telefones'] as $t) {
				if (!$t->salvar()) {
					$retorno = false;
				}
			}
			
			return $retorno;
		}
		
		return false;
	}
	
	public function remover ()
	{
		if (!is_null($this->_dados['id'])) {
			Core::registro('db')->query( sprintf('DELETE FROM enderecos WHERE e_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			foreach ($this->_dados['telefones'] as $t) {
				$t->remover();
			}
			
			$this->fixarID(null);
			
			return true;
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if (!is_null($this->_dados['id'])) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM enderecos WHERE e_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixar('lagradouro', $row['e_lagradouro']);
				$this->fixar('numero', $row['e_numero']);
				$this->fixar('complemento', $row['e_complemento']);
				$this->fixar('bairro', $row['e_bairro']);
				$this->fixar('pais', $row['e_pais']);
				$this->fixar('cidade', $row['e_cidade']);
				$this->fixar('estado', $row['e_estado']);
				$this->fixar('cep', $row['e_cep']);
				
				$this->carregarTelefones();
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixar ($chave, $dado)
	{
		if (empty($dado)) { $dado = null; }
	
		switch ($chave)
		{
			case 'lagradouro':
				$this->_dados['lagradouro'] = $dado; break;
			case 'numero':
				$this->_dados['numero'] = $dado; break;
			case 'complemento':
				$this->_dados['complemento'] = $dado; break;
			case 'bairro':
				$this->_dados['bairro'] = $dado; break;
			case 'pais':
				$this->_dados['pais'] = $dado; break;
			case 'cidade':
				$this->_dados['cidade'] = $dado; break;
			case 'estado':
				$this->_dados['estado'] = $dado; break;
			case 'cep':
				$this->_dados['cep'] = $dado; break;
			default:
				return false;
		}
		
		return true;
	}
	
	public function obter ($chave)
	{
		switch ($chave)
		{
			case 'lagradouro':
				return $this->_dados['lagradouro'];
			case 'numero':
				return $this->_dados['numero'];
			case 'complemento':
				return $this->_dados['complemento'];
			case 'bairro':
				return $this->_dados['bairro'];
			case 'pais':
				return $this->_dados['pais'];
			case 'cidade':
				return $this->_dados['cidade'];
			case 'estado':
				return $this->_dados['estado'];
			case 'cep':
				return $this->_dados['cep'];
			default:
				return false;
		}
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function adicionarTelefone (Telefone &$telefone)
	{
		$this->_dados['telefones'][] = $telefone;
	}
	
	public function &obterTelefones ()
	{
		return $this->_dados['telefones'];
	}
	
	public function carregarTelefones ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM telefones WHERE telefones.t_endereco = %s ORDER BY t_tipo',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
				{
					$obj = new Telefone($row['t_id']);
					$obj->fixarTipo($row['t_tipo']);
					$obj->fixarDDD($row['t_ddd']);
					$obj->fixarNumero($row['t_numero']);
					$obj->fixarEndereco($this);
					
					$this->adicionarTelefone($obj);
				}
			}
			$rs->free();
		}
	}
	
}

?>