<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class Habilidade {
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'descricao' => null,
								'descritor' => null  );

		$this->fixarID($id);
	}

	static public function &obterNovaHabilidade ($nome, $descricao, $ordem)
	{
		Core::registro('db')->query( sprintf('INSERT INTO habilidades (h_nome, h_descricao, h_ordem) VALUES (%s, %s, %s)',
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($descricao),
            Core::registro('db')->formatarValor($ordem) ) );

		$obj = new Habilidade( Core::registro('db')->insert_id );

		$obj->fixarNome($nome);

		return $obj;
	}

	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE habilidades SET h_nome = %s, h_descricao = %s, h_descritor = %s, h_ordem = %s WHERE h_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['descricao']),
				Core::registro('db')->formatarValor($this->_dados['descritor']),
				Core::registro('db')->formatarValor($this->_dados['ordem']),
                Core::registro('db')->formatarValor($this->_dados['id'])));

			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}

	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM habilidades WHERE h_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$this->fixarID(null);

			return (Core::registro('db')->errno == 0);
		}

		return false;
	}

	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT h_nome,h_descricao,h_ordem,h_descritor FROM habilidades WHERE h_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$this->fixarNome($row['h_nome']);
				$this->fixarDescricao($row['h_descricao']);

                $this->fixarOrdem($row['h_ordem']);
				$this->fixarDescritor($row['h_descritor']);

				return true;
			}
			$rs->free();
		}

		return false;
	}

	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}

    public function fixarOrdem ($ordem)
    {
        $this->_dados['ordem'] = $ordem;
    }

    public function obterOrdem ()
    {
        return $this->_dados['ordem'];
    }

	public function obterID ()
	{
		return $this->_dados['id'];
	}

	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}

	public function obterNome ()
	{
		return $this->_dados['nome'];
	}

	public function fixarDescricao ($descricao)
	{
		$this->_dados['descricao'] = $descricao;
	}

	public function obterDescricao ()
	{
		return $this->_dados['descricao'];
	}

	public function fixarDescritor ($descritor)
	{
		$this->_dados['descritor'] = $descritor;
	}

	public function obterDescritor ()
	{
		return $this->_dados['descritor'];
	}

	// sem critério ainda
	public function podeRemover ()
	{
		return true;
	}
	static public function obterArrayHabilidadesParaFormulario (Simulado &$simulado, $com_descricao = FALSE) {
		$habilidades = array();

		$rs = Core::registro('db')->query('
			SELECT h_id, h_nome,h_descricao FROM habilidades
			ORDER BY h_ordem ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ($com_descricao)
					$habilidades[$row['h_id']] = array($row['h_nome'], $row['h_descricao']);
				else
					$habilidades[$row['h_id']] = $row['h_nome'];
			}
		}
		$rs->free();

		return $habilidades;
	}

	static public function obterArrayTdsHabilidadesParaFormulario ($com_descricao = FALSE) {
		$habilidades = array();

		$rs = Core::registro('db')->query('SELECT h_id, h_nome, h_descricao FROM habilidades ORDER BY h_ordem ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ($com_descricao)
					$habilidades[$row['h_id']] = array($row['h_nome'], $row['h_descricao']);
				else
					$habilidades[$row['h_id']] = $row['h_nome'];
			}
		}
		$rs->free();

		return $habilidades;
	}

	static public function obterArrayHabilidades () {
		$habilidades = array();

		$rs = Core::registro('db')->query('SELECT * FROM habilidades ORDER BY h_nome ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$habilidades[$row['h_id']] = $row['h_nome'];
			}
		}
		$rs->free();

		return $habilidades;
	}

	static public function fixarDisciplina(){
		return false;
	}
}

?>