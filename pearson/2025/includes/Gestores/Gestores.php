<?
if (!defined('CORE_INCLUIDO')) { exit(); }
class Gestores
{
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array('id' => null,
							  'usuario' => null,
							  'json_instituicoes' => null);

		$this->fixarID($id);
	}

	static public function &obterNovoGestor (UsuarioInstituido &$usuario)
	{
		Core::registro('db')->query( sprintf('INSERT INTO gestores(g_usuario) VALUES (%s)',
			Core::registro('db')->formatarValor($usuario->obterID()) ) );

		$obj = new Gestores(Core::registro('db')->insert_id);

		$obj->fixarUsuario($usuario);

		return $obj;
	}

	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE gestores SET g_usuario = %s, g_instituicoes = %s WHERE g_id = %s',
				Core::registro('db')->formatarValor($this->_dados['usuario']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['json_instituicoes']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}

	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM gestores WHERE g_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$retorno = Core::registro('db')->errno == 0;

			if ( $retorno ) {
				@$this->_dados['usuario']->remover();
			}

			$this->fixarID(null);

			return $retorno;
		}

		return false;
	}

	public function carregar (UsuarioInstituido &$usuario = null)
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM gestores WHERE g_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$this->fixarInstituicoes($row['g_instituicoes']);

				if ( $usuario == null ) {
					$usuario = new UsuarioInstituido($row['g_usuario']);
					$usuario->carregar();
				}
				$this->fixarUsuario($usuario);

				return true;
			}
			//$rs->free();
		}

		return false;
	}

	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}

	public function obterID ()
	{
		return $this->_dados['id'];
	}

	public function &obterUsuario ()
	{
		return $this->_dados['usuario'];
	}

	public function fixarUsuario (UsuarioInstituido &$usuario)
	{
		$this->_dados['usuario'] = $usuario;
	}

	public function fixarInstituicoes ($json)
	{
		$this->_dados['json_instituicoes'] = $json;
	}

	public function obterInstituicoes ()
	{
		return $this->_dados['json_instituicoes'];
	}

	public function obterGestorPorUsuarioID ($uid)
	{
		if (!empty($uid)) {
			$rs = Core::registro('db')->query(sprintf('SELECT * FROM gestores WHERE g_usuario = %s',
				Core::registro('db')->formatarValor($uid)));

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				return $row['g_id'];
			}
			$rs->free();
		}

		return false;
	}
}
?>