<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class EquivalenciaOpcao
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'valor' => null,
								'equivale' => null,
								'questao' => null );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovaEquivalenciaOpcao (Questao &$questao)
	{
		Core::registro('db')->query( sprintf('INSERT INTO questoes_equivalencias_opcoes (qe_questao) VALUES (%s)',
			Core::registro('db')->formatarValor($questao->obterID()) ) );

		$obj = new EquivalenciaOpcao ( Core::registro('db')->insert_id );
		
		$obj->fixarQuestao( $questao );
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE questoes_equivalencias_opcoes SET qe_valor = %s, qe_equivale = %s, qe_questao = %s WHERE qe_id = %s',
				Core::registro('db')->formatarValor($this->_dados['valor']),
				Core::registro('db')->formatarValor($this->_dados['equivale']),
				Core::registro('db')->formatarValor( $this->_dados['questao']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
						
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM questoes_equivalencias_opcoes WHERE qe_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar (Questao &$questao = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM questoes_equivalencias_opcoes WHERE qe_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarValor($row['qe_valor']);
				$this->fixarEquivale($row['qe_equivale']);
				
				if ($questao == null) {
					$questao = new Questao($row['qe_questao']);
					$questao->carregar();
				}
				
				$this->fixarQuestao($questao);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarValor ($valor)
	{
		$this->_dados['valor'] = $valor;
	}
	
	public function obterValor ()
	{
		return $this->_dados['valor'];
	}
	
	public function fixarEquivale ($equivale)
	{
		$this->_dados['equivale'] = $equivale;
	}
	
	public function obterEquivale ()
	{
		return $this->_dados['equivale'];
	}
	
	public function fixarQuestao (Questao &$questao)
	{
		$this->_dados['questao'] = $questao;
	}
	
	public function &obterQuestao ()
	{
		return $this->_dados['questao'];
	}
	
	public function podeRemover ()
	{
		return true;
	}

}

?>