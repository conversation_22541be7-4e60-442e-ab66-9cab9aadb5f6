<?
if (!defined('CORE_INCLUIDO')) exit();

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);

require_once 'ProporcaoDeDesempenho.php';

// por facilidade ? tudo public
class AnalizadorSimuladoProvaFloripa {
	const DESISTENCIA_NAO_COMPARECEU = 0;
	const DESISTENCIA_RENDIMENTO_NULO = 1;

	public $simulado;
	public $simuladoQuestoes;
	public $simuladoQuestoesPorID;
	public $desistencias;

	public $inscritos;
	public $respostas;

	public $nomesTurmas;
	public $nomesSeries;
	public $nomesInstituicoes;
	public $nomesInstituicoesComDREeMunicipio;
	public $nomesDisciplinas;
	public $nomesPequenosDisciplinas;
	public $nomesQuestoes;

	public $instituicoesPorTurma;

	public $rendimentoPorQuestao;
	public $rendimentoPorDisciplina;
	public $rendimentoGlobal;
	public $rendimentoPorTurma;
	public $rendimentoPorTurmaPorQuestao;
	public $rendimentoPorTurmaPorDisciplina;
	public $rendimentoPorTurmaPorProporcao;
	public $rendimentoPorTurmaPorProporcaoPorDisciplina;
	public $rendimentoPorSerie;
	public $rendimentoPorSeriePorQuestao;
	public $rendimentoPorSeriePorDisciplina;
	public $rendimentoPorSeriePorProporcao;
	public $rendimentoPorSeriePorProporcaoPorDisciplina;
	public $rendimentoPorInstituicao;
	public $rendimentoPorInstituicaoPorQuestao;
	public $rendimentoPorInstituicaoPorDisciplina;
	public $rendimentoPorInstituicaoPorProporcao;
	public $rendimentoPorInstituicaoPorProporcaoPorDisciplina;

	public $REL_ESPECIAIS_RemoverDoCalculo;
	public $REL_AUSENTES_RemoverDoCalculo;
	public $REL_RENDNULO_RemoverDoCalculo;
	public $REL_AUSENTES_FASE_RemoverDoCalculo;

	// Cache para otimização de performance
	private $_cacheQuantidadePorDisciplina = array();
	private $_calculosRealizados = array();
	private $_cacheConsultas = array();

	public function __construct () {
		$this->REL_ESPECIAIS_RemoverDoCalculo = strtolower(Core::diretiva('REL:ESPECIAIS:RemoverDoCalculo'));
		$this->REL_AUSENTES_RemoverDoCalculo = strtolower(Core::diretiva('REL:AUSENTES:RemoverDoCalculo'));
		$this->REL_RENDNULO_RemoverDoCalculo = strtolower(Core::diretiva('REL:RENDNULO:RemoverDoCalculo'));
		$this->REL_AUSENTES_FASE_RemoverDoCalculo = strtolower(Core::diretiva('REL:AUSENTES_FASE:RemoverDoCalculo'));
		$this->_resetarParametros();
	}

	/**
	 * Verifica se um cálculo específico já foi realizado
	 */
	private function _calculoJaRealizado($nomeCalculo) {
		return isset($this->_calculosRealizados[$nomeCalculo]) && $this->_calculosRealizados[$nomeCalculo] === true;
	}

	/**
	 * Marca um cálculo como realizado
	 */
	private function _marcarCalculoRealizado($nomeCalculo) {
		$this->_calculosRealizados[$nomeCalculo] = true;
	}

	/**
	 * Obtém quantidade de questões por disciplina com cache
	 */
	private function _obterQuantidadePorDisciplina($simuladoID, $disciplinaID) {
		$chave = $simuladoID . '_' . $disciplinaID;

		if (!isset($this->_cacheQuantidadePorDisciplina[$chave])) {
			$questao = new Questao(null);
			$this->_cacheQuantidadePorDisciplina[$chave] = $questao->carregarQuantidadePorSimuluadoEDisciplina($simuladoID, $disciplinaID);
		}

		return $this->_cacheQuantidadePorDisciplina[$chave];
	}

	/**
	 * Cache para consultas SQL repetitivas
	 */
	private function _executarConsultaComCache($sql, $chaveCache) {
		if (!isset($this->_cacheConsultas[$chaveCache])) {
			$rs = Core::registro('db')->query($sql);
			$resultado = array();

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$resultado[] = $row;
				}
			}
			$rs->free();

			$this->_cacheConsultas[$chaveCache] = $resultado;
		}

		return $this->_cacheConsultas[$chaveCache];
	}

	/**
	 * Carrega todas as quantidades por disciplina de uma vez
	 */
	private function _preCarregarQuantidadesPorDisciplina($simuladoID) {
		if ($this->_calculoJaRealizado('quantidadesPorDisciplina_' . $simuladoID)) {
			return;
		}

		$sql = sprintf(
			'SELECT q_disciplina, COUNT(*) as quantidade
			 FROM questoes
			 WHERE q_simulado = %s
			 GROUP BY q_disciplina',
			Core::registro('db')->formatarValor($simuladoID)
		);

		$resultado = $this->_executarConsultaComCache($sql, 'quantidades_disciplina_' . $simuladoID);

		foreach ($resultado as $row) {
			$chave = $simuladoID . '_' . $row['q_disciplina'];
			$this->_cacheQuantidadePorDisciplina[$chave] = $row['quantidade'];
		}

		$this->_marcarCalculoRealizado('quantidadesPorDisciplina_' . $simuladoID);
	}

	/**
	 * Lazy loading para rendimento por disciplina
	 */
	public function obterRendimentoPorDisciplina() {
		if (!$this->_calculoJaRealizado('rendimentoPorDisciplina')) {
			$this->calcularRendimentoPorDisciplinaDosInscritos();
		}
		return $this->rendimentoPorDisciplina;
	}

	/**
	 * Lazy loading para rendimento por turma
	 */
	public function obterRendimentoPorTurma() {
		if (!$this->_calculoJaRealizado('rendimentoPorTurma')) {
			$this->calcularRendimentoPorTurma();
		}
		return $this->rendimentoPorTurma;
	}

	/**
	 * Lazy loading para rendimento por série
	 */
	public function obterRendimentoPorSerie() {
		if (!$this->_calculoJaRealizado('rendimentoPorSerie')) {
			$this->calcularRendimentoPorSerie();
		}
		return $this->rendimentoPorSerie;
	}

	/**
	 * Lazy loading para rendimento por instituição
	 */
	public function obterRendimentoPorInstituicao() {
		if (!$this->_calculoJaRealizado('rendimentoPorInstituicao')) {
			$this->calcularRendimentoPorInstituicao();
		}
		return $this->rendimentoPorInstituicao;
	}

	/**
	 * Retorna informações sobre quais cálculos já foram realizados
	 */
	public function obterStatusCalculos() {
		return array(
			'rendimentoPorQuestao' => $this->_calculoJaRealizado('rendimentoPorQuestao'),
			'rendimentoGlobal' => $this->_calculoJaRealizado('rendimentoGlobal'),
			'rendimentoPorDisciplina' => $this->_calculoJaRealizado('rendimentoPorDisciplina'),
			'rendimentoPorTurma' => $this->_calculoJaRealizado('rendimentoPorTurma'),
			'rendimentoPorSerie' => $this->_calculoJaRealizado('rendimentoPorSerie'),
			'rendimentoPorInstituicao' => $this->_calculoJaRealizado('rendimentoPorInstituicao'),
			'quantidadesPorDisciplina' => $this->_calculoJaRealizado('quantidadesPorDisciplina_' . $this->simulado->obterID())
		);
	}

	/**
	 * Força a recalculação de um tipo específico de cálculo
	 */
	public function forcarRecalculo($tipoCalculo) {
		if (isset($this->_calculosRealizados[$tipoCalculo])) {
			unset($this->_calculosRealizados[$tipoCalculo]);
		}

		// Limpa caches relacionados se necessário
		if ($tipoCalculo === 'rendimentoPorDisciplina') {
			$this->_cacheQuantidadePorDisciplina = array();
		}
	}

	/**
	 * Retorna estatísticas de cache para debug
	 */
	public function obterEstatisticasCache() {
		return array(
			'cache_quantidade_disciplinas' => count($this->_cacheQuantidadePorDisciplina),
			'cache_consultas' => count($this->_cacheConsultas),
			'calculos_realizados' => count($this->_calculosRealizados),
			'memoria_usada' => memory_get_usage(true),
			'memoria_pico' => memory_get_peak_usage(true)
		);
	}

	/**
	 * Executa todos os cálculos básicos de forma otimizada
	 * Este método deve ser usado quando se sabe que todos os cálculos serão necessários
	 */
	public function executarCalculosCompletos() {
		$inicio = microtime(true);

		// Pré-carrega dados que serão usados em múltiplos cálculos
		$this->_preCarregarQuantidadesPorDisciplina($this->simulado->obterID());

		// Executa cálculos na ordem de dependência
		$this->calcularRendimentoPorQuestaoDosInscritos();
		$this->calcularRendimentoGlobalDosInscritos();
		$this->calcularRendimentoPorDisciplinaDosInscritos();
		$this->calcularRendimentoPorTurma();
		$this->calcularRendimentoPorSerie();
		$this->calcularRendimentoPorInstituicao();

		$tempoExecucao = microtime(true) - $inicio;

		return array(
			'tempo_execucao' => $tempoExecucao,
			'calculos_realizados' => $this->obterStatusCalculos(),
			'estatisticas_cache' => $this->obterEstatisticasCache()
		);
	}

	public function carregarInscritos($todasInstituicoes = false, $regularOUintegral = false, $instituioesEspecificas = false, $tipoRede = false) {
		if ($this->simulado->obterID() == null)
			return false;
	
		$regularOUintegralSQL = '';
		if ($regularOUintegral !== false) {
			if ($regularOUintegral == 'regular') {
				$regularOUintegralSQL = 'si.si_integral = 0 AND ';
			} elseif ($regularOUintegral == 'integral') {
				$regularOUintegralSQL = 'si.si_integral = 1 AND ';
			}
		}
	
		$this->inscritos = array();
	
		$instituicao = Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil();
		$instituicoesSQL = '';
	
		if (!$todasInstituicoes) {
			$instituicoesSQL = ' t.t_instituicao = ' . Core::registro('db')->formatarValor($instituicao->obterID()) . ' AND ';
			if (is_array($instituioesEspecificas) && count($instituioesEspecificas) > 0) {
				$instituicoesSQL = ' t.t_instituicao IN (' . implode(',', array_keys($instituioesEspecificas)) . ') AND ';
			}
		}
	
		// Modifica o filtro para tipo de instituição (E = Estadual, M = Municipal)
		$tipoRedeSQL = '';
		if ($tipoRede !== false && $tipoRede !== '' && $tipoRede !== null) {
			// Só aplica o filtro se for E ou M
			$tipoRedeSQL = ' i.i_tipo_rede = ' . Core::registro('db')->formatarValor($tipoRede) . ' AND ';
		}
		// Se $tipoRede for vazio (''), significa "Ambos", então não aplicamos filtro
	
		$serie = ProvaFloripa::$seriesPorSimulado[1];
		// erro de serie
		//$serie = ProvaFloripa::$seriesPorSimulado[ $this->simulado->obterID() ];
	
		//$nomesInstituicoes = Instituicao::obterArrayInstituicoesParaFormulario();
		$nomesInstituicoesComDREeMunicipio = Instituicao::obterArrayInstituicoesParaFormularioComDREeMunicipio();
		foreach ($nomesInstituicoesComDREeMunicipio as $iID => $dados) {
			$nomesInstituicoes[$iID] = $dados['nome'];
		}
	
		$rs = Core::registro('db')->query(sprintf(
			'SELECT u.u_id,u.u_nome,u.u_endereco,u.u_instituicao,u.u_foto,u.u_email,
				si.si_aluno, a.a_matricula, a.a_portador_necessidade, a.a_numero_chamada,
				a.a_turma, t.t_nome, si.si_tipo,si.si_id, si.si_atestado, si.si_integral, 
				si.si_nao_alfabetico, a.a_cor 
			 FROM simulados_inscricoes si
					INNER JOIN alunos a ON a.a_id = si.si_aluno
					INNER JOIN usuarios u ON u.u_id = a.a_usuario
					LEFT JOIN turmas t ON t.t_id = a.a_turma
					LEFT JOIN simulados s ON s.s_id = si.si_simulado
					LEFT JOIN instituicoes i ON i.i_id = u.u_instituicao
			 WHERE %s %s %s si.si_simulado = %s',
			$instituicoesSQL,
			$regularOUintegralSQL,
			$tipoRedeSQL,
			Core::registro('db')->formatarValor($this->simulado->obterID())
		));

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				// USUÁRIO
				$usuario = new UsuarioInstituido($row['u_id']);
				$usuario->fixarNome($row['u_nome']);
				$usuario->fixarEndereco(new Endereco($row['u_endereco']));
				$usuario->fixarInstituicao(new Instituicao($row['u_instituicao']));
				$usuario->fixarFoto($row['u_foto']);
				$usuario->fixarEmail(new Email($row['u_email']));
	
				// ALUNO
				$aluno = new Aluno($row['si_aluno']);
				$aluno->fixarUsuario($usuario);
				$aluno->fixarMatricula($row['a_matricula']);
				$aluno->fixarPortadorNecessidade($row['a_portador_necessidade']);
				$aluno->fixarNumeroChamada($row['a_numero_chamada']);
				$aluno->fixarCor($row['a_cor']);
				$aluno->fixarTurma(new Turma($row['a_turma']));
				$aluno->obterTurma()->fixarNome($row['t_nome']);
				$aluno->obterTurma()->fixarSerie($serie);
				$aluno->obterTurma()->fixarInstituicao($instituicao);
	
				$inscricao = new Inscricao($row['si_id']);
	
				// INSCRICAO
				$inscricao->fixarTipo($row['si_tipo']);
				$inscricao->fixarAluno($aluno);
				$inscricao->fixarSimulado($this->simulado);
				$inscricao->fixarAtestado($row['si_atestado']);
				$inscricao->fixarEdIntegral($row['si_integral']);
				$inscricao->fixarNaoAlfabetico($row['si_nao_alfabetico']);
	
				$this->inscritos[$row['si_id']] = $inscricao;
				$this->respostas[$row['si_id']] = array();
				$this->rendimentoPorQuestao[$row['si_id']] = array();
				$this->rendimentoGlobal[$row['si_id']] = array();
	
				if (!isset($this->nomesTurmas[$row['a_turma']]))
					$this->nomesTurmas[$row['a_turma']] = $row['t_nome'];
	
				if (!isset($this->nomesSeries[$serie->obterID()]))
					$this->nomesSeries[$serie->obterID()] = $serie->obterNome();
	
				if (!isset($this->nomesInstituicoesComDREeMunicipio[$row['u_instituicao']]))
					$this->nomesInstituicoesComDREeMunicipio[$row['u_instituicao']] = @$nomesInstituicoesComDREeMunicipio[$row['u_instituicao']];
	
				if (!isset($this->nomesInstituicoes[$row['u_instituicao']]))
					$this->nomesInstituicoes[$row['u_instituicao']] = @$nomesInstituicoes[$row['u_instituicao']];
	
				if (!isset($this->instituicoesPorTurma[$row['a_turma']]))
					$this->instituicoesPorTurma[$row['a_turma']] = $row['u_instituicao'];
			}
		}
		$rs->free();
	
		asort($this->nomesTurmas, SORT_LOCALE_STRING);
		asort($this->nomesSeries, SORT_LOCALE_STRING);
	}

    public function carregarAusentesTurma($turmaId, $simuladoID)
    {
        $rs = Core::registro('db')->query( sprintf(
                            'SELECT COUNT( * )  as total
                FROM simulados_inscricoes si
                INNER JOIN alunos a ON si.si_aluno = a.a_id
                WHERE a.a_turma = %s
                AND si.si_simulado = %s
                AND NOT 
                EXISTS (               
                SELECT 1 
                FROM respostas r
                WHERE r.r_inscricao = si.si_id
                )',
            Core::registro('db')->formatarValor( $turmaId ),
            Core::registro('db')->formatarValor( $simuladoID )) );

        if ($rs->num_rows) {
            while ($row = $rs->fetch_assoc()) {
                    return $row['total'];

            }
        }
        $rs->free();
    }

    public function carregarAusentesInstituicao($instituicaoId, $simuladoID)
    {
        $rs = Core::registro('db')->query( sprintf(
            'SELECT COUNT( * )  as total
                FROM simulados_inscricoes si
                INNER JOIN alunos a ON si.si_aluno = a.a_id
                INNER JOIN turmas t ON a.a_turma = t.t_id
                INNER JOIN instituicoes i ON i.i_id = t.t_instituicao
                WHERE i.i_id = %s
                AND si.si_simulado = %s
                AND NOT 
                EXISTS (               
                SELECT 1 
                FROM respostas r
                WHERE r.r_inscricao = si.si_id)',
            Core::registro('db')->formatarValor( $instituicaoId ),
            Core::registro('db')->formatarValor( $simuladoID )) );

        if ($rs->num_rows) {
            while ($row = $rs->fetch_assoc()) {
                return $row['total'];

            }
        }
        $rs->free();
    }

	public function carregarRespostasDosInscritos ($regularOUintegral = false) {
		if ( $this->simulado->obterID() == null )
			return false;

		$regularOUintegralSQL = '';
		if($regularOUintegral !== false){
			if($regularOUintegral == 'regular'){
				$regularOUintegralSQL = 's.si_integral = 0 AND ';
			}
			elseif($regularOUintegral == 'integral'){
				$regularOUintegralSQL = 's.si_integral = 1 AND ';
			}
		}

		$rs = Core::registro('db')->query( sprintf(
			'SELECT r.r_id, r.r_valor,r.r_questao,s.si_id,s.si_integral FROM respostas r
			  INNER JOIN simulados_inscricoes s ON s.si_id = r.r_inscricao
			  WHERE %s s.si_simulado = %s AND r.r_valor IS NOT NULL',
			$regularOUintegralSQL,  
			Core::registro('db')->formatarValor( $this->simulado->obterID() ) ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ( !isset($this->inscritos[ $row['si_id'] ]) )
					continue;

				$this->respostas[ $row['si_id'] ][$row['r_questao']] = array(
					'id' => $row['r_id'],
					'valor' => $row['r_valor'],
					'questao' => $row['r_questao'],
					'inscricao' => $row['si_id']
				);
			}

			// adiciona as respostar aos inscritos (pra uso externo)
			foreach ($this->respostas as $iID => &$respostas)
				$this->inscritos[ $iID ]->fixarRespostasArray( $respostas );
		}
		$rs->free();
	}

	 public function eliminarInscritosQueNaoCompareceram ($diretiva_ctrl = true) {
	 	if($diretiva_ctrl){
		 	if($this->REL_AUSENTES_RemoverDoCalculo != 'sim'){
		 		return false;
		 	}
	 	}

        if ( $this->simulado->obterID() == null )
            return false;

        $rs = Core::registro('db')->query( sprintf(
            'SELECT si.si_id as inscricao
                FROM simulados_inscricoes si
                INNER JOIN alunos a ON si.si_aluno = a.a_id
                INNER JOIN turmas t ON a.a_turma = t.t_id
                INNER JOIN instituicoes i ON i.i_id = t.t_instituicao
                WHERE si.si_simulado = %s
                AND NOT 
                EXISTS (               
	                SELECT 1 
	                FROM respostas r
                	WHERE r.r_inscricao = si.si_id
                )',
            Core::registro('db')->formatarValor( $this->simulado->obterID() )) );

        if ($rs->num_rows) {
            while ($row = $rs->fetch_assoc()) {
                $this->_removerInscricaoPeloID($row['inscricao']);
                $this->desistencias[self::DESISTENCIA_NAO_COMPARECEU][] = $row['inscricao'];

            }
        }
        $rs->free();
    }

	public function eliminarInscritosComRendimentoNulo ($diretiva_ctrl = true) {
		if($diretiva_ctrl){
			if($this->REL_RENDNULO_RemoverDoCalculo != 'sim'){
				return false;
			}
		}

		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			if ( $desempenho['rendimento'] == 0 ) {
				$this->desistencias[self::DESISTENCIA_RENDIMENTO_NULO][] = $iID;
				$this->_removerInscricaoPeloID($iID);
			}
		}
	}

	public function calcularRendimentoPorMunicipio() {
		if ($this->simulado->obterID() == null)
			return false;

		foreach ($this->rendimentoGlobal as $iID => $desempenho) {
			// Não entra na média se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			// Não alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1) {
				continue;
			}

			$instID = @$this->inscritos[$iID]->obterAluno()->obterUsuario()->obterInstituicao()->obterID();
			
			// Obter o município desta instituição
			$munID = @$this->instituicoesPorMunicipio[$instID];
			
			if (!$munID) {
				continue; // Pular se não encontrar município
			}

			if (!isset($this->rendimentoPorMunicipio[$munID]))
				$this->rendimentoPorMunicipio[$munID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => $desempenho['pontos_questao'],
					'total_inscritos' => 0
				);

			$this->rendimentoPorMunicipio[$munID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoPorMunicipio[$munID]['rendimento'] += $desempenho['rendimento'];
			$this->rendimentoPorMunicipio[$munID]['total_inscritos']++;
		}

		foreach ($this->rendimentoPorMunicipio as $munID => $desempenho) {
			if ($this->rendimentoPorMunicipio[$munID]['total_inscritos'] > 0) {
				$this->rendimentoPorMunicipio[$munID]['total_pontos'] /= $this->rendimentoPorMunicipio[$munID]['total_inscritos'];
				$this->rendimentoPorMunicipio[$munID]['rendimento'] /= $this->rendimentoPorMunicipio[$munID]['total_inscritos'];
			}
		}
	}

	public function calcularRendimentoPorMunicipioPorQuestao() {
		if ($this->simulado->obterID() == null)
			return false;

		foreach ($this->inscritos as $iID => &$inscricao) {
			// Não entra na média se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			// Não alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1) {
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();
			
			// Obter o município desta instituição
			$munID = @$this->instituicoesPorMunicipio[$instID];
			
			if (!$munID) {
				continue; // Pular se não encontrar município
			}

			if (!isset($this->rendimentoPorMunicipioPorQuestao[$munID]))
				$this->rendimentoPorMunicipioPorQuestao[$munID] = array();

			foreach ($this->rendimentoPorQuestao[$iID] as $qID => $desempenho) {
				if (!isset($this->rendimentoPorMunicipioPorQuestao[$munID][$qID]))
					$this->rendimentoPorMunicipioPorQuestao[$munID][$qID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['total_inscritos']++;
			}
		}

		foreach ($this->rendimentoPorMunicipioPorQuestao as $munID => $desempenhoPorMunicipio) {
			foreach ($desempenhoPorMunicipio as $qID => $desempenho) {
				if ($this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['total_inscritos'] > 0) {
					$this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['total_pontos'] /= $this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['total_inscritos'];
					$this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['rendimento'] /= $this->rendimentoPorMunicipioPorQuestao[$munID][$qID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorMunicipioPorDisciplina() {
		if ($this->simulado->obterID() == null)
			return false;

		foreach ($this->inscritos as $iID => &$inscricao) {
			// Não entra na média se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			// Não alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1) {
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();
			
			// Obter o município desta instituição
			$munID = @$this->instituicoesPorMunicipio[$instID];
			
			if (!$munID) {
				continue; // Pular se não encontrar município
			}

			if (!isset($this->rendimentoPorMunicipioPorDisciplina[$munID]))
				$this->rendimentoPorMunicipioPorDisciplina[$munID] = array();

			foreach ($this->rendimentoPorQuestao[$iID] as $qID => $desempenho) {
				$dID = @$this->simuladoQuestoesPorID[$qID]->obterDisciplina()->obterID();

				if (!isset($this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]))
					$this->rendimentoPorMunicipioPorDisciplina[$munID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['total_inscritos']++;
			}
		}

		foreach ($this->rendimentoPorMunicipioPorDisciplina as $munID => $desempenhoPorMunicipio) {
			foreach ($desempenhoPorMunicipio as $dID => $desempenho) {
				if ($this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['total_inscritos'] > 0) {
					$this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['total_pontos'] /= $this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['total_inscritos'];
					$this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['rendimento'] /= $this->rendimentoPorMunicipioPorDisciplina[$munID][$dID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorMunicipioPorProporcao() {
		if ($this->simulado->obterID() == null)
			return false;

		foreach ($this->inscritos as $iID => &$inscricao) {
			// Não entra na média se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			// Não alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1) {
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();
			
			// Obter o município desta instituição
			$munID = @$this->instituicoesPorMunicipio[$instID];
			
			if (!$munID) {
				continue; // Pular se não encontrar município
			}

			if (!isset($this->rendimentoPorMunicipioPorProporcao[$munID]))
				$this->rendimentoPorMunicipioPorProporcao[$munID] = new ProporcaoDeDesempenho();

			$this->rendimentoPorMunicipioPorProporcao[$munID]->adicionarRendimento($this->rendimentoGlobal[$iID]['rendimento']);
		}

		foreach ($this->rendimentoPorMunicipioPorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorQuestaoDosInscritos ($diretiva_ctrl = true) {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoPorQuestao')) {
			return true;
		}

		$removerPorFase = false;
		if($diretiva_ctrl){
		 	if($this->REL_AUSENTES_FASE_RemoverDoCalculo == 'sim'){
		 		$removerPorFase = true;
		 	}
	 	}

		// Pre-processa questões para otimizar loops
		$questoesPorTipo = array();
		foreach ( $this->simuladoQuestoes as $fase => $tipos ) {
			foreach ( $tipos as $tipo => $questoesNumeradas ) {
				if (!isset($questoesPorTipo[$tipo])) {
					$questoesPorTipo[$tipo] = array();
				}
				foreach ( $questoesNumeradas as $numero => $questoesArray ) {
					foreach ( $questoesArray as &$q ) {
						if ( !$q->questaoDeOpcao() ) {
							$questoesPorTipo[$tipo][] = array(
								'questao' => $q,
								'fase' => $fase,
								'numero' => $numero
							);
						}
					}
				}
			}
		}

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$inscricao->verificarAusentePorFases();
			$oapf = $inscricao->obterAusentePorFases();
			$tipoInscricao = $inscricao->obterTipo();
			$linguaID = $inscricao->obterLingua() ? $inscricao->obterLingua()->obterID() : null;

			// Usa questões pré-processadas
			if (isset($questoesPorTipo[$tipoInscricao])) {
				foreach ($questoesPorTipo[$tipoInscricao] as $questaoData) {
					$q = $questaoData['questao'];
					$fase = $questaoData['fase'];
					$numero = $questaoData['numero'];

					if(!array_key_exists($fase, $oapf) && $removerPorFase){
						continue;
					}

					if ( $q->obterDisciplina()->obterLingua() && $linguaID != $q->obterDisciplina()->obterID() )
						continue;

					$desempenho = array( 'rendimento' => 100, 'pontos' => $q->obterPontosQuestao() );

					if ( !$q->foiAnulada() ) {
						$valor = null;
						if ( isset($this->respostas[$iID][$q->obterID()]) )
							$valor = $this->respostas[$iID][$q->obterID()]['valor'];

						$desempenho = $q->obterDesempenho( $tipoInscricao, $numero, $valor );
					}

					$desempenho['pontos_questao'] = $q->obterPontosQuestao();

					$this->rendimentoPorQuestao[$iID][$q->obterID()] = $desempenho;
				}
			}
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoPorQuestao');
	}

	public function calcularRendimentoGlobalDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoGlobal')) {
			return true;
		}

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$this->rendimentoGlobal[$iID] = array('total_pontos' => 0, 'rendimento' => 0, 'pontos_questao' => 0);
			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				$this->rendimentoGlobal[$iID]['pontos_questao'] += $desempenho['pontos_questao'];
				$this->rendimentoGlobal[$iID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoGlobal[$iID]['rendimento'] += $desempenho['rendimento'] * $desempenho['pontos_questao'];
			}

			if ( $this->rendimentoGlobal[$iID]['pontos_questao'] > 0 )
				$this->rendimentoGlobal[$iID]['rendimento'] /= $this->rendimentoGlobal[$iID]['pontos_questao'];
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoGlobal');
	}

	public function calcularRendimentoPorDisciplinaDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoPorDisciplina')) {
			return true;
		}

		// Pré-carrega todas as quantidades por disciplina de uma vez
		$this->_preCarregarQuantidadesPorDisciplina($this->simulado->obterID());

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			if (!isset($this->rendimentoPorDisciplina[$iID]))
				$this->rendimentoPorDisciplina[$iID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				$dID = @$this->simuladoQuestoesPorID[$qID]->obterDisciplina()->obterID();

				if (!isset($this->rendimentoPorDisciplina[$iID][$dID]))
					$this->rendimentoPorDisciplina[$iID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorDisciplina[$iID][$dID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorDisciplina[$iID][$dID]['rendimento'] += $desempenho['rendimento'] ;
				$this->rendimentoPorDisciplina[$iID][$dID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorDisciplina as $iID => $desempenhoPorInscrito) {
			foreach ( $desempenhoPorInscrito as $dID => $desempenho ) {
				if ( $this->rendimentoPorDisciplina[$iID][$dID]['total_inscritos'] > 0 ) {

					$this->rendimentoPorDisciplina[$iID][$dID]['total_pontos'] /= $this->rendimentoPorDisciplina[$iID][$dID]['total_inscritos'];

					// Usa cache para evitar múltiplas consultas
					$quantidade = $this->_obterQuantidadePorDisciplina($this->simulado->obterID(), $dID);

					$this->rendimentoPorDisciplina[$iID][$dID]['rendimento'] /= $quantidade;

				}
			}
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoPorDisciplina');
	}

	public function calcularRendimentoPorTurma () {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoPorTurma')) {
			return true;
		}

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurma[$tID]))
				$this->rendimentoPorTurma[$tID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => $desempenho['pontos_questao'],
					'total_inscritos' => 0
				);

			$this->rendimentoPorTurma[$tID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoPorTurma[$tID]['rendimento'] += $desempenho['rendimento'];
			$this->rendimentoPorTurma[$tID]['total_inscritos']++;
		}

		foreach ( $this->rendimentoPorTurma as $tID => $desempenho ) {
			if ( $this->rendimentoPorTurma[$tID]['total_inscritos'] > 0 ) {
				$this->rendimentoPorTurma[$tID]['total_pontos'] /= $this->rendimentoPorTurma[$tID]['total_inscritos'];
				$this->rendimentoPorTurma[$tID]['rendimento'] /= $this->rendimentoPorTurma[$tID]['total_inscritos'];
			}
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoPorTurma');
	}

	public function calcularRendimentoPorSerie () {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoPorSerie')) {
			return true;
		}

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSerie[$sID]))
				$this->rendimentoPorSerie[$sID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => $desempenho['pontos_questao'],
					'total_inscritos' => 0
				);

			$this->rendimentoPorSerie[$sID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoPorSerie[$sID]['rendimento'] += $desempenho['rendimento'];
			$this->rendimentoPorSerie[$sID]['total_inscritos']++;
		}

		foreach ( $this->rendimentoPorSerie as $sID => $desempenho ) {
			if ( $this->rendimentoPorSerie[$sID]['total_inscritos'] > 0 ) {
				$this->rendimentoPorSerie[$sID]['total_pontos'] /= $this->rendimentoPorSerie[$sID]['total_inscritos'];
				$this->rendimentoPorSerie[$sID]['rendimento'] /= $this->rendimentoPorSerie[$sID]['total_inscritos'];
			}
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoPorSerie');
	}

	public function calcularRendimentoPorInstituicao () {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoPorInstituicao')) {
			return true;
		}

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$this->inscritos[$iID]->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicao[$instID]))
				$this->rendimentoPorInstituicao[$instID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => $desempenho['pontos_questao'],
					'total_inscritos' => 0
				);

			$this->rendimentoPorInstituicao[$instID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoPorInstituicao[$instID]['rendimento'] += $desempenho['rendimento'];
			$this->rendimentoPorInstituicao[$instID]['total_inscritos']++;
		}

		foreach ( $this->rendimentoPorInstituicao as $instID => $desempenho ) {
			if ( $this->rendimentoPorInstituicao[$instID]['total_inscritos'] > 0 ) {
				$this->rendimentoPorInstituicao[$instID]['total_pontos'] /= $this->rendimentoPorInstituicao[$instID]['total_inscritos'];
				$this->rendimentoPorInstituicao[$instID]['rendimento'] /= $this->rendimentoPorInstituicao[$instID]['total_inscritos'];
			}
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoPorInstituicao');
	}

	public function calcularRendimentoPorTurmaPorQuestao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorQuestao[$tID]))
				$this->rendimentoPorTurmaPorQuestao[$tID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				if (!isset($this->rendimentoPorTurmaPorQuestao[$tID][$qID]))
					$this->rendimentoPorTurmaPorQuestao[$tID][$qID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorTurmaPorQuestao as $tID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $qID => $desempenho ) {
				if ( $this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_pontos'] /= $this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos'];
					$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['rendimento'] /= $this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorSeriePorQuestao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorQuestao[$sID]))
				$this->rendimentoPorSeriePorQuestao[$sID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				if (!isset($this->rendimentoPorSeriePorQuestao[$sID][$qID]))
					$this->rendimentoPorSeriePorQuestao[$sID][$qID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorSeriePorQuestao[$sID][$qID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorSeriePorQuestao as $sID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $qID => $desempenho ) {
				if ( $this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_pontos'] /= $this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos'];
					$this->rendimentoPorSeriePorQuestao[$sID][$qID]['rendimento'] /= $this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorInstituicaoPorQuestao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicaoPorQuestao[$instID]))
				$this->rendimentoPorInstituicaoPorQuestao[$instID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				if (!isset($this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]))
					$this->rendimentoPorInstituicaoPorQuestao[$instID][$qID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorInstituicaoPorQuestao as $instID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $qID => $desempenho ) {
				if ( $this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['total_pontos'] /= $this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['total_inscritos'];
					$this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['rendimento'] /= $this->rendimentoPorInstituicaoPorQuestao[$instID][$qID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorTurmaPorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorDisciplina[$tID]))
				$this->rendimentoPorTurmaPorDisciplina[$tID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				$dID = @$this->simuladoQuestoesPorID[$qID]->obterDisciplina()->obterID();

				if (!isset($this->rendimentoPorTurmaPorDisciplina[$tID][$dID]))
					$this->rendimentoPorTurmaPorDisciplina[$tID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorTurmaPorDisciplina as $tID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $dID => $desempenho ) {
				if ( $this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_pontos'] /= $this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_inscritos'];
					$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['rendimento'] /= $this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorSeriePorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorDisciplina[$sID]))
				$this->rendimentoPorSeriePorDisciplina[$sID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				$dID = @$this->simuladoQuestoesPorID[$qID]->obterDisciplina()->obterID();

				if (!isset($this->rendimentoPorSeriePorDisciplina[$sID][$dID]))
					$this->rendimentoPorSeriePorDisciplina[$sID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorSeriePorDisciplina as $sID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $dID => $desempenho ) {
				if ( $this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_pontos'] /= $this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_inscritos'];
					$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['rendimento'] /= $this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorInstituicaoPorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicaoPorDisciplina[$instID]))
				$this->rendimentoPorInstituicaoPorDisciplina[$instID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				$dID = @$this->simuladoQuestoesPorID[$qID]->obterDisciplina()->obterID();

				if (!isset($this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]))
					$this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['rendimento'] += $desempenho['rendimento'] ;
				$this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorInstituicaoPorDisciplina as $instID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $dID => $desempenho ) {
				if ( $this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['total_pontos'] /= $this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['total_inscritos'];
					$this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['rendimento'] /= $this->rendimentoPorInstituicaoPorDisciplina[$instID][$dID]['total_inscritos'];

				}
			}
		}
	}

	public function calcularRendimentoPorTurmaPorProporcao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorProporcao[$tID]))
				$this->rendimentoPorTurmaPorProporcao[$tID] = new ProporcaoDeDesempenho();

			$this->rendimentoPorTurmaPorProporcao[$tID]->adicionarRendimento( $this->rendimentoGlobal[$iID]['rendimento'] );
		}

		foreach ( $this->rendimentoPorTurmaPorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorSeriePorProporcao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorProporcao[$sID]))
				$this->rendimentoPorSeriePorProporcao[$sID] = new ProporcaoDeDesempenho();

			$this->rendimentoPorSeriePorProporcao[$sID]->adicionarRendimento( $this->rendimentoGlobal[$iID]['rendimento'] );
		}

		foreach ( $this->rendimentoPorSeriePorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorInstituicaoPorProporcao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicaoPorProporcao[$instID]))
				$this->rendimentoPorInstituicaoPorProporcao[$instID] = new ProporcaoDeDesempenho();

			$this->rendimentoPorInstituicaoPorProporcao[$instID]->adicionarRendimento( $this->rendimentoGlobal[$iID]['rendimento'] );
		}

		foreach ( $this->rendimentoPorInstituicaoPorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorTurmaPorProporcaoPorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		// Verifica se o cálculo já foi realizado
		if ($this->_calculoJaRealizado('rendimentoPorTurmaPorProporcaoPorDisciplina')) {
			return true;
		}

		// Usa lazy loading para garantir que os dados estão disponíveis
		$rendimentoPorDisciplina = $this->obterRendimentoPorDisciplina();

		foreach ($rendimentoPorDisciplina as $iID => $rendimentoPorDisciplina) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID]))
				$this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] = array();

			foreach ($rendimentoPorDisciplina as $dID => $desempenho) {
				if (!isset($this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID][$dID]))
					$this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID][$dID] = new ProporcaoDeDesempenho();

				$this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID][$dID]->adicionarRendimento( $desempenho['rendimento'] );
			}
		}

		foreach ( $this->rendimentoPorTurmaPorProporcaoPorDisciplina as $tID => $rendimentoPorProporcaoPorDisciplina) {
			foreach ($rendimentoPorProporcaoPorDisciplina as $dID => &$proporcaoDesempenho) {
				$proporcaoDesempenho->calcularProporcoesPorcentagens();
			}
		}

		// Marca o cálculo como realizado
		$this->_marcarCalculoRealizado('rendimentoPorTurmaPorProporcaoPorDisciplina');
	}

	public function calcularRendimentoPorSeriePorProporcaoPorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		if (!count($this->rendimentoPorDisciplina))
			$this->calcularRendimentoPorDisciplinaDosInscritos();

		foreach ($this->rendimentoPorDisciplina as $iID => $rendimentoPorDisciplina) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID]))
				$this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] = array();

			foreach ($rendimentoPorDisciplina as $dID => $desempenho) {
				if (!isset($this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID][$dID]))
					$this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID][$dID] = new ProporcaoDeDesempenho();

				$this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID][$dID]->adicionarRendimento( $desempenho['rendimento'] );
			}
		}

		foreach ( $this->rendimentoPorSeriePorProporcaoPorDisciplina as $sID => $rendimentoPorProporcaoPorDisciplina) {
			foreach ($rendimentoPorProporcaoPorDisciplina as $dID => &$proporcaoDesempenho) {
				$proporcaoDesempenho->calcularProporcoesPorcentagens();
			}
		}
	}

	public function calcularRendimentoPorInstituicaoPorProporcaoPorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		if (!count($this->rendimentoPorDisciplina))
			$this->calcularRendimentoPorDisciplinaDosInscritos();

		foreach ($this->rendimentoPorDisciplina as $iID => $rendimentoPorDisciplina) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$this->inscritos[$iID]->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID]))
				$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID] = array();

			foreach ($rendimentoPorDisciplina as $dID => $desempenho) {
				if (!isset($this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID][$dID]))
					$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID][$dID] = new ProporcaoDeDesempenho();

				$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID][$dID]->adicionarRendimento( $desempenho['rendimento'] );
			}
		}

		foreach ( $this->rendimentoPorInstituicaoPorProporcaoPorDisciplina as $instID => $rendimentoPorProporcaoPorDisciplina) {
			foreach ($rendimentoPorProporcaoPorDisciplina as $dID => &$proporcaoDesempenho) {
				$proporcaoDesempenho->calcularProporcoesPorcentagens();
			}
		}
	}



	public function calcularRendimentoPorInstituicaoPorProporcao4 () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$inscricao->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicaoPorProporcao[$instID]))
				$this->rendimentoPorInstituicaoPorProporcao[$instID] = new ProporcaoDeDesempenho4();

			$this->rendimentoPorInstituicaoPorProporcao[$instID]->adicionarRendimento( $this->rendimentoGlobal[$iID]['rendimento'] );
		}

		foreach ( $this->rendimentoPorInstituicaoPorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorSeriePorProporcao4PorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		if (!count($this->rendimentoPorDisciplina))
			$this->calcularRendimentoPorDisciplinaDosInscritos();

		foreach ($this->rendimentoPorDisciplina as $iID => $rendimentoPorDisciplina) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID]))
				$this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID] = array();

			foreach ($rendimentoPorDisciplina as $dID => $desempenho) {
				if (!isset($this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID][$dID]))
					$this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID][$dID] = new ProporcaoDeDesempenho4();

				$this->rendimentoPorSeriePorProporcaoPorDisciplina[$sID][$dID]->adicionarRendimento( $desempenho['rendimento'] );
			}
		}

		foreach ( $this->rendimentoPorSeriePorProporcaoPorDisciplina as $sID => $rendimentoPorProporcaoPorDisciplina) {
			foreach ($rendimentoPorProporcaoPorDisciplina as $dID => &$proporcaoDesempenho) {
				$proporcaoDesempenho->calcularProporcoesPorcentagens();
			}
		}
	}

	public function calcularRendimentoPorInstituicaoPorProporcao4PorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		if (!count($this->rendimentoPorDisciplina))
			$this->calcularRendimentoPorDisciplinaDosInscritos();

		foreach ($this->rendimentoPorDisciplina as $iID => $rendimentoPorDisciplina) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$instID = @$this->inscritos[$iID]->obterAluno()->obterUsuario()->obterInstituicao()->obterID();

			if (!isset($this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID]))
				$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID] = array();

			foreach ($rendimentoPorDisciplina as $dID => $desempenho) {
				if (!isset($this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID][$dID]))
					$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID][$dID] = new ProporcaoDeDesempenho4();

				$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina[$instID][$dID]->adicionarRendimento( $desempenho['rendimento'] );
			}
		}

		foreach ( $this->rendimentoPorInstituicaoPorProporcaoPorDisciplina as $instID => $rendimentoPorProporcaoPorDisciplina) {
			foreach ($rendimentoPorProporcaoPorDisciplina as $dID => &$proporcaoDesempenho) {
				$proporcaoDesempenho->calcularProporcoesPorcentagens();
			}
		}
	}

	public function calcularRendimentoPorSeriePorProporcao4 () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorProporcao[$sID]))
				$this->rendimentoPorSeriePorProporcao[$sID] = new ProporcaoDeDesempenho4();

			$this->rendimentoPorSeriePorProporcao[$sID]->adicionarRendimento( $this->rendimentoGlobal[$iID]['rendimento'] );
		}

		foreach ( $this->rendimentoPorSeriePorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorTurmaPorProporcao4 () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorProporcao[$tID]))
				$this->rendimentoPorTurmaPorProporcao[$tID] = new ProporcaoDeDesempenho4();

			$this->rendimentoPorTurmaPorProporcao[$tID]->adicionarRendimento( $this->rendimentoGlobal[$iID]['rendimento'] );
		}

		foreach ( $this->rendimentoPorTurmaPorProporcao as &$proporcaoDesempenho) {
			$proporcaoDesempenho->calcularProporcoesPorcentagens();
		}
	}

	public function calcularRendimentoPorTurmaPorProporcao4PorDisciplina () {
		if ( $this->simulado->obterID() == null )
			return false;

		if (!count($this->rendimentoPorDisciplina))
			$this->calcularRendimentoPorDisciplinaDosInscritos();

		foreach ($this->rendimentoPorDisciplina as $iID => $rendimentoPorDisciplina) {
			// n?o entra na m?dia se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade() && $this->REL_ESPECIAIS_RemoverDoCalculo == 'sim')
				continue;

			#nao_alfabetico
			if (@$this->inscritos[$iID]->obterNaoAlfabetico() == 1){
				continue;
			}

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID]))
				$this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID] = array();

			foreach ($rendimentoPorDisciplina as $dID => $desempenho) {
				if (!isset($this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID][$dID]))
					$this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID][$dID] = new ProporcaoDeDesempenho4();

				$this->rendimentoPorTurmaPorProporcaoPorDisciplina[$tID][$dID]->adicionarRendimento( $desempenho['rendimento'] );
			}
		}

		foreach ( $this->rendimentoPorTurmaPorProporcaoPorDisciplina as $tID => $rendimentoPorProporcaoPorDisciplina) {
			foreach ($rendimentoPorProporcaoPorDisciplina as $dID => &$proporcaoDesempenho) {
				$proporcaoDesempenho->calcularProporcoesPorcentagens();
			}
		}
	}




	public function limitarInscritosPorTurmas ($turmasIDs) {
		if ( !is_array($turmasIDs) )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$tID = @$inscricao->obterAluno()->obterTurma()->obterID();
			if ( !in_array($tID, $turmasIDs) )
				$this->_removerInscricaoPeloID($iID);
		}
	}

	public function limitarInscritosPorAlunos ($alunosIDs) {
		if ( !is_array($alunosIDs) )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$aID = $inscricao->obterAluno()->obterID();
			if ( !in_array($aID, $alunosIDs) )
				$this->_removerInscricaoPeloID($iID);
		}
	}

	public function limitarInscritosPorCor ($cores = array()) {
		if ( !is_array($cores) )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$aCOR = $inscricao->obterAluno()->obterCor();
			if ( !in_array($aCOR, $cores) )
				$this->_removerInscricaoPeloID($iID);
		}
	}

	public function fixarSimulado (Simulado &$simulado) {
		$this->_resetarParametros();
		$this->simulado = $simulado;

		$this->simuladoQuestoes = $simulado->obterQuestoesParaFormulario(false, true, true);

		foreach ( $this->simuladoQuestoes as $fase => $tipos ) {
			foreach ( $tipos as $tipo => $questoesNumeradas ) {
				foreach ( $questoesNumeradas as $numero => $questoesNumeradas ) {
					foreach ( $questoesNumeradas as &$q ) {
						if ( !isset($this->simuladoQuestoesPorID[ $q->obterID() ]) ) {
							$this->simuladoQuestoesPorID[ $q->obterID() ] = $q;

							$this->nomesQuestoes[ $q->obterID() ] = !$q->foiAnulada() ? $q->obterIdentificador() : '<span style="color: red;">'.$q->obterIdentificador().'</span>';

							if ( !isset( $this->nomesDisciplinas[ $q->obterDisciplina()->obterID() ] ) ) {
								$this->nomesDisciplinas[ $q->obterDisciplina()->obterID() ] = $q->obterDisciplina()->obterNome();
								$this->nomesPequenosDisciplinas[ $q->obterDisciplina()->obterID() ] = $q->obterDisciplina()->obterNomePequeno();
							}
						}
					}
				}
			}
		}

		$disciplinasOrdenadas = Disciplina::obterArrayDisciplinasParaFormulario(false, false);
		foreach($disciplinasOrdenadas as $dID => $dNome) {
			if (!isset($this->nomesDisciplinas[$dID]))
				unset($disciplinasOrdenadas[$dID]);
		}

		$this->nomesDisciplinas = $disciplinasOrdenadas;

		$tempNomes = $this->nomesPequenosDisciplinas;
		$this->nomesPequenosDisciplinas = $this->nomesDisciplinas;
		foreach ($this->nomesPequenosDisciplinas as $dID => &$dNome)
			$dNome = $tempNomes[$dID];

		asort($this->nomesQuestoes, SORT_LOCALE_STRING);
	}

	public function &obterDesistencias ($resultadoQuantitativo = false) {
		if ( !$resultadoQuantitativo )
			return $this->desistencias;

		$desistencias = array();
		foreach ( $this->desistencias as $tipo => $desistencia )
			$desistencias[$tipo] = count($desistencia);

		return $desistencias;
	}

	protected function _resetarParametros () {
		$this->simulado = new Simulado(null);
		$this->simuladoQuestoes = array();
		$this->simuladoQuestoesPorID = array();

		$this->inscritos = array();
		$this->respostas = array();

		$this->nomesTurmas = array();
		$this->nomesSeries = array();
		$this->nomesInstituicoes = array();
		$this->nomesInstituicoesComDREeMunicipio = array();
		$this->nomesDisciplinas = array();
		$this->nomesPequenosDisciplinas = array();
		$this->nomesQuestoes = array();

		$this->instituicoesPorTurma = array();

		$this->rendimentoPorQuestao = array();
		$this->rendimentoGlobal = array();
		$this->rendimentoPorDisciplina = array();
		$this->rendimentoPorTurma = array();
		$this->rendimentoPorTurmaPorQuestao = array();
		$this->rendimentoPorTurmaPorDisciplina = array();
		$this->rendimentoPorTurmaPorProporcao = array();
		$this->rendimentoPorTurmaPorProporcaoPorDisciplina = array();
		$this->rendimentoPorSerie = array();
		$this->rendimentoPorSeriePorQuestao = array();
		$this->rendimentoPorSeriePorDisciplina = array();
		$this->rendimentoPorSeriePorProporcao = array();
		$this->rendimentoPorSeriePorProporcaoPorDisciplina = array();
		$this->rendimentoPorInstituicao = array();
		$this->rendimentoPorInstituicaoPorQuestao = array();
		$this->rendimentoPorInstituicaoPorDisciplina = array();
		$this->rendimentoPorInstituicaoPorProporcao = array();
		$this->rendimentoPorInstituicaoPorProporcaoPorDisciplina = array();

		$this->desistencias = array(
			self::DESISTENCIA_NAO_COMPARECEU => array(),
			self::DESISTENCIA_RENDIMENTO_NULO => array()
		);

		// Limpa cache de performance
		$this->_cacheQuantidadePorDisciplina = array();
		$this->_calculosRealizados = array();
		$this->_cacheConsultas = array();
	}

	protected function _removerInscricaoPeloID ($iID) {
		unset($this->inscritos[$iID]);
		unset($this->respostas[$iID]);
		unset($this->rendimentoPorQuestao[$iID]);
		unset($this->rendimentoGlobal[$iID]);
	}

	static public function obterDescricaoDesistencia ($tipo) {
		switch ( $tipo ) {
			case self::DESISTENCIA_NAO_COMPARECEU:
				return 'N?o compareceu';
			case self::DESISTENCIA_RENDIMENTO_NULO:
				return 'Todas as quest?es em branco';
			default:
				return 'Tipo de desist?ncia desconhecido';
		}
	}
}

?>