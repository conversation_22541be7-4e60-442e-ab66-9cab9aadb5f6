<?
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);

include_once 'AnalizadorSimulados.php';

class AnalizadorSimuladoHabilidade
{
	public static $HAB_NAO_ADQUIRIDA;
	public static $HAB_NAO_ADQUIRIDA_VALOR;
	public static $HAB_NAO_ADQUIRIDA_TEXTO;
	public static $HAB_NAO_ADQUIRIDA_COR;

	public static $HAB_EM_AQUISICAO;
	public static $HAB_EM_AQUISICAO_VALOR;
	public static $HAB_EM_AQUISICAO_TEXTO;
	public static $HAB_EM_AQUISICAO_COR;

	public static $HAB_ADQUIRIDA;
	public static $HAB_ADQUIRIDA_VALOR;
	public static $HAB_ADQUIRIDA_TEXTO;
	public static $HAB_ADQUIRIDA_COR;

	public static $CONT_NAO_ADQUIRIDA;
	public static $CONT_NAO_ADQUIRIDA_VALOR;
	public static $CONT_NAO_ADQUIRIDA_TEXTO;
	public static $CONT_NAO_ADQUIRIDA_COR;

	public static $CONT_EM_AQUISICAO;
	public static $CONT_EM_AQUISICAO_VALOR;
	public static $CONT_EM_AQUISICAO_TEXTO;
	public static $CONT_EM_AQUISICAO_COR;

	public static $CONT_ADQUIRIDA;
	public static $CONT_ADQUIRIDA_VALOR;
	public static $CONT_ADQUIRIDA_TEXTO;
	public static $CONT_ADQUIRIDA_COR;

	public static $QUESTAO_NAO_ADQUIRIDA;
	public static $QUESTAO_NAO_ADQUIRIDA_VALOR;
	public static $QUESTAO_NAO_ADQUIRIDA_TEXTO;
	public static $QUESTAO_NAO_ADQUIRIDA_COR;

	public static $QUESTAO_EM_AQUISICAO;
	public static $QUESTAO_EM_AQUISICAO_VALOR;
	public static $QUESTAO_EM_AQUISICAO_TEXTO;
	public static $QUESTAO_EM_AQUISICAO_COR;

	public static $QUESTAO_ADQUIRIDA;
	public static $QUESTAO_ADQUIRIDA_VALOR;
	public static $QUESTAO_ADQUIRIDA_TEXTO;
	public static $QUESTAO_ADQUIRIDA_COR;

	public static $PROPORCAO_1;
	public static $PROPORCAO_1_VALOR;
	public static $PROPORCAO_1_TEXTO;
	public static $PROPORCAO_1_COR;

	public static $PROPORCAO_2;
	public static $PROPORCAO_2_VALOR;
	public static $PROPORCAO_2_TEXTO;
	public static $PROPORCAO_2_COR;

	public static $PROPORCAO_3;
	public static $PROPORCAO_3_VALOR;
	public static $PROPORCAO_3_TEXTO;
	public static $PROPORCAO_3_COR;

	public static $PROPORCAO_4;
	public static $PROPORCAO_4_VALOR;
	public static $PROPORCAO_4_TEXTO;
	public static $PROPORCAO_4_COR;

	public static $PROPORCAO_5;
	public static $PROPORCAO_5_VALOR;
	public static $PROPORCAO_5_TEXTO;
	public static $PROPORCAO_5_COR;

	public static $MEDIA_TURMAS_COR;
	public static $MEDIA_SERIES_COR;

	public static $DIF_BAIXA;
	public static $DIF_BAIXA_VALOR;
	public static $DIF_BAIXA_TEXTO;

	public static $DIF_MEDIA;
	public static $DIF_MEDIA_VALOR;
	public static $DIF_MEDIA_TEXTO;

	public static $DIF_ALTA;
	public static $DIF_ALTA_VALOR;
	public static $DIF_ALTA_TEXTO;

	public static $MEDIA_ESCOLA_VALOR;
	public static $MEDIA_ESCOLA_BAIXA_COR;

	const DESISTENCIA_NAO_COMPARECEU = 0;
	const DESISTENCIA_NAO_COMPARECEU_EM_TODAS_AS_FASES = 1;
	const DESISTENCIA_TIPO_PROVA_INVALIDA = 2;
	const DESISTENCIA_LINGUA_ESTRANGEIRA_INVALIDA = 3;
	const DESISTENCIA_RENDIMENTO_NULO = 4;

	public $simulado;
	public $simuladoQuestoes;
	public $simuladoQuestoesPorID;
	public $desistencias;
	public $participantesPorFase;

	public $nomesTurmas;
	public $nomesSeries;
	public $nomesHabilidades;
	public $descricoesHabilidades;
	public $questoesPorHabilidades;
	public $macroHabilidades;
	public $nomesConteudos;
	public $questoesPorConteudo;

	public $inscritos;
	public $respostas;

	public $colocacao;

	public $rendimentoPorQuestao;
	public $rendimentoGlobal;
	public $rendimentoPorHabilidade;
	public $rendimentoPorConteudo;

	public $rendimentoPorTurma;
	public $rendimentoPorTurmaPorQuestao;
	public $rendimentoPorTurmaPorHabilidade;
	public $rendimentoPorTurmaPorConteudo;
	public $faixaRendimentoPorTurma;
	public $faixaRendimentoPorTurmaAbs;
	public $habilidadesProporcoesPorTurma;
	public $habilidadesProporcoesPorTurmaAbs;
	public $questoesProporcoesPorTurma;
	public $questoesProporcoesPorTurmaAbs;
	public $conteudosProporcoesPorTurma;
	public $conteudosProporcoesPorTurmaAbs;

	public $rendimentoPorSerie;
	public $rendimentoPorSeriePorQuestao;
	public $rendimentoPorSeriePorHabilidade;
	public $rendimentoPorSeriePorConteudo;
	public $faixaRendimentoPorSerie;
	public $faixaRendimentoPorSerieAbs;
	public $habilidadesProporcoesPorSerie;
	public $habilidadesProporcoesPorSerieAbs;
	public $questoesProporcoesPorSerie;
	public $questoesProporcoesPorSerieAbs;
	public $conteudosProporcoesPorSerie;
	public $conteudosProporcoesPorSerieAbs;

	public function __construct () {
		$instituicao = Core::registro('instituicao');

		if ( Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado() != null )
			$instituicao = Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()->obterInstituicao();

		self::$HAB_NAO_ADQUIRIDA = 1;
		self::$HAB_NAO_ADQUIRIDA_VALOR = Core::diretiva('REL:HAB_NAO_ADQUIRIDA:valor', 49, $instituicao);
		self::$HAB_NAO_ADQUIRIDA_TEXTO = Core::diretiva('REL:HAB_NAO_ADQUIRIDA:texto', 'a desenvolver', $instituicao);
		self::$HAB_NAO_ADQUIRIDA_COR =   Core::diretiva('REL:HAB_NAO_ADQUIRIDA:cor', '#A93A49', $instituicao);

		self::$HAB_EM_AQUISICAO = 2;
		self::$HAB_EM_AQUISICAO_VALOR = Core::diretiva('REL:HAB_EM_AQUISICAO:valor', 89, $instituicao);
		self::$HAB_EM_AQUISICAO_TEXTO = Core::diretiva('REL:HAB_EM_AQUISICAO:texto', 'em desenvolvimento', $instituicao);
		self::$HAB_EM_AQUISICAO_COR =   Core::diretiva('REL:HAB_EM_AQUISICAO:cor', '#EEB200', $instituicao);

		self::$HAB_ADQUIRIDA = 3;
		self::$HAB_ADQUIRIDA_VALOR = Core::diretiva('REL:HAB_ADQUIRIDA:valor', 100, $instituicao);
		self::$HAB_ADQUIRIDA_TEXTO = Core::diretiva('REL:HAB_ADQUIRIDA:texto', 'desenvolvido', $instituicao);
		self::$HAB_ADQUIRIDA_COR =   Core::diretiva('REL:HAB_ADQUIRIDA:cor', '#3B786F', $instituicao);

		self::$CONT_NAO_ADQUIRIDA = 1;
		self::$CONT_NAO_ADQUIRIDA_VALOR = Core::diretiva('REL:CONT_NAO_ADQUIRIDA:valor', 49, $instituicao);
		self::$CONT_NAO_ADQUIRIDA_TEXTO = Core::diretiva('REL:CONT_NAO_ADQUIRIDA:texto', 'nao domina', $instituicao);
		self::$CONT_NAO_ADQUIRIDA_COR =   Core::diretiva('REL:CONT_NAO_ADQUIRIDA:cor', '#A93A49', $instituicao);

		self::$CONT_EM_AQUISICAO = 2;
		self::$CONT_EM_AQUISICAO_VALOR = Core::diretiva('REL:CONT_EM_AQUISICAO:valor', 89, $instituicao);
		self::$CONT_EM_AQUISICAO_TEXTO = Core::diretiva('REL:CONT_EM_AQUISICAO:texto', 'domina parcialmente', $instituicao);
		self::$CONT_EM_AQUISICAO_COR =   Core::diretiva('REL:CONT_EM_AQUISICAO:cor', '#EEB200', $instituicao);

		self::$CONT_ADQUIRIDA = 3;
		self::$CONT_ADQUIRIDA_VALOR = Core::diretiva('REL:CONT_ADQUIRIDA:valor', 100, $instituicao);
		self::$CONT_ADQUIRIDA_TEXTO = Core::diretiva('REL:CONT_ADQUIRIDA:texto', 'domina', $instituicao);
		self::$CONT_ADQUIRIDA_COR =   Core::diretiva('REL:CONT_ADQUIRIDA:cor', '#3B786F', $instituicao);

		self::$QUESTAO_NAO_ADQUIRIDA = 1;
		self::$QUESTAO_NAO_ADQUIRIDA_VALOR = Core::diretiva('REL:QUESTAO_NAO_ADQUIRIDA:valor', 59, $instituicao);
		self::$QUESTAO_NAO_ADQUIRIDA_TEXTO = Core::diretiva('REL:QUESTAO_NAO_ADQUIRIDA:texto', 'incorreto (-)', $instituicao);
		self::$QUESTAO_NAO_ADQUIRIDA_COR =   Core::diretiva('REL:QUESTAO_NAO_ADQUIRIDA:cor', '#A93A49', $instituicao);

		self::$QUESTAO_EM_AQUISICAO = 2;
		self::$QUESTAO_EM_AQUISICAO_VALOR = Core::diretiva('REL:QUESTAO_EM_AQUISICAO:valor', 70, $instituicao);
		self::$QUESTAO_EM_AQUISICAO_TEXTO = Core::diretiva('REL:QUESTAO_EM_AQUISICAO:texto', 'parcialmente correto (+/-)', $instituicao);
		self::$QUESTAO_EM_AQUISICAO_COR =   Core::diretiva('REL:QUESTAO_EM_AQUISICAO:cor', '#EEB200', $instituicao);

		self::$QUESTAO_ADQUIRIDA = 3;
		self::$QUESTAO_ADQUIRIDA_VALOR = Core::diretiva('REL:QUESTAO_ADQUIRIDA:valor', 100, $instituicao);
		self::$QUESTAO_ADQUIRIDA_TEXTO = Core::diretiva('REL:QUESTAO_ADQUIRIDA:texto', 'correto (+)', $instituicao);
		self::$QUESTAO_ADQUIRIDA_COR =   Core::diretiva('REL:QUESTAO_ADQUIRIDA:cor', '#3B786F', $instituicao);

		self::$PROPORCAO_1 = 1;
		self::$PROPORCAO_1_VALOR = Core::diretiva('REL:PROPORCAO_1:valor', 20, $instituicao);
		self::$PROPORCAO_1_TEXTO = Core::diretiva('REL:PROPORCAO_1:texto', '0% a 20%', $instituicao);
		self::$PROPORCAO_1_COR =   Core::diretiva('REL:PROPORCAO_1:cor', '#99cbfe', $instituicao);

		self::$PROPORCAO_2 = 2;
		self::$PROPORCAO_2_VALOR = Core::diretiva('REL:PROPORCAO_2:valor', 40, $instituicao);
		self::$PROPORCAO_2_TEXTO = Core::diretiva('REL:PROPORCAO_2:texto', '21% a 40%', $instituicao);
		self::$PROPORCAO_2_COR =   Core::diretiva('REL:PROPORCAO_2:cor', '#6e92db', $instituicao);

		self::$PROPORCAO_3 = 3;
		self::$PROPORCAO_3_VALOR = Core::diretiva('REL:PROPORCAO_3:valor', 60, $instituicao);
		self::$PROPORCAO_3_TEXTO = Core::diretiva('REL:PROPORCAO_3:texto', '41% a 60%', $instituicao);
		self::$PROPORCAO_3_COR =   Core::diretiva('REL:PROPORCAO_3:cor', '#6e92db', $instituicao);

		self::$PROPORCAO_4 = 4;
		self::$PROPORCAO_4_VALOR = Core::diretiva('REL:PROPORCAO_4:valor', 80, $instituicao);
		self::$PROPORCAO_4_TEXTO = Core::diretiva('REL:PROPORCAO_4:texto', '61% a 80%', $instituicao);
		self::$PROPORCAO_4_COR =   Core::diretiva('REL:PROPORCAO_4:cor', '#303fa7', $instituicao);

		self::$PROPORCAO_5 = 5;
		self::$PROPORCAO_5_VALOR = Core::diretiva('REL:PROPORCAO_5:valor', 100, $instituicao);
		self::$PROPORCAO_5_TEXTO = Core::diretiva('REL:PROPORCAO_5:texto', '81% a 100%', $instituicao);
		self::$PROPORCAO_5_COR =   Core::diretiva('REL:PROPORCAO_5:cor', '#010181', $instituicao);

		self::$MEDIA_TURMAS_COR =   Core::diretiva('REL:MEDIA_TURMAS:cor', '#31859d', $instituicao);
		self::$MEDIA_SERIES_COR =   Core::diretiva('REL:MEDIA_SERIES:cor', '#a5cdd9', $instituicao);

		self::$DIF_BAIXA = 1;
		self::$DIF_BAIXA_VALOR = Core::diretiva('REL:DIF_BAIXA:valor', 15, $instituicao);
		self::$DIF_BAIXA_TEXTO = Core::diretiva('REL:DIF_BAIXA:texto', '-', $instituicao);

		self::$DIF_MEDIA = 2;
		self::$DIF_MEDIA_VALOR = Core::diretiva('REL:DIF_MEDIA:valor', 25, $instituicao);
		self::$DIF_MEDIA_TEXTO = Core::diretiva('REL:DIF_MEDIA:texto', '<span style="color: #FFA500;">^</span>', $instituicao);

		self::$DIF_ALTA = 3;
		self::$DIF_ALTA_VALOR = Core::diretiva('REL:DIF_ALTA:valor', 100, $instituicao);
		self::$DIF_ALTA_TEXTO = Core::diretiva('REL:DIF_ALTA:texto', '<span style="color: red;">^^</span>', $instituicao);

		self::$MEDIA_ESCOLA_VALOR = Core::diretiva('REL:MEDIA_ESCOLA:valor', 60, $instituicao);
		self::$MEDIA_ESCOLA_BAIXA_COR = Core::diretiva('REL:MEDIA_ESCOLA_BAIXA:cor', '#FF6565', $instituicao);

		$this->_resetarParametros();
	}

	public function carregarInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		$todasInstituicoes = false;

		$this->inscritos = array();

		$instituicao = Core::registro('instituicao');
		$instituicoesSQL = '';
		if (!$todasInstituicoes) {
			if ( Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado() != null )
				$instituicao = Core::modulo('_perfil_falso')->obterUsuarioDoPerfilSelecionado()->obterInstituicao();

			$instituicoesSQL = ' t_instituicao = '. Core::registro('db')->formatarValor( $instituicao->obterID() ) .' AND ';
		}

		$rs = Core::registro('db')->query( sprintf(
					'SELECT *, series.s_nome as serie_nome FROM simulados_inscricoes
					INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno
					INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario
					LEFT JOIN turmas ON turmas.t_id = alunos.a_turma
					LEFT JOIN series ON series.s_id = turmas.t_serie
					LEFT JOIN simulados ON simulados.s_id = simulados_inscricoes.si_simulado
					WHERE %s si_simulado = %s',
				$instituicoesSQL,
				Core::registro('db')->formatarValor( $this->simulado->obterID() ) ) );

		if ($rs->num_rows) {
			$turmas = $this->obterTurmas();

			while ($row = $rs->fetch_assoc()) {
				// USUÁRIO
				$usuario = new UsuarioInstituido($row['u_id']);
				$usuario->fixarNome($row['u_nome']);
				$usuario->fixarEndereco( new Endereco($row['u_endereco']) );
				$usuario->fixarInstituicao( new Instituicao($row['u_instituicao']) );
				$usuario->fixarFoto($row['u_foto']);
				$usuario->fixarEmail( new Email($row['u_email']) );

				// ALUNO
				$aluno = new Aluno($row['si_aluno']);
				$aluno->fixarUsuario($usuario);
				$aluno->fixarMatricula($row['a_matricula']);
				$aluno->fixarNumeroChamada($row['a_numero_chamada']);

				if (isset($turmas[$row['a_turma']]))
					$aluno->fixarTurma( $turmas[$row['a_turma']] );
				else
					$aluno->fixarTurma( new Turma($row['a_turma']) );

				$inscricao = new Inscricao( $row['si_id'] );

				// INSCRICAO
				$inscricao->fixarTipo( $row['si_tipo'] );
				$inscricao->fixarAluno( $aluno );
				$inscricao->fixarSimulado( $this->simulado );

				$this->inscritos[ $row['si_id'] ] = $inscricao;
				$this->respostas[ $row['si_id'] ] = array();
				$this->rendimentoPorQuestao[ $row['si_id'] ] = array();
				$this->rendimentoGlobal[ $row['si_id'] ] = array();
				$this->colocacao[ $row['si_id'] ] = 999999;

				if (!isset($this->nomesTurmas[$row['a_turma']]))
					$this->nomesTurmas[$row['a_turma']] = $row['t_nome'];

				if (!isset($this->nomesSeries[$row['t_serie']]))
					$this->nomesSeries[$row['t_serie']] = $row['serie_nome'];
			}
		}
		$rs->free();

		asort($this->nomesSeries, SORT_LOCALE_STRING);
		asort($this->nomesTurmas, SORT_LOCALE_STRING);
	}

	public function carregarRespostasDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		$rs = Core::registro('db')->query( sprintf(
			  'SELECT respostas.*, simulados_inscricoes.si_id FROM respostas
			  INNER JOIN simulados_inscricoes ON simulados_inscricoes.si_id = respostas.r_inscricao
			  WHERE simulados_inscricoes.si_simulado = %s AND r_valor IS NOT NULL',
			Core::registro('db')->formatarValor( $this->simulado->obterID() ) ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ( !isset($this->inscritos[ $row['si_id'] ]) )
					continue;

				$this->respostas[ $row['si_id'] ][$row['r_questao']] = array(
					'id' => $row['r_id'],
				   	'valor' => $row['r_valor'],
				   	'questao' => $row['r_questao'],
				   	'inscricao' => $row['si_id'],
					'nivel_seguranca' => $row['r_nivel_seguranca'],
					'observacao_professor' => ($row['r_observacao_professor'] != NULL ? explode(',', $row['r_observacao_professor']) : array()),
					'observacao_professor_texto' => $row['r_observacao_professor_texto'],
				);

				/*
				$resposta = new Resposta( $row['r_id'] );
				$resposta->fixarValor( $row['r_valor'] );
				$resposta->fixarQuestao( new Questao($row['r_questao']) );
				$resposta->fixarInscricao( $this->inscritos[ $row['si_id'] ] );

				$this->inscritos[ $row['si_id'] ]->adicionarRespostaMarcada( $resposta );
				$this->respostas[ $row['si_id'] ][$row['r_questao']] = $resposta;
				*/
			}

			// adiciona as respostar aos inscritos (pra uso externo)
			foreach ($this->respostas as $iID => &$respostas)
				$this->inscritos[ $iID ]->fixarRespostasArray( $respostas );
		}
		$rs->free();
	}

	public function analisarTiposDeProvasELinguasDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			if ( count($this->respostas[$iID]) == 0 ) {
				$this->desistencias[self::DESISTENCIA_NAO_COMPARECEU][] = $iID;
				$this->_removerInscricaoPeloID($iID);
				continue;
			}

			if ( $this->simulado->obterNumeroDeTipos() == 1 ) {
				$inscricao->fixarTipo( 1 );
			} else if ( $inscricao->obterTipo() < 1 || $inscricao->obterTipo() > $this->simulado->obterNumeroDeTipos() ) {
				$this->desistencias[self::DESISTENCIA_TIPO_PROVA_INVALIDA][] = $iID;
				$this->_removerInscricaoPeloID($iID);
				continue;
			}
		}
	}

	public function analisarRespostasDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			if ( $this->simulado->obterDuracao() > 1 ) {
				$numeroQuestoesPorFase = array();
				$numeroRespostasPorFase = array();
				foreach ( $this->simuladoQuestoes as $fase => $tipos ) {
					foreach ( $tipos[ $inscricao->obterTipo() ] as $numero => $questoesNumeradas ) {
						foreach ( $questoesNumeradas as &$q ) {
							if ( $q->questaoDeOpcao() )
								continue;

							if ( $q->obterDisciplina()->obterLingua() && $inscricao->obterLingua()->obterID() != $q->obterDisciplina()->obterID() )
								continue;

							if ( !isset($numeroQuestoesPorFase[ $q->obterFaseDuracao() ]) ) {
								$numeroQuestoesPorFase[ $q->obterFaseDuracao() ] = 0;
								$numeroRespostasPorFase[ $q->obterFaseDuracao() ] = 0;
							}

							if ( $q->obterTipo() != Questao::DISCURSIVA )
								$numeroQuestoesPorFase[ $q->obterFaseDuracao() ]++;

							if ( isset($this->respostas[$iID][$q->obterID()]) )
								$numeroRespostasPorFase[ $q->obterFaseDuracao() ]++;
						}
					}
				}

				foreach ( $numeroQuestoesPorFase as $fase => $numeroQuestoes ) {
					if ( $numeroRespostasPorFase[$fase] < $numeroQuestoes ) {
						$this->desistencias[self::DESISTENCIA_NAO_COMPARECEU_EM_TODAS_AS_FASES][] = $iID;
						$this->_removerInscricaoPeloID($iID);
						continue;
					}

					$this->participantesPorFase[$fase][] = $iID;
				}
			} else {
				$this->participantesPorFase[1][] = $iID;
			}
		}
	}

	public function calcularRendimentoPorQuestaoDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			foreach ( $this->simuladoQuestoes as $fase => $tipos ) {
				foreach ( $tipos[ $inscricao->obterTipo() ] as $numero => $questoesNumeradas ) {
					foreach ( $questoesNumeradas as &$q ) {
						if ( $q->questaoDeOpcao() )
							continue;

						if ( $q->obterDisciplina()->obterLingua() && $inscricao->obterLingua()->obterID() != $q->obterDisciplina()->obterID() )
							continue;

						$desempenho = array( 'rendimento' => 100, 'pontos' => $q->obterPontosQuestao() );

						if ( !$q->foiAnulada() ) {
							$valor = null;
							if ( isset($this->respostas[$iID][$q->obterID()]) )
								$valor = $this->respostas[$iID][$q->obterID()]['valor'];
								//$valor = $this->respostas[$iID][$q->obterID()]->obterValor();

							$desempenho = $q->obterDesempenho( $inscricao->obterTipo(), $numero, $valor );
						}

						$desempenho['pontos_questao'] = $q->obterPontosQuestao();

						$this->rendimentoPorQuestao[$iID][$q->obterID()] = $desempenho;
					}
				}
			}
		}
	}

	public function calcularRendimentoGlobalDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$this->rendimentoGlobal[$iID] = array('total_pontos' => 0, 'rendimento' => 0, 'pontos_questao' => 0);
			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				$this->rendimentoGlobal[$iID]['pontos_questao'] += $desempenho['pontos_questao'];
				$this->rendimentoGlobal[$iID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoGlobal[$iID]['rendimento'] += $desempenho['rendimento'] * $desempenho['pontos_questao'];
			}

			if ( $this->rendimentoGlobal[$iID]['pontos_questao'] > 0 )
				$this->rendimentoGlobal[$iID]['rendimento'] /= $this->rendimentoGlobal[$iID]['pontos_questao'];
		}
	}

	public function calcularRendimentoPorHabilidadeDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$this->rendimentoPorHabilidade[$iID] = array();

			foreach ($this->questoesPorHabilidades as $hID => $questoesPorHabilidade) {
				$this->rendimentoPorHabilidade[$iID][$hID] = array('total_pontos' => 0, 'rendimento' => 0, 'pontos_questao' => 0);

				foreach ($questoesPorHabilidade as $qID) {
					$this->rendimentoPorHabilidade[$iID][$hID]['pontos_questao'] += $this->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
					$this->rendimentoPorHabilidade[$iID][$hID]['total_pontos'] += $this->rendimentoPorQuestao[$iID][$qID]['pontos'];
					$this->rendimentoPorHabilidade[$iID][$hID]['rendimento'] += $this->rendimentoPorQuestao[$iID][$qID]['rendimento'] * $this->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
				}

				if ($this->rendimentoPorHabilidade[$iID][$hID]['pontos_questao'] > 0)
					$this->rendimentoPorHabilidade[$iID][$hID]['rendimento'] /= $this->rendimentoPorHabilidade[$iID][$hID]['pontos_questao'];
			}
		}
	}

	public function calcularRendimentoPorConteudoDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$this->rendimentoPorConteudo[$iID] = array();

			foreach ($this->questoesPorConteudo as $cID => $questoesPorConteudo) {
				$this->rendimentoPorConteudo[$iID][$cID] = array('total_pontos' => 0, 'rendimento' => 0, 'pontos_questao' => 0);

				foreach ($questoesPorConteudo as $qID) {
					$this->rendimentoPorConteudo[$iID][$cID]['pontos_questao'] += $this->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
					$this->rendimentoPorConteudo[$iID][$cID]['total_pontos'] += $this->rendimentoPorQuestao[$iID][$qID]['pontos'];
					$this->rendimentoPorConteudo[$iID][$cID]['rendimento'] += $this->rendimentoPorQuestao[$iID][$qID]['rendimento'] * $this->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
				}

				if ($this->rendimentoPorConteudo[$iID][$cID]['pontos_questao'] > 0)
					$this->rendimentoPorConteudo[$iID][$cID]['rendimento'] /= $this->rendimentoPorConteudo[$iID][$cID]['pontos_questao'];
			}
		}
	}

	public function calcularRendimentoPorTurma () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurma[$tID]))
				$this->rendimentoPorTurma[$tID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => $desempenho['pontos_questao'],
					'total_inscritos' => 0
				);

			$this->rendimentoPorTurma[$tID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoPorTurma[$tID]['rendimento'] += $desempenho['rendimento'];
			$this->rendimentoPorTurma[$tID]['total_inscritos']++;
		}

		foreach ( $this->rendimentoPorTurma as $tID => $desempenho ) {
			if ( $this->rendimentoPorTurma[$tID]['total_inscritos'] > 0 ) {
				$this->rendimentoPorTurma[$tID]['total_pontos'] /= $this->rendimentoPorTurma[$tID]['total_inscritos'];
				$this->rendimentoPorTurma[$tID]['rendimento'] /= $this->rendimentoPorTurma[$tID]['total_inscritos'];
			}
		}
	}

	public function calcularRendimentoPorSerie () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSerie[$sID]))
				$this->rendimentoPorSerie[$sID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => $desempenho['pontos_questao'],
					'total_inscritos' => 0
				);

			$this->rendimentoPorSerie[$sID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoPorSerie[$sID]['rendimento'] += $desempenho['rendimento'];
			$this->rendimentoPorSerie[$sID]['total_inscritos']++;
		}

		foreach ( $this->rendimentoPorSerie as $sID => $desempenho ) {
			if ( $this->rendimentoPorSerie[$sID]['total_inscritos'] > 0 ) {
				$this->rendimentoPorSerie[$sID]['total_pontos'] /= $this->rendimentoPorSerie[$sID]['total_inscritos'];
				$this->rendimentoPorSerie[$sID]['rendimento'] /= $this->rendimentoPorSerie[$sID]['total_inscritos'];
			}
		}
	}

	public function calcularRendimentoPorTurmaPorQuestao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorQuestao[$tID]))
				$this->rendimentoPorTurmaPorQuestao[$tID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				if (!isset($this->rendimentoPorTurmaPorQuestao[$tID][$qID]))
					$this->rendimentoPorTurmaPorQuestao[$tID][$qID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorTurmaPorQuestao as $tID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $qID => $desempenho ) {
				if ( $this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_pontos'] /= $this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos'];
					$this->rendimentoPorTurmaPorQuestao[$tID][$qID]['rendimento'] /= $this->rendimentoPorTurmaPorQuestao[$tID][$qID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularRendimentoPorSeriePorQuestao () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorQuestao[$sID]))
				$this->rendimentoPorSeriePorQuestao[$sID] = array();

			foreach ( $this->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
				if (!isset($this->rendimentoPorSeriePorQuestao[$sID][$qID]))
					$this->rendimentoPorSeriePorQuestao[$sID][$qID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => $desempenho['pontos_questao'],
						'total_inscritos' => 0
					);

				$this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_pontos'] += $desempenho['pontos'];
				$this->rendimentoPorSeriePorQuestao[$sID][$qID]['rendimento'] += $desempenho['rendimento'];
				$this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos']++;
			}
		}

		foreach ( $this->rendimentoPorSeriePorQuestao as $sID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $qID => $desempenho ) {
				if ( $this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos'] > 0 ) {
					$this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_pontos'] /= $this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos'];
					$this->rendimentoPorSeriePorQuestao[$sID][$qID]['rendimento'] /= $this->rendimentoPorSeriePorQuestao[$sID][$qID]['total_inscritos'];
				}
			}
		}
	}

	public function calcularFaixaRendimentoPorTurma () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->faixaRendimentoPorTurma[$tID]))
				$this->faixaRendimentoPorTurma[$tID] = array(
					self::$PROPORCAO_1 => 0,
					self::$PROPORCAO_2 => 0,
					self::$PROPORCAO_3 => 0,
					self::$PROPORCAO_4 => 0,
					self::$PROPORCAO_5 => 0
				);

			$rendimento = (int) $desempenho['rendimento'];

			if ($rendimento <= self::$PROPORCAO_1_VALOR)
				$this->faixaRendimentoPorTurma[$tID][self::$PROPORCAO_1]++;
			elseif ($rendimento <= self::$PROPORCAO_2_VALOR)
				$this->faixaRendimentoPorTurma[$tID][self::$PROPORCAO_2]++;
			elseif ($rendimento <= self::$PROPORCAO_3_VALOR)
				$this->faixaRendimentoPorTurma[$tID][self::$PROPORCAO_3]++;
			elseif ($rendimento <= self::$PROPORCAO_4_VALOR)
				$this->faixaRendimentoPorTurma[$tID][self::$PROPORCAO_4]++;
			else
				$this->faixaRendimentoPorTurma[$tID][self::$PROPORCAO_5]++;
		}

		$this->faixaRendimentoPorTurmaAbs = $this->faixaRendimentoPorTurma;
		foreach($this->faixaRendimentoPorTurma as $tID => &$proporcao) {
			$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
		}
	}

	public function calcularFaixaRendimentoPorSerie () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->faixaRendimentoPorSerie[$sID]))
				$this->faixaRendimentoPorSerie[$sID] = array(
					self::$PROPORCAO_1 => 0,
					self::$PROPORCAO_2 => 0,
					self::$PROPORCAO_3 => 0,
					self::$PROPORCAO_4 => 0,
					self::$PROPORCAO_5 => 0
				);

			$rendimento = (int) $desempenho['rendimento'];

			if ($rendimento <= self::$PROPORCAO_1_VALOR)
				$this->faixaRendimentoPorSerie[$sID][self::$PROPORCAO_1]++;
			elseif ($rendimento <= self::$PROPORCAO_2_VALOR)
				$this->faixaRendimentoPorSerie[$sID][self::$PROPORCAO_2]++;
			elseif ($rendimento <= self::$PROPORCAO_3_VALOR)
				$this->faixaRendimentoPorSerie[$sID][self::$PROPORCAO_3]++;
			elseif ($rendimento <= self::$PROPORCAO_4_VALOR)
				$this->faixaRendimentoPorSerie[$sID][self::$PROPORCAO_4]++;
			else
				$this->faixaRendimentoPorSerie[$sID][self::$PROPORCAO_5]++;
		}

		$this->faixaRendimentoPorSerieAbs = $this->faixaRendimentoPorSerie;
		foreach($this->faixaRendimentoPorSerie as $sID => &$proporcao) {
			$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
		}
	}

	public function calcularHabilidadesProporcoesPorTurma () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoPorHabilidade as $iID => $rendPorHab ) {
			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->habilidadesProporcoesPorTurma[$tID]))
				$this->habilidadesProporcoesPorTurma[$tID] = array();

			foreach($rendPorHab as $hID => $desempenho) {
				if (!isset($this->habilidadesProporcoesPorTurma[$tID][$hID]))
					$this->habilidadesProporcoesPorTurma[$tID][$hID] = array(
						self::$HAB_ADQUIRIDA => 0,
						self::$HAB_EM_AQUISICAO => 0,
						self::$HAB_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > self::$HAB_EM_AQUISICAO_VALOR)
					$this->habilidadesProporcoesPorTurma[$tID][$hID][self::$HAB_ADQUIRIDA]++;
				elseif ($rendimento > self::$HAB_NAO_ADQUIRIDA_VALOR)
					$this->habilidadesProporcoesPorTurma[$tID][$hID][self::$HAB_EM_AQUISICAO]++;
				else
					$this->habilidadesProporcoesPorTurma[$tID][$hID][self::$HAB_NAO_ADQUIRIDA]++;
			}
		}

		$this->habilidadesProporcoesPorTurmaAbs = $this->habilidadesProporcoesPorTurma;
		foreach($this->habilidadesProporcoesPorTurma as $tID => &$proporcoes) {
			foreach($proporcoes as $hID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularHabilidadesProporcoesPorSerie () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoPorHabilidade as $iID => $rendPorHab ) {
			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->habilidadesProporcoesPorSerie[$sID]))
				$this->habilidadesProporcoesPorSerie[$sID] = array();

			foreach($rendPorHab as $hID => $desempenho) {
				if (!isset($this->habilidadesProporcoesPorSerie[$sID][$hID]))
					$this->habilidadesProporcoesPorSerie[$sID][$hID] = array(
						self::$HAB_ADQUIRIDA => 0,
						self::$HAB_EM_AQUISICAO => 0,
						self::$HAB_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > self::$HAB_EM_AQUISICAO_VALOR)
					$this->habilidadesProporcoesPorSerie[$sID][$hID][self::$HAB_ADQUIRIDA]++;
				elseif ($rendimento > self::$HAB_NAO_ADQUIRIDA_VALOR)
					$this->habilidadesProporcoesPorSerie[$sID][$hID][self::$HAB_EM_AQUISICAO]++;
				else
					$this->habilidadesProporcoesPorSerie[$sID][$hID][self::$HAB_NAO_ADQUIRIDA]++;
			}
		}

		$this->habilidadesProporcoesPorSerieAbs = $this->habilidadesProporcoesPorSerie;
		foreach($this->habilidadesProporcoesPorSerie as $sID => &$proporcoes) {
			foreach($proporcoes as $hID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularQuestoesProporcoesPorTurma () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoPorQuestao as $iID => $rendPorQuestao ) {
			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->questoesProporcoesPorTurma[$tID]))
				$this->questoesProporcoesPorTurma[$tID] = array();

			foreach($rendPorQuestao as $hID => $desempenho) {
				if (!isset($this->questoesProporcoesPorTurma[$tID][$hID]))
					$this->questoesProporcoesPorTurma[$tID][$hID] = array(
						self::$QUESTAO_ADQUIRIDA => 0,
						self::$QUESTAO_EM_AQUISICAO => 0,
						self::$QUESTAO_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > self::$QUESTAO_EM_AQUISICAO_VALOR)
					$this->questoesProporcoesPorTurma[$tID][$hID][self::$QUESTAO_ADQUIRIDA]++;
				elseif ($rendimento > self::$QUESTAO_NAO_ADQUIRIDA_VALOR)
					$this->questoesProporcoesPorTurma[$tID][$hID][self::$QUESTAO_EM_AQUISICAO]++;
				else
					$this->questoesProporcoesPorTurma[$tID][$hID][self::$QUESTAO_NAO_ADQUIRIDA]++;
			}
		}

		$this->questoesProporcoesPorTurmaAbs = $this->questoesProporcoesPorTurma;
		foreach($this->questoesProporcoesPorTurma as $tID => &$proporcoes) {
			foreach($proporcoes as $hID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularQuestoesProporcoesPorSerie () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoPorQuestao as $iID => $rendPorQuestao ) {
			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->questoesProporcoesPorSerie[$sID]))
				$this->questoesProporcoesPorSerie[$sID] = array();

			foreach($rendPorQuestao as $hID => $desempenho) {
				if (!isset($this->questoesProporcoesPorSerie[$sID][$hID]))
					$this->questoesProporcoesPorSerie[$sID][$hID] = array(
						self::$QUESTAO_ADQUIRIDA => 0,
						self::$QUESTAO_EM_AQUISICAO => 0,
						self::$QUESTAO_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > self::$QUESTAO_EM_AQUISICAO_VALOR)
					$this->questoesProporcoesPorSerie[$sID][$hID][self::$QUESTAO_ADQUIRIDA]++;
				elseif ($rendimento > self::$QUESTAO_NAO_ADQUIRIDA_VALOR)
					$this->questoesProporcoesPorSerie[$sID][$hID][self::$QUESTAO_EM_AQUISICAO]++;
				else
					$this->questoesProporcoesPorSerie[$sID][$hID][self::$QUESTAO_NAO_ADQUIRIDA]++;
			}
		}

		$this->questoesProporcoesPorSerieAbs = $this->questoesProporcoesPorSerie;
		foreach($this->questoesProporcoesPorSerie as $sID => &$proporcoes) {
			foreach($proporcoes as $hID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularConteudosProporcoesPorTurma () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoPorConteudo as $iID => $rendPorCont ) {
			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->conteudosProporcoesPorTurma[$tID]))
				$this->conteudosProporcoesPorTurma[$tID] = array();

			foreach($rendPorCont as $cID => $desempenho) {
				if (!isset($this->conteudosProporcoesPorTurma[$tID][$cID]))
					$this->conteudosProporcoesPorTurma[$tID][$cID] = array(
						self::$HAB_ADQUIRIDA => 0,
						self::$HAB_EM_AQUISICAO => 0,
						self::$HAB_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > self::$HAB_EM_AQUISICAO_VALOR)
					$this->conteudosProporcoesPorTurma[$tID][$cID][self::$HAB_ADQUIRIDA]++;
				elseif ($rendimento > self::$HAB_NAO_ADQUIRIDA_VALOR)
					$this->conteudosProporcoesPorTurma[$tID][$cID][self::$HAB_EM_AQUISICAO]++;
				else
					$this->conteudosProporcoesPorTurma[$tID][$cID][self::$HAB_NAO_ADQUIRIDA]++;
			}
		}

		$this->conteudosProporcoesPorTurmaAbs = $this->conteudosProporcoesPorTurma;
		foreach($this->conteudosProporcoesPorTurma as $tID => &$proporcoes) {
			foreach($proporcoes as $cID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularConteudosProporcoesPorSerie () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoPorConteudo as $iID => $rendPorCont ) {
			$sID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterSerie()->obterID();

			if (!isset($this->conteudosProporcoesPorSerie[$sID]))
				$this->conteudosProporcoesPorSerie[$sID] = array();

			foreach($rendPorCont as $cID => $desempenho) {
				if (!isset($this->conteudosProporcoesPorSerie[$sID][$cID]))
					$this->conteudosProporcoesPorSerie[$sID][$cID] = array(
						self::$HAB_ADQUIRIDA => 0,
						self::$HAB_EM_AQUISICAO => 0,
						self::$HAB_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > self::$HAB_EM_AQUISICAO_VALOR)
					$this->conteudosProporcoesPorSerie[$sID][$cID][self::$HAB_ADQUIRIDA]++;
				elseif ($rendimento > self::$HAB_NAO_ADQUIRIDA_VALOR)
					$this->conteudosProporcoesPorSerie[$sID][$cID][self::$HAB_EM_AQUISICAO]++;
				else
					$this->conteudosProporcoesPorSerie[$sID][$cID][self::$HAB_NAO_ADQUIRIDA]++;
			}
		}

		$this->conteudosProporcoesPorSerieAbs = $this->conteudosProporcoesPorSerie;
		foreach($this->conteudosProporcoesPorSerie as $sID => &$proporcoes) {
			foreach($proporcoes as $cID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	/*
	public function calcularRendimentoPorTurmaPorHabilidade () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			// não entra na média se for portador de necessidades especiais
			if ($this->inscritos[$iID]->obterAluno()->obterPortadorNecessidade())
				continue;

			$tID = @$this->inscritos[$iID]->obterAluno()->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorHabilidade[$tID]))
				$this->rendimentoPorTurmaPorHabilidade[$tID] = array();

			foreach ($this->questoesPorHabilidades as $hID => $questoesPorHabilidade) {
				if (!isset($this->rendimentoPorTurmaPorHabilidade[$tID][$hID]))
					$this->rendimentoPorTurmaPorHabilidade[$tID][$hID] = array('total_pontos' => 0, 'rendimento' => 0, 'pontos_questao' => 0);

				foreach ($questoesPorHabilidade as $qID) {
					$this->rendimentoPorTurmaPorHabilidade[$tID][$hID]['pontos_questao'] += $this->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
					$this->rendimentoPorTurmaPorHabilidade[$tID][$hID]['total_pontos'] += $this->rendimentoPorQuestao[$iID][$qID]['pontos'];
					$this->rendimentoPorTurmaPorHabilidade[$tID][$hID]['rendimento'] += $this->rendimentoPorQuestao[$iID][$qID]['rendimento'] * $this->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
				}

				if ($this->rendimentoPorTurmaPorHabilidade[$tID][$hID]['pontos_questao'] > 0)
					$this->rendimentoPorTurmaPorHabilidade[$tID][$hID]['rendimento'] /= $this->rendimentoPorTurmaPorHabilidade[$tID][$hID]['pontos_questao'];
			}
		}
	}
	*/

	public function calcularColocacaoDosInscritos () {
		if ( $this->simulado->obterID() == null )
			return false;

		$colocacaoTemporaria = $this->rendimentoGlobal;
		foreach ( $colocacaoTemporaria as $iID => $desempenho ) {
			$colocacaoTemporaria[$iID]['iID'] = $iID;
		}

		usort($colocacaoTemporaria, array($this, '_ajustarColocacao'));

		$i = 0;
		$ultimoRendimento = 0;
		foreach ( $colocacaoTemporaria as $desempenho ) {
			if ( $desempenho['rendimento'] != $ultimoRendimento )
				$i++;

			$this->colocacao[ $desempenho['iID'] ] = $i;

			$ultimoRendimento = $desempenho['rendimento'];
		}
	}

	public function obterColocacaoCustomizada ($colocacaoCustomizada) {
		$colocacaoFinal = array();

		if ( $this->simulado->obterID() == null || !is_array($colocacaoCustomizada) )
			return $colocacaoFinal;

		usort($colocacaoCustomizada, array($this, '_ajustarColocacaoCustomizada'));

		$i = 0;
		$ultimaColocacao = 0;
		foreach ( $colocacaoCustomizada as $colocacao ) {
			if ( !isset($colocacao['colocacao']) || !isset($colocacao['iID']) )
				continue;

			if ( $colocacao['colocacao'] != $ultimaColocacao )
				$i++;

			$colocacaoFinal[ $colocacao['iID'] ] = $i;

			$ultimaColocacao = $colocacao['colocacao'];
		}

		return $colocacaoFinal;
	}

	public function eliminarRendimentosNulos () {
		if ( $this->simulado->obterID() == null )
			return false;

		foreach ( $this->rendimentoGlobal as $iID => $desempenho ) {
			if ( $desempenho['rendimento'] == 0 ) {
				$this->desistencias[self::DESISTENCIA_RENDIMENTO_NULO][] = $iID;
				$this->_removerInscricaoPeloID($iID);
			}
		}
	}

	public function limitarInscritosPorTurmas ($turmasIDs) {
		if ( !is_array($turmasIDs) )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$tID = @$inscricao->obterAluno()->obterTurma()->obterID();
			if ( !in_array($tID, $turmasIDs) )
				$this->_removerInscricaoPeloID($iID);
		}
	}

	public function limitarInscritosPorAlunos ($alunosIDs) {
		if ( !is_array($alunosIDs) )
			return false;

		foreach ( $this->inscritos as $iID => &$inscricao ) {
			$aID = $inscricao->obterAluno()->obterID();
			if ( !in_array($aID, $alunosIDs) )
				$this->_removerInscricaoPeloID($iID);
		}
	}

	public function fixarSimulado (Simulado &$simulado) {
		$this->_resetarParametros();
		$this->simulado = $simulado;

		$this->simuladoQuestoes = $simulado->obterQuestoesParaFormulario(false, true, true);

		foreach ( $this->simuladoQuestoes as $fase => $tipos ) {
			foreach ( $tipos as $tipo => $questoesNumeradas ) {
				foreach ( $questoesNumeradas as $numero => $questoesNumeradas ) {
					foreach ( $questoesNumeradas as &$q ) {
						if ( !isset($this->simuladoQuestoesPorID[ $q->obterID() ]) ) {
							$this->simuladoQuestoesPorID[ $q->obterID() ] = $q;

							foreach ($q->obterHabilidades() as $habilidade) {
								if (!isset( $this->questoesPorHabilidades[$habilidade->obterID()] ))
									$this->questoesPorHabilidades[$habilidade->obterID()] = array();

								$this->questoesPorHabilidades[$habilidade->obterID()][] = $q->obterID();
							}

							foreach ($q->obterConteudos() as $conteudo) {
								if (!isset( $this->questoesPorConteudo[$conteudo->obterID()] ))
									$this->questoesPorConteudo[$conteudo->obterID()] = array();

								$this->questoesPorConteudo[$conteudo->obterID()][] = $q->obterID();
							}
						}
					}
				}
			}
		}

		$this->macroHabilidades = Habilidade::obterArrayMacroHabilidadesParaFormulario(FALSE, $this->simulado->obterInstituicao());

		foreach (Habilidade::obterArrayHabilidadesParaFormulario(new Serie(null), TRUE, $this->simulado->obterInstituicao()) as $hID => $habilidade) {
			$this->nomesHabilidades[$hID] = $habilidade[0].(isset($this->macroHabilidades[$hID]) ? ' (matriz)' : '');
			$this->descricoesHabilidades[$hID] = $habilidade[1];
		}

		asort($this->nomesHabilidades, SORT_LOCALE_STRING);

		$nomesHabilidades = array();
		foreach ($this->nomesHabilidades as $hID => $hNome) {
			if (isset($this->macroHabilidades[$hID]))
				$nomesHabilidades[$hID] = $hNome;
		}

		foreach ($this->nomesHabilidades as $hID => $hNome) {
			if (!isset($this->macroHabilidades[$hID]))
				$nomesHabilidades[$hID] = $hNome;
		}

		$this->nomesHabilidades = $nomesHabilidades;

		$questoesPorHabilidades = array();

		foreach($this->nomesHabilidades as $hID => $hNome) {
			if (isset($this->questoesPorHabilidades[$hID]))
				$questoesPorHabilidades[$hID] = $this->questoesPorHabilidades[$hID];
		}

		$this->questoesPorHabilidades = $questoesPorHabilidades;

		for ($i = 1; $i <= $this->simulado->obterDuracao(); $i++) {
			$this->desistencias[ self::DESISTENCIA_NAO_COMPARECEU_EM_TODAS_AS_FASES ][ $i ] = array();
			$this->participantesPorFase[$i] = array();
		}

		foreach (Conteudo::obterArrayConteudosParaFormulario($this->simulado->obterSerie(), $this->simulado->obterDisciplina(), $this->simulado->obterInstituicao()) as $cID => $conteudo) {
			$this->nomesConteudos[$cID] = $conteudo;
		}

		asort($this->nomesConteudos, SORT_LOCALE_STRING);

		$questoesPorConteudo = array();

		foreach($this->nomesConteudos as $cID => $cNome) {
			if (isset($this->questoesPorConteudo[$cID]))
				$questoesPorConteudo[$cID] = $this->questoesPorConteudo[$cID];
		}

		$this->questoesPorConteudo = $questoesPorConteudo;
	}

	public function &obterDesistencias ($resultadoQuantitativo = false) {
		if ( !$resultadoQuantitativo )
			return $this->desistencias;

		$desistencias = array();
		foreach ( $this->desistencias as $tipo => $desistencia ) {
			if ( $tipo != self::DESISTENCIA_NAO_COMPARECEU_EM_TODAS_AS_FASES ) {
				$desistencias[$tipo] = count($desistencia);
			} else {
				foreach ( $desistencia as $fase => $desistenciaPorFase ) {
					@$desistencias[$tipo] += count($desistenciaPorFase);
				}
			}
		}

		return $desistencias;
	}

	public function &obterParticipantesPorFase ($resultadoQuantitativo = false) {
		if ( !$resultadoQuantitativo )
			return $this->participantesPorFase;

		$participantes = array();
		foreach ( $this->participantesPorFase as $fase => $participantesPorFase ) {
			@$participantes[$fase] += count($participantesPorFase);
		}

		return $participantes;
	}

	public function &obterSimulado () {
		return $this->simulado;
	}

	public function &obterInscritos () {
		return $this->inscritos;
	}

	public function &obterRendimentoPorQuestao () {
		return $this->rendimentoPorQuestao;
	}

	public function &obterRendimentoGlobal () {
		return $this->rendimentoGlobal;
	}

	public function &obterColocacaoDosInscritos () {
		return $this->colocacao;
	}

	public function &obterRespostasDosInscritos () {
		return $this->respostas;
	}

	public function &obterQuestoesDoSimulado ($porID = false) {
		if ( !$porID )
			return $this->simuladoQuestoes;
		else
			return $this->simuladoQuestoesPorID;
	}

	public function obterSerie () {
		return $this->simulado->obterSerie();
	}

	public function obterProfessor () {
		return $this->simulado->obterProfessor();
	}

	public function obterDisciplina () {
		return $this->simulado->obterDisciplina();
	}

	public function obterTurmas ($chaves = FALSE) {
		return $this->simulado->obterTurmas($chaves);
	}

	protected function _resetarParametros () {
		$this->simulado = new Simulado(null);
		$this->simuladoQuestoes = array();
		$this->simuladoQuestoesPorID = array();

		$this->inscritos = array();
		$this->respostas = array();

		$this->nomesTurmas = array();
		$this->nomesSeries = array();
		$this->nomesHabilidades = array();
		$this->descricoesHabilidades = array();
		$this->questoesPorHabilidades = array();
		$this->macroHabilidades = array();
		$this->nomesConteudos = array();
		$this->questoesPorConteudo = array();

		$this->colocacao = array();

		$this->rendimentoPorQuestao = array();
		$this->rendimentoGlobal = array();
		$this->rendimentoPorHabilidade = array();
		$this->rendimentoPorConteudo = array();

		$this->rendimentoPorTurma = array();
		$this->rendimentoPorTurmaPorQuestao = array();
		$this->rendimentoPorTurmaPorHabilidade = array();
		$this->rendimentoPorTurmaPorConteudo = array();
		$this->faixaRendimentoPorTurma = array();
		$this->faixaRendimentoPorTurmaAbs = array();
		$this->habilidadesProporcoesPorTurma = array();
		$this->habilidadesProporcoesPorTurmaAbs = array();
		$this->questoesProporcoesPorTurma = array();
		$this->questoesProporcoesPorTurmaAbs = array();
		$this->conteudosProporcoesPorTurma = array();
		$this->conteudosProporcoesPorTurmaAbs = array();

		$this->rendimentoPorSerie = array();
		$this->rendimentoPorSeriePorQuestao = array();
		$this->rendimentoPorSeriePorHabilidade = array();
		$this->rendimentoPorSeriePorConteudo = array();
		$this->faixaRendimentoPorSerie = array();
		$this->faixaRendimentoPorSerieAbs = array();
		$this->habilidadesProporcoesPorSerie = array();
		$this->habilidadesProporcoesPorSerieAbs = array();
		$this->questoesProporcoesPorSerie = array();
		$this->questoesProporcoesPorSerieAbs = array();
		$this->conteudosProporcoesPorSerie = array();
		$this->conteudosProporcoesPorSerieAbs = array();

		$this->desistencias = array(   self::DESISTENCIA_NAO_COMPARECEU => array(),
										self::DESISTENCIA_NAO_COMPARECEU_EM_TODAS_AS_FASES => array(),
										self::DESISTENCIA_TIPO_PROVA_INVALIDA => array(),
										self::DESISTENCIA_LINGUA_ESTRANGEIRA_INVALIDA => array(),
										self::DESISTENCIA_RENDIMENTO_NULO => array()   );
	}

	protected function _removerInscricaoPeloID ($iID) {
		unset($this->inscritos[$iID]);
		unset($this->respostas[$iID]);
		unset($this->rendimentoPorQuestao[$iID]);
		unset($this->rendimentoPorHabilidade[$iID]);
		unset($this->rendimentoPorConteudo[$iID]);
		unset($this->rendimentoGlobal[$iID]);
		unset($this->colocacao[$iID]);
	}

	static public function obterDescricaoDesistencia ($tipo) {
		switch ( $tipo ) {
			case self::DESISTENCIA_NAO_COMPARECEU:
				return 'Não compareceu';
			case self::DESISTENCIA_NAO_COMPARECEU_EM_TODAS_AS_FASES:
				return 'Não compareceu em todas as fases';
			case self::DESISTENCIA_TIPO_PROVA_INVALIDA:
				return 'Tipo de prova inválido';
			case self::DESISTENCIA_LINGUA_ESTRANGEIRA_INVALIDA:
				return 'Língua estrangeira inválida';
			case self::DESISTENCIA_RENDIMENTO_NULO:
				return 'Todas as questões em branco';
			default:
				return 'Tipo de desistência desconhecido';
		}
	}

	protected function _ajustarColocacao ($a, $b) {
		if ( $a['rendimento'] == $b['rendimento'] )
			return 0;

		return $b['rendimento'] > $a['rendimento'] ? 1 : -1;
	}

	protected function _ajustarColocacaoCustomizada ($a, $b) {
		if ( !isset( $a['colocacao'] ) )
			return 0;

		if ( $a['colocacao'] == $b['colocacao'] )
			return 0;

		return $a['colocacao'] > $b['colocacao'] ? 1 : -1;
	}
}

?>