<?
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);

include_once 'AnalizadorSimulado.php';

class AnalizadorSimulados
{
	public $analisadores;

	public $alunos;
	public $alunosInscricoes;

	public $nomesTurmas;
	public $nomesSeries;
	public $nomesHabilidades;
	public $descricoesHabilidades;
	public $macroHabilidades;
	public $nomesDisciplinas;
	public $nomesDisciplinasPequenos;

	public $rendimentoGlobalAluno;

	public $rendimentoAlunoPorDisciplina;
	public $rendimentoAlunoPorHabilidade;
	public $rendimentoAlunoPorHabilidadeNasDisciplinas;

	public $rendimentoGlobalPorTurma;
	public $rendimentoPorTurmaPorDisciplina;
	public $rendimentoProporcoesPorTurmaPorDisciplina;
	public $rendimentoProporcoesPorTurmaPorDisciplinaAbs;
	public $habilidadesProporcoesPorTurma;
	public $habilidadesProporcoesPorTurmaAbs;
	public $habilidadesProporcoesNasDisciplinasPorTurma;
	public $habilidadesProporcoesNasDisciplinasPorTurmaAbs;

	public $rendimentoGlobalPorSerie;
	public $rendimentoPorSeriePorDisciplina;
	public $rendimentoProporcoesPorSeriePorDisciplina;
	public $rendimentoProporcoesPorSeriePorDisciplinaAbs;
	public $habilidadesProporcoesPorSerie;
	public $habilidadesProporcoesPorSerieAbs;
	public $habilidadesProporcoesNasDisciplinasPorSerie;
	public $habilidadesProporcoesNasDisciplinasPorSerieAbs;

	public function __construct () {
		$this->_resetarParametros();
	}

	public function carregarInscritos () {
		foreach($this->analisadores as $analisador) {
			$analisador->carregarInscritos();

			foreach($analisador->nomesTurmas as $tID => $tNome) {
				if (!isset($this->nomesTurmas[$tID]))
					$this->nomesTurmas[$tID] = $tNome;
			}

			foreach($analisador->nomesSeries as $sID => $sNome) {
				if (!isset($this->nomesSeries[$sID]))
					$this->nomesSeries[$sID] = $sNome;
			}
		}

		asort($this->nomesTurmas, SORT_LOCALE_STRING);
		asort($this->nomesSeries, SORT_LOCALE_STRING);

		// carrega alunos
		foreach($this->analisadores as $analisador) {
			foreach($analisador->inscritos as $iID => $inscricao) {
				$aID = $inscricao->obterAluno()->obterID();

				if (!isset($this->alunos[$aID])) {
					$this->alunos[$aID] = $inscricao->obterAluno();
					$this->alunosInscricoes[$aID] = array();
				}

				$this->alunosInscricoes[$aID][$iID] = TRUE;
			}
		}
	}

	public function carregarRespostasDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->carregarRespostasDosInscritos();
	}

	public function analisarTiposDeProvasELinguasDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->analisarTiposDeProvasELinguasDosInscritos();
	}

	public function analisarRespostasDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->analisarRespostasDosInscritos();
	}

	public function calcularRendimentoPorQuestaoDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoPorQuestaoDosInscritos();
	}

	public function calcularRendimentoGlobalDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoGlobalDosInscritos();
	}

	public function calcularRendimentoPorHabilidadeDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoPorHabilidadeDosInscritos();
	}

	public function calcularRendimentoGlobalAluno () {
		foreach($this->analisadores as $analisador) {
			foreach ( $analisador->inscritos as $iID => &$inscricao ) {
				$aID = @$inscricao->obterAluno()->obterID();

				if (!isset($this->rendimentoGlobalAluno[$aID]))
					$this->rendimentoGlobalAluno[$aID] = array(
							'total_pontos' => 0,
							'rendimento' => 0,
							'pontos_questao' => 0,
							'rendimento_total' => 0
					);

				foreach ( $analisador->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
					$this->rendimentoGlobalAluno[$aID]['pontos_questao'] += $desempenho['pontos_questao'];
					$this->rendimentoGlobalAluno[$aID]['total_pontos'] += $desempenho['pontos'];
					$this->rendimentoGlobalAluno[$aID]['rendimento'] += $desempenho['rendimento'] * $desempenho['pontos_questao'];
				}
			}
		}

		foreach ( $this->rendimentoGlobalAluno as $aID => $desempenho) {
			$this->rendimentoGlobalAluno[$aID]['rendimento_total'] = $desempenho['rendimento'];

			if ( $desempenho['pontos_questao'] > 0 )
				$this->rendimentoGlobalAluno[$aID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
		}
	}

	public function calcularRendimentoGlobalPorTurma () {
		foreach ( $this->rendimentoGlobalAluno as $aID => $desempenho ) {
			$tID = @$this->alunos[$aID]->obterTurma()->obterID();

			if (!isset($this->rendimentoGlobalPorTurma[$tID]))
				$this->rendimentoGlobalPorTurma[$tID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => 0,
					'alunos' => 0
				);

			$this->rendimentoGlobalPorTurma[$tID]['pontos_questao'] += $desempenho['pontos_questao'];
			$this->rendimentoGlobalPorTurma[$tID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoGlobalPorTurma[$tID]['rendimento'] += $desempenho['rendimento_total'];
			$this->rendimentoGlobalPorTurma[$tID]['alunos']++;
		}

		foreach ( $this->rendimentoGlobalPorTurma as $tID => $desempenho) {
			if ( $this->rendimentoGlobalPorTurma[$tID]['pontos_questao'] > 0 )
				$this->rendimentoGlobalPorTurma[$tID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
				$this->rendimentoGlobalPorTurma[$tID]['total_pontos'] = $desempenho['total_pontos'] / $desempenho['alunos'];
		}
	}

	public function calcularRendimentoGlobalPorSerie () {
		foreach ( $this->rendimentoGlobalAluno as $aID => $desempenho ) {
			$sID = @$this->alunos[$aID]->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoGlobalPorSerie[$sID]))
				$this->rendimentoGlobalPorSerie[$sID] = array(
					'total_pontos' => 0,
					'rendimento' => 0,
					'pontos_questao' => 0,
					'alunos' => 0
				);

			$this->rendimentoGlobalPorSerie[$sID]['pontos_questao'] += $desempenho['pontos_questao'];
			$this->rendimentoGlobalPorSerie[$sID]['total_pontos'] += $desempenho['total_pontos'];
			$this->rendimentoGlobalPorSerie[$sID]['rendimento'] += $desempenho['rendimento_total'];
			$this->rendimentoGlobalPorSerie[$sID]['alunos']++;
		}

		foreach ( $this->rendimentoGlobalPorSerie as $sID => $desempenho) {
			if ( $this->rendimentoGlobalPorSerie[$sID]['pontos_questao'] > 0 ) {
				$this->rendimentoGlobalPorSerie[$sID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
				$this->rendimentoGlobalPorSerie[$sID]['total_pontos'] = $desempenho['total_pontos'] / $desempenho['alunos'];
			}
		}
	}

	public function calcularRendimentoAlunoPorDisciplina () {
		foreach($this->analisadores as $analisador) {
			foreach ( $analisador->inscritos as $iID => &$inscricao ) {
				$aID = @$inscricao->obterAluno()->obterID();

				if (!isset($this->rendimentoAlunoPorDisciplina[$aID]))
					$this->rendimentoAlunoPorDisciplina[$aID] = array();

				foreach ( $analisador->rendimentoPorQuestao[$iID] as $qID => $desempenho ) {
					$dID = $analisador->simulado->obterDisciplina()->obterID();

					if (!isset($this->rendimentoAlunoPorDisciplina[$aID][$dID]))
						$this->rendimentoAlunoPorDisciplina[$aID][$dID] = array(
							'total_pontos' => 0,
							'rendimento' => 0,
							'pontos_questao' => 0,
							'rendimento_total' => 0
						);

					$this->rendimentoAlunoPorDisciplina[$aID][$dID]['pontos_questao'] += $desempenho['pontos_questao'];
					$this->rendimentoAlunoPorDisciplina[$aID][$dID]['total_pontos'] += $desempenho['pontos'];
					$this->rendimentoAlunoPorDisciplina[$aID][$dID]['rendimento'] += $desempenho['rendimento'] * $desempenho['pontos_questao'];
				}
			}
		}

		foreach ( $this->rendimentoAlunoPorDisciplina as $aID => $desempenhoPorDisciplina) {
			foreach ( $desempenhoPorDisciplina as $dID => $desempenho ) {
				$this->rendimentoAlunoPorDisciplina[$aID][$dID]['rendimento_total'] = $desempenho['rendimento'];

				if ( $desempenho['pontos_questao'] > 0 )
					$this->rendimentoAlunoPorDisciplina[$aID][$dID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
			}
		}
	}

	public function calcularRendimentoAlunoPorHabilidade () {
		foreach($this->analisadores as $analisador) {
			foreach ( $analisador->inscritos as $iID => &$inscricao ) {
				$aID = @$inscricao->obterAluno()->obterID();

				if (!isset($this->rendimentoAlunoPorHabilidade[$aID]))
					$this->rendimentoAlunoPorHabilidade[$aID] = array();

				foreach ($analisador->questoesPorHabilidades as $hID => $questoesPorHabilidade) {
					if (!isset($this->macroHabilidades[$hID]))
						continue;

					if (!isset($this->rendimentoAlunoPorHabilidade[$aID][$hID]))
						$this->rendimentoAlunoPorHabilidade[$aID][$hID] = array(
							'total_pontos' => 0,
							'rendimento' => 0,
							'pontos_questao' => 0,
							'rendimento_total' => 0
						);

					foreach ($questoesPorHabilidade as $qID) {
						$this->rendimentoAlunoPorHabilidade[$aID][$hID]['pontos_questao'] += $analisador->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
						$this->rendimentoAlunoPorHabilidade[$aID][$hID]['total_pontos'] += $analisador->rendimentoPorQuestao[$iID][$qID]['pontos'];
						$this->rendimentoAlunoPorHabilidade[$aID][$hID]['rendimento'] += $analisador->rendimentoPorQuestao[$iID][$qID]['rendimento'] * $analisador->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
					}
				}
			}
		}

		foreach ( $this->rendimentoAlunoPorHabilidade as $aID => $desempenhoPorHabilidade) {
			foreach ( $desempenhoPorHabilidade as $hID => $desempenho ) {
				$this->rendimentoAlunoPorHabilidade[$aID][$hID]['rendimento_total'] = $desempenho['rendimento'];

				if ( $desempenho['pontos_questao'] > 0 )
					$this->rendimentoAlunoPorHabilidade[$aID][$hID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
			}
		}
	}

	public function calcularRendimentoAlunoPorHabilidadeNasDisciplinas () {
		foreach($this->analisadores as $analisador) {
			$dID = $analisador->simulado->obterDisciplina()->obterID();

			foreach ( $analisador->inscritos as $iID => &$inscricao ) {
				$aID = @$inscricao->obterAluno()->obterID();

				if (!isset($this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID]))
					$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID] = array();

				if (!isset($this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID]))
					$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID] = array();

				foreach ($analisador->questoesPorHabilidades as $hID => $questoesPorHabilidade) {
					if (!isset($this->macroHabilidades[$hID]))
						continue;

					if (!isset($this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID]))
						$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID] = array(
							'total_pontos' => 0,
							'rendimento' => 0,
							'pontos_questao' => 0,
							'rendimento_total' => 0
						);

					foreach ($questoesPorHabilidade as $qID) {
						$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID]['pontos_questao'] += $analisador->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
						$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID]['total_pontos'] += $analisador->rendimentoPorQuestao[$iID][$qID]['pontos'];
						$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID]['rendimento'] += $analisador->rendimentoPorQuestao[$iID][$qID]['rendimento'] * $analisador->rendimentoPorQuestao[$iID][$qID]['pontos_questao'];
					}
				}
			}
		}

		foreach ( $this->rendimentoAlunoPorHabilidadeNasDisciplinas as $aID => $desempenhoPorDisciplina) {
			foreach ( $desempenhoPorDisciplina as $dID => $desempenhoPorHabilidade ) {
				foreach ( $desempenhoPorHabilidade as $hID => $desempenho ) {
					$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID]['rendimento_total'] = $desempenho['rendimento'];

					if ( $desempenho['pontos_questao'] > 0 )
						$this->rendimentoAlunoPorHabilidadeNasDisciplinas[$aID][$dID][$hID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
				}
			}
		}
	}

	public function calcularRendimentoPorTurmaPorDisciplina () {
		foreach ( $this->rendimentoAlunoPorDisciplina as $aID => $rendPorDisciplina ) {
			$tID = @$this->alunos[$aID]->obterTurma()->obterID();

			if (!isset($this->rendimentoPorTurmaPorDisciplina[$tID]))
				$this->rendimentoPorTurmaPorDisciplina[$tID] = array();

			foreach ( $rendPorDisciplina as $dID => $desempenho ) {
				if (!isset($this->rendimentoPorTurmaPorDisciplina[$tID][$dID]))
					$this->rendimentoPorTurmaPorDisciplina[$tID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => 0
					);

				$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['pontos_questao'] += $desempenho['pontos_questao'];
				$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['total_pontos'] += $desempenho['total_pontos'];
				$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['rendimento'] += $desempenho['rendimento_total'];
			}
		}

		foreach ( $this->rendimentoPorTurmaPorDisciplina as $tID => $desempenhoPorTurma) {
			foreach ( $desempenhoPorTurma as $dID => $desempenho ) {
				if ( $this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['pontos_questao'] > 0 )
					$this->rendimentoPorTurmaPorDisciplina[$tID][$dID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
			}
		}
	}

	public function calcularRendimentoPorSeriePorDisciplina () {
		foreach ( $this->rendimentoAlunoPorDisciplina as $aID => $rendPorDisciplina ) {
			$sID = @$this->alunos[$aID]->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoPorSeriePorDisciplina[$sID]))
				$this->rendimentoPorSeriePorDisciplina[$sID] = array();

			foreach ( $rendPorDisciplina as $dID => $desempenho ) {
				if (!isset($this->rendimentoPorSeriePorDisciplina[$sID][$dID]))
					$this->rendimentoPorSeriePorDisciplina[$sID][$dID] = array(
						'total_pontos' => 0,
						'rendimento' => 0,
						'pontos_questao' => 0
					);

				$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['pontos_questao'] += $desempenho['pontos_questao'];
				$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['total_pontos'] += $desempenho['total_pontos'];
				$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['rendimento'] += $desempenho['rendimento_total'];
			}
		}

		foreach ( $this->rendimentoPorSeriePorDisciplina as $sID => $desempenhoPorSerie) {
			foreach ( $desempenhoPorSerie as $dID => $desempenho ) {
				if ( $this->rendimentoPorSeriePorDisciplina[$sID][$dID]['pontos_questao'] > 0 )
					$this->rendimentoPorSeriePorDisciplina[$sID][$dID]['rendimento'] = $desempenho['rendimento'] / $desempenho['pontos_questao'];
			}
		}
	}

	public function calcularRendimentoProporcoesPorTurmaPorDisciplina () {
		foreach ( $this->rendimentoAlunoPorDisciplina as $aID => $rendPorDisciplina ) {
			$tID = @$this->alunos[$aID]->obterTurma()->obterID();

			if (!isset($this->rendimentoProporcoesPorTurmaPorDisciplina[$tID]))
				$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID] = array();

			foreach ( $rendPorDisciplina as $dID => $desempenho ) {
				if (!isset($this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID]))
					$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID] = array(
						AnalizadorSimulado::$PROPORCAO_1 => 0,
						AnalizadorSimulado::$PROPORCAO_2 => 0,
						AnalizadorSimulado::$PROPORCAO_3 => 0,
						AnalizadorSimulado::$PROPORCAO_4 => 0,
						AnalizadorSimulado::$PROPORCAO_5 => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento <= AnalizadorSimulado::$PROPORCAO_1_VALOR)
					$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID][AnalizadorSimulado::$PROPORCAO_1]++;
				elseif ($rendimento <= AnalizadorSimulado::$PROPORCAO_2_VALOR)
					$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID][AnalizadorSimulado::$PROPORCAO_2]++;
				elseif ($rendimento <= AnalizadorSimulado::$PROPORCAO_3_VALOR)
					$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID][AnalizadorSimulado::$PROPORCAO_3]++;
				elseif ($rendimento <= AnalizadorSimulado::$PROPORCAO_4_VALOR)
					$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID][AnalizadorSimulado::$PROPORCAO_4]++;
				else
					$this->rendimentoProporcoesPorTurmaPorDisciplina[$tID][$dID][AnalizadorSimulado::$PROPORCAO_5]++;
			}
		}

		$this->rendimentoProporcoesPorTurmaPorDisciplinaAbs = $this->rendimentoProporcoesPorTurmaPorDisciplina;
		foreach($this->rendimentoProporcoesPorTurmaPorDisciplina as $tID => &$proporcoes) {
			foreach($proporcoes as $dID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularRendimentoProporcoesPorSeriePorDisciplina () {
		foreach ( $this->rendimentoAlunoPorDisciplina as $aID => $rendPorDisciplina ) {
			$sID = @$this->alunos[$aID]->obterTurma()->obterSerie()->obterID();

			if (!isset($this->rendimentoProporcoesPorSeriePorDisciplina[$sID]))
				$this->rendimentoProporcoesPorSeriePorDisciplina[$sID] = array();

			foreach ( $rendPorDisciplina as $dID => $desempenho ) {
				if (!isset($this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID]))
					$this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID] = array(
						AnalizadorSimulado::$PROPORCAO_1 => 0,
						AnalizadorSimulado::$PROPORCAO_2 => 0,
						AnalizadorSimulado::$PROPORCAO_3 => 0,
						AnalizadorSimulado::$PROPORCAO_4 => 0,
						AnalizadorSimulado::$PROPORCAO_5 => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento <= AnalizadorSimulado::$PROPORCAO_1_VALOR)
					$this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID][AnalizadorSimulado::$PROPORCAO_1]++;
				elseif ($rendimento <= AnalizadorSimulado::$PROPORCAO_2_VALOR)
					$this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID][AnalizadorSimulado::$PROPORCAO_2]++;
				elseif ($rendimento <= AnalizadorSimulado::$PROPORCAO_3_VALOR)
					$this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID][AnalizadorSimulado::$PROPORCAO_3]++;
				elseif ($rendimento <= AnalizadorSimulado::$PROPORCAO_4_VALOR)
					$this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID][AnalizadorSimulado::$PROPORCAO_4]++;
				else
					$this->rendimentoProporcoesPorSeriePorDisciplina[$sID][$dID][AnalizadorSimulado::$PROPORCAO_5]++;
			}
		}

		$this->rendimentoProporcoesPorSeriePorDisciplinaAbs = $this->rendimentoProporcoesPorSeriePorDisciplina;
		foreach($this->rendimentoProporcoesPorSeriePorDisciplina as $sID => &$proporcoes) {
			foreach($proporcoes as $dID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularHabilidadesProporcoesPorTurma () {
		foreach ( $this->rendimentoAlunoPorHabilidade as $aID => $rendPorHab ) {
			$tID = @$this->alunos[$aID]->obterTurma()->obterID();

			if (!isset($this->habilidadesProporcoesPorTurma[$tID]))
				$this->habilidadesProporcoesPorTurma[$tID] = array();

			foreach($rendPorHab as $hID => $desempenho) {
				if (!isset($this->habilidadesProporcoesPorTurma[$tID][$hID]))
					$this->habilidadesProporcoesPorTurma[$tID][$hID] = array(
						AnalizadorSimulado::$HAB_ADQUIRIDA => 0,
						AnalizadorSimulado::$HAB_EM_AQUISICAO => 0,
						AnalizadorSimulado::$HAB_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > AnalizadorSimulado::$HAB_EM_AQUISICAO_VALOR)
					$this->habilidadesProporcoesPorTurma[$tID][$hID][AnalizadorSimulado::$HAB_ADQUIRIDA]++;
				elseif ($rendimento > AnalizadorSimulado::$HAB_NAO_ADQUIRIDA_VALOR)
					$this->habilidadesProporcoesPorTurma[$tID][$hID][AnalizadorSimulado::$HAB_EM_AQUISICAO]++;
				else
					$this->habilidadesProporcoesPorTurma[$tID][$hID][AnalizadorSimulado::$HAB_NAO_ADQUIRIDA]++;
			}
		}

		$this->habilidadesProporcoesPorTurmaAbs = $this->habilidadesProporcoesPorTurma;
		foreach($this->habilidadesProporcoesPorTurma as $tID => &$proporcoes) {
			foreach($proporcoes as $hID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularHabilidadesProporcoesPorSerie () {
		foreach ( $this->rendimentoAlunoPorHabilidade as $aID => $rendPorHab ) {
			$sID = @$this->alunos[$aID]->obterTurma()->obterSerie()->obterID();

			if (!isset($this->habilidadesProporcoesPorSerie[$sID]))
				$this->habilidadesProporcoesPorSerie[$sID] = array();

			foreach($rendPorHab as $hID => $desempenho) {
				if (!isset($this->habilidadesProporcoesPorSerie[$sID][$hID]))
					$this->habilidadesProporcoesPorSerie[$sID][$hID] = array(
						AnalizadorSimulado::$HAB_ADQUIRIDA => 0,
						AnalizadorSimulado::$HAB_EM_AQUISICAO => 0,
						AnalizadorSimulado::$HAB_NAO_ADQUIRIDA => 0
					);

				$rendimento = (int) $desempenho['rendimento'];

				if ($rendimento > AnalizadorSimulado::$HAB_EM_AQUISICAO_VALOR)
					$this->habilidadesProporcoesPorSerie[$sID][$hID][AnalizadorSimulado::$HAB_ADQUIRIDA]++;
				elseif ($rendimento > AnalizadorSimulado::$HAB_NAO_ADQUIRIDA_VALOR)
					$this->habilidadesProporcoesPorSerie[$sID][$hID][AnalizadorSimulado::$HAB_EM_AQUISICAO]++;
				else
					$this->habilidadesProporcoesPorSerie[$sID][$hID][AnalizadorSimulado::$HAB_NAO_ADQUIRIDA]++;
			}
		}

		$this->habilidadesProporcoesPorSerieAbs = $this->habilidadesProporcoesPorSerie;
		foreach($this->habilidadesProporcoesPorSerie as $sID => &$proporcoes) {
			foreach($proporcoes as $hID => &$proporcao) {
				$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
			}
		}
	}

	public function calcularHabilidadesProporcoesNasDisciplinasPorTurma () {
		foreach ( $this->rendimentoAlunoPorHabilidadeNasDisciplinas as $aID => $rendPorDisciplina ) {
			$tID = @$this->alunos[$aID]->obterTurma()->obterID();

			if (!isset($this->habilidadesProporcoesNasDisciplinasPorTurma[$tID]))
				$this->habilidadesProporcoesNasDisciplinasPorTurma[$tID] = array();

			foreach($rendPorDisciplina as $dID => $rendPorHab) {
				foreach($rendPorHab as $hID => $desempenho) {
					if (!isset($this->habilidadesProporcoesNasDisciplinasPorTurma[$tID][$dID][$hID]))
						$this->habilidadesProporcoesNasDisciplinasPorTurma[$tID][$dID][$hID] = array(
							AnalizadorSimulado::$HAB_ADQUIRIDA => 0,
							AnalizadorSimulado::$HAB_EM_AQUISICAO => 0,
							AnalizadorSimulado::$HAB_NAO_ADQUIRIDA => 0
						);

					$rendimento = (int) $desempenho['rendimento'];

					if ($rendimento > AnalizadorSimulado::$HAB_EM_AQUISICAO_VALOR)
						$this->habilidadesProporcoesNasDisciplinasPorTurma[$tID][$dID][$hID][AnalizadorSimulado::$HAB_ADQUIRIDA]++;
					elseif ($rendimento > AnalizadorSimulado::$HAB_NAO_ADQUIRIDA_VALOR)
						$this->habilidadesProporcoesNasDisciplinasPorTurma[$tID][$dID][$hID][AnalizadorSimulado::$HAB_EM_AQUISICAO]++;
					else
						$this->habilidadesProporcoesNasDisciplinasPorTurma[$tID][$dID][$hID][AnalizadorSimulado::$HAB_NAO_ADQUIRIDA]++;
				}
			}
		}

		$this->habilidadesProporcoesNasDisciplinasPorTurmaAbs = $this->habilidadesProporcoesNasDisciplinasPorTurma;
		foreach($this->habilidadesProporcoesNasDisciplinasPorTurma as $tID => &$proporcoesPorDisciplina) {
			foreach($proporcoesPorDisciplina as $dID => &$proporcoes) {
				foreach($proporcoes as $hID => &$proporcao) {
					$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
				}
			}
		}
	}

	public function calcularHabilidadesProporcoesNasDisciplinasPorSerie () {
		foreach ( $this->rendimentoAlunoPorHabilidadeNasDisciplinas as $aID => $rendPorDisciplina ) {
			$sID = @$this->alunos[$aID]->obterTurma()->obterSerie()->obterID();

			if (!isset($this->habilidadesProporcoesNasDisciplinasPorSerie[$sID]))
				$this->habilidadesProporcoesNasDisciplinasPorSerie[$sID] = array();

			foreach($rendPorDisciplina as $dID => $rendPorHab) {
				foreach($rendPorHab as $hID => $desempenho) {
					if (!isset($this->habilidadesProporcoesNasDisciplinasPorSerie[$sID][$dID][$hID]))
						$this->habilidadesProporcoesNasDisciplinasPorSerie[$sID][$dID][$hID] = array(
							AnalizadorSimulado::$HAB_ADQUIRIDA => 0,
							AnalizadorSimulado::$HAB_EM_AQUISICAO => 0,
							AnalizadorSimulado::$HAB_NAO_ADQUIRIDA => 0
						);

					$rendimento = (int) $desempenho['rendimento'];

					if ($rendimento > AnalizadorSimulado::$HAB_EM_AQUISICAO_VALOR)
						$this->habilidadesProporcoesNasDisciplinasPorSerie[$sID][$dID][$hID][AnalizadorSimulado::$HAB_ADQUIRIDA]++;
					elseif ($rendimento > AnalizadorSimulado::$HAB_NAO_ADQUIRIDA_VALOR)
						$this->habilidadesProporcoesNasDisciplinasPorSerie[$sID][$dID][$hID][AnalizadorSimulado::$HAB_EM_AQUISICAO]++;
					else
						$this->habilidadesProporcoesNasDisciplinasPorSerie[$sID][$dID][$hID][AnalizadorSimulado::$HAB_NAO_ADQUIRIDA]++;
				}
			}
		}

		$this->habilidadesProporcoesNasDisciplinasPorSerieAbs = $this->habilidadesProporcoesNasDisciplinasPorSerie;
		foreach($this->habilidadesProporcoesNasDisciplinasPorSerie as $sID => &$proporcoesPorDisciplina) {
			foreach($proporcoesPorDisciplina as $dID => &$proporcoes) {
				foreach($proporcoes as $hID => &$proporcao) {
					$proporcao = RelatorioListagem::calcularPorcentagemPerfeita($proporcao);
				}
			}
		}
	}

	/*
	public function calcularRendimentoPorTurma () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoPorTurma();
	}

	public function calcularRendimentoPorSerie () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoPorSerie();
	}

	public function calcularRendimentoPorTurmaPorQuestao () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoPorTurmaPorQuestao();
	}

	public function calcularRendimentoPorSeriePorQuestao () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularRendimentoPorSeriePorQuestao();
	}

	public function calcularHabilidadesProporcoesPorTurma () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularHabilidadesProporcoesPorTurma();
	}

	public function calcularHabilidadesProporcoesPorSerie () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularHabilidadesProporcoesPorSerie();
	}

	public function calcularQuestoesProporcoesPorTurma () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularQuestoesProporcoesPorTurma();
	}

	public function calcularQuestoesProporcoesPorSerie () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularQuestoesProporcoesPorSerie();
	}

	public function calcularColocacaoDosInscritos () {
		foreach($this->analisadores as $analisador)
			$analisador->calcularColocacaoDosInscritos();
	}
*/

	public function eliminarRendimentosNulos () {
		foreach($this->analisadores as $analisador)
			$analisador->eliminarRendimentosNulos();

		// carrega alunos
		$this->alunos = array();
		$this->alunosInscricoes = array();

		foreach($this->analisadores as $analisador) {
			foreach($analisador->inscritos as $iID => $inscricao) {
				$aID = $inscricao->obterAluno()->obterID();

				if (!isset($this->alunos[$aID])) {
					$this->alunos[$aID] = $inscricao->obterAluno();
					$this->alunosInscricoes[$aID] = array();
				}

				$this->alunosInscricoes[$aID][$iID] = TRUE;
			}
		}
	}

	public function limitarInscritosPorTurmas ($turmasIDs) {
		foreach($this->analisadores as $analisador)
			$analisador->limitarInscritosPorTurmas($turmasIDs);

		foreach($this->analisadores as $analisador) {
			foreach($analisador->inscritos as $iID => $inscricao) {
				$aID = $inscricao->obterAluno()->obterID();

				if (!isset($this->alunos[$aID])) {
					$this->alunos[$aID] = $inscricao->obterAluno();
					$this->alunosInscricoes[$aID] = array();
				}

				$this->alunosInscricoes[$aID][$iID] = TRUE;
			}
		}
	}

	public function limitarInscritosPorAlunos ($alunosIDs) {
		foreach($this->analisadores as $analisador)
			$analisador->limitarInscritosPorAlunos($alunosIDs);

		foreach($this->analisadores as $analisador) {
			foreach($analisador->inscritos as $iID => $inscricao) {
				$aID = $inscricao->obterAluno()->obterID();

				if (!isset($this->alunos[$aID])) {
					$this->alunos[$aID] = $inscricao->obterAluno();
					$this->alunosInscricoes[$aID] = array();
				}

				$this->alunosInscricoes[$aID][$iID] = TRUE;
			}
		}
	}

	public function adicionarSimulado (Simulado &$simulado) {
		if ($simulado->obterID() == NULL)
			return;

		$this->analisadores[$simulado->obterID()] = new AnalizadorSimulado();
		$this->analisadores[$simulado->obterID()]->fixarSimulado($simulado);

		if (!isset($this->nomesDisciplinas[$simulado->obterDisciplina()->obterID()])) {
			$this->nomesDisciplinas[$simulado->obterDisciplina()->obterID()] = $simulado->obterDisciplina()->obterNome();
			$this->nomesDisciplinasPequenos[$simulado->obterDisciplina()->obterID()] = $simulado->obterDisciplina()->obterNomePequeno();

			asort($this->nomesDisciplinas, SORT_LOCALE_STRING);

			$nomesPequenos = array();
			foreach($this->nomesDisciplinas as $dID => $dNome)
				$nomesPequenos[$dID] = $this->nomesDisciplinasPequenos[$dID];

			$this->nomesDisciplinasPequenos = $nomesPequenos;
		}

		foreach($this->analisadores[$simulado->obterID()]->nomesHabilidades as $hID => $hNome) {
			if (!isset($this->nomesHabilidades[$hID]))
				$this->nomesHabilidades[$hID] = $hNome;
		}

		foreach($this->analisadores[$simulado->obterID()]->descricoesHabilidades as $hID => $hDescricao) {
			if (!isset($this->descricoesHabilidades[$hID]))
				$this->descricoesHabilidades[$hID] = $hDescricao;
		}

		foreach($this->analisadores[$simulado->obterID()]->macroHabilidades as $hID => $hNome) {
			if (!isset($this->macroHabilidades[$hID]))
				$this->macroHabilidades[$hID] = $hNome;
		}

		foreach($this->nomesHabilidades as $hID => $hNome) {
			if (!isset($this->macroHabilidades[$hID])) {
				unset($this->nomesHabilidades[$hID]);
				unset($this->descricoesHabilidades[$hID]);
			} else {
				$this->nomesHabilidades[$hID] = str_replace(' (matriz)', '', $hNome);
			}
		}

		asort($this->nomesHabilidades, SORT_LOCALE_STRING);
	}

	public function adicionarSimulados ($simulados) {
		foreach($simulados as $simu)
			$this->adicionarSimulado($simu);
	}

	public function obterIDCache() {
		return implode('', array_keys($this->analisadores));
	}

	public function obterNumeroInscritos () {
		$total = 0;

		foreach($this->analisadores as $analisador)
			$total += count($analisador->inscritos);

		return $total;
	}

	public function removerNomesDadosNaoUsadas() {
		foreach($this->nomesHabilidades as $hID => $hNome) {
			$usado = FALSE;

			foreach($this->rendimentoAlunoPorHabilidade as $aID => $rendimentos) {
				if (isset($rendimentos[$hID])) {
					$usado = TRUE;
					break;
				}
			}

			foreach($this->rendimentoAlunoPorHabilidadeNasDisciplinas as $aID => $rendimentosPorDisciplina) {
				foreach($rendimentosPorDisciplina as $dID => $rendimentos) {
					if (isset($rendimentos[$hID])) {
						$usado = TRUE;
						break 2;
					}
				}
			}

			if (!$usado) {
				unset($this->nomesHabilidades[$hID]);
				unset($this->descricoesHabilidades[$hID]);
				unset($this->macroHabilidades[$hID]);
			}
		}

		foreach($this->nomesDisciplinas as $dID => $dNome) {
			$usado = FALSE;

			foreach($this->rendimentoAlunoPorDisciplina as $aID => $rendimentos) {
				if (isset($rendimentos[$dID])) {
					$usado = TRUE;
					break;
				}
			}

			foreach($this->rendimentoAlunoPorHabilidadeNasDisciplinas as $aID => $rendimentos) {
				if (isset($rendimentos[$dID])) {
					$usado = TRUE;
					break;
				}
			}

			if (!$usado) {
				unset($this->nomesDisciplinas[$dID]);
				unset($this->nomesDisciplinasPequenos[$dID]);
			}
		}
	}

	public function obterSerie () {
		foreach($this->analisadores as $analisador)
			return $analisador->obterSerie();
	}

	protected function _resetarParametros () {
		$this->analisadores = array();

		$this->alunos = array();
		$this->alunosInscricoes = array();

		$this->nomesTurmas = array();
		$this->nomesSeries = array();
		$this->nomesHabilidades = array();
		$this->descricoesHabilidades = array();
		$this->macroHabilidades = array();
		$this->nomesDisciplinas = array();
		$this->nomesDisciplinasPequenos = array();

		$this->rendimentoGlobalAluno = array();

		$this->rendimentoAlunoPorDisciplina = array();
		$this->rendimentoAlunoPorHabilidade = array();
		$this->rendimentoAlunoPorHabilidadeNasDisciplinas = array();

		$this->rendimentoGlobalPorTurma = array();
		$this->rendimentoPorTurmaPorDisciplina = array();
		$this->rendimentoProporcoesPorTurmaPorDisciplina = array();
		$this->rendimentoProporcoesPorTurmaPorDisciplinaAbs = array();
		$this->habilidadesProporcoesPorTurma = array();
		$this->habilidadesProporcoesPorTurmaAbs = array();
		$this->habilidadesProporcoesNasDisciplinasPorTurma = array();
		$this->habilidadesProporcoesNasDisciplinasPorTurmaAbs = array();

		$this->rendimentoGlobalPorSerie = array();
		$this->rendimentoPorSeriePorDisciplina = array();
		$this->rendimentoProporcoesPorSeriePorDisciplina = array();
		$this->rendimentoProporcoesPorSeriePorDisciplinaAbs = array();
		$this->habilidadesProporcoesPorSerie = array();
		$this->habilidadesProporcoesPorSerieAbs = array();
		$this->habilidadesProporcoesNasDisciplinasPorSerie = array();
		$this->habilidadesProporcoesNasDisciplinasPorSerieAbs = array();
	}
}

?>