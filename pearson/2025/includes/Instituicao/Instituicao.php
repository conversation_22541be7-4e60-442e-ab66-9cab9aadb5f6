<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Endereco', null, true);

class Instituicao
{	
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'nome_diretor' => null,
								'telefone' => null,
								'municipio' => null,
								'endereco' => null,								
								'numero_alunos' => null,						
								'numero_profs' => null,						
								'numero_turmas' => null,
								'regiao' => null,
								'cod_rede' => null,
								'tipo_rede' => '0');
		
		$this->fixarID($id);
	}
	
	static public function &obterNovaInstituicao ($nome, Endereco &$endereco)
	{
		Core::registro('db')->query( sprintf("INSERT INTO instituicoes(i_nome, i_endereco) VALUES (%s, %s)", 
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($endereco->obterID()) ) );

		$obj = new Instituicao(Core::registro('db')->insert_id);
		
		$obj->fixarNome($nome);
		$obj->fixarEndereco($endereco);		
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf(
				'UPDATE instituicoes SET i_nome = %s, i_nome_diretor = %s, i_telefone = %s, i_municipio = %s, i_endereco = %s, i_regiao = %s, i_cod_rede = %s, i_tipo_rede = %s WHERE i_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['nome_diretor']),
				Core::registro('db')->formatarValor($this->_dados['telefone']),
				Core::registro('db')->formatarValor($this->_dados['municipio']),
				Core::registro('db')->formatarValor($this->_dados['endereco']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['regiao']),
				Core::registro('db')->formatarValor($this->_dados['cod_rede']),
				Core::registro('db')->formatarValor($this->_dados['tipo_rede']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ($removerEndereco = true)
	{
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query(sprintf(
				'DELETE FROM instituicoes, turmas USING instituicoes 
				LEFT JOIN turmas ON turmas.t_instituicao = instituicoes.i_id
				WHERE i_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$retorno = (Core::registro('db')->errno == 0);
			
			$this->fixarID(null);
			
			if ($removerEndereco && $this->_dados['endereco'] != null)
				@$this->_dados['endereco']->remover();
			
			return $retorno;
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM instituicoes WHERE i_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNome($row['i_nome']);
				$this->fixarNomeDiretor($row['i_nome_diretor']);
				$this->fixarTelefone($row['i_telefone']);
				$this->fixarMunicipio($row['i_municipio']);
				$this->fixarRegiao($row['i_regiao']);
				$this->fixarCodRede($row['i_cod_rede']);
				$this->fixarTipoRede($row['i_tipo_rede']);
				$endereco = new Endereco($row['i_endereco']);
				if ($endereco->carregar())
					$this->fixarEndereco($endereco);
				
				$this->obterNumeroAlunos();
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}
	
	public function obterNome ()
	{
		return $this->_dados['nome'];
	}

	public function fixarRegiao($regiao)
    {
        $this->_dados['regiao'] = $regiao;
    }

    public function obterRegiao()
    {
        return $this->_dados['regiao'];
    }

	public function fixarCodRede ($cod_rede)
	{
		$this->_dados['cod_rede'] = $cod_rede;
	}

	public function obterCodRede ()
	{
		return $this->_dados['cod_rede'];
	}
	
	public function fixarNomeDiretor ($nome_diretor) {
		$this->_dados['nome_diretor'] = $nome_diretor;
	}
	
	public function obterNomeDiretor () {
		return $this->_dados['nome_diretor'];
	}

	public function fixarTipoRede ($tipo_rede) {
		$this->_dados['tipo_rede'] = $tipo_rede;
	}

	public function obterTipoRede () {
		return $this->_dados['tipo_rede'];
	}
	
	public function fixarTelefone ($telefone) {
		$this->_dados['telefone'] = $telefone;
	}
	
	public function obterTelefone () {
		return $this->_dados['telefone'];
	}
	
	public function fixarMunicipio ($municipio) {
		$this->_dados['municipio'] = $municipio;
	}
	
	public function obterMunicipio () {
		return $this->_dados['municipio'];
	}
	
	public function fixarEndereco (Endereco &$endereco)
	{
		$this->_dados['endereco'] = $endereco;
	}
	
	public function &obterEndereco ()
	{
		return $this->_dados['endereco'];
	}
	
	public function fixarNumeroAlunos ($n)
	{
		$this->_dados['numero_alunos'] = $n;
	}
	
	public function obterNumeroAlunos ()
	{
		/* if ( $this->_dados['id'] != null && $this->_dados['numero_alunos'] == null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) as total FROM alunos LEFT JOIN usuarios ON alunos.a_usuario = usuarios.u_id WHERE usuarios.u_instituicao = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
			
				$this->_dados['numero_alunos'] = $row['total'];
			}
			$rs->free();
		}
	
		return $this->_dados['numero_alunos']; */

		$this->_dados['numero_alunos'] = 0;
	}
	
	public function fixarNumeroProfs ($n)
	{
		$this->_dados['numero_profs'] = $n;
	}
	
	public function obterNumeroProfs ()
	{
		$this->_dados['numero_profs'] = 0;

		if ( $this->_dados['id'] != null) {
			$rs = Core::registro('db')->query(sprintf('
					SELECT COUNT(0) as total 
					FROM professores 
					LEFT JOIN usuarios ON professores.p_usuario = usuarios.u_id 
					WHERE usuarios.u_instituicao = %s',
				Core::registro('db')->formatarValor($this->_dados['id'])));
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				$this->_dados['numero_profs'] = $row['total'];
			}
			$rs->free();
		}
	
		return $this->_dados['numero_profs'];
	}
	
	public function obterNumeroTurmas ()
	{
		$this->_dados['numero_turmas'] = 0;

		if ( $this->_dados['id'] != null) {
			$rs = Core::registro('db')->query(sprintf('
					SELECT COUNT(0) as total 
					FROM turmas  
					WHERE t_instituicao = %s',
				Core::registro('db')->formatarValor($this->_dados['id'])));
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				$this->_dados['numero_turmas'] = $row['total'];
			}
			$rs->free();
		}
	
		return $this->_dados['numero_turmas'];
	}
	
	// se tem alunos não pode remover
	public function podeRemover ()
	{
		return $this->obterNumeroAlunos() == 0;
	}
	
	public static function obterArrayInstituicoesParaFormulario ()
	{
		$instituicoes = array();

		$rs = Core::registro('db')->query( 'SELECT i_id,i_nome FROM instituicoes ORDER BY i_nome ASC' );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$instituicoes[ $row['i_id'] ] = $row['i_nome'];
			}
		}
		$rs->free();
		
		return $instituicoes;
	}
	
	public static function obterArrayInstituicoesParaFormularioComDREeMunicipio ()
	{
		$instituicoes = array();

		$rs = Core::registro('db')->query( 'SELECT i_id,i_nome,i_municipio,i_regiao FROM instituicoes ORDER BY i_nome ASC' );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$instituicoes[$row['i_id']] = array('nome' => $row['i_nome'], 'municipio' => $row['i_municipio'], 'regiao' => $row['i_regiao']);
			}
		}
		$rs->free();
		
		return $instituicoes;
	}
}

?>