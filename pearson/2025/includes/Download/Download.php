<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class Download {
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'descricao' => null,
								'data' => null,
								'arquivo' => null,
								'grupos' => array(),
								'instituicao' => null );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoDownload ($nome)
	{
		Core::registro('db')->query( sprintf('INSERT INTO downloads(d_nome) VALUES (%s)', 
			Core::registro('db')->formatarValor($nome) ) );

		$obj = new Download(Core::registro('db')->insert_id);
		
		$obj->fixarNome($nome);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('
				UPDATE downloads SET d_nome = %s, d_descricao = %s, d_data = %s, d_arquivo = %s, d_grupos = %s, d_instituicao = %s 
				WHERE d_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['descricao']),
				Core::registro('db')->formatarValor($this->_dados['data']),
				Core::registro('db')->formatarValor($this->_dados['arquivo']),
				Core::registro('db')->formatarValor( implode(',', $this->_dados['grupos']) ),
				Core::registro('db')->formatarValor( @$this->_dados['instituicao']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}
	
	public function remover () {		
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('DELETE FROM downloads WHERE d_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$this->fixarID(null);

			if ( Core::registro('db')->affected_rows && $this->_dados['arquivo'] != null )
				@unlink( Core::diretiva('DOWNLOADS:DIRETORIO:valor') . $this->_dados['arquivo'] );

			return Core::registro('db')->errno == 0;
		}
		
		return false;
	}
	
	public function carregar ($carregarInstituicao = false)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM downloads WHERE d_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNome($row['d_nome']);
				$this->fixarDescricao($row['d_descricao']);
				$this->fixarData($row['d_data']);
				$this->fixarArquivo($row['d_arquivo']);
				$this->fixarGrupos( explode(',', $row['d_grupos']) );
				$this->fixarInstituicao( new Instituicao($row['d_instituicao']) );
				
				if ($carregarInstituicao) {
					if ($row['d_instituicao'] == Core::registro('instituicao')->obterID())
						$this->fixarInstituicao(Core::registro('instituicao'));
					else
						$this->obterInstituicao()->carregar();
				}

				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}
	
	public function obterNome ()
	{
		return $this->_dados['nome'];
	}
	
	public function fixarDescricao ($descricao)
	{
		$this->_dados['descricao'] = $descricao;
	}
	
	public function obterDescricao ()
	{
		return $this->_dados['descricao'];
	}
	
	public function fixarData ($data)
	{
		$this->_dados['data'] = $data;
	}
	
	public function obterData ($formatada = false)
	{
		if ($formatada && $this->_dados['data'] != null) 
			return strftime('%d/%m/%Y', $this->_dados['data']);
		else
			return $this->_dados['data'];
	}
	
	public function fixarArquivo ($arquivo)
	{
		$this->_dados['arquivo'] = $arquivo;
	}
	
	public function obterArquivo ($comPasta = false)
	{
		return $comPasta ? Core::diretiva('DOWNLOADS:DIRETORIO:valor') . $this->_dados['arquivo'] : $this->_dados['arquivo'];
	}
	
	public function fixarGrupos ($grupos)
	{
		$this->_dados['grupos'] = $grupos;
	}
	
	public function obterGrupos ()
	{
		return $this->_dados['grupos'];
	}
	
	public function fixarInstituicao (Instituicao &$inst)
	{
		$this->_dados['instituicao'] = $inst;
	}
	
	public function &obterInstituicao ()
	{
		return $this->_dados['instituicao'];
	}

	public function podeRemover ()
	{
		return true;
	}
	
	public function validarInstituicao ()
	{
		if ( $this->_dados['id'] != null ) {
			if ( $this->_dados['instituicao'] == null || 
					$this->_dados['instituicao']->obterID() == null || 
						Core::modulo('_seletor_instituicoes')->podeSelecionarInstituicao() || 
							Core::registro('instituicao')->obterID() == $this->_dados['instituicao']->obterID() )
				return true;
		}

		return false;
	}
	
	public function validarGrupos ()
	{
		if ( $this->_dados['id'] != null ) {
			if ( in_array(Core::registro('usuario')->obterGrupo(), $this->_dados['grupos']) || 
					Core::registro('permissoes')->temPermissao('downloads.adicionar') ||
						Core::registro('permissoes')->temPermissao('downloads.editar') )
				return true;
		}

		return false;
	}
}

?>