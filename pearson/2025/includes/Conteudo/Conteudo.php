<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('Disciplina', null, true);

class Conteudo
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'disciplina' => null,
								'simulado' => null );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoConteudo ($nome, Simulado &$simu)
	{
		Core::registro('db')->query( sprintf('INSERT INTO simulados_conteudos (sc_nome, sc_simulado) VALUES (%s, %s)', 
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($simu->obterID()) ) );

		$obj = new Conteudo ( Core::registro('db')->insert_id );
		
		$obj->fixarNome( $nome );
		$obj->fixarSimulado( $simu );
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE simulados_conteudos SET sc_nome = %s, sc_disciplina = %s,  sc_simulado = %s WHERE sc_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor( @$this->_dados['disciplina']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['simulado']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	// @todo: ao remover atualizar questoes com esses conteudos
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM simulados_conteudos WHERE sc_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$retorno = (Core::registro('db')->errno == 0);
			
			if ($retorno)
				$this->atualizarConteudosQuestoes();
			
			$this->fixarID(null);
			
			return $retorno;
		}

		return false;
	}

	public function carregar (Simulado &$simulado = null, Disciplina &$disciplina = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM simulados_conteudos WHERE sc_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNome($row['sc_nome']);

				if ( $disciplina == null ) {
					$this->fixarDisciplina( new Disciplina( $row['sc_disciplina'] ) );
					$this->obterDisciplina()->carregar();
				} else {
					$this->fixarDisciplina( $disciplina );
				}
				
				if ( $simulado == null ) {
					$this->fixarSimulado( new Simulado( $row['sc_simulado'] ) );
					$this->obterSimulado()->carregar();
				} else {
					$this->fixarSimulado( $simulado );
				}
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}
	
	public function obterNome ()
	{
		return $this->_dados['nome'];
	}

	public function fixarDisciplina (Disciplina &$disciplina)
	{
		$this->_dados['disciplina'] = $disciplina;
	}
	
	public function &obterDisciplina ()
	{
		return $this->_dados['disciplina'];
	}
	
	public function fixarSimulado (Simulado &$simu)
	{
		$this->_dados['simulado'] = $simu;
	}
	
	public function &obterSimulado ()
	{
		return $this->_dados['simulado'];
	}
	
	public function podeRemover ()
	{
		return true;
	}
	
	public function validarInstituicao ()
	{
		if ( $this->_dados['id'] != null ) {
			return $this->_dados['simulado']->validarInstituicao();
		}
		
		return false;
	}
	
	public function atualizarConteudosQuestoes () {
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE questoes SET q_conteudo = NULL WHERE q_conteudo = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
						
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}

	static public function obterArrayConteudosParaFormulario (Simulado $simu, Disciplina $disciplina = null)
	{
		$conteudos = array();

		if ($simu != null) {
			$disciplinaSQL = '';
			if ( $disciplina != null )
				$disciplinaSQL = ' AND sc_disciplina = '. Core::registro('db')->formatarValor( $disciplina->obterID() );
		
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM simulados_conteudos WHERE sc_simulado = %s %s AND sc_disciplina IS NOT NULL ORDER BY sc_disciplina ASC, sc_nome ASC',
				Core::registro('db')->formatarValor( $simu->obterID() ),
				$disciplinaSQL ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					if ( $disciplina == null ) {
						if ( !isset($conteudos[$row['sc_disciplina']]) )
							$conteudos[$row['sc_disciplina']] = array();
	
						@$conteudos[$row['sc_disciplina']][$row['sc_id']] = $row['sc_nome'];
					} else {
						$conteudos[$row['sc_id']] = $row['sc_nome'];
					}
				}
			}
			$rs->free();
		}

		return $conteudos;
	}
	
	static public function obterIDUltimaDisciplinaCadastrada (Simulado &$simu) {
		if ( $simu != null && $simu->obterID() != null ) {
			$rs = Core::registro('db')->query( sprintf(
				  'SELECT sc_disciplina FROM simulados_conteudos WHERE sc_simulado = %s ORDER BY sc_id DESC LIMIT 0,1',
				  Core::registro('db')->formatarValor( $simu->obterID() ) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				return $row['sc_disciplina'];
			}
			$rs->free();
		}
		
		return false;
	}
	
	static public function obterArrayConteudosSemSimulado (Disciplina $disciplina = null)
	{
		$conteudos = array();

		if ($disciplina != null) {
			$rs = Core::registro('db')->query(sprintf('SELECT * FROM simulados_conteudos WHERE sc_disciplina = %s AND sc_disciplina IS NOT NULL ORDER BY sc_nome ASC',
				Core::registro('db')->formatarValor($disciplina->obterID())));

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$conteudos[$row['sc_id']] = $row['sc_nome'];
				}
			}
			$rs->free();
		}
		else{
			$rs = Core::registro('db')->query('SELECT * FROM simulados_conteudos ORDER BY sc_nome ASC');

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$conteudos[$row['sc_id']] = $row['sc_nome'];
				}
			}
			$rs->free();
		}

		return $conteudos;
	}
}

?>