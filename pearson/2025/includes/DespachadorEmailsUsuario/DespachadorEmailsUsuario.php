<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class DespachadorEmailsUsuario
{
    public static function enviarEmailValidacaoEmail (Email &$email, $enviarEmailRedefinicaoSenha = true) {
        if ( $email != null && $email->obterID() != null ) {
            if ( $email->podeSolicitarEmailVerificacaoEmail() ) {
                $email->fixarDataUltimoEnvio(time());
                if ( !Filtrador::md5Valido( $email->obterChaveConfirmacao() ) )
                    $email->fixarChaveConfirmacao( randomico(true, 16) );

                if ( $email->salvar() ) {
                    Gerenciador_URL::autoFixarIndexCompleto(true);

                    $link = Gerenciador_URL::gerarLink('login', 'confirmaremail', array('email' => $email->obterEndereco(), 'chave_confirmacao' => $email->obterChaveConfirmacao()));

                    $texto_HTML = 'Voc&ecirc; est&aacute; recebendo esta mensagem para confirmar o cadastro do seu endere&ccedil;o de e-mail. Caso n&atilde;o tenha solicitado essa verifica&ccedil;&atilde;o, por favor ignore este contato.<br><br>
									Para confirmar seu endereço de e-mail, por favor clique no link abaixo:<br>
									<a href="'. $link .'">'. $link .'</a><br><br>
									Atenciosamente,<br><br>
                                                                  
                                                                        Equipe Avaliativa<br><br><br>

									Caso tenha d&uacute;vidas ou dificuldades no cadastramento, n&atilde;o responda ao remetente dessa mensagem.<br>
									Utilize o endere&ccedil;o: <a href="mailto: <EMAIL>"><EMAIL></a>';

                    $idUsuario = AutenticadorUsuariosInstituidos::obterIDUsuarioPeloLogin( $email->obterEndereco() );

                    if ( $idUsuario !== false && $enviarEmailRedefinicaoSenha ) {
                        $usuario = new UsuarioInstituido($idUsuario);

                        $recuperadorID = RecuperadorSenhasUsuario::obterIDPeloPeloUsuario($usuario);

                        if ( $recuperadorID !== false ) {
                            $usuario->carregar();
                            $recuperador = new RecuperadorSenhasUsuario($recuperadorID);
                            $recuperador->carregar($usuario);

                            if ( $recuperador->podeSolicitarEmailRecuperacaoSenha() ) {
                                $chave_confirmacao_senha = @self::enviarEmailRecuperacaoSenha($recuperador, true);
                                if ( $chave_confirmacao_senha !== false ) {
                                    $link = Gerenciador_URL::gerarLink('login', 'confirmaremail', array('email' => $email->obterEndereco(), 'chave_confirmacao' => $email->obterChaveConfirmacao(), 'chave_confirmacao_senha' => $chave_confirmacao_senha));

                                    $texto_HTML = 'Voc&ecirc; est&aacute; recebendo esta mensagem para confirmar o cadastro do seu endere&ccedil;o de e-mail. Caso n&atilde;o tenha solicitado essa verifica&ccedil;&atilde;o, por favor ignore este contato.<br><br>
													Para confirmar seu endere&ccedil;o de e-mail, por favor clique no link abaixo:<br>
													<a href="'. $link .'">'. $link .'</a><br><br>
													Nessa p&aacute;gina voc&ecirc; poder&aacute; tambжm definir sua senha de acesso.<br><br>
													Atenciosamente,<br><br>

                                                                                                        Equipe Avaliativa<br><br><br>
 
													Caso tenha d&uacute;vidas ou dificuldades no cadastramento, n&atilde;o responda ao remetente dessa mensagem.<br>
													Utilize o endere&ccedil;o: <a href="mailto: <EMAIL>"><EMAIL></a>';

                                }
                            }
                        }
                    }

                    Gerenciador_URL::autoFixarIndexCompleto(false);

                    try {
                        $mail = new Zend_Mail();
                        $mail->setBodyHtml($texto_HTML, null, Zend_Mime::TYPE_HTML);
                        $mail->setFrom(CORE_EMAIL_FROM_ENDERECO, CORE_EMAIL_FROM_NOME);
                        $mail->addTo($email->obterEndereco());
                        $mail->setSubject('Confirmaусo de cadastro');
                        $mail->send();

                        return true;
                    } catch (Exception $e) {
                        return false;
                    }
                }
            } else {
                $idUsuario = AutenticadorUsuariosInstituidos::obterIDUsuarioPeloLogin( $email->obterEndereco() );

                if ( $idUsuario !== false && $enviarEmailRedefinicaoSenha ) {
                    $usuario = new UsuarioInstituido($idUsuario);

                    $recuperadorID = RecuperadorSenhasUsuario::obterIDPeloPeloUsuario($usuario);

                    if ( $recuperadorID !== false ) {
                        $usuario->carregar();
                        $recuperador = new RecuperadorSenhasUsuario($recuperadorID);
                        $recuperador->carregar($usuario);

                        return @self::enviarEmailRecuperacaoSenha($recuperador);
                    }
                }
            }
        }

        return false;
    }

    public static function enviarEmailRecuperacaoSenha (RecuperadorSenhasUsuario &$recuperador, $obterChave = false) {
        if ( $recuperador != null && $recuperador->obterID() != null && $recuperador->podeSolicitarEmailRecuperacaoSenha() ) {
            $recuperador->fixarDataUltimoEnvio(time());
            if ( !Filtrador::md5Valido( $recuperador->obterChaveConfirmacao() ) )
                $recuperador->fixarChaveConfirmacao( randomico(true, 16) );

            if ( $recuperador->salvar() ) {
                if ( !$obterChave ) {
                    Gerenciador_URL::autoFixarIndexCompleto(true);
                    $link = Gerenciador_URL::gerarLink('login', 'redefinirsenha', array('email' => $recuperador->obterUsuario()->obterEmail()->obterEndereco(), 'chave_confirmacao' => $recuperador->obterChaveConfirmacao()));
                    Gerenciador_URL::autoFixarIndexCompleto(false);

                    $texto_HTML = 'Voc&ecirc; solicitou recuperar sua senha de acesso no Avaliar Rede. Se voc&ecirc; n&atilde;o fez essa solicita&ccedil;&atilde;o, por favor ignore este contato.<br><br>
									Para recriar sua senha, por favor clique no link abaixo:<br>
									<a href="'. $link .'">'. $link .'</a><br><br>									
									Nessa p&aacute;gina voc&ecirc; poder&aacute; criar uma nova senha.<br><br>									
									Atenciosamente,<br><br>

                                                                        Equipe Avaliativa<br><br><br>

									<a href="mailto: <EMAIL>">Avaliativa - Gest&atilde;o de Informa&ccedil;&otilde;es Educacionais</a><br><br>
									Caso tenha d&uacute;vidas ou dificuldades no recupera&ccedil;&atilde;o da senha, n&atilde;o responda ao remetente dessa mensagem.<br>
									Utilize o endere&ccedil;o: <a href="mailto: <EMAIL>"><EMAIL></a>';

                    try {
                        $mail = new Zend_Mail();
                        $mail->setBodyHtml($texto_HTML, null, Zend_Mime::TYPE_HTML);
                        $mail->setFrom(CORE_EMAIL_FROM_ENDERECO, CORE_EMAIL_FROM_NOME);
                        $mail->addTo($recuperador->obterUsuario()->obterEmail()->obterEndereco());
                        $mail->setSubject('Avalia Rede - Recuperando senha de acesso');
                        $mail->send();

                        return true;
                    } catch (Exception $e) {
                        return false;
                    }
                } else {
                    return $recuperador->obterChaveConfirmacao();
                }
            }
        }

        return false;
    }

    public static function enviarEmailMensagem ($emailsEnds, $titulo, $msgTexto) {
        $arr_erros = array();

        if (isset($emailsEnds) && isset($titulo)) {
            foreach ($emailsEnds as $ke => $ve){
                try {
                    $mail = new Zend_Mail();
                    $mail->setBodyHtml($msgTexto, null, Zend_Mime::TYPE_HTML);
                    $mail->setFrom(CORE_EMAIL_FROM_ENDERECO_MSG, CORE_EMAIL_FROM_NOME_MSG);
                    if (defined('CORE_EMAIL_BCC_ENDERECO') && CORE_EMAIL_BCC_ENDERECO != "") {
                        $mail->addBcc(CORE_EMAIL_BCC_ENDERECO);
                    }
                    $mail->addTo($ve['email']);
                    $mail->setSubject($titulo);
                    $mail->send();
                } catch (Exception $e) {
                    $arr_erros[] = $ve['email'];
                }
            }

            if(count($arr_erros)>0){
                return true;
            }
            else{
                return false;
            }
        }
        else{
            return false;
        }
    }

    public static function enviarAlertaBOT ($assunto = '', $msgTexto = '') 
    {
        $mail = new Zend_Mail();
        $mail->setBodyHtml($msgTexto, null, Zend_Mime::TYPE_HTML);
        $mail->setFrom(CORE_EMAIL_FROM_ENDERECO_MSG, CORE_EMAIL_FROM_NOME_MSG);
        $mail->addTo('<EMAIL>');
        $mail->setSubject($assunto);
        $mail->send();
    }
}
?>