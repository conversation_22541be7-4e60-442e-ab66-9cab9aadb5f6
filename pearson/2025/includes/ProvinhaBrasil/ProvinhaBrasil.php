<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Disciplina', null, true);
Core::incluir('Simulado', null, true);
Core::incluir('Inscricao', null, true);

final class ProvinhaBrasil {
	const VARIACAO_POSITIVA = '<img style="width:12px;" border="0" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
rendimento_subiu.gif">';
	const VARIACAO_NULA = '<img style="width:12px;" border="0" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
rendimento_estatico.gif">';
	const VARIACAO_NEGATIVA = '<img style="width:12px;" border="0" src="https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/
rendimento_caiu.gif">';
	const VARIACAO_NAO_APLICAVEL = '';

	const NIVEL_1_COMUM = 1;
	const NIVEL_2_COMUM = 2;
	const NIVEL_3_COMUM = 3;
	const NIVEL_4_COMUM = 4;
	const NIVEL_5_COMUM = 5;
	const NIVEL_6_COMUM = 6;
	const NIVEL_7_COMUM = 7;
	const NIVEL_8_COMUM = 8;
	const NIVEL_9_COMUM = 9;
	const NIVEL_10_COMUM = 10;

	/*const NIVEL_1_ANTERIOR = NIVEL_1_ANTERIOR;
	const NIVEL_2_ANTERIOR = NIVEL_2_ANTERIOR;
	const NIVEL_3_ANTERIOR = NIVEL_3_ANTERIOR;
	const NIVEL_4_ANTERIOR = NIVEL_4_ANTERIOR;
	const NIVEL_5_ANTERIOR = NIVEL_5_ANTERIOR;*/

	const TESTE_1_NIVEL_1_COR = '#b32b02';
	const TESTE_1_NIVEL_2_COR = '#f1501f';
	const TESTE_1_NIVEL_3_COR = '#f4d826';
	const TESTE_1_NIVEL_4_COR = '#8be37d';
	const TESTE_1_NIVEL_5_COR = '#539e48';

	const TESTE_2_NIVEL_1_COR = '#b32b02';
	const TESTE_2_NIVEL_2_COR = '#f1501f';
	const TESTE_2_NIVEL_3_COR = '#f4d826';
	const TESTE_2_NIVEL_4_COR = '#8be37d';
	const TESTE_2_NIVEL_5_COR = '#539e48';

	const TESTE_1_COR = '#00C000';
	const TESTE_1_NOME = 'Teste 1';

	const TESTE_2_COR = '#C3C3C3';
	const TESTE_2_NOME = 'Teste 2';

	const EVADIU_COR = '#C3C3C3';

	const HAB_NAO_ADQUIRIDA = 1;
	const HAB_NAO_ADQUIRIDA_COR = '#A93A49'; //'#E61E0C';//'#eeeeee'; //'#00dd00';
	const HAB_NAO_ADQUIRIDA_TEXTO = 'Habilidade a desenvolver';
	const COM_NAO_ADQUIRIDA_TEXTO = 'Competência a desenvolver';
	const HAB_NAO_ADQUIRIDA_PORCENTAGEM = 37;

	const HAB_EM_AQUISICAO = 2;
	const HAB_EM_AQUISICAO_COR = '#EEB200'; //'#F4B60B';//'#cccccc'; //'#00b300';
	const HAB_EM_AQUISICAO_TEXTO = 'Habilidade em desenvolvimento';//Habilidade em desenvolvimento';
	const COM_EM_AQUISICAO_TEXTO = 'Competência em desenvolvimento';
	const HAB_EM_AQUISICAO_PORCENTAGEM = 67;

	const HAB_ADQUIRIDA = 3;
	const HAB_ADQUIRIDA_COR = '#3B786F'; //'#119500';//'#999999'; //'#008000';
	const HAB_ADQUIRIDA_TEXTO = 'Habilidade desenvolvida';//Habilidade desenvolvida';
	const COM_ADQUIRIDA_TEXTO = 'Competência desenvolvida';

	//const NIVEL_1 = NIVEL_1;
	//const NIVEL_1_COR = COR_NIVEL_1; // '#99cbfe'; // '#eeeeee';
	//const NIVEL_1_NOME = NIVEL_1_NOME;
	//const NIVEL_1_TEXTO = NIVEL_1_TEXTO;
	const NIVEL_1_LARGURA_TABELA = '14%'; //'36%'; // 36 - 54% original

	//const NIVEL_2 = NIVEL_2;
	//const NIVEL_2_COR = COR_NIVEL_2; // '#6e92db'; // '#cccccc';
	//const NIVEL_2_NOME = NIVEL_2_NOME;
	//const NIVEL_2_TEXTO = NIVEL_2_TEXTO;
	const NIVEL_2_LARGURA_TABELA = '14%'; //'11%'; // 11 - 17% original

	//const NIVEL_3 = NIVEL_3;
	//const NIVEL_3_COR = COR_NIVEL_3; // '#5069c2'; // '#999999';
	//const NIVEL_3_NOME = NIVEL_3_NOME;
	//const NIVEL_3_TEXTO = NIVEL_3_TEXTO;
	const NIVEL_3_LARGURA_TABELA = '14%'; //'9%'; // 9 - 13% original

	//const NIVEL_4 = NIVEL_4;
	//const NIVEL_4_COR = COR_NIVEL_4; // '#303fa7'; // '#999999';
	//const NIVEL_4_NOME = NIVEL_4_NOME;
	//const NIVEL_4_TEXTO = NIVEL_4_TEXTO;
	const NIVEL_4_LARGURA_TABELA = '14%'; //'6%'; // 5 - 8% original

	//const NIVEL_5 = NIVEL_5;
	//const NIVEL_5_COR = COR_NIVEL_5; // '#010181'; // '#999999';
	//const NIVEL_5_NOME = NIVEL_5_NOME;
	//const NIVEL_5_TEXTO = NIVEL_5_TEXTO;
	const NIVEL_5_LARGURA_TABELA = '14%'; //'6%'; // 5 - 8% original

	//const NIVEL_COMPACTO_123 = NIVEL_3;
	//const NIVEL_COMPACTO_123_COR = NIVEL_COMPACTO_123_COR;
	//const NIVEL_COMPACTO_123_NOME = NIVEL_COMPACTO_123_NOME;
	//const NIVEL_COMPACTO_123_TEXTO = NIVEL_COMPACTO_123_TEXTO;
	const NIVEL_COMPACTO_123_LARGURA_TABELA = '14%';

	//const NIVEL_COMPACTO_45 = NIVEL_4;
	//const NIVEL_COMPACTO_45_COR = NIVEL_COMPACTO_45_COR;
	//const NIVEL_COMPACTO_45_NOME = NIVEL_COMPACTO_45_NOME;
	//const NIVEL_COMPACTO_45_TEXTO = NIVEL_COMPACTO_45_TEXTO;
	const NIVEL_COMPACTO_45_LARGURA_TABELA = '14%'; 

	//const NIVEL_NLEITOR = NIVEL_3;
	//const NIVEL_NLEITOR_COR = NIVEL_NLEITOR_COR;
	//const NIVEL_NLEITOR_NOME = NIVEL_NLEITOR_NOME;
	//const NIVEL_NLEITOR_TEXTO = NIVEL_NLEITOR_TEXTO;
	const NIVEL_NLEITOR_LARGURA_TABELA = '14%';

	//const NIVEL_LEITOR = NIVEL_4;
	//const NIVEL_LEITOR_COR = NIVEL_LEITOR_COR;
	//const NIVEL_LEITOR_NOME = NIVEL_LEITOR_NOME;
	//const NIVEL_LEITOR_TEXTO = NIVEL_LEITOR_TEXTO;
	const NIVEL_LEITOR_LARGURA_TABELA = '14%';

	const RENDIMENTO_BAIXO = 340;
	const RENDIMENTO_BAIXO_COR = '#9accff'; // '#eeeeee';
	const RENDIMENTO_BAIXO_TEXTO = 'As habilidades desenvolvidas são ainda muito iniciais no processamento da leitura';
	const RENDIMENTO_BAIXO_LARGURA_TABELA = '41%';

	const RENDIMENTO_INTERMEDIARIO = 440;
	const RENDIMENTO_INTERMEDIARIO_COR = '#3365ff'; // '#cccccc';
	const RENDIMENTO_INTERMEDIARIO_TEXTO = 'Os alunos desse nível já conseguem ler palavras';
	const RENDIMENTO_INTERMEDIARIO_LARGURA_TABELA = '12%';

	const RENDIMENTO_RECOMENDAVEL = 500;
	const RENDIMENTO_RECOMENDAVEL_COR = '#000080'; // '#999999';
	const RENDIMENTO_RECOMENDAVEL_TEXTO = 'Os alunos desse nível já conseguem ler frases e pequenos textos';
	const RENDIMENTO_RECOMENDAVEL_LARGURA_TABELA = '7%';

	const COR_TEXTO = '#ffffff';

	const QUESTAO_25 = 25;
	const QUESTAO_26 = 26;
	const QUESTAO_27 = 27;

	static public $NIVEL_1_ANTERIOR, 
				  $NIVEL_2_ANTERIOR, 
				  $NIVEL_3_ANTERIOR, 
				  $NIVEL_4_ANTERIOR, 
				  $NIVEL_5_ANTERIOR, 
				  $NIVEL_1, 
				  $NIVEL_2, 
				  $NIVEL_3, 
				  $NIVEL_4, 
				  $NIVEL_5, 
				  $NIVEL_6, 
				  $NIVEL_7, 
				  $NIVEL_8, 
				  $NIVEL_9, 
				  $NIVEL_10, 
				  $NIVEL_1_COR, 
				  $NIVEL_2_COR, 
				  $NIVEL_3_COR, 
				  $NIVEL_4_COR, 
				  $NIVEL_5_COR, 
				  $NIVEL_6_COR, 
				  $NIVEL_7_COR, 
				  $NIVEL_8_COR, 
				  $NIVEL_9_COR, 
				  $NIVEL_10_COR, 
				  $NIVEL_1_NOME, 
				  $NIVEL_1_TEXTO, 
				  $NIVEL_2_NOME, 
				  $NIVEL_2_TEXTO, 
				  $NIVEL_3_NOME, 
				  $NIVEL_3_TEXTO, 
				  $NIVEL_4_NOME, 
				  $NIVEL_4_TEXTO, 
				  $NIVEL_5_NOME, 
				  $NIVEL_5_TEXTO, 
				  $NIVEL_6_NOME, 
				  $NIVEL_6_TEXTO, 
				  $NIVEL_7_NOME, 
				  $NIVEL_7_TEXTO, 
				  $NIVEL_8_NOME, 
				  $NIVEL_8_TEXTO, 
				  $NIVEL_9_NOME, 
				  $NIVEL_9_TEXTO, 
				  $NIVEL_10_NOME, 
				  $NIVEL_10_TEXTO, 
				  $NIVEL_COMPACTO_123, 
				  $NIVEL_COMPACTO_123_COR, 
				  $NIVEL_COMPACTO_123_NOME, 
				  $NIVEL_COMPACTO_123_TEXTO, 
				  $NIVEL_COMPACTO_45, 
				  $NIVEL_COMPACTO_45_COR, 
				  $NIVEL_COMPACTO_45_NOME, 
				  $NIVEL_COMPACTO_45_TEXTO, 
				  $NIVEL_NLEITOR, 
				  $NIVEL_NLEITOR_COR, 
				  $NIVEL_NLEITOR_NOME, 
				  $NIVEL_NLEITOR_TEXTO, 
				  $NIVEL_LEITOR, 
				  $NIVEL_LEITOR_COR, 
				  $NIVEL_LEITOR_NOME, 
				  $NIVEL_LEITOR_TEXTO = null;

	static public $instituicao;
	static public $alfabetizacao;
	static public $serie;
	static public $grupoPB = 5;
	static public $titulo_relatorios = 'Provinha Brasil 2008.2';
	static public $simulado;

	static public $habilidades;

	static public function carregar() {
		//self::$instituicao = new Instituicao(1);

		//self::$alfabetizacao = new Disciplina(1);
		//self::$alfabetizacao->fixarInstituicao(self::$instituicao);
		//self::$alfabetizacao->fixarNome('Alfabetização');

		self::$serie = new Serie(1);
		self::$serie->fixarNome('2 série/ano');
		//self::$serie->fixarInstituicao(self::$instituicao);

		//self::$simulado = new Simulado(1);
		//if (!self::$simulado->carregar())
		//	self::$simulado->fixarID(null);

		self::$habilidades = array();

		self::$NIVEL_1_ANTERIOR = Core::diretiva('NIVEL_1_ANTERIOR_TMP');
		self::$NIVEL_2_ANTERIOR = Core::diretiva('NIVEL_2_ANTERIOR_TMP');
		self::$NIVEL_3_ANTERIOR = Core::diretiva('NIVEL_3_ANTERIOR_TMP');
		self::$NIVEL_4_ANTERIOR = Core::diretiva('NIVEL_4_ANTERIOR_TMP');
		self::$NIVEL_5_ANTERIOR = Core::diretiva('NIVEL_5_ANTERIOR_TMP');

		self::$NIVEL_1 = Core::diretiva('NIVEL_1_TMP');
		self::$NIVEL_2 = Core::diretiva('NIVEL_2_TMP');
		self::$NIVEL_3 = Core::diretiva('NIVEL_3_TMP');
		self::$NIVEL_4 = Core::diretiva('NIVEL_4_TMP');
		self::$NIVEL_5 = Core::diretiva('NIVEL_5_TMP');
		self::$NIVEL_6 = Core::diretiva('NIVEL_6_TMP');
		self::$NIVEL_7 = Core::diretiva('NIVEL_7_TMP');
		self::$NIVEL_8 = Core::diretiva('NIVEL_8_TMP');
		self::$NIVEL_9 = Core::diretiva('NIVEL_9_TMP');
		self::$NIVEL_10 = Core::diretiva('NIVEL_10_TMP');

		self::$NIVEL_1_COR = Core::diretiva('NIVEL_1_COR_TMP');
		self::$NIVEL_2_COR = Core::diretiva('NIVEL_2_COR_TMP');
		self::$NIVEL_3_COR = Core::diretiva('NIVEL_3_COR_TMP');
		self::$NIVEL_4_COR = Core::diretiva('NIVEL_4_COR_TMP');
		self::$NIVEL_5_COR = Core::diretiva('NIVEL_5_COR_TMP');
		self::$NIVEL_6_COR = Core::diretiva('NIVEL_6_COR_TMP');
		self::$NIVEL_7_COR = Core::diretiva('NIVEL_7_COR_TMP');
		self::$NIVEL_8_COR = Core::diretiva('NIVEL_8_COR_TMP');
		self::$NIVEL_9_COR = Core::diretiva('NIVEL_9_COR_TMP');
		self::$NIVEL_10_COR = Core::diretiva('NIVEL_10_COR_TMP');

		self::$NIVEL_1_NOME = Core::diretiva('NIVEL_1_NOME_TMP');
		self::$NIVEL_1_TEXTO = Core::diretiva('NIVEL_1_TEXTO_TMP');

		self::$NIVEL_2_NOME = Core::diretiva('NIVEL_2_NOME_TMP');
		self::$NIVEL_2_TEXTO = Core::diretiva('NIVEL_2_TEXTO_TMP');

		self::$NIVEL_3_NOME = Core::diretiva('NIVEL_3_NOME_TMP');
		self::$NIVEL_3_TEXTO = Core::diretiva('NIVEL_3_TEXTO_TMP');

		self::$NIVEL_4_NOME = Core::diretiva('NIVEL_4_NOME_TMP');
		self::$NIVEL_4_TEXTO = Core::diretiva('NIVEL_4_TEXTO_TMP');

		self::$NIVEL_5_NOME = Core::diretiva('NIVEL_5_NOME_TMP');
		self::$NIVEL_5_TEXTO = Core::diretiva('NIVEL_5_TEXTO_TMP');

		self::$NIVEL_6_NOME = Core::diretiva('NIVEL_6_NOME_TMP');
		self::$NIVEL_6_TEXTO = Core::diretiva('NIVEL_6_TEXTO_TMP');

		self::$NIVEL_7_NOME = Core::diretiva('NIVEL_7_NOME_TMP');
		self::$NIVEL_7_TEXTO = Core::diretiva('NIVEL_7_TEXTO_TMP');

		self::$NIVEL_8_NOME = Core::diretiva('NIVEL_8_NOME_TMP');
		self::$NIVEL_8_TEXTO = Core::diretiva('NIVEL_8_TEXTO_TMP');

		self::$NIVEL_9_NOME = Core::diretiva('NIVEL_9_NOME_TMP');
		self::$NIVEL_9_TEXTO = Core::diretiva('NIVEL_9_TEXTO_TMP');

		self::$NIVEL_10_NOME = Core::diretiva('NIVEL_10_NOME_TMP');
		self::$NIVEL_10_TEXTO = Core::diretiva('NIVEL_10_TEXTO_TMP');

		self::$NIVEL_COMPACTO_123 = Core::diretiva('NIVEL_COMPACTO_123_TMP');
		self::$NIVEL_COMPACTO_123_COR = Core::diretiva('NIVEL_COMPACTO_123_COR_TMP');
		self::$NIVEL_COMPACTO_123_NOME = Core::diretiva('NIVEL_COMPACTO_123_NOME_TMP');
		self::$NIVEL_COMPACTO_123_TEXTO = Core::diretiva('NIVEL_COMPACTO_123_TEXTO_TMP');

		self::$NIVEL_COMPACTO_45 = Core::diretiva('NIVEL_COMPACTO_45_TMP');
		self::$NIVEL_COMPACTO_45_COR = Core::diretiva('NIVEL_COMPACTO_45_COR_TMP');
		self::$NIVEL_COMPACTO_45_NOME = Core::diretiva('NIVEL_COMPACTO_45_NOME_TMP');
		self::$NIVEL_COMPACTO_45_TEXTO = Core::diretiva('NIVEL_COMPACTO_45_TEXTO_TMP');

		self::$NIVEL_NLEITOR = Core::diretiva('NIVEL_NLEITOR_TMP');
		self::$NIVEL_NLEITOR_COR = Core::diretiva('NIVEL_NLEITOR_COR_TMP');
		self::$NIVEL_NLEITOR_NOME = Core::diretiva('NIVEL_NLEITOR_NOME_TMP');
		self::$NIVEL_NLEITOR_TEXTO = Core::diretiva('NIVEL_NLEITOR_TEXTO_TMP');

		self::$NIVEL_LEITOR = Core::diretiva('NIVEL_LEITOR_TMP');
		self::$NIVEL_LEITOR_COR = Core::diretiva('NIVEL_LEITOR_COR_TMP');
		self::$NIVEL_LEITOR_NOME = Core::diretiva('NIVEL_LEITOR_NOME_TMP');
		self::$NIVEL_LEITOR_TEXTO = Core::diretiva('NIVEL_LEITOR_TEXTO_TMP');

		/*
		self::$habilidades = array(
			array(
				'nome' => 'hab1',
				'descricao' => 'Identificar sons, sílabas e outras unidades sonoras',
				self::HAB_NAO_ADQUIRIDA => 13,
				self::HAB_ADQUIRIDA => 13
			),
			array(
				'nome' => 'hab2',
				'descricao' => 'Distinguir letras de outros sinais gráficos',
				self::HAB_NAO_ADQUIRIDA => 200,
				self::HAB_ADQUIRIDA => 300
			),
			array(
				'nome' => 'hab3',
				'descricao' => 'Conhecer as direções e o alinhamento da escrita',
				self::HAB_NAO_ADQUIRIDA => 250,
				self::HAB_ADQUIRIDA => 350
			),
			array(
				'nome' => 'hab4',
				'descricao' => 'Identificar o conceito de palavra',
				self::HAB_NAO_ADQUIRIDA => 250,
				self::HAB_ADQUIRIDA => 350
			),
			array(
				'nome' => 'hab5',
				'descricao' => 'Identificar número de sílabas',
				self::HAB_NAO_ADQUIRIDA => 250,
				self::HAB_ADQUIRIDA => 400
			),
			array(
				'nome' => 'hab6',
				'descricao' => 'Compreender palavras lidas silenciosamente',
				self::HAB_NAO_ADQUIRIDA => 300,
				self::HAB_ADQUIRIDA => 400
			),
			array(
				'nome' => 'hab7',
				'descricao' => 'Identificar letras do alfabeto',
				self::HAB_NAO_ADQUIRIDA => 300,
				self::HAB_ADQUIRIDA => 400
			),
			array(
				'nome' => 'hab8',
				'descricao' => 'Distinguir, como leitor, diferentes tipos de letras',
				self::HAB_NAO_ADQUIRIDA => 300,
				self::HAB_ADQUIRIDA => 400
			),
			array(
				'nome' => 'hab9',
				'descricao' => 'Reconhecer ordem alfabética',
				self::HAB_NAO_ADQUIRIDA => 300,
				self::HAB_ADQUIRIDA => 400
			),
			array(
				'nome' => 'hab10',
				'descricao' => 'Identificar elementos que constroem a narrativa',
				self::HAB_NAO_ADQUIRIDA => 350,
				self::HAB_ADQUIRIDA => 450
			),
			array(
				'nome' => 'hab11',
				'descricao' => 'Localizar informação',
				self::HAB_NAO_ADQUIRIDA => 350,
				self::HAB_ADQUIRIDA => 450
			)
		);
		*/
	}

	static public function obterSeriePeloNomeSimulado($nome) {
		return str_replace('Provinha Brasil 2008.1 - ', '', $nome);
	}

	static public function obterTurmaComNome($turma) {
		return (stristr($turma, 'turma') === false ? 'Turma ' . $turma : $turma);
	}
}

ProvinhaBrasil::carregar();

$municipioD = Core::diretiva('CORE:MunicipioName');
@define('MUNICIPIO', $municipioD);
?>