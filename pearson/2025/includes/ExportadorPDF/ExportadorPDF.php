<?
if (!function_exists('set_magic_quotes_runtime')) {
    function set_magic_quotes_runtime($new_setting) {
		#var_dump($new_setting);
        return true;
    }
}

if (!defined('CORE_INCLUIDO')) exit();

class ExportadorPDF {
	const RETRATO = 'portrait';
	const PAISAGEM = 'landscape';

	protected $_dompdf;
	protected $_nome_arquivo;

	public function __construct($nome_arquivo) {
		$this->_nome_arquivo = $nome_arquivo;
	}

	public function exportar($html, $tipo_orientacao = self::RETRATO, $salvar_em_arquivo = false, $papel = 'a4') {
		// bug de locale
		$locale_old = setlocale(LC_ALL, 0);
		setlocale(LC_ALL, 'C');
		setlocale(LC_COLLATE, $locale_old);
		setlocale(LC_CTYPE, $locale_old);
		setlocale(LC_MONETARY, $locale_old);
		//setlocale(LC_NUMERIC, $locale_old);
		setlocale(LC_TIME, $locale_old);
		setlocale(LC_MESSAGES, $locale_old);

		require_once 'dompdf/dompdf_config.inc.php';

		if (!$salvar_em_arquivo)
			Core::modulo('index')->fixarTipoDeSaida(Modulo::ARQUIVO, array('nome_arquivo' => $this->_nome_arquivo, 'mime' => 'text/pdf'));

		$this->_dompdf = new DOMPDF();
		$this->_dompdf->set_paper($papel, $tipo_orientacao);
		$html = preg_replace('/>\s+</', '><', $html);

		$html = utf8_decode($html);

		$this->_dompdf->load_html($html);
		$this->_dompdf->render();
	}

	public function obterSaida ()	{
		$this->_dompdf->stream($this->_nome_arquivo);

		unset($this->_dompdf);
	}

	public function exportarParaArquivo($html, $tipo_orientacao = self::RETRATO, $pasta, $caminho_completo = true) {
		$this->exportar($html, $tipo_orientacao, true);

		if (!$caminho_completo)
			$pasta =  $pasta . iconv('ISO-8859-1', 'ASCII//IGNORE', $this->_nome_arquivo); // bug de escrita do nome do arquivo

		clearstatcache();

		chmod(dirname($pasta), 0777);
		chmod($pasta, 0777);

		if (!file_exists(dirname($pasta)))
			mkdir(dirname($pasta), 0777, true);

		chmod(dirname($pasta), 0777);
		chmod($pasta, 0777);

		$conteudo = $this->_dompdf->output();
		$fp = fopen($pasta, 'w', false);
		if ($fp) {
			//fwrite($fp, $conteudo);
			file_put_contents($pasta, $conteudo);
			fclose($fp);
		}

		unset($this->_dompdf);
	}

	public function obterTipoSaida() {
		return Modulo::ARQUIVO;
	}
}

?>
