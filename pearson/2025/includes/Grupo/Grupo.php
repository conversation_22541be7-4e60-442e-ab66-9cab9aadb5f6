<?
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('Modelo_Abstrato', null, true);

class Grupo extends Core_Modelo_Abstrato
{
	public function __construct ($id)
	{
		parent::__construct($id);

		$this->_dados['nome'] = NULL;
		$this->_dados['descricao'] = NULL;
	}

	static public function &novo ($nome, $descricao)
	{
		return parent::novo(array(
			'nome' => $nome,
			'descricao' => $descricao
		), get_class());
	}

	static public function obterArrayParaFormulario ($coluna = 'nome')
	{
		$dados = array();

		$rs = Core::registro('db')->query( sprintf(
			'SELECT * FROM grupos ORDER BY g_%s ASC',
			$coluna
		) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				if ($row['g_id'])
					$dados[$row['g_id']] = isset($row['g_'.$coluna]) ? $row['g_'.$coluna] : '';
			}
		}
		$rs->free();

		return $dados;
	}
}

?>