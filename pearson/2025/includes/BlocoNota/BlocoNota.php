<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class BlocoNota {
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'texto' => null,
								'data' => null,
								'usuario' => null,
								'identificador' => null );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoBlocoNota ($nome, $identificador, Usuario &$usuario = null)
	{
		if ($usuario == null)
			$usuario = Core::registro('usuario');
		
		$data = time();
			
		Core::registro('db')->query( sprintf('INSERT INTO bloco_notas(bn_nome, bn_data, bn_identificador, bn_usuario) VALUES (%s, %s, %s, %s)', 
			Core::registro('db')->formatarValor( $nome ),
			Core::registro('db')->formatarValor( $data ),
			Core::registro('db')->formatarValor( $identificador ),
			Core::registro('db')->formatarValor( $usuario->obterID() ) ) );

		$obj = new BlocoNota(Core::registro('db')->insert_id);
		
		$obj->fixarNome($nome);
		$obj->fixarData($data);
		$obj->fixarUsuario($usuario);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('
				UPDATE bloco_notas SET bn_nome = %s, bn_texto = %s, bn_data = %s, bn_usuario = %s, bn_identificador = %s WHERE bn_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['texto']),
				Core::registro('db')->formatarValor($this->_dados['data']),
				Core::registro('db')->formatarValor($this->_dados['usuario']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['identificador']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}
	
	public function remover () {		
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('DELETE FROM bloco_notas WHERE bn_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$this->fixarID(null);

			return Core::registro('db')->errno == 0;
		}
		
		return false;
	}
	
	public function carregar ($carregarUsuario = false)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM bloco_notas WHERE bn_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNome($row['bn_nome']);
				$this->fixarTexto($row['bn_texto']);
				$this->fixarData($row['bn_data']);
				$this->fixarUsuario(new UsuarioInstituido($row['bn_usuario']));
				$this->fixarIdentificador($row['bn_identificador']);
				
				if ($carregarUsuario) {
					if ($row['bn_usuario'] == Core::registro('usuario')->obterID())
						$this->fixarUsuario(Core::registro('usuario'));
					else
						@$this->obterUsuario()->carregar();
				}

				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}
	
	public function obterNome ()
	{
		return $this->_dados['nome'];
	}
	
	public function fixarTexto ($texto)
	{
		$this->_dados['texto'] = $texto;
	}
	
	public function obterTexto ()
	{
		return $this->_dados['texto'];
	}
	
	public function fixarData ($data)
	{
		$this->_dados['data'] = $data;
	}
	
	public function obterData ($formatada = false)
	{
		if ($formatada && $this->_dados['data'] != null) 
			return strftime('%d/%m/%Y %H:%M', $this->_dados['data']);
		else
			return $this->_dados['data'];
	}
	
	public function fixarIdentificador ($identificador)
	{
		$this->_dados['identificador'] = $identificador;
	}
	
	public function obterIdentificador ()
	{
		return $this->_dados['identificador'];
	}
	
	public function fixarUsuario (Usuario &$usuario)
	{
		$this->_dados['usuario'] = $usuario;
	}
	
	public function &obterUsuario ()
	{
		return $this->_dados['usuario'];
	}

	public function podeRemover ()
	{
		return true;
	}
	
	public function validarInstituicao ()
	{
		return true;
	}
	
	static public function obterNumeroNotas ($identificador, Usuario &$usuario = null)
	{
		$total = 0;
		
		if ($usuario == null)
			$usuario = Core::registro('usuario');
				
		$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM bloco_notas WHERE bn_identificador = %s AND bn_usuario = %s', 
			Core::registro('db')->formatarValor( $identificador ),
			Core::registro('db')->formatarValor( $usuario->obterID() ) ) );
			
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();

			$total = $row['total'];
		}
		$rs->free();
		
		return $total;
	}
}

?>