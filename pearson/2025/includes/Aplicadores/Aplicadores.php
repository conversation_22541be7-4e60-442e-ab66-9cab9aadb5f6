<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class Aplicadores {
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'subid' => null,
								'usuario' => null,
								'data_aplicacao' => null,
								'periodo' => null,
								'aplicacao' => null,
								'ci_instituicao' => null,
								'ci_instituicao_datahora' => null,
								'co_instituicao' => null,
								'co_instituicao_datahora' => null,
								'ci_turma' => null,
								'ci_turma_datahora' => null,
								'oculto' => 0 );
		
		$this->fixarID($id);
	}
	
	static public function obterNovoAplicador ($usuario)
	{
		Core::registro('db')->query( sprintf('INSERT INTO aplicadores (ap_usuario) VALUES (%s)', 
			Core::registro('db')->formatarValor($usuario) ) );

		$obj = new Aplicadores(Core::registro('db')->insert_id);
		
		$obj->fixarUsuario($usuario);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('
				UPDATE aplicadores SET ap_subid = %s, ap_usuario = %s, ap_data_aplicacao = %s, ap_periodo = %s, ap_aplicacao = %s, ap_ci_instituicao = %s, ap_ci_instituicao_datahora = %s, ap_co_instituicao = %s, ap_co_instituicao_datahora = %s, ap_ci_turma = %s, ap_ci_turma_datahora = %s, ap_oculto = %s
				WHERE ap_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['subid']),
				Core::registro('db')->formatarValor($this->_dados['usuario']),
				Core::registro('db')->formatarValor($this->_dados['data_aplicacao']),
				Core::registro('db')->formatarValor($this->_dados['periodo']),
				Core::registro('db')->formatarValor($this->_dados['aplicacao']),
				Core::registro('db')->formatarValor($this->_dados['ci_instituicao']),
				Core::registro('db')->formatarValor($this->_dados['ci_instituicao_datahora']),
				Core::registro('db')->formatarValor($this->_dados['co_instituicao']),
				Core::registro('db')->formatarValor($this->_dados['co_instituicao_datahora']),
				Core::registro('db')->formatarValor($this->_dados['ci_turma']),
				Core::registro('db')->formatarValor($this->_dados['ci_turma_datahora']),
				Core::registro('db')->formatarValor($this->_dados['oculto']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}
	
	public function remover () {		
		if ($this->_dados['id'] != null) {
			Core::registro('db')->query( sprintf('
				UPDATE aplicadores SET ap_oculto = %s
				WHERE ap_id = %s', 
				Core::registro('db')->formatarValor(1),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM aplicadores WHERE ap_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarSubID($row['ap_subid']);
				$this->fixarUsuario($row['ap_usuario']);
				$this->fixarDataAplicacao($row['ap_data_aplicacao']);
				$this->fixarPeriodo($row['ap_periodo']);
				$this->fixarAplicacao($row['ap_aplicacao']);
				$this->fixarCIInstituicao($row['ap_ci_instituicao']);
				$this->fixarCIInstituicaoDT($row['ap_ci_instituicao_datahora']);
				$this->fixarCOInstituicao($row['ap_co_instituicao']);
				$this->fixarCOInstituicaoDT($row['ap_co_instituicao_datahora']);
				$this->fixarCITurma($row['ap_ci_turma']);
				$this->fixarCITurmaDT($row['ap_ci_turma_datahora']);
				$this->fixarOculto($row['ap_oculto']);

				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarSubID ($subid)
	{
		$this->_dados['subid'] = $subid;
	}
	
	public function obterSubID ()
	{
		return $this->_dados['subid'];
	}
	
	public function fixarUsuario ($usuario)
	{
		$this->_dados['usuario'] = $usuario;
	}
	
	public function obterUsuario ()
	{
		return $this->_dados['usuario'];
	}
	
	public function fixarDataAplicacao ($data_aplicacao)
	{
		$this->_dados['data_aplicacao'] = $data_aplicacao;
	}
	
	public function obterDataAplicacao ()
	{
		if ($formatada && $this->_dados['data_aplicacao'] != null) 
			return strftime('%d/%m/%Y', $this->_dados['data_aplicacao']);
		else
			return $this->_dados['data_aplicacao'];
	}
	
	public function fixarPeriodo ($periodo)
	{
		$this->_dados['periodo'] = $periodo;
	}
	
	public function obterPeriodo ()
	{
		return $this->_dados['periodo'];
	}
	
	public function fixarAplicacao ($aplicacao)
	{
		$this->_dados['aplicacao'] = $aplicacao;
	}
	
	public function obterAplicacao ()
	{
		return $this->_dados['aplicacao'];
	}
	
	public function fixarCIInstituicao ($ci_instituicao)
	{
		$this->_dados['ci_instituicao'] = $ci_instituicao;
	}
	
	public function obterCIInstituicao ()
	{
		return $this->_dados['ci_instituicao'];
	}
	
	public function fixarCIInstituicaoDT ($instituicao_datahora)
	{
		$this->_dados['ci_instituicao_datahora'] = $instituicao_datahora;
	}
	
	public function obterCIInstituicaoDT ()
	{
		if ($formatada && $this->_dados['ci_instituicao_datahora'] != null) 
			return strftime('%d/%m/%Y', $this->_dados['ci_instituicao_datahora']);
		else
			return $this->_dados['ci_instituicao_datahora'];
	}
	
	public function fixarCOInstituicao ($co_instituicao)
	{
		$this->_dados['co_instituicao'] = $co_instituicao;
	}
	
	public function obterCOInstituicao ()
	{
		return $this->_dados['co_instituicao'];
	}
	
	public function fixarCOInstituicaoDT ($co_instituicao_datahora)
	{
		$this->_dados['co_instituicao_datahora'] = $co_instituicao_datahora;
	}
	
	public function obterCOInstituicaoDT ()
	{
		if ($formatada && $this->_dados['co_instituicao_datahora'] != null) 
			return strftime('%d/%m/%Y', $this->_dados['co_instituicao_datahora']);
		else
			return $this->_dados['co_instituicao_datahora'];
	}
	
	public function fixarCITurma ($ci_turma)
	{
		$this->_dados['ci_turma'] = $ci_turma;
	}
	
	public function &obterCITurma ()
	{
		return $this->_dados['ci_turma'];
	}
	
	public function fixarCITurmaDT ($ci_turma_datahora)
	{
		$this->_dados['ci_turma_datahora'] = $ci_turma_datahora;
	}
	
	public function obterCITurmaDT ($formatada = false)
	{
		if ($formatada && $this->_dados['ci_turma_datahora'] != null) 
			return strftime('%d/%m/%Y', $this->_dados['ci_turma_datahora']);
		else
			return $this->_dados['ci_turma_datahora'];
	}
	
	public function fixarOculto ($oculto)
	{
		$this->_dados['oculto'] = $oculto;
	}
	
	public function obterOculto ()
	{
		return $this->_dados['oculto'];
	}

	public function podeRemover ()
	{
		return true;
	}
	
	static public function verificarExistenciaInstituicao ($id)
	{
		$rs = Core::registro('db')->query( sprintf('SELECT i_nome FROM instituicoes WHERE i_id = %s LIMIT 1;', 
			Core::registro('db')->formatarValor($id) ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				return $row['i_nome'];
			}
		}	
		$rs->free();
		
		return false;
	}
	
	static public function verificarExistenciaTurma ($id)
	{
		$rs = Core::registro('db')->query( sprintf('SELECT CONCAT(s_nome, " - ", t_nome) as turma FROM turmas INNER JOIN series ON t_serie = s_id WHERE t_id = %s LIMIT 1;', 
			Core::registro('db')->formatarValor($id) ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				return $row['turma'];
			}
		}	
		$rs->free();
		
		return false;
	}
	
	static public function verificarAplicadorNaInstituicao ($uid, $iid, $p, $data, $aplicacao = null)
	{
		if($aplicacao != null){
			$rs = Core::registro('db')->query( sprintf('SELECT ap_id FROM aplicadores WHERE ap_usuario = %s AND ap_ci_instituicao = %s AND ap_periodo = %s AND ap_data_aplicacao = %s AND ap_aplicacao = %s LIMIT 1;', 
				Core::registro('db')->formatarValor($uid),
				Core::registro('db')->formatarValor($iid),
				Core::registro('db')->formatarValor($p),
				Core::registro('db')->formatarValor($data),
				Core::registro('db')->formatarValor($aplicacao) ) );
		}
		else{
			$rs = Core::registro('db')->query( sprintf('SELECT ap_id FROM aplicadores WHERE ap_usuario = %s AND ap_ci_instituicao = %s AND ap_periodo = %s AND ap_data_aplicacao = %s LIMIT 1;', 
				Core::registro('db')->formatarValor($uid),
				Core::registro('db')->formatarValor($iid),
				Core::registro('db')->formatarValor($p),
				Core::registro('db')->formatarValor($data) ) );
		}
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				return $row['ap_id'];
			}
		}	
		$rs->free();
		
		return false;
	}
	
	static public function verificarAplicadorNaInstituicaoETurma ($uid, $iid, $tid, $p, $data, $aplicacao = null)
	{
		if($aplicacao != null){
			$rs = Core::registro('db')->query( sprintf('SELECT ap_id FROM aplicadores WHERE ap_usuario = %s AND ap_ci_instituicao = %s AND ap_ci_turma = %s AND ap_periodo = %s AND ap_data_aplicacao = %s AND ap_aplicacao = %s LIMIT 1;', 
				Core::registro('db')->formatarValor($uid),
				Core::registro('db')->formatarValor($iid),
				Core::registro('db')->formatarValor($tid),
				Core::registro('db')->formatarValor($p),
				Core::registro('db')->formatarValor($data),
				Core::registro('db')->formatarValor($aplicacao) ) );
		}
		else{
			$rs = Core::registro('db')->query( sprintf('SELECT ap_id FROM aplicadores WHERE ap_usuario = %s AND ap_ci_instituicao = %s AND ap_ci_turma = %s AND ap_periodo = %s AND ap_data_aplicacao = %s LIMIT 1;', 
				Core::registro('db')->formatarValor($uid),
				Core::registro('db')->formatarValor($iid),
				Core::registro('db')->formatarValor($tid),
				Core::registro('db')->formatarValor($p),
				Core::registro('db')->formatarValor($data) ) );
		}
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				return $row['ap_id'];
			}
		}	
		$rs->free();
		
		return false;
	}

	public function obterDadosPeloUsuario (UsuarioInstituido &$usuario)
	{
		$professores = array();

		if ( $usuario != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM aplicadores WHERE ap_usuario = %s',
				Core::registro('db')->formatarValor( $usuario->obterID() ) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){
					$professores[$row['ap_id']]['ap_subid'] = $row['ap_subid'];
					$professores[$row['ap_id']]['ap_usuario'] = $row['ap_usuario'];
					$professores[$row['ap_id']]['ap_data_aplicacao'] = $row['ap_data_aplicacao'];
					$professores[$row['ap_id']]['ap_periodo'] = $row['ap_periodo'];
					$professores[$row['ap_id']]['ap_aplicacao'] = $row['ap_aplicacao'];
					$professores[$row['ap_id']]['ap_ci_instituicao'] = $row['ap_ci_instituicao'];
					$professores[$row['ap_id']]['ap_ci_instituicao_datahora'] = $row['ap_ci_instituicao_datahora'];
					$professores[$row['ap_id']]['ap_co_instituicao'] = $row['ap_co_instituicao'];
					$professores[$row['ap_id']]['ap_co_instituicao_datahora'] = $row['ap_co_instituicao_datahora'];
					$professores[$row['ap_id']]['ap_ci_turma'] = $row['ap_ci_turma'];
					$professores[$row['ap_id']]['ap_ci_turma_datahora'] = $row['ap_ci_turma_datahora'];
					$professores[$row['ap_id']]['ap_oculto'] = $row['ap_oculto'];
				}
			}
			$rs->free();
		}

		return $professores;
	}

	public function obterIDInstituicaoPorAplicadorDataPeriodo ($usuario, $data, $periodo)
	{
		$instituicoes = array();

		$rs = Core::registro('db')->query( sprintf('SELECT * FROM aplicadores WHERE ap_usuario = %s AND ap_data_aplicacao = %s AND ap_periodo = %s AND ap_aplicacao = %s LIMIT 1',
			Core::registro('db')->formatarValor($usuario),
			Core::registro('db')->formatarValor($data),
			Core::registro('db')->formatarValor($periodo),
			Core::registro('db')->formatarValor($aplicacao) ) );

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()){
				return $row['ap_ci_instituicao'];
			}
		}
		$rs->free();

		return $instituicoes;
	}
	
	static public function carregarPeloSubID ($subid)
	{
		$professores = array();

		$rs = Core::registro('db')->query( sprintf('SELECT * FROM aplicadores WHERE ap_subid = %s;', 
			Core::registro('db')->formatarValor($subid) ) );
			
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){
					$professores[$row['ap_id']]['ap_id'] = $row['ap_id'];
					$professores[$row['ap_id']]['ap_subid'] = $row['ap_subid'];
					$professores[$row['ap_id']]['ap_usuario'] = $row['ap_usuario'];
					$professores[$row['ap_id']]['ap_data_aplicacao'] = $row['ap_data_aplicacao'];
					$professores[$row['ap_id']]['ap_periodo'] = $row['ap_periodo'];
					$professores[$row['ap_id']]['ap_aplicacao'] = $row['ap_aplicacao'];
					$professores[$row['ap_id']]['ap_ci_instituicao'] = $row['ap_ci_instituicao'];
					$professores[$row['ap_id']]['ap_ci_instituicao_datahora'] = $row['ap_ci_instituicao_datahora'];
					$professores[$row['ap_id']]['ap_co_instituicao'] = $row['ap_co_instituicao'];
					$professores[$row['ap_id']]['ap_co_instituicao_datahora'] = $row['ap_co_instituicao_datahora'];
					$professores[$row['ap_id']]['ap_ci_turma'] = $row['ap_ci_turma'];
					$professores[$row['ap_id']]['ap_ci_turma_datahora'] = $row['ap_ci_turma_datahora'];
					$professores[$row['ap_id']]['ap_oculto'] = $row['ap_oculto'];

				}
			}
			$rs->free();
		
		return $professores;
	}
	
	static public function obterIDTurmaComNomeEAno ($tnome,$aid,$escola)
	{
		$rs = Core::registro('db')->query( sprintf('SELECT t_id FROM turmas INNER JOIN series ON t_serie = s_id WHERE t_instituicao = %s AND t_nome = %s AND s_nome = %s LIMIT 1;', 
			Core::registro('db')->formatarValor($escola),
			Core::registro('db')->formatarValor($tnome),
			Core::registro('db')->formatarValor($aid) ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				return $row['t_id'];
			}
		}	
		$rs->free();
		
		return false;
	}
	
	static public function verificarAplicadorNaInstituicaoII ($uid, $iid, $p, $data)
	{
		$dados = array();

		$rs = Core::registro('db')->query( sprintf('SELECT * FROM aplicadores WHERE ap_usuario = %s AND ap_ci_instituicao = %s AND ap_periodo = %s AND ap_data_aplicacao = %s ORDER BY ap_ci_turma_datahora DESC LIMIT 1;', 
			Core::registro('db')->formatarValor($uid),
			Core::registro('db')->formatarValor($iid),
			Core::registro('db')->formatarValor($p),
			Core::registro('db')->formatarValor($data) ) );
			
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){
					$dados[$row['ap_id']]['ap_id'] = $row['ap_id'];
					$dados[$row['ap_id']]['ap_subid'] = $row['ap_subid'];
					$dados[$row['ap_id']]['ap_usuario'] = $row['ap_usuario'];
					$dados[$row['ap_id']]['ap_data_aplicacao'] = $row['ap_data_aplicacao'];
					$dados[$row['ap_id']]['ap_periodo'] = $row['ap_periodo'];
					$dados[$row['ap_id']]['ap_aplicacao'] = $row['ap_aplicacao'];
					$dados[$row['ap_id']]['ap_ci_instituicao'] = $row['ap_ci_instituicao'];
					$dados[$row['ap_id']]['ap_ci_instituicao_datahora'] = $row['ap_ci_instituicao_datahora'];
					$dados[$row['ap_id']]['ap_co_instituicao'] = $row['ap_co_instituicao'];
					$dados[$row['ap_id']]['ap_co_instituicao_datahora'] = $row['ap_co_instituicao_datahora'];
					$dados[$row['ap_id']]['ap_ci_turma'] = $row['ap_ci_turma'];
					$dados[$row['ap_id']]['ap_ci_turma_datahora'] = $row['ap_ci_turma_datahora'];
					$dados[$row['ap_id']]['ap_oculto'] = $row['ap_oculto'];

				}
			}
			$rs->free();
		
		return $dados;
	}
}

?>