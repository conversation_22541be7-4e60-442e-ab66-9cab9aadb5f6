<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Simulado', null, true);
Core::incluir('CursoVestibular', null, true);
Core::incluir('Aluno', null, true);
Core::incluir('Disciplina', null, true);
Core::incluir('Resposta', null, true);

class Inscricao
{

	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'aluno' => null,
								'curso_vestibular' => null,
								'lingua' => null,
								'tipo' => null,
								'respostas' => array(),
								'respostas_array' => array(),
								'simulado' => null,
								'tempo' => null,
								'finalizado' => '',
								'finalizado_data' => '',
								'atestado' => 0,
								'ausente' => null,
								'ausente_por_fases' => array(),
								'ed_integral' => 0,
								'nao_alfabetico' => 0,
								'nota_anterior' => 0	);
		
		$this->fixarID($id);
	}
	
	static public function &obterNovaInscricao (Simulado &$simu, Aluno &$aluno)
	{
		Core::registro('db')->query( sprintf('INSERT INTO simulados_inscricoes (si_simulado, si_aluno) VALUES (%s, %s)', 
			Core::registro('db')->formatarValor( @$simu->obterID() ),
			Core::registro('db')->formatarValor( @$aluno->obterID() ) ) );

		$obj = new Inscricao ( Core::registro('db')->insert_id );
		
		$obj->fixarSimulado( $simu );
		$obj->fixarAluno( $aluno );
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			if ($this->_dados['curso_vestibular'] == null) { $this->_dados['curso_vestibular'] = new CursoVestibular(null); }
			if ($this->_dados['lingua'] == null) { $this->_dados['lingua'] = new Disciplina(null); }
			
			Core::registro('db')->query( sprintf('UPDATE simulados_inscricoes SET si_tipo = %s, si_curso_vestibular = %s, si_lingua = %s, si_aluno = %s, si_simulado = %s, si_tempo = %s, si_finalizado = %s, si_finalizado_data = %s, si_atestado = %s, si_integral = %s, si_nao_alfabetico = %s WHERE si_id = %s',
				Core::registro('db')->formatarValor( $this->_dados['tipo'] ),
				Core::registro('db')->formatarValor( $this->_dados['curso_vestibular']->obterID() ),
				Core::registro('db')->formatarValor( $this->_dados['lingua']->obterID() ),
				Core::registro('db')->formatarValor( $this->_dados['aluno']->obterID() ),
				Core::registro('db')->formatarValor( $this->_dados['simulado']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['tempo']),
				Core::registro('db')->formatarValor( $this->_dados['finalizado'] ),
				Core::registro('db')->formatarValor($this->_dados['finalizado_data']),
				Core::registro('db')->formatarValor( $this->_dados['atestado'] ),
				Core::registro('db')->formatarValor( $this->_dados['ed_integral'] ),
				Core::registro('db')->formatarValor( $this->_dados['nao_alfabetico'] ),
				Core::registro('db')->formatarValor( $this->_dados['id'] ) ) );

			$retorno = (Core::registro('db')->errno == 0);
			
			foreach ($this->_dados['respostas'] as &$r) {
				if ( !$r->salvar() ) {
					$retorno = false;
				}
			}
			
			return $retorno;
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM simulados_inscricoes, respostas USING simulados_inscricoes 
												  LEFT JOIN respostas ON respostas.r_inscricao = simulados_inscricoes.si_id 
												  WHERE si_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar (Simulado &$simu = null, Aluno &$aluno = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM simulados_inscricoes WHERE si_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$curso = new CursoVestibular( $row['si_curso_vestibular'] );
				$curso->carregar();
				$this->fixarCursoVestibular( $curso );
				
				$disciplina = new Disciplina( $row['si_lingua'] );
				$disciplina->carregar();
				$this->fixarLingua( $disciplina );
				
				$this->fixarTipo($row['si_tipo']);
				
				if ( $simu == null ) {
					$simu = new Simulado($row['si_simulado']);
					$simu->carregar();
				}
				$this->fixarSimulado( $simu );
				
				if ( $aluno == null ) {
					$aluno = new Aluno($row['si_aluno']);
					$aluno->carregar();
				}
				$this->fixarAluno( $aluno );

				$this->fixarTempoTotalDeRealizacaoDoAlunoNoSimulado($row['si_tempo']);

				$this->fixarSimuladoFinalizado($row['si_finalizado']);

				$this->fixarSimuladoFinalizadoData($row['si_finalizado_data']);
				
				$this->fixarAtestado($row['si_atestado']);
				$this->fixarEdIntegral($row['si_integral']);
				$this->fixarNaoAlfabetico($row['si_nao_alfabetico']);

				$this->carregarRespostasMarcadas();
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function carregarSemRespostas (Simulado &$simu = null, Aluno &$aluno = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM simulados_inscricoes WHERE si_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$curso = new CursoVestibular( $row['si_curso_vestibular'] );
				$curso->carregar();
				$this->fixarCursoVestibular( $curso );
				
				$disciplina = new Disciplina( $row['si_lingua'] );
				$disciplina->carregar();
				$this->fixarLingua( $disciplina );
				
				$this->fixarTipo($row['si_tipo']);
				
				if ( $simu == null ) {
					$simu = new Simulado($row['si_simulado']);
					$simu->carregar();
				}
				$this->fixarSimulado( $simu );
				
				if ( $aluno == null ) {
					$aluno = new Aluno($row['si_aluno']);
					$aluno->carregar();
				}
				$this->fixarAluno( $aluno );

				$this->fixarTempoTotalDeRealizacaoDoAlunoNoSimulado($row['si_tempo']);

				$this->fixarSimuladoFinalizado($row['si_finalizado']);

				$this->fixarSimuladoFinalizadoData($row['si_finalizado_data']);
				
				$this->fixarAtestado($row['si_atestado']);
				$this->fixarEdIntegral($row['si_integral']);
				$this->fixarNaoAlfabetico($row['si_nao_alfabetico']);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarTempoTotalDeRealizacaoDoAlunoNoSimulado($tempo)
	{
		$this->_dados['tempo'] = $tempo;
	}

	public function obterTempoTotalDeRealizacaoDoAlunoNoSimulado()
	{
		return $this->_dados['tempo'];
	}

	public function fixarSimuladoFinalizado ($fim)
	{
		$this->_dados['finalizado'] = $fim;
	}

	public function obterSimuladoFinalizado ()
	{
		return $this->_dados['finalizado'];
	}

	public function fixarSimuladoFinalizadoData ($data)
	{
		$this->_dados['finalizado_data'] = $data;
	}

	public function obterSimuladoFinalizadoData ()
	{
		return $this->_dados['finalizado_data'];
	}
	
	public function fixarAtestado ($atestado)
	{
		$this->_dados['atestado'] = $atestado;
	}
	
	public function obterAtestado ()
	{
		return $this->_dados['atestado'];
	}
	
	public function fixarEdIntegral ($ed_integral)
	{
		$this->_dados['ed_integral'] = $ed_integral;
	}
	
	public function obterEdIntegral ()
	{
		return $this->_dados['ed_integral'];
	}

	public function fixarNaoAlfabetico ($nao_alfabetico)
	{
		$this->_dados['nao_alfabetico'] = $nao_alfabetico;
	}
	
	public function obterNaoAlfabetico ()
	{
		return $this->_dados['nao_alfabetico'];
	}
	
	public function fixarAusente ($ausente)
	{
		$this->_dados['ausente'] = $ausente;
	}
	
	public function obterAusente ()
	{
		return $this->_dados['ausente'];
	}
	
	public function fixarAusentePorFases ($ausencias)
	{
		$this->_dados['ausente_por_fases'] = $ausencias;
	}
	
	public function obterAusentePorFases ()
	{
		return $this->_dados['ausente_por_fases'];
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}

	public function fixarTipo ($tipo = null)
	{
		if ( !empty($tipo) )
			$tipo = (int) $tipo;
			
		if ($tipo == 0)
			$tipo = 1;

		$this->_dados['tipo'] = $tipo;
	}
	
	public function obterTipo ()
	{
		return $this->_dados['tipo'];
	}
	
	public function fixarLingua (Disciplina &$lingua)
	{
		$this->_dados['lingua'] = $lingua;
	}
	
	public function &obterLingua ()
	{
		return $this->_dados['lingua'];
	}
	
	public function fixarCursoVestibular (CursoVestibular &$curso)
	{
		$this->_dados['curso_vestibular'] = $curso;
	}
	
	public function &obterCursoVestibular ()
	{
		return $this->_dados['curso_vestibular'];
	}
	
	public function fixarSimulado (Simulado &$simu)
	{
		$this->_dados['simulado'] = $simu;
	}
	
	public function &obterSimulado ()
	{
		return $this->_dados['simulado'];
	}

	public function fixarNotaAnterior ($nota_anterior) {
		$this->_dados['nota_anterior'] = $nota_anterior;
	}

	public function obterNotaAnterior () {
		return $this->_dados['nota_anterior'];
	}
	
	public function fixarAluno (Aluno &$aluno)
	{
		$this->_dados['aluno'] = $aluno;
	}
	
	public function &obterAluno ()
	{
		return $this->_dados['aluno'];
	}
	
	public function adicionarRespostaMarcada (Resposta &$resposta)
	{
		$this->_dados['respostas'][] = $resposta;
	}
	
	public function removerRespostaMarcada ($index)
	{
		unset($this->_dados['respostas'][$index]);
	}
	
	public function &obterRespostasMarcadas ()
	{
		return $this->_dados['respostas'];
	}

	public function limparRespostasMarcadas ()
	{
		$this->_dados['respostas'] = array();
	}
	
	public function adicionarRespostaArray ($resposta)
	{
		$this->_dados['respostas_array'][] = $resposta;
	}
	
	public function fixarRespostasArray ($respostas)
	{
		$this->_dados['respostas_array']= $respostas;
	}
	
	public function &obterRespostasArray ()
	{
		return $this->_dados['respostas_array'];
	}

	public function podeRemover ()
	{
		return true;
	}
	
	public function validarInstituicao ()
	{
		if ( $this->_dados['id'] != null && $this->_dados['aluno'] != null && $this->_dados['aluno']->obterTurma() != null ) {
			return $this->_dados['aluno']->obterTurma()->validarInstituicao();
		}
		
		return false;
	}
	
	public function carregarRespostasMarcadas ()
	{
		if ( $this->_dados['id'] != null && $this->_dados['aluno'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM respostas WHERE r_inscricao = %s',
				Core::registro('db')->formatarValor( $this->_dados['id'] ) ) );
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
				{
					$obj = new Resposta($row['r_id']);
					$obj->fixarValor($row['r_valor']);
					$questao = new Questao($row['r_questao']);
					$questao->carregar();
					$obj->fixarQuestao( $questao );
					$obj->fixarStatus($row['r_status']);
					$obj->fixarChute($row['r_chute']);
					$obj->fixarTempo($row['r_tempo']);
					$obj->fixarNivelSeguranca($row['r_nivel_seguranca']);
					$obj->fixarComentarioAluno($row['r_comentario_aluno']);
					$obj->fixarInscricao( $this );
					
					$this->adicionarRespostaMarcada($obj);
				}
			}
			else{
				$this->fixarAusente(1);
			}
			$rs->free();
		}
	}

	public function carregarRespostasFluencia ()
	{
		$obj = array();

		if ( $this->_dados['id'] != null && $this->_dados['aluno'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM respostas WHERE r_inscricao = %s',
				Core::registro('db')->formatarValor( $this->_dados['id'] ) ) );
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
				{
					$ri = $row['r_id'];
					$rv = $row['r_valor'];
					$rq = $row['r_questao'];

					$obj[$rq] = $rv;
					//$this->adicionarRespostaMarcada($obj);
				}
			}
			else{
				$this->fixarAusente(1);
			}
			$rs->free();

			return $obj;
		}
	}
	
	static public function obterIDInscricaoPeloAluno (Simulado &$simu, Aluno &$aluno)
	{
		$id = false;

		$rs = Core::registro('db')->query( sprintf('SELECT si_id FROM simulados_inscricoes WHERE si_aluno = %s AND si_simulado = %s',
			Core::registro('db')->formatarValor($aluno->obterID()),
			Core::registro('db')->formatarValor($simu->obterID()) ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$id = $row['si_id'];
		}
		$rs->free();
		
		return $id;
	}
	
	static public function obterSimuladoPeloAluno (Aluno &$aluno)
	{
		$id = false;

		$rs = Core::registro('db')->query( sprintf('SELECT si_simulado FROM simulados_inscricoes WHERE si_aluno = %s',
			Core::registro('db')->formatarValor($aluno->obterID()) ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			$id = $row['si_simulado'];
		}
		$rs->free();
		
		return $id;
	}
	
	static public function obterSimuladosPeloAlunoPorSecao (Aluno &$aluno, $secao = '')
	{
		$ids = array();

		$rs = Core::registro('db')->query( sprintf('
			SELECT si_simulado, s_nome 
			FROM simulados_inscricoes 
			INNER JOIN simulados ON si_simulado = s_id
			WHERE si_aluno = %s AND s_secao_rels = %s',
			Core::registro('db')->formatarValor($aluno->obterID()),
			Core::registro('db')->formatarValor($secao) 
		) );

		if ($rs->num_rows > 0) {
			while ($row = $rs->fetch_assoc()){
				$ids[$row['si_simulado']] = $row['s_nome'];
			}
		}
		$rs->free();
		
		return $ids;
	}
	
	static public function obterIDAlunoPeloIDInscricao ($idInscricao)
	{
		$rs = Core::registro('db')->query( sprintf('SELECT si_aluno FROM simulados_inscricoes WHERE si_id = %s',
			Core::registro('db')->formatarValor( $idInscricao ) ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			return $row['si_aluno'];
		}
		$rs->free();
		
		return false;
	}
	
	static public function obterIDSimuladoPeloIDInscricao ($idInscricao)
	{
		$rs = Core::registro('db')->query( sprintf('
			SELECT si_simulado FROM simulados_inscricoes 
			WHERE si_id = %s',
			Core::registro('db')->formatarValor( $idInscricao ) ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			return $row['si_simulado'];
		}
		$rs->free();
		
		return false;
	}

	static public function removerInscritosPelaTurma (Simulado &$simu, Turma &$turma)
	{
		if ( $turma != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM simulados_inscricoes, respostas USING simulados_inscricoes 
												  LEFT JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno 
												  LEFT JOIN respostas ON respostas.r_inscricao = simulados_inscricoes.si_id 
												  WHERE simulados_inscricoes.si_simulado = %s AND alunos.a_turma = %s',
				Core::registro('db')->formatarValor($simu->obterID()),
				Core::registro('db')->formatarValor($turma->obterID())) );
			
			return (Core::registro('db')->errno == 0);
		}
		
		return true;
	}
	
	static public function atualizarInscricoesDefasadas (Simulado &$simu, $novoNumeroTipos)
	{
		if ( $simu->obterNumeroDeTipos() > $novoNumeroTipos ) {
			Core::registro('db')->query( sprintf('UPDATE simulados_inscricoes 
												  SET si_tipo = NULL 
												  WHERE si_simulado = %s AND si_tipo > %s',
				Core::registro('db')->formatarValor( $simu->obterID() ),
				Core::registro('db')->formatarValor( $novoNumeroTipos )) );
		}
	}
	
	static public function removerInscricoesPeloAluno (Aluno &$aluno, Simulado &$simu = null)
	{	
		if ( $aluno != null ) {
			$simuladoSQL = '';
			if ( $simu != null ) {
				$simuladoSQL = ' AND simulados_inscricoes.si_simulado = ' . Core::registro('db')->formatarValor( $simu->obterID() );
			}
		
			Core::registro('db')->query( sprintf('DELETE FROM simulados_inscricoes, respostas USING simulados_inscricoes 
												  LEFT JOIN respostas ON respostas.r_inscricao = simulados_inscricoes.si_id 
												  WHERE simulados_inscricoes.si_aluno = %s %s',
				Core::registro('db')->formatarValor( $aluno->obterID() ),
				$simuladoSQL ) );
			
			return (Core::registro('db')->errno == 0);
		}
		
		return true;
	}
	
	static public function removerInscricoesPeloSimulado (Simulado &$simu)
	{	
		if ( $simu != null ) {		
			Core::registro('db')->query( sprintf('DELETE FROM simulados_inscricoes, respostas USING simulados_inscricoes 
												  LEFT JOIN respostas ON respostas.r_inscricao = simulados_inscricoes.si_id 
												  WHERE simulados_inscricoes.si_simulado = %s',
				Core::registro('db')->formatarValor( $simu->obterID() ) ) );
			
			return (Core::registro('db')->errno == 0);
		}
		
		return true;
	}

	static public function obterIDInscricaoPeloNomeAluno ($nome, Simulado $simulado) {
		if ($simulado != null) {
			$rs = Core::registro('db')->query( sprintf('
				SELECT si_id FROM simulados_inscricoes 
				INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno 
				INNER JOIN usuarios ON usuarios.u_id = alunos.a_usuario 
				WHERE si_simulado = %s AND usuarios.u_nome = %s AND usuarios.u_instituicao = %s',
				Core::registro('db')->formatarValor( $simulado->obterID() ),
				Core::registro('db')->formatarValor( $nome ),
				Core::registro('db')->formatarValor( Core::registro('instituicao')->obterID() ) ) );
	
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				return $row['si_id'];
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function verificarAusente ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT r_id FROM respostas WHERE r_inscricao = %s',
				Core::registro('db')->formatarValor( $this->_dados['id'] ) ) );
				
			if ($rs->num_rows > 0) {
				$this->fixarAusente(0);
			}
			else{
				$this->fixarAusente(1);
			}
			$rs->free();
		}
	}
	
	public function verificarAusentePorFases ()
	{
		if ($this->_dados['id'] != null) {
			$rs = Core::registro('db')->query(sprintf('SELECT q_fase_duracao, count(r_id) as qtd
														FROM respostas
														INNER JOIN questoes ON r_questao = q_id
														WHERE r_inscricao = %s
														GROUP BY q_fase_duracao',
				Core::registro('db')->formatarValor($this->_dados['id'])));
				
			$qtdRespostasPorFase = array();
			if ($rs->num_rows > 0) {
				while ($row = $rs->fetch_assoc()){
					$qtdRespostasPorFase[$row['q_fase_duracao']] = $row['qtd'];
				}
			}

			$this->fixarAusentePorFases($qtdRespostasPorFase);

			$rs->free();
		}
	}
	
	public function carregarRespostasMarcadasPorFase ($fase)
	{
		if ( $this->_dados['id'] != null && $this->_dados['aluno'] != null){
			$rs = Core::registro('db')->query(sprintf('SELECT * FROM respostas 
														INNER JOIN questoes ON r_questao = q_id
														WHERE q_fase_duracao = %s AND r_inscricao = %s',
				Core::registro('db')->formatarValor($fase),
				Core::registro('db')->formatarValor($this->_dados['id'])));
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
				{
					$obj = new Resposta($row['r_id']);
					$obj->fixarValor($row['r_valor']);
					$questao = new Questao($row['r_questao']);
					$questao->carregar();
					$obj->fixarQuestao( $questao );
					$obj->fixarStatus($row['r_status']);
					$obj->fixarChute($row['r_chute']);
					$obj->fixarTempo($row['r_tempo']);
					$obj->fixarNivelSeguranca($row['r_nivel_seguranca']);
					$obj->fixarComentarioAluno($row['r_comentario_aluno']);
					$obj->fixarInscricao( $this );
					
					$this->adicionarRespostaMarcada($obj);
				}
			}
			else{
				$this->fixarAusente(1);
			}
			$rs->free();
		}
	}
	
	public function qtdRespostasPorFase ($fase)
	{
		$qtd = 0;
		if ( $this->_dados['id'] != null && $this->_dados['aluno'] != null){
			$rs = Core::registro('db')->query(sprintf('SELECT count(1) as qtd FROM respostas 
														INNER JOIN questoes ON r_questao = q_id
														WHERE q_fase_duracao = %s AND r_inscricao = %s',
				Core::registro('db')->formatarValor($fase),
				Core::registro('db')->formatarValor($this->_dados['id'])));
				
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()){
					$qtd = $row['qtd'];
				}
			}
			$rs->free();
		}

		return $qtd;
	}
}

?>