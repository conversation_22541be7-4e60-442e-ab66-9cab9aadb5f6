<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('AreaInteresse', null, true);

class CursoVestibular
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'area_interesse' => null);
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoCursoVestibular ($nome, AreaInteresse $area)
	{
		Core::registro('db')->query( sprintf('INSERT INTO cursos_vestibular (c_nome, c_area_interesse) VALUES (%s, %s)', 
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($area->obterID()) ) );

		$obj = new CursoVestibular( Core::registro('db')->insert_id );
		
		$obj->fixarNome($nome);
		$obj->fixarAreaInteresse($area);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE cursos_vestibular SET c_nome = %s, c_area_interesse = %s WHERE c_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['area_interesse']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM cursos_vestibular WHERE c_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM cursos_vestibular WHERE c_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNome($row['c_nome']);
				
				$area = new AreaInteresse($row['c_area_interesse']);
				
				if ($area->carregar()) {
					$this->fixarAreaInteresse( $area );
				}
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}
	
	public function obterNome ()
	{
		return $this->_dados['nome'];
	}
	
	public function fixarAreaInteresse (AreaInteresse &$area)
	{
		$this->_dados['area_interesse'] = $area;
	}
	
	public function &obterAreaInteresse ()
	{
		return $this->_dados['area_interesse'];
	}
	
	// sem critério ainda
	public function podeRemover ()
	{
		$total = 0;
	
		/*
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM alunos WHERE a_turma = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$total = $row['total'];
			}
			$rs->free();
		}
		*/
		
		return ( $total == 0 );
	}
	
	public function validarInstituicao ()
	{
		if ( $this->_dados['id'] != null && $this->_dados['area_interesse'] != null ) {
			return $this->_dados['area_interesse']->validarInstituicao();
		}
		
		return false;
	}
	
	static public function obterArrayCursosVestibularParaFormulario ()
	{
		$cursos = array();
		
		$rs = Core::registro('db')->query( sprintf('SELECT * FROM cursos_vestibular LEFT JOIN areas_interesse ON areas_interesse.a_id = cursos_vestibular.c_area_interesse WHERE areas_interesse.a_instituicao = %s ORDER BY c_nome ASC',
			Core::registro('db')->formatarValor( Core::registro('instituicao')->obterID() ) ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{			
				$cursos[$row['c_id']] = $row['c_nome'];
			}
		}
		$rs->free();
		
		return $cursos;
	}
	
}

?>