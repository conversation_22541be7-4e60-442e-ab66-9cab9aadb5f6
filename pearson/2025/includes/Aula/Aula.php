<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Disciplina', null, true);
Core::incluir('Turma', null, true);
Core::incluir('Professor', null, true);
Core::incluir('Simulado', null, true);

class Aula
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'disciplina' => null,
								'turma' => null,
								'professor' => null,
								'simulado' => null  );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovaAula (Disciplina &$disc, Turma &$turma, Professor &$prof, Simulado &$simu)
	{
		Core::registro('db')->query( sprintf('INSERT INTO aulas(a_disciplina, a_turma, a_professor, a_simulado) VALUES (%s, %s, %s, %s)',
			Core::registro('db')->formatarValor($disc->obterID()),
			Core::registro('db')->formatarValor($turma->obterID()),
			Core::registro('db')->formatarValor($prof->obterID()),
			Core::registro('db')->formatarValor($simu->obterID()) ) );

		$obj = new Aula(Core::registro('db')->insert_id);
		
		$obj->fixarDisciplina($disc);
		$obj->fixarTurma($turma);
		$obj->fixarProfessor($prof);
		$obj->fixarSimulado($simu);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE aulas SET a_disciplina = %s, a_turma = %s, a_professor = %s, a_simulado = %s WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['disciplina']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['turma']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['professor']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['simulado']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM aulas WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			$this->fixarID(null);
			
			return Core::registro('db')->errno == 0;
		}
		
		return false;
	}
	
	public function carregar (Professor &$professor = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT a_disciplina,a_turma,a_professor,a_simulado FROM aulas WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$disciplina = new Disciplina($row['a_disciplina']);
				$disciplina->carregar();
				$this->fixarDisciplina($disciplina);
				
				$turma = new Turma($row['a_turma']);
				$turma->carregar();
				$this->fixarTurma($turma);
				
				if ($professor == null) {
					$professor = new Professor($row['a_professor']);
					$professor->carregar();
				}
				$this->fixarProfessor($professor);

				$simu = new Simulado($row['a_simulado']);
				$simu->carregar();
				$this->fixarSimulado($simu);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarProfessor (Professor &$professor)
	{
		$this->_dados['professor'] = $professor;
	}
	
	public function &obterProfessor ()
	{
		return $this->_dados['professor'];
	}
	
	public function fixarDisciplina (Disciplina &$disciplina)
	{
		$this->_dados['disciplina'] = $disciplina;
	}
	
	public function &obterDisciplina ()
	{
		return $this->_dados['disciplina'];
	}
	
	public function fixarTurma (Turma &$turma)
	{
		$this->_dados['turma'] = $turma;
	}
	
	public function &obterTurma ()
	{
		return $this->_dados['turma'];
	}
	
	public function fixarSimulado (Simulado &$simu)
	{
		$this->_dados['simulado'] = $simu;
	}
	
	public function &obterSimulado ()
	{
		return $this->_dados['simulado'];
	}

	static public function obterIDPelosDados (Disciplina &$disc, Turma &$turma, Professor &$prof, Simulado &$simu)
	{			
		$rs = Core::registro('db')->query( sprintf('SELECT a_id FROM aulas WHERE a_disciplina = %s AND a_turma = %s AND a_professor = %s AND a_simulado = %s',
					Core::registro('db')->formatarValor($disc->obterID()),
					Core::registro('db')->formatarValor($turma->obterID()),
					Core::registro('db')->formatarValor($prof->obterID()),
					Core::registro('db')->formatarValor($simu->obterID()) ) );
				
		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			
			return $row['a_id'];
		}
		$rs->free();
		
		return false;
	}
	
	// @todo fazer...
	public function podeRemover ()
	{
		$total = 0;
	
/*		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM aulas WHERE a_professor = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$total = $row['total'];
			}
			$rs->free();
		}*/
		
		return ( $total == 0 );
	}
	
	public function obterSimuladosPorTurmaID ($tid)
	{
		$rs = Core::registro('db')->query( sprintf('SELECT * FROM aulas WHERE a_turma = %s',
			Core::registro('db')->formatarValor($tid) ) );

		$ret = array();
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$ret[] = $row['a_simulado'];
			}

			$ret = array_unique($ret);
		}
		$rs->free();
		
		return $ret;
	}
}

?>