<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Aula', null, true);

class Competencia
{
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'nome_pequeno' => null,
								'ordem' => null);

		$this->fixarID($id);
	}

	static public function &obterNovaCompetencia ($nome)
	{
		Core::registro('db')->query( sprintf('INSERT INTO competencias (c_nome) VALUES (%s)',
			Core::registro('db')->formatarValor($nome)
		) );

		$obj = new Competencia ( Core::registro('db')->insert_id );

		$obj->fixarNome($nome);

		return $obj;
	}

	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE competencias SET c_nome = %s, c_nome_pequeno = %s, c_ordem = %s WHERE c_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['nome_pequeno']),
				Core::registro('db')->formatarValor($this->_dados['ordem']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}

	public function remover ()//$bynome = false)
	{
		if ($this->_dados['id'] != null){// && $bynome === false) {
			Core::registro('db')->query( sprintf('DELETE FROM competencias WHERE c_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$this->fixarID(null);

			return (Core::registro('db')->errno == 0);
		}/*
		else{
			Core::registro('db')->query( sprintf('DELETE FROM competencias WHERE c_nome = %s',
				Core::registro('db')->formatarValor($bynome) ) );

			$this->fixarID(null);

			return (Core::registro('db')->errno == 0);
		}*/

		return false;
	}

	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM competencias WHERE c_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$this->fixarNome($row['c_nome']);
				$this->fixarNomePequeno($row['c_nome_pequeno']);
				$this->fixarOrdem($row['c_ordem']);

				return true;
			}
			$rs->free();
		}

		return false;
	}

	public function fixarOrdem ($ordem)
	{
		$this->_dados['ordem'] = $ordem;
	}

	public function obterOrdem ()
	{
		return $this->_dados['ordem'];
	}

	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}

	public function obterID ()
	{
		return $this->_dados['id'];
	}

	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}

	public function fixarNomePequeno ($nome_pequeno) {
		$this->_dados['nome_pequeno'] = $nome_pequeno;
	}

	public function obterNomePequeno () {
		return $this->_dados['nome_pequeno'];
	}

	public function obterNome ()
	{
		return $this->_dados['nome'];
	}

	// se tem questoes ou aulas cadastradas não pode remover
	public function podeRemover ()
	{
		$total = 0;

		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM questoes WHERE q_competencia = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();


			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM simulados_conteudos WHERE sc_competencia = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();


			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM aulas WHERE a_competencia = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();


			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM habilidades WHERE h_competencia = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();
		}

		return ( $total == 0 );
	}

	public function obterAulas ()
	{
		$aulas = array();

		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf(
				'SELECT aulas.a_id, turmas.t_nome FROM aulas
				LEFT JOIN turmas ON turmas.t_id = a_turma
				WHERE a_competencia = %s ORDER BY t_nome ASC',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$aula = new Aula($row['a_id']);
					$aula->carregar();

					$aulas[] = $aula;
				}
			}
			$rs->free();
		}

		return $aulas;
	}

	static public function obterArrayCompetenciasParaFormulario ()
	{
		$competencias = array();

		$rs = Core::registro('db')->query('SELECT * FROM competencias ORDER BY c_ordem ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if($row['c_nome'] != 'Produção de Texto'){
					$competencias[$row['c_id']] = $row['c_nome'];
				}

			}
		}
		$rs->free();

		return $competencias;
	}

	static public function obterArrayCompetenciasPorSimulado ($idSimulado)
	{
		$competencias = array();

		$rs = Core::registro('db')->query( sprintf('SELECT distinct d.c_id, d.c_nome FROM questoes q, competencias d
                      WHERE q.q_competencia = d.c_id and q.q_simulado = %s ORDER BY d.c_ordem ASC, c_nome ASC',
			Core::registro('db')->formatarValor($idSimulado) ) );
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$competencias[$row['c_id']] = $row['c_nome'];
			}
		}
		$rs->free();

		return $competencias;
	}

	static public function obterArrayCompetenciasPequenasPorSimulado ($idSimulado)
	{
		$competencias = array();

		$rs = Core::registro('db')->query( sprintf('SELECT distinct d.c_id, d.c_nome, d.c_nome_pequeno FROM questoes q, competencias d
                      WHERE q.q_competencia = d.c_id and q.q_simulado = %s ORDER BY d.c_ordem ASC, c_nome ASC',
			Core::registro('db')->formatarValor($idSimulado) ) );
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$competencias[$row['c_id']] = $row['c_nome_pequeno'];
			}
		}
		$rs->free();

		return $competencias;
	}

	static public function obterArrayTdsCompetenciasParaFormulario ($com_descricao = FALSE) {
		$habilidades = array();

		$rs = Core::registro('db')->query('SELECT h_id, h_nome, h_descricao FROM habilidades ORDER BY h_ordem ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ($com_descricao)
					$habilidades[$row['h_id']] = array($row['h_nome'], $row['h_descricao']);
				else
					$habilidades[$row['h_id']] = $row['h_nome'];
			}
		}
		$rs->free();

		return $habilidades;
	}
}

?>