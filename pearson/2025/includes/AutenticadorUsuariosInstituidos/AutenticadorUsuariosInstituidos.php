<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('AutenticadorUsuarios');
Core::incluir('UsuarioInstituido', null, true);

class AutenticadorUsuariosInstituidos extends AutenticadorUsuarios
{	
	public function checaLogin (Email $email = null, $senha = '')
	{
		$login = $email != null ? $email->obterEndereco() : '';
			
		return parent::checaLogin($login, $senha);
	}
	
	protected function _avaliarLogin ($id, $senha, $login) {
		$loginSQL = 'e_endereco';//(@$this->_config['diferenciar_letras'] ? 'BINARY e_endereco' : 'LCASE(e_endereco)');
	
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT u_id, e_id FROM usuarios 
			  INNER JOIN emails ON u_email = e_id 
			  WHERE u_id = %s AND u_senha = %s AND %s = %s LIMIT 1',
			  Core::registro('db')->formatarValor( $id ),
			  Core::registro('db')->formatarValor( $senha ),
			  $loginSQL,
			  Core::registro('db')->formatarValor( $login ) ) );
			   
		return ($rs->num_rows == 1);
	}

	protected function _obterIDUsuarioPeloLogin ($login, $diferenciarLetras = false)
	{
		return self::obterIDUsuarioPeloLogin($login, $diferenciarLetras);
	}
	
	public static function obterIDUsuarioPeloLogin ($login, $diferenciarLetras = false)
	{
		$id = false;
		
		$emailSQL = 'e_endereco'; #$diferenciarLetras ? 'e_endereco' : 'LCASE(e_endereco)';
		
		$rs = Core::registro('db')->query( sprintf(
			  'SELECT e_id, u_id FROM emails 
			  INNER JOIN usuarios ON usuarios.u_email = emails.e_id 
			  WHERE BINARY %s = %s LIMIT 0,1',
			  $emailSQL,
			  Core::registro('db')->formatarValor( $login ) ) );
			
		if ($rs->num_rows == 1) {
			$row = $rs->fetch_assoc();
			$id = $row['u_id'];
		}
		$rs->free();
		
		return $id;
	}
	
	protected function _sintaxeCorreta ($login, $senha)
	{		
		if(strpos($login, '@') === false){
			return true;
		}
		else{
			return Filtrador::email($login);
		}
	}
}
?>