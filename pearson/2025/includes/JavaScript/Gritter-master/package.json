{"name": "Gritter", "version": "1.7.4", "description": "A small growl-like notification plugin for jQuery", "homepage": "http://boedesign.com/blog/2009/07/11/growl-for-jquery-gritter/", "keywords": ["package", "gritter"], "jam": {"dependencies": {"jquery": ">1.4.2"}, "main": "js/jquery.gritter.js", "shim": {"deps": ["j<PERSON>y"], "exports": "Gritter"}}, "maintainers": [{"name": "<PERSON>", "web": "http://boedesign.com"}], "contributors": [{"name": "<PERSON>", "web": "http://boedesign.com"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "repositories": [{"type": "git", "url": "https://github.com/jboesch/Gritter.git"}], "github": "https://github.com/jboesch/Gritter", "categories": ["UI"]}