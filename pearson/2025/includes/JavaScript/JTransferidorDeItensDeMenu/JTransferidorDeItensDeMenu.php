<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JTransferidorDeItensDeMenu {
	private $_origem;
	private $_destino;
	private $_acao;

	public function __construct ($origem, $destino, $acao = 'onclick="%s"') {
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JTransferidorDeItensDeMenu/JTransferidorDeItensDeMenu.js');
		
		$this->_origem = $origem;
		$this->_destino = $destino;
		$this->_acao = $acao;
	}

	public function obterHTML () {			
		return sprintf($this->_acao, sprintf("meu_TransferidorDeItensDeMenu.transferirItens(this, '%s', '%s');", $this->_origem, $this->_destino));
	}
	
	static public function obterHTMLParaPrepararFormulario($nome, $campos = array()) {
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JTransferidorDeItensDeMenu/JTransferidorDeItensDeMenu.js');
		
		return sprintf('<script type="text/javascript">
		window.addEvent(\'domready\', function() {
			meu_TransferidorDeItensDeMenu.prepararFormulario(\'%s\', %s);
		});
		</script>', $nome, implode_to_javascript($campos));
	}
}

?>