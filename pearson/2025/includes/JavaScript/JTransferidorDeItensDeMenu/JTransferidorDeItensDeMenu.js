/**
** Classe JTransferidorDeItensDeMenu
**/
var JTransferidorDeItensDeMenu = new Class({
	initialize: function () {
		this._campos_selecionar = [];
	},

	transferirItens: function(ativador, origem, destino) {
		origem = $(origem);
		origem_itens = origem.getElements('option');
		destino = $(destino);
		destino_itens = destino.getElements('option');

		origem_itens.each(function(item) {
			if ($type(item) == 'element' && item.selected) {
				item.selected = false;
				destino.adopt(item);
			}
		});
		

	},
	
	prepararFormulario: function(nome, campos) {
		this._campos_selecionar.merge(campos);

		if (window.ie)
			$(nome).onsubmit = this._ajustarSelecaoAoEnviar.bindAsEventListener(this);
		else
			$(nome).addEvent('submit', this._ajustarSelecaoAoEnviar.bindAsEventListener(this));
	},

	_ajustarSelecaoAoEnviar: function(event) {	
		this._campos_selecionar.each(function(item) {
			if ($type($(item)) != 'element')
				return;
		
			itens = $(item).getElements('option');
			
			itens.each(function(sub_item) {
				sub_item.selected = true;
			});
		});
	}
});

var meu_TransferidorDeItensDeMenu = new JTransferidorDeItensDeMenu();