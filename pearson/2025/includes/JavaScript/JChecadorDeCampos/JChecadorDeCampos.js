/**
** Classe JChecadorDeCampos
**/
var JChecadorDeCampos = new Class({
	alterarEstadoDosCampos: function(ativador, campos){
		var novo_estado = $(ativador).checked;

		campos.each(function(campo) {
			if ( !campo.contains('[]') ) {
				$(campo).checked = novo_estado;
			} else {
				campo = $$('input[name^='+ campo.substr(0, campo.length-2) +']');
				
				campo.each(function(sub_campo) {
					if ($type(sub_campo) == 'element')
						sub_campo.checked = novo_estado;
				});
			}
		});
	}
});

var meu_ChecadorDeCampos = new JChecadorDeCampos();