<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JChecadorDeCampos {
	private $_campos;
	private $_acao;

	public function __construct ($campos, $acao = '%s')
	{
		Core::modulo('js')->incluirArquivo('https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/JChecadorDeCampos/JChecadorDeCampos.js');
		
		$this->_campos = implode_to_javascript($campos);
		$this->_acao = $acao;
	}
	
	public function obterHTML()	{
		return sprintf($this->_acao, "meu_ChecadorDeCampos.alterarEstadoDosCampos(this, ". $this->_campos .");");
	}
}

?>