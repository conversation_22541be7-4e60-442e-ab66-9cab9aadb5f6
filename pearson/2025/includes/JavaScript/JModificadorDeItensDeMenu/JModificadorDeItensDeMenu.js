/**
** Classe JModificadorDeItensDeMenu
**/
var JModificadorDeItensDeMenu = new Class({
	modificarMultiplosItens: function(ativador, campo, opcoes) {
		campo = $(campo);
		campo.empty();

		opcoes.each(function(opcao) {
			if (opcao[0] == ativador.value) {
				opcao[1].each(function(item) {
					campo.adopt(new Element('option', {'value': item[0] }).setHTML(item[1]) );
				});
			}
		});
	},
	
	modificarItens: function(ativador, campo, opcoes) {
		campo = $(campo);
		campo.empty();

		opcoes.each(function(opcao) {
			campo.adopt(new Element('option', {'value': opcao[0] }).setHTML(opcao[1]) );
		});
	}
});

var meu_ModificadorDeItensDeMenu = new JModificadorDeItensDeMenu();