<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JModificadorDeMultiplosItensPorMenu {
	private $_campo;
	private $_opcoes;
	private $_acao;
	
	public function __construct ($campo, $opcoesPorValor = array(), $acao = 'onchange="%s"')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JModificadorDeItensDeMenu/JModificadorDeItensDeMenu.js');

		$temp = array();
		foreach($opcoesPorValor as $valor => $opcoes) {
			$subTemp = array();
			foreach ($opcoes as $id => $nome)
				$subTemp[] = "['$id', '$nome']";

			$temp[] = "['$valor', ". implode_to_javascript($subTemp) ."]";
		}

		$this->_opcoes = implode_to_javascript($temp);
		$this->_campo = $campo;
		$this->_acao = $acao;
	}

	public function obterHTML () {			
		return sprintf($this->_acao, sprintf("meu_ModificadorDeItensDeMenu.modificarMultiplosItens(this, '%s', %s);", $this->_campo, $this->_opcoes));
	}
}

?>