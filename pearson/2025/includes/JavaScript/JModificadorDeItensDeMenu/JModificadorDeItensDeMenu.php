<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JModificadorDeItensDeMenu {
	private $_campo;
	private $_opcoes;
	private $_acao;
	
	public function __construct ($campo, $opcoes = array(), $acao = 'onchange="%s"')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JModificadorDeItensDeMenu/JModificadorDeItensDeMenu.js');

		$temp = array();
		foreach ($opcoes as $id => $nome)
			$temp[] = "['$id', '$nome']";

		$this->_opcoes = implode_to_javascript($temp);
		$this->_campo = $campo;
		$this->_acao = $acao;
	}

	public function obterHTML () {			
		return sprintf($this->_acao, sprintf("meu_ModificadorDeItensDeMenu.modificarItens(this, '%s', %s);", $this->_campo, $this->_opcoes));
	}
}

?>