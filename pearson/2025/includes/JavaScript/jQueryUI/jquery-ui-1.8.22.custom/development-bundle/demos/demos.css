body {
	font-size: 62.5%;
}

table {
	font-size: 1em;
}

/* Site
   -------------------------------- */

body {
	font-family: "Trebuchet MS", "Helvetica", "Arial",  "Verdana", "sans-serif";
}

/* Layout
   -------------------------------- */

.layout-grid {
	width: 960px;
}

.layout-grid td {
	vertical-align: top;
}

.layout-grid td.left-nav {
	width: 140px;
}

.layout-grid td.normal {
	border-left: 1px solid #eee;
	padding: 20px 24px;
	font-family: "Trebuchet MS", "Helvetica", "Arial",  "Verdana", "sans-serif";
}

.layout-grid td.demos {
	background: url('/images/demos_bg.jpg') no-repeat;
	height: 337px;
	overflow: hidden;
}

/* Normal
   -------------------------------- */

.normal h3,
.normal h4 {
	margin: 0;
	font-weight: normal;
}

.normal h3 {
	padding: 0 0 9px;
	font-size: 1.8em;
}

.normal h4 {
	padding-bottom: 21px;
	border-bottom: 1px dashed #999;
	font-size: 1.2em;
	font-weight: bold;
}

.normal p {
	font-size: 1.2em;
}

/* Demos */

.demos-nav, .demos-nav dt, .demos-nav dd, .demos-nav ul, .demos-nav li {
	margin: 0;
	padding: 0
}

.demos-nav {
	float: left;
	width: 170px;
	font-size: 1.3em;
}

.demos-nav dt,
.demos-nav h4 {
	margin: 0;
	padding: 0;
	font: normal 1.1em "Trebuchet MS", "Helvetica", "Arial",  "Verdana", "sans-serif";
	color: #e87b10;
}

.demos-nav dt,
.demos-nav h4 {
	margin-top: 1.5em;
	margin-bottom: 0;
	padding-left: 8px;
	padding-bottom:5px;
	line-height: 1.2em;
	border-bottom: 1px solid #F4F4F4;
}

.demos-nav dd a,
.demos-nav li a {
	border-bottom: 1px solid #F4F4F4;
	display:block;
	padding: 4px 3px 4px 8px;
	font-size: 90%;
	text-decoration: none;
	color: #555 ;
	margin:2px 0;
	height:13px;
}

.demos-nav dd a:hover,
.demos-nav dd a:focus,
.demos-nav dd a:hover,
.demos-nav dd a:focus {
	background: #f3f3f3;
	color:#000;
	-moz-border-radius: 5px; -webkit-border-radius: 5px;
}
 .demos-nav dd a.selected {
	background: #555;
	color:#ffffff;
	-moz-border-radius: 5px; -webkit-border-radius: 5px;
}


/* new styles for demo pages, added by Filament 12.29.08
eventually we should convert the font sizes to ems -- using px for now to minimize style conflicts
*/

.normal h3.demo-header { font-size:32px; padding:0 0 5px; border-bottom:1px solid #eee; text-transform: capitalize; }
.normal h4.demo-subheader { font-size:10px; text-transform: uppercase; color:#999; padding:8px 0 3px; border:0; margin:0; }
.normal a:link,
.normal a:visited { color:#1b75bb; text-decoration:none; }
.normal a:hover,
.normal a:active { color:#0b559b; }

#demo-config { padding:20px 0 0; }

#demo-frame { float:left; width:540px; height:380px; border:1px solid #ddd; overflow: auto; position: relative; }
#demo-frame h3, #demo-frame h4 { padding: 0; font-weight: bold; font-size: 1em; }

#demo-config-menu { float:right; width:180px;  }
#demo-config-menu h4 { font-size:13px; color:#666; font-weight:normal; border:0; padding-left:18px; }

#demo-config-menu ul { list-style: none; padding: 0; margin: 0; }

#demo-config-menu li { font-size:12px; padding:0 0 0 10px; margin:3px 0; zoom: 1; }

#demo-config-menu li a:link,
#demo-config-menu li a:visited { display:block; padding:1px 8px 4px; border-bottom:1px dotted #b3b3b3; }
* html #demo-config-menu li a:link,
* html #demo-config-menu li a:visited { padding:1px 8px 2px; }
#demo-config-menu li a:hover,
#demo-config-menu li a:active { background-color:#f6f6f6; }

#demo-config-menu li.demo-config-on { background: url(images/demo-config-on-tile.gif) repeat-x left center; }

#demo-config-menu li.demo-config-on a:link,
#demo-config-menu li.demo-config-on a:visited,
#demo-config-menu li.demo-config-on a:hover,
#demo-config-menu li.demo-config-on a:active { background: url(images/demo-config-on.gif) no-repeat left; padding-left:18px; color:#fff; border:0; margin-left:-10px; margin-top: 0px; margin-bottom: 0px; }

#demo-source, #demo-notes {
	clear: both;
	padding: 20px 0 0;
	font-size: 1.3em;
}

#demo-notes { width:520px; color:#333; font-size: 1em; }
#demo-notes p code, .demo-description p code { padding: 0; font-weight: bold; }
#demo-source pre, #demo-source code { padding: 0; }
code, pre { padding:8px 0 8px 20px ; font-size: 1.2em; line-height:130%;  }

#demo-source a:link,
#demo-source a:visited,
#demo-source a:hover,
#demo-source a:active { font-size:12px; padding-left:13px; background-position: left center; background-repeat: no-repeat; }

#demo-source a.source-open:link,
#demo-source a.source-open:visited,
#demo-source a.source-open:hover,
#demo-source a.source-open:active { background-image: url(images/demo-spindown-open.gif); }

#demo-source a.source-closed:link,
#demo-source a.source-closed:visited,
#demo-source a.source-closed:hover,
#demo-source a.source-closed:active { background-image: url(images/demo-spindown-closed.gif); }

div.demo {
	padding:12px;
	font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif";
}

div.demo h3.docs { clear:left; font-size:12px; font-weight:normal; padding:0 0 1em; margin:0; }

div.demo-description {
	clear:both;
	padding:12px;
	font-family: "Trebuchet MS", "Arial", "Helvetica", "Verdana", "sans-serif";
	font-size: 1.3em;
	line-height: 1.4em;
}

.ui-draggable, .ui-droppable {
	background-position: top left;
}

.left-nav .demos-nav {
	padding-right: 10px;
}

#demo-link { font-size:11px;  padding-top: 6px; clear: both; overflow: hidden; }
#demo-link a span.ui-icon { float:left; margin-right:3px; }

/* Component containers
----------------------------------*/
#widget-docs .ui-widget { font-family: Trebuchet MS,Verdana,Arial,sans-serif; font-size: 1em; }
#widget-docs .ui-widget input, #widget-docs .ui-widget select, #widget-docs .ui-widget textarea, #widget-docs .ui-widget button { font-family: Trebuchet MS,Verdana,Arial,sans-serif; font-size: 1em; }
#widget-docs .ui-widget-header { border: 1px solid #ffffff; background: #464646 url(images/464646_40x100_textures_01_flat_100.png) 50% 50% repeat-x; color: #ffffff; font-weight: bold; }
#widget-docs .ui-widget-header a { color: #ffffff; }
#widget-docs .ui-widget-content { border: 1px solid #ffffff; background: #ffffff url(images/ffffff_40x100_textures_01_flat_75.png) 50% 50% repeat-x; color: #222222; }
#widget-docs .ui-widget-content a { color: #222222; }

/* Interaction states
----------------------------------*/
#widget-docs .ui-state-default, #widget-docs .ui-widget-content #widget-docs .ui-state-default { border: 1px solid #666666; background: #555555 url(images/555555_40x100_textures_03_highlight_soft_75.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff; outline: none; }
#widget-docs .ui-state-default a { color: #ffffff; text-decoration: none; outline: none; }
#widget-docs .ui-state-hover, #widget-docs .ui-widget-content #widget-docs .ui-state-hover, #widget-docs .ui-state-focus, #widget-docs .ui-widget-content #widget-docs .ui-state-focus { border: 1px solid #666666; background: #444444 url(images/444444_40x100_textures_03_highlight_soft_60.png) 50% 50% repeat-x; font-weight: normal; color: #ffffff; outline: none; }
#widget-docs .ui-state-hover a { color: #ffffff; text-decoration: none; outline: none; }
#widget-docs .ui-state-active, #widget-docs .ui-widget-content #widget-docs .ui-state-active { border: 1px solid #666666; background: #ffffff url(images/ffffff_40x100_textures_01_flat_65.png) 50% 50% repeat-x; font-weight: normal; color: #F6921E; outline: none; }
#widget-docs .ui-state-active a { color: #F6921E; outline: none; text-decoration: none; }

/* Interaction Cues
----------------------------------*/
#widget-docs .ui-state-highlight, #widget-docs .ui-widget-content #widget-docs .ui-state-highlight {border: 1px solid #fcefa1; background: #fbf9ee url(images/fbf9ee_40x100_textures_02_glass_55.png) 50% 50% repeat-x; color: #363636; }
#widget-docs .ui-state-error, #widget-docs .ui-widget-content #widget-docs .ui-state-error {border: 1px solid #cd0a0a; background: #fef1ec url(images/fef1ec_40x100_textures_05_inset_soft_95.png) 50% bottom repeat-x; color: #cd0a0a; }
#widget-docs .ui-state-error-text, #widget-docs .ui-widget-content #widget-docs .ui-state-error-text { color: #cd0a0a; }
#widget-docs .ui-state-disabled, #widget-docs .ui-widget-content #widget-docs .ui-state-disabled { opacity: .35; filter:Alpha(Opacity=35); background-image: none; }
#widget-docs .ui-priority-primary, #widget-docs .ui-widget-content #widget-docs .ui-priority-primary { font-weight: bold; }
#widget-docs .ui-priority-secondary, #widget-docs .ui-widget-content #widget-docs .ui-priority-secondary { opacity: .7; filter:Alpha(Opacity=70); font-weight: normal; }

/* Icons
----------------------------------*/

/* states and images */
#demo-frame-wrapper .ui-icon, #widget-docs .ui-icon { width: 16px; height: 16px; background-image: url(images/222222_256x240_icons_icons.png); }
#widget-docs .ui-widget-content .ui-icon {background-image: url(images/222222_256x240_icons_icons.png); }
#widget-docs .ui-widget-header .ui-icon {background-image: url(images/222222_256x240_icons_icons.png); }
#widget-docs .ui-state-default .ui-icon { background-image: url(images/888888_256x240_icons_icons.png); }
#widget-docs .ui-state-hover .ui-icon, #widget-docs .ui-state-focus .ui-icon {background-image: url(images/454545_256x240_icons_icons.png); }
#widget-docs .ui-state-active .ui-icon {background-image: url(images/454545_256x240_icons_icons.png); }
#widget-docs .ui-state-highlight .ui-icon {background-image: url(images/2e83ff_256x240_icons_icons.png); }
#widget-docs .ui-state-error .ui-icon, #widget-docs .ui-state-error-text .ui-icon {background-image: url(images/cd0a0a_256x240_icons_icons.png); }


/* Misc visuals
----------------------------------*/

/* Corner radius */
#widget-docs .ui-corner-tl { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; }
#widget-docs .ui-corner-tr { -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; }
#widget-docs .ui-corner-bl { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; }
#widget-docs .ui-corner-br { -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; }
#widget-docs .ui-corner-top { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; }
#widget-docs .ui-corner-bottom { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; }
#widget-docs .ui-corner-right {  -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; }
#widget-docs .ui-corner-left { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; }
#widget-docs .ui-corner-all { -moz-border-radius: 4px; -webkit-border-radius: 4px; }

/* Overlays */
#widget-docs .ui-widget-overlay { background: #aaaaaa url(images/aaaaaa_40x100_textures_01_flat_0.png) 50% 50% repeat-x; opacity: .30;filter:Alpha(Opacity=30); }
#widget-docs .ui-widget-shadow { margin: -8px 0 0 -8px; padding: 8px; background: #aaaaaa url(images/aaaaaa_40x100_textures_01_flat_0.png) 50% 50% repeat-x; opacity: .30;filter:Alpha(Opacity=30); -moz-border-radius: 8px; -webkit-border-radius: 8px; }

/*
----------------------------------*/

#widget-docs { margin:20px 0 0; border: none; }

#widget-docs h2, #widget-docs h3, #widget-docs h4, #widget-docs p, #widget-docs ul, #widget-docs code { margin:0; padding:0; }
#widget-docs code { display:block; color:#444; font-size:.9em; margin:0 0 1em; }
#widget-docs code strong { color:#000; }
#widget-docs p { margin:0 3em 1.2em 0; }
#widget-docs p.intro { font-size:13px; color:#666; line-height:1.3; }
#widget-docs ul { list-style-type: none; }

#widget-docs h2 { font-size:16px; margin:1.2em 0 .5em; }
#widget-docs h3 { font-size:14px; color:#e6820E; margin:1.5em 0 .5em; }
.normal #widget-docs h4 { font-size:12px; color:#000; border:0; margin:0 0 .5em; }

#docs-overview-main { width:400px; }
#docs-overview-sidebar { float:right; width:200px; }
#docs-overview-sidebar a span { color:#666; }
#widget-docs #docs-overview-main p { margin-right:0; }
#widget-docs #docs-overview-sidebar h4 { padding-left:0; }

.docs-list-header { float:left; width:100%; margin:10px 0 0; border-bottom:1px solid #eee; }
#widget-docs .docs-list-header h2 { float:left; margin:0; }
#widget-docs .docs-list-header p { float:right; margin:5px 0; font-size:11px; }

.docs-list { float:left; width:100%; padding:0 0 10px; }
.docs-list .param-header { float:left; clear:left; width:100%; padding:8px 0; border-top:1px solid #eee; }
#widget-docs .param-header h3, #widget-docs .param-header p { margin:0; float:left; }
#widget-docs .param-header h3 { width:50%; }
#widget-docs .param-header h3 span { background: url(images/demo-spindown-closed.gif) no-repeat left; padding-left:13px; }
#widget-docs .param-open .param-header h3 span { background: url(images/demo-spindown-open.gif) no-repeat left; }
#widget-docs .param-header p { width:24%; }
#widget-docs .param-header p.param-type span { background: url(images/icon-docs-info.gif) no-repeat left; cursor:pointer; border-bottom:1px dashed #ccc; padding-left:15px; }

.param-details { padding-left:13px; }
.param-args { margin:0 0 1.5em; border-top:1px dotted #ccc;}
.param-args td { padding:3px 30px 3px 5px; border-bottom:1px dotted #ccc;  }


/* overrides for ui-tab styles */
#widget-docs ul.ui-tabs-nav { padding:0 0 0 8px; }
#widget-docs .ui-tabs-nav li { margin:5px 5px 0 0; }

#widget-docs .ui-tabs-nav li a:link,
#widget-docs .ui-tabs-nav li a:visited,
#widget-docs .ui-tabs-nav li a:hover,
#widget-docs .ui-tabs-nav li a:active { font-size:14px; padding:4px 1.2em 3px; color:#fff; }

#widget-docs .ui-tabs-nav li.ui-tabs-selected a:link,
#widget-docs .ui-tabs-nav li.ui-tabs-selected a:visited,
#widget-docs .ui-tabs-nav li.ui-tabs-selected a:hover,
#widget-docs .ui-tabs-nav li.ui-tabs-selected a:active { color:#e6820E; }

#widget-docs .ui-tabs-panel { padding:20px 9px; font-size:12px; line-height:1.4; color:#000; }

#widget-docs .ui-widget-content a:link,
#widget-docs .ui-widget-content a:visited { color:#1b75bb; text-decoration:none; }
#widget-docs .ui-widget-content a:hover,
#widget-docs .ui-widget-content a:active { color:#0b559b; }

