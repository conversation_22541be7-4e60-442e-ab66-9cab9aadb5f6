<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Draggable - Handles</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.draggable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#draggable, #draggable2 { width: 100px; height: 100px; padding: 0.5em; float: left; margin: 0 10px 10px 0; }
	#draggable p { cursor: move; }
	</style>
	<script>
	$(function() {
		$( "#draggable" ).draggable({ handle: "p" });
		$( "#draggable2" ).draggable({ cancel: "p.ui-widget-header" });
		$( "div, p" ).disableSelection();
	});
	</script>
</head>
<body>

<div class="demo">
	
<div id="draggable" class="ui-widget-content">
	<p class="ui-widget-header">I can be dragged only by this handle</p>
</div>

<div id="draggable2" class="ui-widget-content">
	<p>You can drag me around&hellip;</p>
	<p class="ui-widget-header">&hellip;but you can't drag me by this handle.</p>
</div>

<!-- ADD CANCEL DEMO -->

</div><!-- End demo -->



<div class="demo-description">
<p>Allow dragging only when the cursor is over a specific part of the draggable.  Use the <code>handle</code> option to specify the jQuery selector of an element (or group of elements) used to drag the object.</p>
<p>Or prevent dragging when the cursor is over a specific element (or group of elements) within the draggable.  Use the <code>cancel</code> option to specify a jQuery selector over which to "cancel" draggable functionality.</p>
</div><!-- End demo-description -->

</body>
</html>
