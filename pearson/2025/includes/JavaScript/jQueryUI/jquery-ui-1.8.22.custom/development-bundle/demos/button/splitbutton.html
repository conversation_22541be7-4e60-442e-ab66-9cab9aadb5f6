<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Button - Split button</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.button.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		$( "#rerun" )
			.button()
			.click(function() {
				alert( "Running the last action" );
			})
			.next()
				.button( {
					text: false,
					icons: {
						primary: "ui-icon-triangle-1-s"
					}
				})
				.click(function() {
					alert( "Could display a menu to select an action" );
				})
				.parent()
					.buttonset();
	});
	</script>
	<style>
		
	</style>
</head>
<body>

<div class="demo">

<div>
	<button id="rerun">Run last action</button>
	<button id="select">Select an action</button>
</div>

</div><!-- End demo -->



<div class="demo-description">
<p>An example of a split button built with two buttons: A plan button with just text, one with only a primary icon and no text. Both are grouped together in a set.</p>
</div><!-- End demo-description -->

</body>
</html>
