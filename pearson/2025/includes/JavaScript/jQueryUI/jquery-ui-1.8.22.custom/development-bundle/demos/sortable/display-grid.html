<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Sortable - Display as grid</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.sortable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#sortable { list-style-type: none; margin: 0; padding: 0; }
	#sortable li { margin: 3px 3px 3px 0; padding: 1px; float: left; width: 100px; height: 90px; font-size: 4em; text-align: center; }
	</style>
	<script>
	$(function() {
		$( "#sortable" ).sortable();
		$( "#sortable" ).disableSelection();
	});
	</script>
</head>
<body>
<div class="demo">

<ul id="sortable">
	<li class="ui-state-default">1</li>
	<li class="ui-state-default">2</li>
	<li class="ui-state-default">3</li>
	<li class="ui-state-default">4</li>
	<li class="ui-state-default">5</li>
	<li class="ui-state-default">6</li>
	<li class="ui-state-default">7</li>
	<li class="ui-state-default">8</li>
	<li class="ui-state-default">9</li>
	<li class="ui-state-default">10</li>
	<li class="ui-state-default">11</li>
	<li class="ui-state-default">12</li>
</ul>

</div><!-- End demo -->



<div class="demo-description">
<p>
	To arrange sortable items as a grid, give them identical dimensions and
	float them using CSS.
</p>
</div><!-- End demo-description -->

</body>
</html>
