<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Effects - Easing demo</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.effects.core.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	.graph {
		float: left;
		margin-left: 10px;
	}
	</style>
	<script>
	$(function() {
		if ( !$( "<canvas/>" )[0].getContext ) {
			$( "<div/>" ).text(
				"Your browser doesn't support canvas, which is required for this demo. " +
				"Give Firefox 3 a try!"
			).appendTo( "#graphs" );
			return;
		}

		var i = 0,
			width = 100,
			height = 100;
		$.each( $.easing, function( name, impl ) {
			// skip linear/jswing and any non functioning implementation
			if ( !$.isFunction( impl ) || /jswing/.test( name ) ) {
				return;
			}
			var graph = $( "<div/>" ).addClass( "graph" ).appendTo( "#graphs" ),
				text = $( "<div/>" ).text( ++i + ". " + name ).appendTo( graph ),
				wrap = $( "<div/>" ).appendTo( graph ).css( 'overflow', 'hidden' ),
				canvas = $( "<canvas/>" ).appendTo( wrap )[ 0 ];
			canvas.width = width;
			canvas.height = height;
			var drawHeight = height * 0.8,
				cradius = 10;
				ctx = canvas.getContext( "2d" );
			ctx.fillStyle = "black";

			ctx.beginPath();
			ctx.moveTo( cradius, 0 );
			ctx.quadraticCurveTo( 0, 0, 0, cradius );
			ctx.lineTo( 0, height - cradius );
			ctx.quadraticCurveTo( 0, height, cradius, height );
			ctx.lineTo( width - cradius, height );
			ctx.quadraticCurveTo( width, height, width, height - cradius );
			ctx.lineTo( width, 0 );
			ctx.lineTo( cradius, 0 );
			ctx.fill();

			ctx.strokeStyle = "#555";
			ctx.beginPath();
			ctx.moveTo( width * 0.1, drawHeight + .5 );
			ctx.lineTo( width * 0.9, drawHeight + .5 );
			ctx.stroke();

			ctx.strokeStyle = "#555";
			ctx.beginPath();
			ctx.moveTo( width * 0.1, drawHeight * .3 - .5 );
			ctx.lineTo( width * 0.9, drawHeight * .3 - .5 );
			ctx.stroke();
			
			ctx.strokeStyle = "white";
			ctx.beginPath();
			ctx.lineWidth = 2;
			ctx.moveTo( width * 0.1, drawHeight );
			$.each( new Array( width ), function( position ) {
				var val = impl( 0, position, 0, 1, height );
				if ( /linear|jswing/.test( name ) ) {
					val = position / width;
				}
				ctx.lineTo( position * 0.8 + width * 0.1,
					drawHeight - drawHeight * val * 0.7 );
			});
			ctx.stroke();
			graph.click(function() {
				wrap
					.animate( { height: "hide" }, 2000, name )
					.delay( 800 )
					.animate( { height: "show" }, 2000, name );
			});

			graph.width( width ).height( height + text.height() + 10 );
		});
	});
	</script>
</head>
<body>

<div class="demo">

<div id="graphs"></div>

</div><!-- End demo -->



<div class="demo-description">
<p><strong>All easings provided by jQuery UI are drawn above, using a HTML canvas element</strong>. Click a diagram to see the easing in action.</p>
</div><!-- End demo-description -->

</body>
</html>
