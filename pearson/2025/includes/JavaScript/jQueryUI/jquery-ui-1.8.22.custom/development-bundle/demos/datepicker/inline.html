<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Datepicker - Display inline</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.datepicker.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		$( "#datepicker" ).datepicker();
	});
	</script>
</head>
<body>

<div class="demo">

Date: <div id="datepicker"></div>

</div><!-- End demo -->



<div class="demo-description">
<p>Display the datepicker embedded in the page instead of in an overlay.  Simply call .datepicker() on a div instead of an input.</p>
</div><!-- End demo-description -->

</body>
</html>
