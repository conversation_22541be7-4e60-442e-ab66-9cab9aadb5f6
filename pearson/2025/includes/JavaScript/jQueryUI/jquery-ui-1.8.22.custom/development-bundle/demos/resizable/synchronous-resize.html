<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Resizable - Synchronous resize</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.resizable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#resizable { background-position: top left; }
	#resizable, #also { width: 150px; height: 120px; padding: 0.5em; }
	#resizable h3, #also h3 { text-align: center; margin: 0; }
	#also { margin-top: 1em; }
	</style>
	<script>
	$(function() {
		$( "#resizable" ).resizable({
			alsoResize: "#also"
		});
		$( "#also" ).resizable();
	});
	</script>
</head>
<body>

<div class="demo">
	
<div id="resizable" class="ui-widget-header">
	<h3 class="ui-state-active">Resize</h3>
</div>

<div id="also" class="ui-widget-content">
	<h3 class="ui-widget-header">will also resize</h3>
</div>

</div><!-- End demo -->



<div class="demo-description">
<p>Resize multiple elements simultaneously by clicking and dragging the sides of one.  Pass a shared selector into the <code>alsoResize</code> option.</p>
</div><!-- End demo-description -->

</body>
</html>
