<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Droppable - Visual feedback</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.draggable.js"></script>
	<script src="../../ui/jquery.ui.droppable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#draggable, #draggable2 { width: 90px; height: 90px; padding: 0.5em; float: left; margin: 10px 10px 10px 0; }
	#droppable, #droppable2 { width: 120px; height: 120px; padding: 0.5em; float: left; margin: 10px; }
	</style>
	<script>
	$(function() {
		$( "#draggable" ).draggable();
		$( "#droppable" ).droppable({
			hoverClass: "ui-state-active",
			drop: function( event, ui ) {
				$( this )
					.addClass( "ui-state-highlight" )
					.find( "p" )
						.html( "Dropped!" );
			}
		});

		$( "#draggable2" ).draggable();
		$( "#droppable2" ).droppable({
			accept: "#draggable2",
			activeClass: "ui-state-hover",
			drop: function( event, ui ) {
				$( this )
					.addClass( "ui-state-highlight" )
					.find( "p" )
						.html( "Dropped!" );
			}
		});
	});
	</script>
</head>
<body>

<div class="demo">

<h3 class="docs">Feedback on hover:</h3>
	
<div id="draggable" class="ui-widget-content">
	<p>Drag me to my target</p>
</div>

<div id="droppable" class="ui-widget-header">
	<p>Drop here</p>
</div>

<h3 class="docs">Feedback on activating draggable:</h3>

<div id="draggable2" class="ui-widget-content">
	<p>Drag me to my target</p>
</div>

<div id="droppable2" class="ui-widget-header">
	<p>Drop here</p>
</div>

</div><!-- End demo -->



<div class="demo-description">
<p>Change the droppable's appearance on hover, or when the droppable is active (an acceptable draggable is dropped on it).  Use the <code>hoverClass</code> or <code>activeClass</code> options to specify respective classes.</p>
</div><!-- End demo-description -->

</body>
</html>
