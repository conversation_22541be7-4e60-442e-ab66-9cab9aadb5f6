<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Dialog - Basic modal</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../external/jquery.bgiframe-2.1.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.draggable.js"></script>
	<script src="../../ui/jquery.ui.position.js"></script>
	<script src="../../ui/jquery.ui.resizable.js"></script>
	<script src="../../ui/jquery.ui.dialog.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<script>
	$(function() {
		// a workaround for a flaw in the demo system (http://dev.jqueryui.com/ticket/4375), ignore!
		$( "#dialog:ui-dialog" ).dialog( "destroy" );
	
		$( "#dialog-modal" ).dialog({
			height: 140,
			modal: true
		});
	});
	</script>
</head>
<body>

<div class="demo">

<div id="dialog-modal" title="Basic modal dialog">
	<p>Adding the modal overlay screen makes the dialog look more prominent because it dims out the page content.</p>
</div>

<!-- Sample page content to illustrate the layering of the dialog -->
<div class="hiddenInViewSource" style="padding:20px;">
	<p>Sed vel diam id libero <a href="http://example.com">rutrum convallis</a>. Donec aliquet leo vel magna. Phasellus rhoncus faucibus ante. Etiam bibendum, enim faucibus aliquet rhoncus, arcu felis ultricies neque, sit amet auctor elit eros a lectus.</p>
	<form>
		<input value="text input" /><br />
		<input type="checkbox" />checkbox<br />
		<input type="radio" />radio<br />
		<select>
			<option>select</option>
		</select><br /><br />
		<textarea>textarea</textarea><br />
	</form>
</div><!-- End sample page content -->

</div><!-- End demo -->



<div class="demo-description">
<p>A modal dialog prevents the user from interacting with the rest of the page until it is closed.</p>
</div><!-- End demo-description -->

</body>
</html>
