<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Sortable - Handle empty lists</title>
	<link rel="stylesheet" href="../../themes/base/jquery.ui.all.css">
	<script src="../../jquery-1.7.2.js"></script>
	<script src="../../ui/jquery.ui.core.js"></script>
	<script src="../../ui/jquery.ui.widget.js"></script>
	<script src="../../ui/jquery.ui.mouse.js"></script>
	<script src="../../ui/jquery.ui.sortable.js"></script>
	<link rel="stylesheet" href="../demos.css">
	<style>
	#sortable1, #sortable2, #sortable3 { list-style-type: none; margin: 0; padding: 0; float: left; margin-right: 10px; background: #eee; padding: 5px; width: 143px;}
	#sortable1 li, #sortable2 li, #sortable3 li { margin: 5px; padding: 5px; font-size: 1.2em; width: 120px; }
	</style>
	<script>
	$(function() {
		$( "ul.droptrue" ).sortable({
			connectWith: "ul"
		});

		$( "ul.dropfalse" ).sortable({
			connectWith: "ul",
			dropOnEmpty: false
		});

		$( "#sortable1, #sortable2, #sortable3" ).disableSelection();
	});
	</script>
</head>
<body>
<div class="demo">

<ul id="sortable1" class='droptrue'>
	<li class="ui-state-default">Can be dropped..</li>
	<li class="ui-state-default">..on an empty list</li>
	<li class="ui-state-default">Item 3</li>
	<li class="ui-state-default">Item 4</li>
	<li class="ui-state-default">Item 5</li>
</ul>

<ul id="sortable2" class='dropfalse'>
	<li class="ui-state-highlight">Cannot be dropped..</li>
	<li class="ui-state-highlight">..on an empty list</li>
	<li class="ui-state-highlight">Item 3</li>
	<li class="ui-state-highlight">Item 4</li>
	<li class="ui-state-highlight">Item 5</li>
</ul>

<ul id="sortable3" class='droptrue'>
</ul>

<br clear="both" />

</div><!-- End demo -->



<div class="demo-description">
<p>
	Prevent all items in a list from being dropped into a separate, empty list
	using the <code>dropOnEmpty</code> option set to <code>false</code>.  By default,
	sortable items can be dropped on empty lists.
</p>
</div><!-- End demo-description -->

</body>
</html>
