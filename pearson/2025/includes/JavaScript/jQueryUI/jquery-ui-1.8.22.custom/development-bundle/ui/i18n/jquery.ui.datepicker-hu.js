/* Hungarian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['hu'] = {
		closeText: 'bez<PERSON>r',
		prevText: 'vissza',
		nextText: 'el<PERSON><PERSON>',
		currentText: 'ma',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>em<PERSON>', 'Október', 'November', 'December'],
		monthNamesShort: ['<PERSON>', 'Feb', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
		'<PERSON><PERSON>', 'Aug', '<PERSON><PERSON><PERSON>', 'Okt', 'Nov', 'Dec'],
		dayNames: ['Vas<PERSON>rnap', '<PERSON><PERSON>t<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
		dayNamesMin: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],
		weekHeader: 'Hét',
		dateFormat: 'yy.mm.dd.',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: true,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['hu']);
});
