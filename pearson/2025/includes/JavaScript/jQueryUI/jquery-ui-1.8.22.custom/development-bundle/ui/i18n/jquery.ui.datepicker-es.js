/* Inicialización en español para la extensión 'UI date picker' para jQuery. */
/* Traducido por Vester (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['es'] = {
		closeText: 'Cerrar',
		prevText: '&#x3c;Ant',
		nextText: 'Sig&#x3e;',
		currentText: 'Hoy',
		monthNames: ['Enero','Febrero','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON>','Agosto','Septiembre','Octubre','Noviembre','Diciembre'],
		monthNamesShort: ['Ene','Feb','Mar','Abr','May','Jun',
		'Jul','Ago','Sep','Oct','Nov','Dic'],
		dayNames: ['Domingo','Lu<PERSON>','<PERSON>es','Mi&eacute;rcoles','<PERSON><PERSON>','Viernes','<PERSON>&aacute;bado'],
		dayNamesShort: ['Dom','Lun','<PERSON>','<PERSON>&eacute;','Juv','Vie','S&aacute;b'],
		dayNamesMin: ['<PERSON>','Lu','Ma','Mi','Ju','Vi','S&aacute;'],
		weekHeader: 'Sm',
		dateFormat: 'dd/mm/yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['es']);
});