/* Portuguese initialisation for the jQuery UI date picker plugin. */
jQuery(function($){
	$.datepicker.regional['pt'] = {
		closeText: '<PERSON><PERSON><PERSON>',
		prevText: '&#x3c;Anterior',
		nextText: '<PERSON><PERSON><PERSON>',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON>','<PERSON><PERSON>','Mar&ccedil;o','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Agos<PERSON>','Setem<PERSON>','Outubro','Novembro','Dezembro'],
		monthNamesShort: ['Jan','Fev','Mar','Abr','<PERSON>','<PERSON>',
		'Jul','A<PERSON>','Set','Out','Nov','Dez'],
		dayNames: ['<PERSON>','Segunda-feira','Ter&ccedil;a-feira','Quarta-feira','Quinta-feira','Sexta-feira','S&aacute;bado'],
		dayNamesShort: ['<PERSON>','Seg','<PERSON>r','<PERSON><PERSON>','Qui','Sex','<PERSON>&aac<PERSON>;b'],
		dayNamesMin: ['Dom','Seg','Ter','<PERSON>ua','<PERSON>ui','Sex','S&aacute;b'],
		weekHeader: 'Sem',
		dateFormat: 'dd/mm/yy',
		firstDay: 0,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['pt']);
});