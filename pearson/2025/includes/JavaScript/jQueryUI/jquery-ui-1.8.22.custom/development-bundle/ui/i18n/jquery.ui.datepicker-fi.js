/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['fi'] = {
		closeText: 'Sul<PERSON>',
		prevText: '&#xAB;<PERSON><PERSON><PERSON>',
		nextText: '<PERSON><PERSON><PERSON>&#xBB;',
		currentText: 'T&#xE4;n&#xE4;&#xE4;n',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>','He<PERSON>ikuu','<PERSON><PERSON>kuu','<PERSON><PERSON>kuu','Toukokuu','Kes&#xE4;kuu',
		'Hein&#xE4;kuu','<PERSON>oku<PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>aku<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON><PERSON>','Helmi','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','Kes&#xE4;',
		'Hein&#xE4;','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','Ma','Ti','<PERSON>','To','P<PERSON>','La'],
		dayNames: ['Sunnuntai','Maanantai','Tiistai','Keskiviikko','Torstai','Perjantai','Lauantai'],
		dayNamesMin: ['Su','Ma','Ti','Ke','To','Pe','La'],
		weekHeader: 'Vk',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['fi']);
});
