/* Croatian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON>. */
jQuery(function($){
	$.datepicker.regional['hr'] = {
		closeText: '<PERSON>at<PERSON><PERSON>',
		prevText: '&#x3c;',
		nextText: '&#x3e;',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>u<PERSON>','<PERSON>op<PERSON>','Stude<PERSON>','Prosinac'],
		monthNamesShort: ['Sij','Velj','O<PERSON>u','<PERSON>ra','<PERSON>vi','Lip',
		'Srp','Kol','Ruj','Lis','Stu','Pro'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON>ned<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON>n','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>'],
		dayNamesMin: ['N<PERSON>','<PERSON>','Ut','Sr','Če','Pe','Su'],
		weekHeader: 'Tje',
		dateFormat: 'dd.mm.yy.',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['hr']);
});