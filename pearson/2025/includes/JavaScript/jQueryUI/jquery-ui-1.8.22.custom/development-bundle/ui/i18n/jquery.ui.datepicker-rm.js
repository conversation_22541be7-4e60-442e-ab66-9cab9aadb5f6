/* Romansh initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['rm'] = {
		closeText: 'Serrar',
		prevText: '&#x3c;Suandant',
		nextText: 'Precedent&#x3e;',
		currentText: 'Actual',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON>av<PERSON>','<PERSON>','Avrigl','Matg','<PERSON>ercladur', '<PERSON><PERSON>ur','Avust','Settember','October','November','December'],
		monthNamesShort: ['<PERSON>ha','Fev','Mar','Avr','Matg','Zer', 'Fan','Avu','Sett','Oct','Nov','Dec'],
		dayNames: ['Dumengia','Glind<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>se<PERSON>na','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['Dum','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','V<PERSON>','Som'],
		dayNamesMin: ['Du','Gl','Ma','Me','Gi','Ve','So'],
		weekHeader: 'emna',
		dateFormat: 'dd/mm/yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['rm']);
});
