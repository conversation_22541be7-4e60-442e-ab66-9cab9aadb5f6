/* Romanian initialisation for the jQuery UI date picker plugin.
 *
 * Written by <PERSON> (<EMAIL>)
 * and <PERSON><PERSON> (<EMAIL>)
 */
jQuery(function($){
	$.datepicker.regional['ro'] = {
		closeText: 'Închide',
		prevText: '&laquo; Luna precedentă',
		nextText: '<PERSON> următoare &raquo;',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','August','Septembrie','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','Decembrie'],
		monthNamesShort: ['<PERSON>', 'Feb', 'Mar', 'Apr', 'Mai', 'Iun',
		'Iul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Du<PERSON>', 'Lu<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','Ma','Mi','Jo','Vi','Sâ'],
		weekHeader: 'Săpt',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['ro']);
});
