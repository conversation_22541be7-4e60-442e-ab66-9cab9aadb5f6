/* Luxembourgish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> <<EMAIL>> */
jQuery(function($){
	$.datepicker.regional['lb'] = {
		closeText: 'Fäerdeg',
		prevText: '<PERSON><PERSON><PERSON>',
		nextText: '<PERSON><PERSON>',
		currentText: 'Ha<PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','September','Okto<PERSON>','November','Dezember'],
		monthNamesShort: ['Jan', 'Feb', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Jun',
		'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'De<PERSON>'],
		dayNames: ['Sonndeg', '<PERSON>éindeg', 'Dënschdeg', 'M<PERSON>ttwoch', '<PERSON>neschdeg', 'Freideg', 'Samschde<PERSON>'],
		dayNamesShort: ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>'],
		dayNamesMin: ['So','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','Fr','Sa'],
		weekHeader: 'W',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['lb']);
});
