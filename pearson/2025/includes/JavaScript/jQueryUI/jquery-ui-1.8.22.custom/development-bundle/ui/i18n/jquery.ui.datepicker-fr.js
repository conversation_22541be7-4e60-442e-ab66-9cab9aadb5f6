/* French initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (kbwood{at}iinet.com.au),
              <PERSON><PERSON><PERSON><PERSON> (<EMAIL>),
              <PERSON><PERSON><PERSON><PERSON> <<EMAIL>> */
jQuery(function($){
	$.datepicker.regional['fr'] = {
		closeText: 'Fermer',
		prevText: 'Précédent',
		nextText: 'Suivant',
		currentText: 'Aujourd\'hui',
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>','Avril','<PERSON>','Juin',
		'<PERSON><PERSON><PERSON>','A<PERSON><PERSON>t','Septembre','Octobre','Novembre','Décembre'],
		monthNamesShort: ['Janv.','Févr.','Mars','Avril','<PERSON>','Juin',
		'Juil.','Août','Sept.','Oct.','Nov.','Déc.'],
		dayNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['Dim.','Lun.','Mar.','Mer.','Jeu.','Ven.','Sam.'],
		dayNamesMin: ['D','L','M','M','J','V','S'],
		weekHeader: 'Sem.',
		dateFormat: 'dd/mm/yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['fr']);
});
