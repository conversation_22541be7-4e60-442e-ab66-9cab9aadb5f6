<p>The jQuery UI effects core extends the <a href="http://docs.jquery.com/Effects/animate" title="Effects/animate">animate</a> function to be able to animate colors as well. It's heavily used by the class transition feature and it's able to color animate the following properties:
</p>
<ul><li> <b>backgroundColor</b>
</li><li> <b>borderBottomColor</b>
</li><li> <b>borderLeftColor</b>
</li><li> <b>borderRightColor</b>
</li><li> <b>borderTopColor</b>
</li><li> <b>color</b>
</li><li> <b>outlineColor</b>
</li></ul>
<p>with one of the following combinations:
</p>
<ul><li> <b>hex (#FF0000)</b>
</li><li> <b>rgb (rgb(255,255,255))</b>
</li><li> <b>names ("black")</b>
</li></ul>
<p><br />
</p><p><div class="options list"><table class="options examples" cellspacing="0"><thead><tr><th>Name</th><th>Type</th></tr></thead><tbody>
</table><b class="options">Example:</b><table class="options examples" cellspacing="0"><thead><tr><th>Name</th><th>Type</th></tr></thead><tbody>
<div class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<div id="demo" class="tabs-container" rel="125">
A simple color animation.<br />
</p>
<pre>$(&quot;.block&quot;).<a href="http://docs.jquery.com/Events/toggle" title="Events/toggle">toggle</a>(function() {
    $(this).<a href="http://docs.jquery.com/Effects/animate" title="Effects/animate">animate</a>({ backgroundColor: &quot;black&quot; }, 1000);
},function() {
    $(this).<a href="http://docs.jquery.com/Effects/animate" title="Effects/animate">animate</a>({ backgroundColor: &quot;#68BFEF&quot; }, 500);
});

</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" 
                    "<a href="http://www.w3.org/TR/html4/loose.dtd" class="external free" title="http://www.w3.org/TR/html4/loose.dtd">http://www.w3.org/TR/html4/loose.dtd</a>"&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;script src="<a href="http://code.jquery.com/jquery-latest.js" class="external free" title="http://code.jquery.com/jquery-latest.js">http://code.jquery.com/jquery-latest.js</a>"&gt;&lt;/script&gt;
  
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.4/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;

  &lt;script&gt;
  $(document).ready(function(){
    $(&quot;.block&quot;).<a href="http://docs.jquery.com/Events/toggle" title="Events/toggle">toggle</a>(function() {
    $(this).<a href="http://docs.jquery.com/Effects/animate" title="Effects/animate">animate</a>({ backgroundColor: &quot;black&quot; }, 1000);
},function() {
    $(this).<a href="http://docs.jquery.com/Effects/animate" title="Effects/animate">animate</a>({ backgroundColor: &quot;#68BFEF&quot; }, 500);
});

  });
  &lt;/script&gt;
  &lt;style&gt;
  .block { 
    color: white;
    background-color: #68BFEF;
    width: 150px; 
    height: 70px;
    margin: 10px; 
  }
  &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;div class=&quot;block&quot;&gt; Click me&lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
</p><p></tbody></table></div>
</p><!-- 
Pre-expand include size: 3730 bytes
Post-expand include size: 5382 bytes
Template argument size: 3450 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3094-1!1!0!!en!2 and timestamp 20120724114846 -->
