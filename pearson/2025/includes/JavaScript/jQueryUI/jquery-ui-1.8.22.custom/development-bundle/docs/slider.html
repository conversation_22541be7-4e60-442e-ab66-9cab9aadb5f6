
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Options</a></li>
<li><a href="#events">Events</a></li>
<li><a href="#methods">Methods</a></li>
<li><a href="#theming">Theming</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI Slider</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <p>The jQuery UI Slider plugin makes selected elements into sliders. There are various options such as multiple handles, and ranges. The handle can be moved with the mouse or the arrow keys.</p>
<p>The start, slide, and stop callbacks receive two arguments: The original browser event and a prepared ui object, view below for a documentation of this object (if you name your second argument 'ui'):
</p><p>The slider widget will create handle elements with the class 'ui-slider-handle' on initialization. You can specify custom handle elements by creating and appending the elements and adding the 'ui-slider-handle' class before init. It will only create the number of handles needed to match the length of value/values. For example, if you specify 'values: [1, 5, 18]' and create one custom handle, the plugin will create the other two.
</p>
<ul>
  <li><b>ui.handle</b>: DOMElement - the current focused handle</li>
  <li><b>ui.value</b>: Integer - the current handle's value</li>
</ul>
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li>UI Core</li>
<li>UI Widget</li>
<li>UI Mouse</li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="">
A simple jQuery UI Slider.<br />
</p>
<pre>$(&quot;#slider&quot;).slider();
</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
    &lt;style type=&quot;text/css&quot;&gt;
    #slider { margin: 10px; }
  &lt;/style&gt;
  &lt;script&gt;
  $(document).ready(function() {
    $(&quot;#slider&quot;).slider();
  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;div id=&quot;slider&quot;&gt;&lt;/div&gt;

&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Options</h2>
    <ul class="options-list">
      
<li class="option" id="option-disabled">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-disabled">disabled</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Disables (true) or enables (false) the slider. Can be set when initialising (first creating) the slider.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>disabled</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ disabled: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>disabled</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var disabled = $( ".selector" ).slider( "option", "disabled" );
//setter
$( ".selector" ).slider( "option", "disabled", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-animate">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-animate">animate</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean, String, Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Whether to slide handle smoothly when user click outside handle on the bar. Will also accept a string representing one of the three predefined speeds ("slow", "normal", or "fast") or the number of milliseconds to run the animation (e.g. 1000).</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>animate</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ animate: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>animate</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var animate = $( ".selector" ).slider( "option", "animate" );
//setter
$( ".selector" ).slider( "option", "animate", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-max">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-max">max</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">100</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The maximum value of the slider.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>max</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ max: 7 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>max</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var max = $( ".selector" ).slider( "option", "max" );
//setter
$( ".selector" ).slider( "option", "max", 7 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-min">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-min">min</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The minimum value of the slider.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>min</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ min: -7 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>min</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var min = $( ".selector" ).slider( "option", "min" );
//setter
$( ".selector" ).slider( "option", "min", -7 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-orientation">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-orientation">orientation</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"horizontal"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>This option determines whether the slider has the min at the left, the max at the right or the min at the bottom, the max at the top. Possible values: 'horizontal', 'vertical'.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>orientation</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ orientation: "vertical" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>orientation</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var orientation = $( ".selector" ).slider( "option", "orientation" );
//setter
$( ".selector" ).slider( "option", "orientation", "vertical" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-range">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-range">range</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean, String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>If set to true, the slider will detect if you have two handles and create a stylable range element between these two. Two other possible values are 'min' and 'max'. A min range goes from the slider min to one handle. A max range goes from one handle to the slider max.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>range</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ range: 'min' });</code></pre>
</dd>

    
<dt>
  Get or set the <code>range</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var range = $( ".selector" ).slider( "option", "range" );
//setter
$( ".selector" ).slider( "option", "range", 'min' );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-step">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-step">step</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">1</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Determines the size or amount of each interval or step the slider takes between min and max. The full specified value range of the slider (max - min) needs to be evenly divisible by the step.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>step</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ step: 5 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>step</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var step = $( ".selector" ).slider( "option", "step" );
//setter
$( ".selector" ).slider( "option", "step", 5 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-value">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-value">value</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Determines the value of the slider, if there's only one handle. If there is more than one handle, determines the value of the first handle.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>value</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ value: 37 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>value</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var value = $( ".selector" ).slider( "option", "value" );
//setter
$( ".selector" ).slider( "option", "value", 37 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-values">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-values">values</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>This option can be used to specify multiple handles. If range is set to true, the length of 'values' should be 2.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a slider with the <code>values</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).slider({ values: [1,5,9] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>values</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var values = $( ".selector" ).slider( "option", "values" );
//setter
$( ".selector" ).slider( "option", "values", [1,5,9] );</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="events">
    <h2 class="top-header">Events</h2>
    <ul class="events-list">
      
<li class="event" id="event-create">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-create">create</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">slidecreate</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when slider is created.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>create</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).slider({
   create: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>create</code> event by type: <code>slidecreate</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;slidecreate&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-start">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-start">start</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">slidestart</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when the user starts sliding.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>start</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).slider({
   start: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>start</code> event by type: <code>slidestart</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;slidestart&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-slide">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-slide">slide</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">slide</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered on every mouse move during slide. Use ui.value (single-handled sliders) to obtain the value of the current handle, $(..).slider('value', index) to get another handles' value.
</p><p>Return false in order to prevent a slide, based on ui.value.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>slide</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).slider({
   slide: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>slide</code> event by type: <code>slide</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;slide&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-change">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-change">change</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">slidechange</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered on slide stop, or if the value is changed programmatically (by the <code>value</code> method).  Takes arguments event and ui.  Use event.originalEvent to detect whether the value changed by mouse, keyboard, or programmatically. Use ui.value (single-handled sliders) to obtain the value of the current handle, $(this).slider('values', index) to get another handle's value.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>change</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).slider({
   change: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>change</code> event by type: <code>slidechange</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;slidechange&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-stop">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-stop">stop</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">slidestop</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when the user stops sliding.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>stop</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).slider({
   stop: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>stop</code> event by type: <code>slidestop</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;slidestop&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="methods">
    <h2 class="top-header">Methods</h2>
    <ul class="methods-list">
      
<li class="method" id="method-destroy">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-destroy">destroy</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "destroy"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Remove the slider functionality completely. This will return the element back to its pre-init state.</p>
  </div>
</li>


<li class="method" id="method-disable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-disable">disable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "disable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Disable the slider.</p>
  </div>
</li>


<li class="method" id="method-enable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-enable">enable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "enable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Enable the slider.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "option"

, optionName

, <span class="optional">[</span>value<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Get or set any slider option. If no value is specified, will act as a getter.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "option"

, options





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Set multiple slider options at once by providing an options object.</p>
  </div>
</li>


<li class="method" id="method-widget">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-widget">widget</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "widget"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Returns the .ui-slider element.</p>
  </div>
</li>


<li class="method" id="method-value">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-value">value</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "value"

, <span class="optional">[</span>value<span class="optional">] </span>





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Gets or sets the value of the slider. For single handle sliders.</p>
  </div>
</li>


<li class="method" id="method-values">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-values">values</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.slider( "values"

, index

, <span class="optional">[</span>value<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Gets or sets the values of the slider. For multiple handle or range sliders.</p>
  </div>
</li>

    </ul>
  </div>
  <div id="theming">
    <h2 class="top-header">Theming</h2>
    <p>The jQuery UI Slider plugin uses the jQuery UI CSS Framework to style its look and feel, including colors and background textures. We recommend using the ThemeRoller tool to create and download custom themes that are easy to build and maintain.
</p>
  <p>If a deeper level of customization is needed, there are widget-specific classes referenced within the jquery.ui.slider.css stylesheet that can be modified. These classes are highlighed in bold below.
</p>
    
  <h3>Sample markup with jQuery UI CSS Framework classes</h3>
  &lt;div class=&quot;ui-slider<strong> ui-slider-horizontal</strong> ui-widget ui-widget-content ui-corner-all&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;a style=&quot;left: 0%;&quot; class=&quot;<strong>ui-slider-handle</strong> ui-state-default ui-corner-all&quot; href=&quot;#&quot;&gt;&lt;/a&gt;<br />
&lt;/div&gt;<br />
  <p class="theme-note">
    <strong>
      Note: This is a sample of markup generated by the slider plugin, not markup you should use to create a slider. The only markup needed for that is &lt;div&gt;&lt;div&gt;.
    </strong>
  </p>

  </div>
</div>

</p><!-- 
Pre-expand include size: 36451 bytes
Post-expand include size: 55617 bytes
Template argument size: 27679 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3776-1!1!0!!en!2 and timestamp 20120724123246 -->
