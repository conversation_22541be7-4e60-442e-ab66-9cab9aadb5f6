
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Options</a></li>
<li><a href="#events">Events</a></li>
<li><a href="#methods">Methods</a></li>
<li><a href="#theming">Theming</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI Tabs</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <p>Tabs are generally used to break content into multiple sections that can be swapped to save space, much like an accordion.</p>
<p>By default a tab widget will swap between tabbed sections onClick, but the events can be changed to onHover through an option. Tab content can be loaded via Ajax by setting an href on a tab.</p>
<p><em>NOTE: Tabs created dynamically using .tabs( "add", ... ) are given an id of ui-tabs-NUM, where NUM is an auto-incrementing id. If you use this naming convention for your own elements, you may encounter problems.</em></p>
<table id="toc" class="toc" summary="Contents"><tr><td><div id="toctitle"><h2>Contents</h2></div>
<ul>
<li class="toclevel-1"><a href="#Events"><span class="tocnumber">1</span> <span class="toctext">Events</span></a></li>
<li class="toclevel-1"><a href="#Ajax_mode"><span class="tocnumber">2</span> <span class="toctext">Ajax mode</span></a>
<ul>
<li class="toclevel-2"><a href="#Back_button_and_bookmarking"><span class="tocnumber">2.1</span> <span class="toctext">Back button and bookmarking</span></a></li>
</ul>
</li>
<li class="toclevel-1"><a href="#How_to..."><span class="tocnumber">3</span> <span class="toctext">How to...</span></a>
<ul>
<li class="toclevel-2"><a href="#...retrieve_the_index_of_the_currently_selected_tab"><span class="tocnumber">3.1</span> <span class="toctext">...retrieve the index of the currently selected tab</span></a></li>
<li class="toclevel-2"><a href="#...open_links_in_the_current_tab_instead_of_leaving_the_page"><span class="tocnumber">3.2</span> <span class="toctext">...open links in the current tab instead of leaving the page</span></a></li>
<li class="toclevel-2"><a href="#...select_a_tab_from_a_text_link_instead_of_clicking_a_tab_itself"><span class="tocnumber">3.3</span> <span class="toctext">...select a tab from a text link instead of clicking a tab itself</span></a></li>
<li class="toclevel-2"><a href="#...prevent_switching_to_the_tab_on_click_depending_on_form_validation"><span class="tocnumber">3.4</span> <span class="toctext">...prevent switching to the tab on click depending on form validation</span></a></li>
<li class="toclevel-2"><a href="#...immediately_select_a_just_added_tab"><span class="tocnumber">3.5</span> <span class="toctext">...immediately select a just added tab</span></a></li>
<li class="toclevel-2"><a href="#...prevent_a_FOUC_.28Flash_of_Unstyled_Content.29_before_tabs_are_initialized"><span class="tocnumber">3.6</span> <span class="toctext">...prevent a FOUC (Flash of Unstyled Content) before tabs are initialized</span></a></li>
</ul>
</li>
<li class="toclevel-1"><a href="#Why_does..."><span class="tocnumber">4</span> <span class="toctext">Why does...</span></a>
<ul>
<li class="toclevel-2"><a href="#...my_slider.2C_Google_Map.2C_sIFR_etc._not_work_when_placed_in_a_hidden_.28inactive.29_tab.3F"><span class="tocnumber">4.1</span> <span class="toctext">...my slider, Google Map, sIFR etc. not work when placed in a hidden (inactive) tab?</span></a></li>
</ul>
</li>
</ul>
</td></tr></table><script type="text/javascript"> if (window.showTocToggle) { var tocShowText = "show"; var tocHideText = "hide"; showTocToggle(); } </script>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=1" title="Template:UIAPIPlugin">edit</a>]</div><a name="Events"></a><h2>Events</h2>
<p>A series of events fire when interacting with a tabs interface:
</p>
<ul><li> tabsselect, tabsload, tabsshow (in that order)
</li><li> tabsadd, tabsremove
</li><li> tabsenable, tabsdisable
</li></ul>
<p>Event binding example:
</p>
<pre>$('#example').bind('tabsselect', function(event, ui) {

    // Objects available in the function context:
    ui.tab     // anchor element of the selected (clicked) tab
    ui.panel   // element, that contains the selected/clicked tab contents
    ui.index   // zero-based index of the selected (clicked) tab

});</pre>
<p>Note that if a handler for the tabsselect event returns false, the clicked tab will not become selected (useful for example if switching to the next tab requires a form validation).
</p>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=2" title="Template:UIAPIPlugin">edit</a>]</div><a name="Ajax_mode"></a><h2> Ajax mode </h2>
<p>Tabs supports loading tab content via Ajax in an unobtrusive manner.
</p><p>The HTML you need is slightly different from the one that is used for static tabs: A list of links pointing to existing resources (from where the content gets loaded) and no additional containers at all (unobtrusive!). The containers' markup is going to be created on the fly:
</p>
<pre>
&lt;div id=&quot;example&quot;&gt;
     &lt;ul&gt;
         &lt;li&gt;&lt;a href=&quot;ahah_1.html&quot;&gt;&lt;span&gt;Content 1&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;
         &lt;li&gt;&lt;a href=&quot;ahah_2.html&quot;&gt;&lt;span&gt;Content 2&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;
         &lt;li&gt;&lt;a href=&quot;ahah_3.html&quot;&gt;&lt;span&gt;Content 3&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;
     &lt;/ul&gt;
&lt;/div&gt;
</pre>
<p>Obviously this degrades gracefully - the links, e.g. the content, will still be accessible with JavaScript disabled.
</p><p>Note that if you wish to reuse an existing container, you
could do so by matching a title attribute and the container's id:
</p>
<pre>
&lt;li&gt;&lt;a href=&quot;hello/world.html&quot; title=&quot;Todo Overview&quot;&gt; ... &lt;/a&gt;&lt;/li&gt;
</pre>
<p>and a container like:
</p>
<pre>
&lt;div id=&quot;Todo_Overview&quot;&gt; ... &lt;/div&gt;
</pre>
<p>(Note how white space is replaced with an underscore)
</p><p>This is useful if you want a human readable hash in the URL instead of
a cryptic generated one.
</p>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=3" title="Template:UIAPIPlugin">edit</a>]</div><a name="Back_button_and_bookmarking"></a><h3>Back button and bookmarking</h3>
<p>Tabs 2 already supported this functionality, although the history plugin needs a rewrite first (it doesn't support Safari 3 and is in general a little inflexible) before it can be build back into the tabs. It is planned and Klaus is working on it whenever he finds the time. Actual bugs in the UI Tabs plugin itself always have higher priority though.
</p>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=4" title="Template:UIAPIPlugin">edit</a>]</div><a name="How_to..."></a><h2>How to...</h2>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=5" title="Template:UIAPIPlugin">edit</a>]</div><a name="...retrieve_the_index_of_the_currently_selected_tab"></a><h3>...retrieve the index of the currently selected tab</h3>
<pre>var $tabs = $('#example').tabs();
var selected = $tabs.tabs('option', 'selected'); // =&gt; 0</pre>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=6" title="Template:UIAPIPlugin">edit</a>]</div><a name="...open_links_in_the_current_tab_instead_of_leaving_the_page"></a><h3>...open links in the current tab instead of leaving the page</h3>
<p>"Hijax" links after tab content has been loaded:
</p>
<pre>$('#example').tabs({
    load: function(event, ui) {
        $(ui.panel).delegate('a', 'click', function(event) {
            $(ui.panel).load(this.href);
            event.preventDefault();
        });
    }
});</pre>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=7" title="Template:UIAPIPlugin">edit</a>]</div><a name="...select_a_tab_from_a_text_link_instead_of_clicking_a_tab_itself"></a><h3>...select a tab from a text link instead of clicking a tab itself</h3>
<pre>var $tabs = $('#example').tabs(); // first tab selected

$('#my-text-link').click(function() { // bind click event to link
    $tabs.tabs('select', 2); // switch to third tab
    return false;
});</pre>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=8" title="Template:UIAPIPlugin">edit</a>]</div><a name="...prevent_switching_to_the_tab_on_click_depending_on_form_validation"></a><h3>...prevent switching to the tab on click depending on form validation</h3>
<p>Returning false in the tabs select handler prevents the clicked tab from becoming selected.
</p>
<pre>$('#example').tabs({
    select: function(event, ui) {
        var isValid = ... // form validation returning true or false
        return isValid;
    }
});</pre>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=9" title="Template:UIAPIPlugin">edit</a>]</div><a name="...immediately_select_a_just_added_tab"></a><h3>...immediately select a just added tab</h3>
<pre>var $tabs = $('#example').tabs({
    add: function(event, ui) {
        $tabs.tabs('select', '#' + ui.panel.id);
    }
});</pre>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=10" title="Template:UIAPIPlugin">edit</a>]</div><a name="...prevent_a_FOUC_.28Flash_of_Unstyled_Content.29_before_tabs_are_initialized"></a><h3>...prevent a FOUC (Flash of Unstyled Content) before tabs are initialized</h3>
<p>Add the necessary classes to hide an inactive tab panel to the HTML right away - note that this will <b>not</b> degrade gracefully with JavaScript being disabled:
</p>
<pre>&lt;div id=&quot;example&quot; class=&quot;ui-tabs&quot;&gt;
  ...
  &lt;div id=&quot;a-tab-panel&quot; class=&quot;ui-tabs-hide&quot;&gt; &lt;/div&gt;
  ...
&lt;/div&gt;</pre>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=11" title="Template:UIAPIPlugin">edit</a>]</div><a name="Why_does..."></a><h2>Why does...</h2>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/Template:UIAPIPlugin?section=12" title="Template:UIAPIPlugin">edit</a>]</div><a name="...my_slider.2C_Google_Map.2C_sIFR_etc._not_work_when_placed_in_a_hidden_.28inactive.29_tab.3F"></a><h3>...my slider, Google Map, sIFR etc. not work when placed in a hidden (inactive) tab?</h3>
<p>Any component that requires some dimensional computation for its initialization won't work in a hidden tab, because the tab panel itself is hidden via <code>display: none</code> so that any elements inside won't report their actual width and height (0 in most browsers).
</p><p>There's an easy workaround. Use the <em>off-left technique</em> for hiding inactive tab panels. E.g. in your style sheet replace the rule for the class selector ".ui-tabs .ui-tabs-hide" with
</p>
<pre>.ui-tabs .ui-tabs-hide {
    position: absolute;
    left: -10000px;
}</pre>
<p>For Google maps you can also resize the map once the tab is displayed like this:
</p>
<pre>$('#example').bind('tabsshow', function(event, ui) {
    if (ui.panel.id == &quot;map-tab&quot;) {
        resizeMap();
    }
});</pre>
resizeMap() will call Google Maps' checkResize() on the particular map.
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li>UI Core</li>
<li>UI Widget</li>
<li><strong>Optional:</strong> <a href="https://github.com/carhartl/jquery-cookie" class="external text" title="https://github.com/carhartl/jquery-cookie">jquery.cookie.js</a> for use with <a href="http://jqueryui.com/demos/tabs/#option-cookie" class="external text" title="http://jqueryui.com/demos/tabs/#option-cookie">cookie option</a> (also included in development-bundle&gt;external folder.)</li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="295">
A simple jQuery UI Tabs.<br />
</p>
<pre>$(&quot;#tabs&quot;).tabs();
</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
  
  &lt;script&gt;
  $(document).ready(function() {
    $(&quot;#tabs&quot;).tabs();
  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;div id=&quot;tabs&quot;&gt;
    &lt;ul&gt;
        &lt;li&gt;&lt;a href=&quot;#fragment-1&quot;&gt;&lt;span&gt;One&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href=&quot;#fragment-2&quot;&gt;&lt;span&gt;Two&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;
        &lt;li&gt;&lt;a href=&quot;#fragment-3&quot;&gt;&lt;span&gt;Three&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;
    &lt;div id=&quot;fragment-1&quot;&gt;
        &lt;p&gt;First tab is active by default:&lt;/p&gt;
        &lt;pre&gt;&lt;code&gt;$('#example').tabs();&lt;/code&gt;&lt;/pre&gt;
    &lt;/div&gt;
    &lt;div id=&quot;fragment-2&quot;&gt;
        Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
        Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
    &lt;/div&gt;
    &lt;div id=&quot;fragment-3&quot;&gt;
        Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
        Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
        Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
    &lt;/div&gt;
&lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Options</h2>
    <ul class="options-list">
      
<li class="option" id="option-disabled">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-disabled">disabled</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Disables (true) or enables (false) the tabs. Can be set when initialising (first creating) the tabs.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>disabled</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ disabled: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>disabled</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var disabled = $( ".selector" ).tabs( "option", "disabled" );
//setter
$( ".selector" ).tabs( "option", "disabled", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-ajaxOptions">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-ajaxOptions">ajaxOptions</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Options</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Additional Ajax options to consider when loading tab content (see $.ajax).</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>ajaxOptions</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ ajaxOptions: { async: false } });</code></pre>
</dd>

    
<dt>
  Get or set the <code>ajaxOptions</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var ajaxOptions = $( ".selector" ).tabs( "option", "ajaxOptions" );
//setter
$( ".selector" ).tabs( "option", "ajaxOptions", { async: false } );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-cache">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-cache">cache</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Whether or not to cache remote tabs content, e.g. load only once or with every click. Cached content is being lazy loaded, e.g once and only once for the first click. Note that to prevent the actual Ajax requests from being cached by the browser you need to provide an extra cache: false flag to ajaxOptions.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>cache</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ cache: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>cache</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var cache = $( ".selector" ).tabs( "option", "cache" );
//setter
$( ".selector" ).tabs( "option", "cache", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-collapsible">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-collapsible">collapsible</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set to true to allow an already selected tab to become unselected again upon reselection.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>collapsible</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ collapsible: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>collapsible</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var collapsible = $( ".selector" ).tabs( "option", "collapsible" );
//setter
$( ".selector" ).tabs( "option", "collapsible", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-cookie">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-cookie">cookie</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Object</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Store the latest selected tab in a cookie. The cookie is then used to determine the initially selected tab if the <i>selected</i> option is not defined. Requires <a href="http://plugins.jquery.com/project/cookie" class="external text" title="http://plugins.jquery.com/project/cookie">cookie plugin</a>, which can also be found in the development-bundle&gt;external folder from the download builder. The object needs to have key/value pairs of the form the cookie plugin expects as options. Available options (example): &#123; expires: 7, path: '/', domain: 'jquery.com', secure: true &#125;. Since jQuery UI 1.7 it is also possible to define the cookie name being used via <i>name</i> property.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>cookie</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ cookie: { expires: 30 } });</code></pre>
</dd>

    
<dt>
  Get or set the <code>cookie</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var cookie = $( ".selector" ).tabs( "option", "cookie" );
//setter
$( ".selector" ).tabs( "option", "cookie", { expires: 30 } );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-deselectable">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-deselectable">deselectable</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>deprecated in jQuery UI 1.7, use collapsible.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>deselectable</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ deselectable: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>deselectable</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var deselectable = $( ".selector" ).tabs( "option", "deselectable" );
//setter
$( ".selector" ).tabs( "option", "deselectable", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-disabled">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-disabled">disabled</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array&lt;Number&gt;</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">[]</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>An array containing the position of the tabs (zero-based index) that should be disabled on initialization.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>disabled</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ disabled: [1, 2] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>disabled</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var disabled = $( ".selector" ).tabs( "option", "disabled" );
//setter
$( ".selector" ).tabs( "option", "disabled", [1, 2] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-event">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-event">event</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"click"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The type of event to be used for selecting a tab.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>event</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ event: "mouseover" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>event</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var event = $( ".selector" ).tabs( "option", "event" );
//setter
$( ".selector" ).tabs( "option", "event", "mouseover" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-fx">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-fx">fx</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Options, Array&lt;Options&gt;</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Enable animations for hiding and showing tab panels. The duration option can be a string representing one of the three predefined speeds ("slow", "normal", "fast") or the duration in milliseconds to run an animation (default is "normal").</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>fx</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ fx: { opacity: 'toggle' } });</code></pre>
</dd>

    
<dt>
  Get or set the <code>fx</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var fx = $( ".selector" ).tabs( "option", "fx" );
//setter
$( ".selector" ).tabs( "option", "fx", { opacity: 'toggle' } );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-idPrefix">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-idPrefix">idPrefix</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"ui-tabs-"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>If the remote tab, its anchor element that is, has no title attribute to generate an id from, an id/fragment identifier is created from this prefix and a unique id returned by $.data(el), for example "ui-tabs-54".</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>idPrefix</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ idPrefix: "ui-tabs-primary" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>idPrefix</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var idPrefix = $( ".selector" ).tabs( "option", "idPrefix" );
//setter
$( ".selector" ).tabs( "option", "idPrefix", "ui-tabs-primary" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-panelTemplate">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-panelTemplate">panelTemplate</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"&lt;div&gt;&lt;/div&gt;"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>HTML template from which a new tab panel is created in case of adding a tab with the add method or when creating a panel for a remote tab on the fly.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>panelTemplate</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ panelTemplate: "&lt;li&gt;&lt;/li&gt;" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>panelTemplate</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var panelTemplate = $( ".selector" ).tabs( "option", "panelTemplate" );
//setter
$( ".selector" ).tabs( "option", "panelTemplate", "&lt;li&gt;&lt;/li&gt;" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-selected">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-selected">selected</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Zero-based index of the tab to be selected on initialization. To set all tabs to unselected pass -1 as value.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>selected</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ selected: 3 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>selected</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var selected = $( ".selector" ).tabs( "option", "selected" );
//setter
$( ".selector" ).tabs( "option", "selected", 3 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-spinner">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-spinner">spinner</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"&lt;em&gt;Loading&amp;#8230;&lt;/em&gt;"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The HTML content of this string is shown in a tab title while remote content is loading. Pass in empty string to deactivate that behavior. An span element must be present in the A tag of the title, for the spinner content to be visible.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>spinner</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ spinner: "Retrieving data..." });</code></pre>
</dd>

    
<dt>
  Get or set the <code>spinner</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var spinner = $( ".selector" ).tabs( "option", "spinner" );
//setter
$( ".selector" ).tabs( "option", "spinner", "Retrieving data..." );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-tabTemplate">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-tabTemplate">tabTemplate</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"&lt;li&gt;&lt;a href&#61;"#&#123;href&#125;"&gt;&lt;span&gt;#&#123;label&#125;&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>HTML template from which a new tab is created and added. The placeholders #&#123;href&#125; and #&#123;label&#125; are replaced with the url and tab label that are passed as arguments to the add method.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a tabs with the <code>tabTemplate</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).tabs({ tabTemplate: "&lt;div&gt;&lt;a href&#61;"#&#123;href&#125;"&gt;&lt;span&gt;#&#123;label&#125;&lt;/span&gt;&lt;/a&gt;&lt;/div&gt;" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>tabTemplate</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var tabTemplate = $( ".selector" ).tabs( "option", "tabTemplate" );
//setter
$( ".selector" ).tabs( "option", "tabTemplate", "&lt;div&gt;&lt;a href&#61;"#&#123;href&#125;"&gt;&lt;span&gt;#&#123;label&#125;&lt;/span&gt;&lt;/a&gt;&lt;/div&gt;" );</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="events">
    <h2 class="top-header">Events</h2>
    <ul class="events-list">
      
<li class="event" id="event-create">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-create">create</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabscreate</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when tabs is created.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>create</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   create: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>create</code> event by type: <code>tabscreate</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabscreate&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-select">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-select">select</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsselect</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when clicking a tab.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>select</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   select: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>select</code> event by type: <code>tabsselect</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsselect&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-load">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-load">load</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsload</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered after the content of a remote tab has been loaded.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>load</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   load: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>load</code> event by type: <code>tabsload</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsload&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-show">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-show">show</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsshow</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when a tab is shown.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>show</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   show: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>show</code> event by type: <code>tabsshow</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsshow&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-add">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-add">add</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsadd</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when a tab is added.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>add</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   add: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>add</code> event by type: <code>tabsadd</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsadd&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-remove">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-remove">remove</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsremove</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when a tab is removed.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>remove</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   remove: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>remove</code> event by type: <code>tabsremove</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsremove&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-enable">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-enable">enable</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsenable</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when a tab is enabled.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>enable</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   enable: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>enable</code> event by type: <code>tabsenable</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsenable&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-disable">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-disable">disable</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">tabsdisable</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when a tab is disabled.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>disable</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).tabs({
   disable: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>disable</code> event by type: <code>tabsdisable</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;tabsdisable&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="methods">
    <h2 class="top-header">Methods</h2>
    <ul class="methods-list">
      
<li class="method" id="method-destroy">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-destroy">destroy</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "destroy"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Remove the tabs functionality completely. This will return the element back to its pre-init state.</p>
  </div>
</li>


<li class="method" id="method-disable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-disable">disable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "disable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Disable the tabs.</p>
  </div>
</li>


<li class="method" id="method-enable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-enable">enable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "enable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Enable the tabs.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "option"

, optionName

, <span class="optional">[</span>value<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Get or set any tabs option. If no value is specified, will act as a getter.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "option"

, options





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Set multiple tabs options at once by providing an options object.</p>
  </div>
</li>


<li class="method" id="method-widget">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-widget">widget</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "widget"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Returns the .ui-tabs element.</p>
  </div>
</li>


<li class="method" id="method-add">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-add">add</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "add"

, url

, label

, <span class="optional">[</span>index<span class="optional">] </span>

)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Add a new tab. The second argument is either a URL consisting of a fragment identifier only to create an in-page tab or a full url (relative or absolute, no cross-domain support) to turn the new tab into an Ajax (remote) tab. The third is the zero-based position where to insert the new tab. Optional, by default a new tab is appended at the end.</p>
  </div>
</li>


<li class="method" id="method-remove">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-remove">remove</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "remove"

, index





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Remove a tab. The second argument is the zero-based index of the tab to be removed. Instead of an index, the href of the tab may be passed.</p>
  </div>
</li>


<li class="method" id="method-enable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-enable">enable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "enable"

, index





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Enable a disabled tab.  To enable more than one tab at once reset the disabled property like: <code>$('#example').tabs("option","disabled",[]);</code>. The second argument is the zero-based index of the tab to be enabled. Instead of an index, the href of the tab may be passed.</p>
  </div>
</li>


<li class="method" id="method-disable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-disable">disable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "disable"

, index





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Disable a tab. The selected tab cannot be disabled. To disable more than one tab at once use: <code>$('#example').tabs("option","disabled", [1, 2, 3]);</code>  The second argument is the zero-based index of the tab to be disabled. Instead of an index, the href of the tab may be passed.</p>
  </div>
</li>


<li class="method" id="method-select">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-select">select</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "select"

, index





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Select a tab, as if it were clicked. The second argument is the zero-based index of the tab to be selected or the id selector of the panel the tab is associated with (the tab's href fragment identifier, e.g. hash, points to the panel's id).</p>
  </div>
</li>


<li class="method" id="method-load">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-load">load</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "load"

, index





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Reload the content of an Ajax tab programmatically. This method always loads the tab content from the remote location, even if cache is set to true. The second argument is the zero-based index of the tab to be reloaded.</p>
  </div>
</li>


<li class="method" id="method-url">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-url">url</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "url"

, index

, url



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Change the url from which an Ajax (remote) tab will be loaded. The specified URL will be used for subsequent loads. Note that you can not only change the URL for an existing remote tab with this method, but also turn an in-page tab into a remote tab.  The second argument is the zero-based index of the tab of which its URL is to be updated.  The third is a URL the content of the tab is loaded from.</p>
  </div>
</li>


<li class="method" id="method-length">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-length">length</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "length"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Retrieve the number of tabs of the first matched tab pane.</p>
  </div>
</li>


<li class="method" id="method-abort">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-abort">abort</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "abort"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Terminate all running tab ajax requests and animations.</p>
  </div>
</li>


<li class="method" id="method-rotate">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-rotate">rotate</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.tabs( "rotate"

, ms

, <span class="optional">[</span>continuing<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Set up an automatic rotation through tabs of a tab pane.  The second argument is an amount of time in milliseconds until the next tab in the cycle gets activated. Use 0 or null to stop the rotation.  The third controls whether or not to continue the rotation after a tab has been selected by a user. Default: false.</p>
  </div>
</li>

    </ul>
  </div>
  <div id="theming">
    <h2 class="top-header">Theming</h2>
    <p>The jQuery UI Tabs plugin uses the jQuery UI CSS Framework to style its look and feel, including colors and background textures. We recommend using the ThemeRoller tool to create and download custom themes that are easy to build and maintain.
</p>
  <p>If a deeper level of customization is needed, there are widget-specific classes referenced within the jquery.ui.tabs.css stylesheet that can be modified. These classes are highlighed in bold below.
</p>
    
  <h3>Sample markup with jQuery UI CSS Framework classes</h3>
  &lt;div class=&quot;<strong>ui-tabs</strong> ui-widget ui-widget-content ui-corner-all&quot; id=&quot;tabs&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;ul class=&quot;<strong>ui-tabs-nav</strong> ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li class=&quot;ui-state-default ui-corner-top ui-tabs-selected ui-state-active&quot;&gt;&lt;a href=&quot;#tabs-1&quot;&gt;Nunc tincidunt&lt;/a&gt;&lt;/li&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li class=&quot;ui-state-default ui-corner-top&quot;&gt;&lt;a href=&quot;#tabs-2&quot;&gt;Proin dolor&lt;/a&gt;&lt;/li&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;div class=&quot;<strong>ui-tabs-panel</strong> ui-widget-content ui-corner-bottom&quot; id=&quot;tabs-1&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;p&gt;Tab one content goes here.&lt;/p&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br />
&nbsp;&nbsp;&nbsp; ...<br />
&lt;/div&gt;<br />
  <p class="theme-note">
    <strong>
      Note: This is a sample of markup generated by the tabs plugin, not markup you should use to create a tabs. The only markup needed for that is <br />&lt;div id=&quot;tabs&quot;&gt;<br />
&#160;&#160;&#160;&lt;ul&gt;<br />
&#160;&#160;&#160;&#160;&#160;&#160;&lt;li&gt;&lt;a href=&quot;#tabs-1&quot;&gt;Nunc tincidunt&lt;/a&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&#160;&#160;&#160;&lt;li&gt;&lt;a href=&quot;#tabs-2&quot;&gt;Proin dolor&lt;/a&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&#160;&#160;&#160;&lt;li&gt;&lt;a href=&quot;#tabs-3&quot;&gt;Aenean lacinia&lt;/a&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&lt;/ul&gt;<br />
&#160;&#160;&#160;&lt;div id=&quot;tabs-1&quot;&gt;<br />
&#160;&#160;&#160;&#160;&#160;&#160;&lt;p&gt;Tab 1 content&lt;/p&gt;<br />
&#160;&#160;&#160;&lt;/div&gt;<br />
&#160;&#160;&#160;&lt;div id=&quot;tabs-2&quot;&gt;<br />
&#160;&#160;&#160;&#160;&#160;&#160;&lt;p&gt;Tab 2 content&lt;/p&gt;<br />
&#160;&#160;&#160;&lt;/div&gt;<br />
&#160;&#160;&#160;&lt;div id=&quot;tabs-3&quot;&gt;<br />
&#160;&#160;&#160;&#160;&#160;&#160;&lt;p&gt;Tab 3 content&lt;/p&gt;<br />
&#160;&#160;&#160;&lt;/div&gt;<br />
&lt;/div&gt;.
    </strong>
  </p>

  </div>
</div>

</p><!-- 
Pre-expand include size: 57666 bytes
Post-expand include size: 97531 bytes
Template argument size: 57634 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3777-1!1!0!!en!2 and timestamp 20120724123246 -->
