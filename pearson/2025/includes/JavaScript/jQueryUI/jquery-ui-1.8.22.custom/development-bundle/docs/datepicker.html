
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Options</a></li>
<li><a href="#events">Events</a></li>
<li><a href="#methods">Methods</a></li>
<li><a href="#theming">Theming</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI Datepicker</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <p>The jQuery UI Datepicker is a highly configurable plugin that adds datepicker functionality to your pages. You can customize the date format and language, restrict the selectable date ranges and add in buttons and other navigation options easily.</p>
<p>By default, the datepicker calendar opens in a small overlay onFocus and closes automatically onBlur or when a date is selected. For an inline calendar, simply attach the datepicker to a div or span.
</p><p>You can use keyboard shortcuts to drive the datepicker:
</p>
<ul>
 <li>page up/down - previous/next month</li>
 <li>ctrl+page up/down - previous/next year</li>
 <li>ctrl+home - current month or open when closed</li>
 <li>ctrl+left/right - previous/next day</li>
 <li>ctrl+up/down - previous/next week</li>
 <li>enter - accept the selected date</li>
 <li>ctrl+end - close and erase the date</li>
 <li>escape - close the datepicker without selection</li>
</ul>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/UI/API/1.8/Datepicker?section=1" title="Edit section: Utility functions">edit</a>]</div><a name="Utility_functions"></a><h3 id="utility-functions">Utility functions</h3>
<ul>
 <li><a href="http://docs.jquery.com/UI/Datepicker/setDefaults" title="UI/Datepicker/setDefaults">$.datepicker.setDefaults( settings )</a> - Set settings for all datepicker instances.</li>
 <li><a href="http://docs.jquery.com/UI/Datepicker/formatDate" title="UI/Datepicker/formatDate">$.datepicker.formatDate( format, date, settings )</a> - Format a date into a string value with a specified format.</li>
 <li><a href="http://docs.jquery.com/UI/Datepicker/parseDate" title="UI/Datepicker/parseDate">$.datepicker.parseDate( format, value, settings ) </a> - Extract a date from a string value with a specified format.</li>
 <li><a href="http://docs.jquery.com/UI/Datepicker/iso8601Week" title="UI/Datepicker/iso8601Week">$.datepicker.iso8601Week( date )</a> - Determine the week of the year for a given date: 1 to 53.</li>
 <li><a href="http://docs.jquery.com/UI/Datepicker/noWeekends" title="UI/Datepicker/noWeekends">$.datepicker.noWeekends</a> - Set as beforeShowDay function to prevent selection of weekends.</li>
</ul>
<div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/UI/API/1.8/Datepicker?section=2" title="Edit section: Localization">edit</a>]</div><a name="Localization"></a><h3>Localization</h3>
<p>Datepicker provides support for localizing its content to cater for different languages
	and date formats. Each localization is contained within its own file with the 
	language code appended to the name, e.g. <code>jquery.ui.datepicker-fr.js</code> for French.
	The desired localization file should be included after the main datepicker code. They add their settings to the set
	of available localizations and automatically apply them as defaults for all instances.</p>
<p>The <code>$.datepicker.regional</code> attribute holds an array of localizations,
	indexed by language code, with &quot;&quot; referring to the default (English). Each entry is
	an object with the following attributes: <code>closeText</code>, <code>prevText</code>,
	<code>nextText</code>, <code>currentText</code>, <code>monthNames</code>,
	<code>monthNamesShort</code>, <code>dayNames</code>, <code>dayNamesShort</code>, 
	<code>dayNamesMin</code>, <code>weekHeader</code>, <code>dateFormat</code>,
	<code>firstDay</code>, <code>isRTL</code>, <code>showMonthAfterYear</code>,
	and <code>yearSuffix</code>.</p>
<p>You can restore the default localizations with:</p>
<p><code>$.datepicker.setDefaults($.datepicker.regional[&quot;&quot;]);</code>
</p>
<p>And can then override an individual datepicker for a specific locale:</p>
<p><code>$(selector).datepicker($.datepicker.regional['fr']);</code>
</p>
The localization files are included when using the <a href="http://jqueryui.com/download/" class="external text" title="http://jqueryui.com/download/">jQuery UI download builder</a>.
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li>UI Core</li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="220">
A simple jQuery UI Datepicker.<br />
</p>
<pre>$(&quot;#datepicker&quot;).datepicker();
</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
  
  &lt;script&gt;
  $(document).ready(function() {
    $(&quot;#datepicker&quot;).datepicker();
  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;div id=&quot;datepicker&quot;&gt;&lt;/div&gt;

&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Options</h2>
    <ul class="options-list">
      
<li class="option" id="option-disabled">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-disabled">disabled</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Disables (true) or enables (false) the datepicker. Can be set when initialising (first creating) the datepicker.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>disabled</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ disabled: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>disabled</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var disabled = $( ".selector" ).datepicker( "option", "disabled" );
//setter
$( ".selector" ).datepicker( "option", "disabled", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-altField">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-altField">altField</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Selector, jQuery, Element</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">&quot;&quot;</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The jQuery selector for another field that is to be updated with the selected date from the datepicker. Use the <code><a href="http://docs.jquery.com/UI/Datepicker#option-altFormat" title="UI/Datepicker">altFormat</a></code> setting to change the format of the date within this field. Leave as blank for no alternate field.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>altField</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ altField: "#actualDate" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>altField</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var altField = $( ".selector" ).datepicker( "option", "altField" );
//setter
$( ".selector" ).datepicker( "option", "altField", "#actualDate" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-altFormat">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-altFormat">altFormat</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">&quot;&quot;</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code> to be used for the <code><a href="http://docs.jquery.com/UI/Datepicker#option-altField" title="UI/Datepicker">altField</a></code> option. This allows one date format to be shown to the user for selection purposes, while a different format is actually sent behind the scenes. For a full list of the possible formats see the <a href="http://docs.jquery.com/UI/Datepicker/formatDate" title="UI/Datepicker/formatDate">formatDate</a> function</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>altFormat</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ altFormat: "yy-mm-dd" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>altFormat</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var altFormat = $( ".selector" ).datepicker( "option", "altFormat" );
//setter
$( ".selector" ).datepicker( "option", "altFormat", "yy-mm-dd" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-appendText">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-appendText">appendText</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">&quot;&quot;</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display after each date field, e.g. to show the required format.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>appendText</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ appendText: "(yyyy-mm-dd)" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>appendText</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var appendText = $( ".selector" ).datepicker( "option", "appendText" );
//setter
$( ".selector" ).datepicker( "option", "appendText", "(yyyy-mm-dd)" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-autoSize">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-autoSize">autoSize</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set to true to automatically resize the input field to accommodate dates in the current <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code>.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>autoSize</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ autoSize: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>autoSize</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var autoSize = $( ".selector" ).datepicker( "option", "autoSize" );
//setter
$( ".selector" ).datepicker( "option", "autoSize", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-buttonImage">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-buttonImage">buttonImage</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">&quot;&quot;</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The URL for the popup button image. If set, <code><a href="http://docs.jquery.com/UI/Datepicker#option-buttonText" title="UI/Datepicker">buttonText</a></code> becomes the <i>alt</i> value and is not directly displayed.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>buttonImage</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ buttonImage: "/images/datepicker.gif" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>buttonImage</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var buttonImage = $( ".selector" ).datepicker( "option", "buttonImage" );
//setter
$( ".selector" ).datepicker( "option", "buttonImage", "/images/datepicker.gif" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-buttonImageOnly">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-buttonImageOnly">buttonImageOnly</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set to true to place an image after the field to use as the trigger without it appearing on a button.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>buttonImageOnly</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ buttonImageOnly: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>buttonImageOnly</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var buttonImageOnly = $( ".selector" ).datepicker( "option", "buttonImageOnly" );
//setter
$( ".selector" ).datepicker( "option", "buttonImageOnly", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-buttonText">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-buttonText">buttonText</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"..."</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display on the trigger button. Use in conjunction with <code><a href="http://docs.jquery.com/UI/Datepicker#option-showOn" title="UI/Datepicker">showOn</a></code> equal to 'button' or 'both'.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>buttonText</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ buttonText: "Choose" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>buttonText</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var buttonText = $( ".selector" ).datepicker( "option", "buttonText" );
//setter
$( ".selector" ).datepicker( "option", "buttonText", "Choose" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-calculateWeek">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-calculateWeek">calculateWeek</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Function</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">$.datepicker.iso8601Week</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>A function to calculate the week of the year for a given date. The default implementation uses the ISO 8601 definition: weeks start on a Monday; the first week of the year contains the first Thursday of the year.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>calculateWeek</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ calculateWeek: myWeekCalc });</code></pre>
</dd>

    
<dt>
  Get or set the <code>calculateWeek</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var calculateWeek = $( ".selector" ).datepicker( "option", "calculateWeek" );
//setter
$( ".selector" ).datepicker( "option", "calculateWeek", myWeekCalc );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-changeMonth">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-changeMonth">changeMonth</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Allows you to change the month by selecting from a drop-down list. You can enable this feature by setting the attribute to true.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>changeMonth</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ changeMonth: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>changeMonth</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var changeMonth = $( ".selector" ).datepicker( "option", "changeMonth" );
//setter
$( ".selector" ).datepicker( "option", "changeMonth", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-changeYear">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-changeYear">changeYear</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Allows you to change the year by selecting from a drop-down list. You can enable this feature by setting the attribute to true. Use the <code><a href="http://docs.jquery.com/UI/Datepicker#option-yearRange" title="UI/Datepicker">yearRange</a></code> option to control which years are made available for selection.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>changeYear</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ changeYear: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>changeYear</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var changeYear = $( ".selector" ).datepicker( "option", "changeYear" );
//setter
$( ".selector" ).datepicker( "option", "changeYear", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-closeText">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-closeText">closeText</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"Done"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display for the close link. This attribute is one of the regionalisation attributes. Use the <code><a href="http://docs.jquery.com/UI/Datepicker#option-showButtonPanel" title="UI/Datepicker">showButtonPanel</a></code> to display this button.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>closeText</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ closeText: "X" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>closeText</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var closeText = $( ".selector" ).datepicker( "option", "closeText" );
//setter
$( ".selector" ).datepicker( "option", "closeText", "X" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-constrainInput">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-constrainInput">constrainInput</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">true</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When true entry in the input field is constrained to those characters allowed by the current <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code>.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>constrainInput</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ constrainInput: false });</code></pre>
</dd>

    
<dt>
  Get or set the <code>constrainInput</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var constrainInput = $( ".selector" ).datepicker( "option", "constrainInput" );
//setter
$( ".selector" ).datepicker( "option", "constrainInput", false );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-currentText">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-currentText">currentText</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"Today"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display for the current day link. This attribute is one of the regionalisation attributes. Use the <code><a href="http://docs.jquery.com/UI/Datepicker#option-showButtonPanel" title="UI/Datepicker">showButtonPanel</a></code> to display this button.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>currentText</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ currentText: "Now" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>currentText</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var currentText = $( ".selector" ).datepicker( "option", "currentText" );
//setter
$( ".selector" ).datepicker( "option", "currentText", "Now" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-dateFormat">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-dateFormat">dateFormat</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"mm/dd/yy"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The format for parsed and displayed dates. This attribute is one of the regionalisation attributes. For a full list of the possible formats see the <code><a href="http://docs.jquery.com/UI/Datepicker/formatDate" title="UI/Datepicker/formatDate">formatDate</a></code> function.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>dateFormat</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ dateFormat: "yy-mm-dd" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>dateFormat</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var dateFormat = $( ".selector" ).datepicker( "option", "dateFormat" );
//setter
$( ".selector" ).datepicker( "option", "dateFormat", "yy-mm-dd" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-dayNames">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-dayNames">dayNames</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The list of long day names, starting from Sunday, for use as requested via the <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code> setting. They also appear as popup hints when hovering over the corresponding column headings. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>dayNames</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ dayNames: ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>dayNames</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var dayNames = $( ".selector" ).datepicker( "option", "dayNames" );
//setter
$( ".selector" ).datepicker( "option", "dayNames", ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-dayNamesMin">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-dayNamesMin">dayNamesMin</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The list of minimised day names, starting from Sunday, for use as column headers within the datepicker. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>dayNamesMin</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ dayNamesMin: ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>dayNamesMin</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var dayNamesMin = $( ".selector" ).datepicker( "option", "dayNamesMin" );
//setter
$( ".selector" ).datepicker( "option", "dayNamesMin", ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-dayNamesShort">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-dayNamesShort">dayNamesShort</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The list of abbreviated day names, starting from Sunday, for use as requested via the <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code> setting. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>dayNamesShort</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ dayNamesShort: ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>dayNamesShort</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var dayNamesShort = $( ".selector" ).datepicker( "option", "dayNamesShort" );
//setter
$( ".selector" ).datepicker( "option", "dayNamesShort", ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-defaultDate">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-defaultDate">defaultDate</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Date, Number, String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set the date to highlight on first opening if the field is blank. Specify either an actual date via a Date object or as a string in the current <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code>, or a number of days from today (e.g. +7) or a string of values and periods ('y' for years, 'm' for months, 'w' for weeks, 'd' for days, e.g. '+1m +7d'), or null for today.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>defaultDate</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ defaultDate: +7 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>defaultDate</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var defaultDate = $( ".selector" ).datepicker( "option", "defaultDate" );
//setter
$( ".selector" ).datepicker( "option", "defaultDate", +7 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-duration">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-duration">duration</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String, Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"normal"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Control the speed at which the datepicker appears, it may be a time in milliseconds or a string representing one of the three predefined speeds ("slow", "normal", "fast").</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>duration</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ duration: "slow" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>duration</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var duration = $( ".selector" ).datepicker( "option", "duration" );
//setter
$( ".selector" ).datepicker( "option", "duration", "slow" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-firstDay">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-firstDay">firstDay</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set the first day of the week: Sunday is 0, Monday is 1, ... This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>firstDay</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ firstDay: 1 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>firstDay</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var firstDay = $( ".selector" ).datepicker( "option", "firstDay" );
//setter
$( ".selector" ).datepicker( "option", "firstDay", 1 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-gotoCurrent">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-gotoCurrent">gotoCurrent</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When true the current day link moves to the currently selected date instead of today.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>gotoCurrent</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ gotoCurrent: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>gotoCurrent</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var gotoCurrent = $( ".selector" ).datepicker( "option", "gotoCurrent" );
//setter
$( ".selector" ).datepicker( "option", "gotoCurrent", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-hideIfNoPrevNext">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-hideIfNoPrevNext">hideIfNoPrevNext</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Normally the previous and next links are disabled when not applicable (see <code><a href="http://docs.jquery.com/UI/Datepicker#option-minDate" title="UI/Datepicker">minDate</a></code>/<code><a href="http://docs.jquery.com/UI/Datepicker#option-maxDate" title="UI/Datepicker">maxDate</a></code>). You can hide them altogether by setting this attribute to true.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>hideIfNoPrevNext</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ hideIfNoPrevNext: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>hideIfNoPrevNext</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var hideIfNoPrevNext = $( ".selector" ).datepicker( "option", "hideIfNoPrevNext" );
//setter
$( ".selector" ).datepicker( "option", "hideIfNoPrevNext", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-isRTL">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-isRTL">isRTL</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>True if the current language is drawn from right to left. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>isRTL</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ isRTL: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>isRTL</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var isRTL = $( ".selector" ).datepicker( "option", "isRTL" );
//setter
$( ".selector" ).datepicker( "option", "isRTL", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-maxDate">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-maxDate">maxDate</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Date, Number, String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set a maximum selectable date via a Date object or as a string in the current <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code>, or a number of days from today (e.g. +7) or a string of values and periods ('y' for years, 'm' for months, 'w' for weeks, 'd' for days, e.g. '+1m +1w'), or null for no limit.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>maxDate</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ maxDate: "+1m +1w" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>maxDate</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var maxDate = $( ".selector" ).datepicker( "option", "maxDate" );
//setter
$( ".selector" ).datepicker( "option", "maxDate", "+1m +1w" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-minDate">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-minDate">minDate</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Date, Number, String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set a minimum selectable date via a Date object or as a string in the current <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code>, or a number of days from today (e.g. +7) or a string of values and periods ('y' for years, 'm' for months, 'w' for weeks, 'd' for days, e.g. '-1y -1m'), or null for no limit.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>minDate</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ minDate: new Date(2007, 1 - 1, 1) });</code></pre>
</dd>

    
<dt>
  Get or set the <code>minDate</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var minDate = $( ".selector" ).datepicker( "option", "minDate" );
//setter
$( ".selector" ).datepicker( "option", "minDate", new Date(2007, 1 - 1, 1) );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-monthNames">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-monthNames">monthNames</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The list of full month names, for use as requested via the <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code> setting. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>monthNames</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ monthNames: ["Januar","Februar","Marts","April","Maj","Juni","Juli","August","September","Oktober","November","December"] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>monthNames</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var monthNames = $( ".selector" ).datepicker( "option", "monthNames" );
//setter
$( ".selector" ).datepicker( "option", "monthNames", ["Januar","Februar","Marts","April","Maj","Juni","Juli","August","September","Oktober","November","December"] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-monthNamesShort">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-monthNamesShort">monthNamesShort</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The list of abbreviated month names, as used in the month header on each datepicker and as requested via the <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code> setting. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>monthNamesShort</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ monthNamesShort: ["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Aug","Sep","Okt","Nov","Dec"] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>monthNamesShort</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var monthNamesShort = $( ".selector" ).datepicker( "option", "monthNamesShort" );
//setter
$( ".selector" ).datepicker( "option", "monthNamesShort", ["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Aug","Sep","Okt","Nov","Dec"] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-navigationAsDateFormat">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-navigationAsDateFormat">navigationAsDateFormat</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When true the <code><a href="http://docs.jquery.com/UI/Datepicker/formatDate" title="UI/Datepicker/formatDate">formatDate</a></code> function is applied to the <code><a href="http://docs.jquery.com/UI/Datepicker#option-prevText" title="UI/Datepicker">prevText</a></code>, <code><a href="http://docs.jquery.com/UI/Datepicker#option-nextText" title="UI/Datepicker">nextText</a></code>, and <code><a href="http://docs.jquery.com/UI/Datepicker#option-currentText" title="UI/Datepicker">currentText</a></code> values before display, allowing them to display the target month names for example.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>navigationAsDateFormat</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ navigationAsDateFormat: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>navigationAsDateFormat</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var navigationAsDateFormat = $( ".selector" ).datepicker( "option", "navigationAsDateFormat" );
//setter
$( ".selector" ).datepicker( "option", "navigationAsDateFormat", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-nextText">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-nextText">nextText</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"Next"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display for the next month link. This attribute is one of the regionalisation attributes. With the standard ThemeRoller styling, this value is replaced by an icon.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>nextText</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ nextText: "Later" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>nextText</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var nextText = $( ".selector" ).datepicker( "option", "nextText" );
//setter
$( ".selector" ).datepicker( "option", "nextText", "Later" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-numberOfMonths">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-numberOfMonths">numberOfMonths</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number, Array</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">1</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set how many months to show at once. The value can be a straight integer, or can be a two-element array to define the number of rows and columns to display.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>numberOfMonths</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ numberOfMonths: [2, 3] });</code></pre>
</dd>

    
<dt>
  Get or set the <code>numberOfMonths</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var numberOfMonths = $( ".selector" ).datepicker( "option", "numberOfMonths" );
//setter
$( ".selector" ).datepicker( "option", "numberOfMonths", [2, 3] );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-prevText">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-prevText">prevText</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"Prev"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display for the previous month link. This attribute is one of the regionalisation attributes. With the standard ThemeRoller styling, this value is replaced by an icon.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>prevText</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ prevText: "Earlier" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>prevText</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var prevText = $( ".selector" ).datepicker( "option", "prevText" );
//setter
$( ".selector" ).datepicker( "option", "prevText", "Earlier" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-selectOtherMonths">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-selectOtherMonths">selectOtherMonths</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When true days in other months shown before or after the current month are selectable. This only applies if <code><a href="http://docs.jquery.com/UI/Datepicker#option-showOtherMonths" title="UI/Datepicker">showOtherMonths</a></code> is also true.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>selectOtherMonths</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ selectOtherMonths: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>selectOtherMonths</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var selectOtherMonths = $( ".selector" ).datepicker( "option", "selectOtherMonths" );
//setter
$( ".selector" ).datepicker( "option", "selectOtherMonths", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-shortYearCutoff">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-shortYearCutoff">shortYearCutoff</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String, Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"+10"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set the cutoff year for determining the century for a date (used in conjunction with <code><a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">dateFormat</a></code> 'y'). If a numeric value (0-99) is provided then this value is used directly. If a string value is provided then it is converted to a number and added to the current year. Once the cutoff year is calculated, any dates entered with a year value less than or equal to it are considered to be in the current century, while those greater than it are deemed to be in the previous century.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>shortYearCutoff</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ shortYearCutoff: 50 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>shortYearCutoff</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var shortYearCutoff = $( ".selector" ).datepicker( "option", "shortYearCutoff" );
//setter
$( ".selector" ).datepicker( "option", "shortYearCutoff", 50 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showAnim">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showAnim">showAnim</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"show"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set the name of the animation used to show/hide the datepicker. Use 'show' (the default), 'slideDown', 'fadeIn', any of the show/hide <a href="http://docs.jquery.com/UI/Effects" class="external text" title="http://docs.jquery.com/UI/Effects">jQuery UI effects</a>, or &quot;&quot; for no animation.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showAnim</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showAnim: "fold" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showAnim</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showAnim = $( ".selector" ).datepicker( "option", "showAnim" );
//setter
$( ".selector" ).datepicker( "option", "showAnim", "fold" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showButtonPanel">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showButtonPanel">showButtonPanel</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Whether to show the button panel.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showButtonPanel</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showButtonPanel: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showButtonPanel</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showButtonPanel = $( ".selector" ).datepicker( "option", "showButtonPanel" );
//setter
$( ".selector" ).datepicker( "option", "showButtonPanel", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showCurrentAtPos">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showCurrentAtPos">showCurrentAtPos</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Specify where in a <a href="http://docs.jquery.com/UI/Datepicker#option-numberOfMonths" title="UI/Datepicker">multi-month</a> display the current month shows, starting from 0 at the top/left.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showCurrentAtPos</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showCurrentAtPos: 3 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showCurrentAtPos</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showCurrentAtPos = $( ".selector" ).datepicker( "option", "showCurrentAtPos" );
//setter
$( ".selector" ).datepicker( "option", "showCurrentAtPos", 3 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showMonthAfterYear">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showMonthAfterYear">showMonthAfterYear</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Whether to show the month after the year in the header. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showMonthAfterYear</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showMonthAfterYear: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showMonthAfterYear</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showMonthAfterYear = $( ".selector" ).datepicker( "option", "showMonthAfterYear" );
//setter
$( ".selector" ).datepicker( "option", "showMonthAfterYear", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showOn">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showOn">showOn</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"focus"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Have the datepicker appear automatically when the field receives focus ("focus"), appear only when a button is clicked ("button"), or appear when either event taks place ("both").</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showOn</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showOn: "both" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showOn</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showOn = $( ".selector" ).datepicker( "option", "showOn" );
//setter
$( ".selector" ).datepicker( "option", "showOn", "both" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showOptions">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showOptions">showOptions</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Options</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">{}</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>If using one of the jQuery UI effects for <code><a href="http://docs.jquery.com/UI/Datepicker#option-showAnim" title="UI/Datepicker">showAnim</a></code>, you can provide additional settings for that animation via this option.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showOptions</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showOptions: {direction: 'up'}  });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showOptions</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showOptions = $( ".selector" ).datepicker( "option", "showOptions" );
//setter
$( ".selector" ).datepicker( "option", "showOptions", {direction: 'up'}  );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showOtherMonths">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showOtherMonths">showOtherMonths</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Display dates in other months (non-selectable) at the start or end of the current month. To make these days selectable use <code><a href="http://docs.jquery.com/UI/Datepicker#option-selectOtherMonths" title="UI/Datepicker">selectOtherMonths</a></code>.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showOtherMonths</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showOtherMonths: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showOtherMonths</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showOtherMonths = $( ".selector" ).datepicker( "option", "showOtherMonths" );
//setter
$( ".selector" ).datepicker( "option", "showOtherMonths", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-showWeek">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-showWeek">showWeek</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When true a column is added to show the week of the year. The <code><a href="http://docs.jquery.com/UI/Datepicker#option-calculateWeek" title="UI/Datepicker">calculateWeek</a></code> option determines how the week of the year is calculated. You may also want to change the <code><a href="http://docs.jquery.com/UI/Datepicker#option-firstDay" title="UI/Datepicker">firstDay</a></code> option.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>showWeek</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ showWeek: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>showWeek</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var showWeek = $( ".selector" ).datepicker( "option", "showWeek" );
//setter
$( ".selector" ).datepicker( "option", "showWeek", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-stepMonths">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-stepMonths">stepMonths</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">1</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Set how many months to move when clicking the Previous/Next links.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>stepMonths</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ stepMonths: 3 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>stepMonths</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var stepMonths = $( ".selector" ).datepicker( "option", "stepMonths" );
//setter
$( ".selector" ).datepicker( "option", "stepMonths", 3 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-weekHeader">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-weekHeader">weekHeader</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"Wk"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The text to display for the week of the year column heading. This attribute is one of the regionalisation attributes. Use <code><a href="http://docs.jquery.com/UI/Datepicker#option-showWeek" title="UI/Datepicker">showWeek</a></code> to display this column.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>weekHeader</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ weekHeader: "W" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>weekHeader</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var weekHeader = $( ".selector" ).datepicker( "option", "weekHeader" );
//setter
$( ".selector" ).datepicker( "option", "weekHeader", "W" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-yearRange">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-yearRange">yearRange</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"c-10:c+10"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Control the range of years displayed in the year drop-down: either relative to today's year (-nn:+nn), relative to the currently selected year (c-nn:c+nn), absolute (nnnn:nnnn), or combinations of these formats (nnnn:-nn). Note that this option only affects what appears in the drop-down, to restrict which dates may be selected use the <code><a href="http://docs.jquery.com/UI/Datepicker#option-minDate" title="UI/Datepicker">minDate</a></code> and/or <code><a href="http://docs.jquery.com/UI/Datepicker#option-maxDate" title="UI/Datepicker">maxDate</a></code> options.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>yearRange</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ yearRange: "2000:2010" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>yearRange</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var yearRange = $( ".selector" ).datepicker( "option", "yearRange" );
//setter
$( ".selector" ).datepicker( "option", "yearRange", "2000:2010" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-yearSuffix">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-yearSuffix">yearSuffix</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">&quot;&quot;</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Additional text to display after the year in the month headers. This attribute is one of the regionalisation attributes.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a datepicker with the <code>yearSuffix</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).datepicker({ yearSuffix: "CE" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>yearSuffix</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var yearSuffix = $( ".selector" ).datepicker( "option", "yearSuffix" );
//setter
</p>
$( ".selector" ).datepicker( "option", "yearSuffix", "CE" );</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="events">
    <h2 class="top-header">Events</h2>
    <ul class="events-list">
      <p><b>Known issue: Datepicker does not trigger a create event.</b></p>
<li class="event" id="event-beforeShow">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-beforeShow">beforeShow</a></h3>
    <dl>
      <dt class="event-type-label"></dt>
        <dd class="event-type">function(input, inst)</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>Can be a function that takes an input field and current datepicker instance and returns an options object to update the datepicker with. It is called just before the datepicker is displayed.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>beforeShow</code> event as an init option.
</dt>
<dd>
<pre><code>$('.selector').datepicker({
   beforeShow: function(input, inst) { ... }
});</code></pre>
</dd>

    </dl>
  </div>
</li>

<p>
<li class="event" id="event-beforeShowDay">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-beforeShowDay">beforeShowDay</a></h3>
    <dl>
      <dt class="event-type-label"></dt>
        <dd class="event-type">function(date)</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>The function takes a date as a parameter and must return an array with [0] equal to true/false indicating whether or not this date is selectable, [1] equal to a CSS class name(s) or &quot;&quot; for the default presentation, and [2] an optional popup tooltip for this date. It is called for each day in the datepicker before it is displayed.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>beforeShowDay</code> event as an init option.
</dt>
<dd>
<pre><code>$('.selector').datepicker({
   beforeShowDay: function(date) { ... }
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-onChangeMonthYear">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-onChangeMonthYear">onChangeMonthYear</a></h3>
    <dl>
      <dt class="event-type-label"></dt>
        <dd class="event-type">function(year, month, inst)</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>Allows you to define your own event when the datepicker moves to a new month and/or year. The function receives the selected year, month (1-12), and the datepicker instance as parameters. <code>this</code> refers to the associated input field.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>onChangeMonthYear</code> event as an init option.
</dt>
<dd>
<pre><code>$('.selector').datepicker({
   onChangeMonthYear: function(year, month, inst) { ... }
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-onClose">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-onClose">onClose</a></h3>
    <dl>
      <dt class="event-type-label"></dt>
        <dd class="event-type">function(dateText, inst)</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>Allows you to define your own event when the datepicker is closed, whether or not a date is selected. The function receives the selected date as text (&quot;&quot; if none) and the datepicker instance as parameters. <code>this</code> refers to the associated input field.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>onClose</code> event as an init option.
</dt>
<dd>
<pre><code>$('.selector').datepicker({
   onClose: function(dateText, inst) { ... }
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-onSelect">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-onSelect">onSelect</a></h3>
    <dl>
      <dt class="event-type-label"></dt>
        <dd class="event-type">function(dateText, inst)</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>Allows you to define your own event when the datepicker is selected. The function receives the selected date as text and the datepicker instance as parameters. <code>this</code> refers to the associated input field.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>onSelect</code> event as an init option.
</dt>
<dd>
<pre><code>$('.selector').datepicker({
   onSelect: function(dateText, inst) { ... }
});</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="methods">
    <h2 class="top-header">Methods</h2>
    <ul class="methods-list">
      
<li class="method" id="method-destroy">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-destroy">destroy</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "destroy"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Remove the datepicker functionality completely. This will return the element back to its pre-init state.</p>
  </div>
</li>


<li class="method" id="method-disable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-disable">disable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "disable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Disable the datepicker.</p>
  </div>
</li>


<li class="method" id="method-enable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-enable">enable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "enable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Enable the datepicker.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "option"

, optionName

, <span class="optional">[</span>value<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Get or set any datepicker option. If no value is specified, will act as a getter.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "option"

, options





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Set multiple datepicker options at once by providing an options object.</p>
  </div>
</li>


<li class="method" id="method-widget">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-widget">widget</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "widget"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Returns the .ui-datepicker element.</p>
  </div>
</li>


<li class="method" id="method-dialog">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-dialog">dialog</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "dialog"

, date

, <span class="optional">[</span>onSelect<span class="optional">] </span>

, <span class="optional">[</span>settings<span class="optional">] </span>

, <span class="optional">[</span>pos<span class="optional">] </span>)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Open a datepicker in a "dialog" box.
</p><p>dateText: the initial date for the date picker as either a Date or a string in the current date format.
</p><p>onSelect: A callback function when a date is selected. The function receives the date text and date picker instance as parameters.
</p><p>settings: The new settings for the date picker.
</p><p>pos: The position of the top/left of the dialog as [x, y] or a MouseEvent that contains the coordinates. If not specified the dialog is centered on the screen.</p>
  </div>
</li>


<li class="method" id="method-isDisabled">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-isDisabled">isDisabled</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "isDisabled"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Determine whether a date picker has been disabled.</p>
  </div>
</li>


<li class="method" id="method-hide">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-hide">hide</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "hide"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Close a previously opened date picker.</p>
  </div>
</li>


<li class="method" id="method-show">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-show">show</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "show"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Call up a previously attached date picker. If the datepicker is attached to an input, the input must be visible for the datepicker to be shown.</p>
  </div>
</li>


<li class="method" id="method-refresh">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-refresh">refresh</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "refresh"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Redraw a date picker, after having made some external modifications.</p>
  </div>
</li>


<li class="method" id="method-getDate">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-getDate">getDate</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "getDate"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Returns the current date for the datepicker or null if no date has been selected.</p>
  </div>
</li>


<li class="method" id="method-setDate">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-setDate">setDate</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.datepicker( "setDate"

, date





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Sets the current date for the datepicker. The new date may be a Date object or a string in the current <a href="http://docs.jquery.com/UI/Datepicker#option-dateFormat" title="UI/Datepicker">date format</a> (e.g. '01/26/2009'), a number of days from today (e.g. +7) or a string of values and periods ('y' for years, 'm' for months, 'w' for weeks, 'd' for days, e.g. '+1m +7d'), or null to clear the selected date.</p>
  </div>
</li>

    </ul>
  </div>
  <div id="theming">
    <h2 class="top-header">Theming</h2>
    <p>The jQuery UI Datepicker plugin uses the jQuery UI CSS Framework to style its look and feel, including colors and background textures. We recommend using the ThemeRoller tool to create and download custom themes that are easy to build and maintain.
</p>
  <p>If a deeper level of customization is needed, there are widget-specific classes referenced within the jquery.ui.datepicker.css stylesheet that can be modified. These classes are highlighed in bold below.
</p>
    
  <h3>Sample markup with jQuery UI CSS Framework classes</h3>
  &lt;div id=&quot;ui-datepicker-div&quot; class=&quot;<strong>ui-datepicker</strong> ui-widget ui-widget-content ui-helper-clearfix ui-corner-all ui-helper-hidden-accessible&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;div class=&quot;<strong>ui-datepicker-header</strong> ui-widget-header ui-helper-clearfix ui-corner-all&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;a class=&quot;<strong>ui-datepicker-prev</strong> ui-corner-all&quot; title=&quot;Prev&quot;&gt;&lt;span class=&quot;ui-icon ui-icon-circle-triangle-w&quot;&gt;Prev&lt;/span&gt;&lt;/a&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;a class=&quot;<strong>ui-datepicker-next</strong> ui-corner-all&quot; title=&quot;Next&quot;&gt;&lt;span class=&quot;ui-icon ui-icon-circle-triangle-e&quot;&gt;Next&lt;/span&gt;&lt;/a&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class=&quot;<strong>ui-datepicker-title</strong>&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;span class=&quot;<strong>ui-datepicker-month</strong>&quot;&gt;January&lt;/span&gt;&lt;span class=&quot;<strong>ui-datepicker-year</strong>&quot;&gt;2009&lt;/span&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;table class=&quot;<strong>ui-datepicker-calendar</strong>&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;thead&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;tr&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;th class=&quot;<strong>ui-datepicker-week-end</strong>&quot;&gt;&lt;span title=&quot;Sunday&quot;&gt;Su&lt;/span&gt;&lt;/th&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;...<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/tr&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/thead&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;tbody&gt;&lt;tr&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;td class=&quot;<strong>ui-datepicker-week-end ui-datepicker-other-month</strong> &quot;&gt;&nbsp;1&nbsp;&lt;/td&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;...<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/tr&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/tbody&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;/table&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;div class=&quot;<strong>ui-datepicker-buttonpane</strong> ui-widget-content&quot;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;button type=&quot;button&quot; class=&quot;<strong>ui-datepicker-current</strong> ui-state-default ui-priority-secondary ui-corner-all&quot;&gt;Today&lt;/button&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;button type=&quot;button&quot; class=&quot;<strong>ui-datepicker-close</strong> ui-state-default ui-priority-primary ui-corner-all&quot;&gt;Done&lt;/button&gt;<br />
&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br />
&lt;/div&gt;<br />
  <p class="theme-note">
    <strong>
      Note: This is a sample of markup generated by the datepicker plugin, not markup you should use to create a datepicker. The only markup needed for that is &lt;input type="text" /&gt; or &lt;div&gt;&lt;/div&gt;.
    </strong>
  </p>

  </div>
</div>

</p><!-- 
Pre-expand include size: 97232 bytes
Post-expand include size: 193557 bytes
Template argument size: 121057 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3774-1!1!0!!en!2 and timestamp 20120724110022 -->
