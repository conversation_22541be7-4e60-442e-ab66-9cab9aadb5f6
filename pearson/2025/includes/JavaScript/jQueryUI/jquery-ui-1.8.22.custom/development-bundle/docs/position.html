
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Arguments</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI Position Utility</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <p>Utility script for positioning any widget relative to the window, document, a particular element, or the cursor/mouse.</p>
<p><em>Note: jQuery UI does not support positioning hidden elements.</em></p>
<p>Does not need ui.core.js or effects.core.js.</p>
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li><i>none (only jQuery core)</i></li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="300">
Clicking on the green element transfers to the other.<br />
</p>
<pre>
$(&quot;#position1&quot;).position({
  my: &quot;center&quot;,
  at: &quot;center&quot;,
  of: &quot;#targetElement&quot;
});
$(&quot;#position2&quot;).position({
  my: &quot;left top&quot;,
  at: &quot;left top&quot;,
  of: &quot;#targetElement&quot;
});
$(&quot;#position3&quot;).position({
  my: &quot;right center&quot;,
  at: &quot;right bottom&quot;,
  of: &quot;#targetElement&quot;
});
$(document).mousemove(function(ev){
  $(&quot;#position4&quot;).position({
    my: &quot;left bottom&quot;,
    of: ev,
    offset: &quot;3 -3&quot;,
    collision: &quot;fit&quot;
  });
});

</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
  &lt;style type=&quot;text/css&quot;&gt;
#targetElement { width:240px;height:200px;background-color:#999;margin:30px auto; }
.positionDiv { width:50px;height:50px;opacity:0.6; }
#position1 {background-color:#F00;}
#position2 {background-color:#0F0;}
#position3 {background-color:#00F;}
#position4 {background-color:#FF0;}
&lt;/style&gt;

  &lt;script&gt;
  $(document).ready(function() {
    
$(&quot;#position1&quot;).position({
  my: &quot;center&quot;,
  at: &quot;center&quot;,
  of: &quot;#targetElement&quot;
});
$(&quot;#position2&quot;).position({
  my: &quot;left top&quot;,
  at: &quot;left top&quot;,
  of: &quot;#targetElement&quot;
});
$(&quot;#position3&quot;).position({
  my: &quot;right center&quot;,
  at: &quot;right bottom&quot;,
  of: &quot;#targetElement&quot;
});
$(document).mousemove(function(ev){
  $(&quot;#position4&quot;).position({
    my: &quot;left bottom&quot;,
    of: ev,
    offset: &quot;3 -3&quot;,
    collision: &quot;fit&quot;
  });
});

  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;div id=&quot;targetElement&quot;&gt;
  &lt;div class=&quot;positionDiv&quot; id=&quot;position1&quot;&gt;&lt;/div&gt;
  &lt;div class=&quot;positionDiv&quot; id=&quot;position2&quot;&gt;&lt;/div&gt;
  &lt;div class=&quot;positionDiv&quot; id=&quot;position3&quot;&gt;&lt;/div&gt;
  &lt;div class=&quot;positionDiv&quot; id=&quot;position4&quot;&gt;&lt;/div&gt;
&lt;/div&gt;

&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Arguments</h2>
    <ul class="options-list">
      
<li class="option" id="option-my">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-my">my</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"center"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Defines which position on <b>the element being positioned</b> to align with the target element: "horizontal vertical" alignment. A single value such as "right" will default to "right center", "top" will default to "center top" (following CSS convention). Acceptable values: "top", "center", "bottom", "left", "right". Example: "left top" or "center center"</p>
  </div>
</li>


<li class="option" id="option-at">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-at">at</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"center"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Defines which position on <b>the target element</b> to align the positioned element against: "horizontal vertical" alignment. A single value such as "right" will default to "right center", "top" will default to "center top" (following CSS convention). Acceptable values: "top", "center", "bottom", "left", "right". Example: "left top" or "center center"</p>
  </div>
</li>


<li class="option" id="option-of">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-of">of</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Selector, Element, jQuery, Event</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Element to position against. If you provide a selector, the first matching element will be used. If you provide a jQuery object, the first element will be used. If you provide an event object, the pageX and pageY properties will be used. Example: "#top-menu"</p>
  </div>
</li>


<li class="option" id="option-offset">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-offset">offset</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Add these left-top values to the calculated position, eg. "50 50" (left top) A single value such as "50" will apply to both.</p>
  </div>
</li>


<li class="option" id="option-collision">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-collision">collision</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"flip"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When the positioned element overflows the window in some direction, move it to an alternative position. Similar to my and at, this accepts a single value or a pair for horizontal/vertical, eg. "flip", "fit", "fit flip", "fit none".
</p>
<ul><li> <b>flip</b>: to the opposite side and the collision detection is run again to see if it will fit. Whichever side allows more of the element to be visible will be used.
</li><li> <b>fit</b>: so the element keeps in the desired direction, but is re-positioned so it fits.
</li><li> <b>none</b>: not do collision detection.
</li></ul>
<p></p>
  </div>
</li>


<li class="option" id="option-using">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-using">using</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Function</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">null</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>When specified the actual property setting is delegated to this callback. Receives a single parameter which is a hash of top and left values for the position that should be set.</p>
  </div>
</li>

    </ul>
  </div>
</div>

</p><!-- 
Pre-expand include size: 7638 bytes
Post-expand include size: 12524 bytes
Template argument size: 8194 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3784-1!1!0!!en!2 and timestamp 20120724101048 -->
