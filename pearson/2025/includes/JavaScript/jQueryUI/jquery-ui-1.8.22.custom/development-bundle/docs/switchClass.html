
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Arguments</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI switchClass</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <div class="editsection" style="float:right;margin-left:5px;">[<a href="http://docs.jquery.com/action/edit/UI/Effects/switchClass?section=1" title="Edit section: switchClass( remove, add, [duration] )">edit</a>]</div><a name="switchClass.28_remove.2C_add.2C_.5Bduration.5D_.29"></a><h3>switchClass( remove, add, <span class="optional">[</span>duration<span class="optional">]</span> )</h3>
<p>Switches from the class defined in the first argument to the class defined as second argument, using an optional transition.</p>
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li>Effects Core</li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="170">
Switch the class 'highlight' to 'blue' when a paragraph is clicked with a one second transition.<br />
</p>
<pre>$(&quot;p&quot;).<a href="http://docs.jquery.com/Events/click" title="Events/click">click</a>(function () {
      $(this).<strong class="selflink">switchClass</strong>(&quot;highlight&quot;, &quot;blue&quot;, 1000);
    });
</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ui.jquery.com/latest/ui/effects.core.js&quot;&gt;&lt;/script&gt;
&lt;style type=&quot;text/css&quot;&gt;
  p { margin: 4px; font-size:16px; font-weight:bolder; 
      cursor:pointer; }
  .blue { background: blue; }
  .highlight { background:yellow; }
&lt;/style&gt;
  &lt;script&gt;
  $(document).ready(function() {
    $(&quot;p&quot;).<a href="http://docs.jquery.com/Events/click" title="Events/click">click</a>(function () {
      $(this).<strong class="selflink">switchClass</strong>(&quot;highlight&quot;, &quot;blue&quot;, 1000);
    });
  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;p class=&quot;highlight&quot;&gt;Click to switch&lt;/p&gt;
&lt;p class=&quot;highlight&quot;&gt;to blue&lt;/p&gt;
&lt;p class=&quot;highlight&quot;&gt;on these&lt;/p&gt;
&lt;p class=&quot;highlight&quot;&gt;paragraphs&lt;/p&gt;

&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Arguments</h2>
    <ul class="options-list">
      
<li class="option" id="option-remove">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-remove">remove</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The CSS class that will be removed.</p>
  </div>
</li>


<li class="option" id="option-add">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-add">add</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The CSS class that will be added.</p>
  </div>
</li>


<li class="option" id="option-duration">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-duration">duration</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String, Number</dd>
      
      <dt class="option-optional-label">Optional</dt>
      
    </dl>
  </div>
  <div class="option-description">
    <p>A string representing one of the three predefined speeds ("slow", "normal", or "fast") or the number of milliseconds to run the animation (e.g. 1000).</p>
  </div>
</li>

    </ul>
  </div>
</div>

</p><!-- 
Pre-expand include size: 5818 bytes
Post-expand include size: 7682 bytes
Template argument size: 4812 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:2609-1!1!0!!en!2 and timestamp 20120724021125 -->
