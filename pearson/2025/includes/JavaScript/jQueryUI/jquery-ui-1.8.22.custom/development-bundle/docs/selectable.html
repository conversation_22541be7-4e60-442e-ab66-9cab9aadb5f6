
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Options</a></li>
<li><a href="#events">Events</a></li>
<li><a href="#methods">Methods</a></li>
<li><a href="#theming">Theming</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI Selectable</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <p>The jQuery UI Selectable plugin allows for elements to be selected by dragging a box (sometimes called a lasso) with the mouse over the elements. Also, elements can be selected by click or drag while holding the Ctrl/Meta key, allowing for multiple (non-contiguous) selections.</p>
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li>UI Core</li>
<li>UI Widget</li>
<li>UI Mouse</li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="">
A simple jQuery UI Selectable.<br />
</p>
<pre>$(&quot;#selectable&quot;).selectable();
</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
  &lt;style type=&quot;text/css&quot;&gt;
#selectable .ui-selecting {
	background: silver;
}
#selectable .ui-selected {
	background: gray;
}
&lt;/style&gt;

  &lt;script&gt;
  $(document).ready(function() {
    $(&quot;#selectable&quot;).selectable();
  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;ul id=&quot;selectable&quot;&gt;
&lt;li&gt;Item 1&lt;/li&gt;
&lt;li&gt;Item 2&lt;/li&gt;
&lt;li&gt;Item 3&lt;/li&gt;
&lt;li&gt;Item 4&lt;/li&gt;
&lt;li&gt;Item 5&lt;/li&gt;
&lt;/ul&gt;

&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Options</h2>
    <ul class="options-list">
      
<li class="option" id="option-disabled">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-disabled">disabled</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Disables (true) or enables (false) the selectable. Can be set when initialising (first creating) the selectable.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>disabled</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ disabled: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>disabled</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var disabled = $( ".selector" ).selectable( "option", "disabled" );
//setter
$( ".selector" ).selectable( "option", "disabled", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-autoRefresh">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-autoRefresh">autoRefresh</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">true</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>This determines whether to refresh (recalculate) the position and size of each selectee at the beginning of each select operation. If you have many many items, you may want to set this to false and call the refresh method manually.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>autoRefresh</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ autoRefresh: false });</code></pre>
</dd>

    
<dt>
  Get or set the <code>autoRefresh</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var autoRefresh = $( ".selector" ).selectable( "option", "autoRefresh" );
//setter
$( ".selector" ).selectable( "option", "autoRefresh", false );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-cancel">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-cancel">cancel</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Selector</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">":input,option"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Prevents selecting if you start on elements matching the selector.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>cancel</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ cancel: ":input,option" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>cancel</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var cancel = $( ".selector" ).selectable( "option", "cancel" );
//setter
$( ".selector" ).selectable( "option", "cancel", ":input,option" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-delay">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-delay">delay</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Integer</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Time in milliseconds to define when the selecting should start. It helps preventing unwanted selections when clicking on an element.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>delay</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ delay: 20 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>delay</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var delay = $( ".selector" ).selectable( "option", "delay" );
//setter
$( ".selector" ).selectable( "option", "delay", 20 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-distance">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-distance">distance</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Integer</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Tolerance, in pixels, for when selecting should start. If specified, selecting will not start until after mouse is dragged beyond distance.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>distance</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ distance: 20 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>distance</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var distance = $( ".selector" ).selectable( "option", "distance" );
//setter
$( ".selector" ).selectable( "option", "distance", 20 );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-filter">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-filter">filter</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Selector</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"*"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The matching child elements will be made selectees (able to be selected).</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>filter</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ filter: "li" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>filter</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var filter = $( ".selector" ).selectable( "option", "filter" );
//setter
$( ".selector" ).selectable( "option", "filter", "li" );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-tolerance">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-tolerance">tolerance</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">String</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">"touch"</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Possible values: 'touch', 'fit'.
</p>
<ul>
<li><b>fit</b>: draggable overlaps the droppable entirely</li>
<li><b>touch</b>: draggable overlaps the droppable any amount</li>
</ul>
<p></p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a selectable with the <code>tolerance</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).selectable({ tolerance: "fit" });</code></pre>
</dd>

    
<dt>
  Get or set the <code>tolerance</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var tolerance = $( ".selector" ).selectable( "option", "tolerance" );
//setter
$( ".selector" ).selectable( "option", "tolerance", "fit" );</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="events">
    <h2 class="top-header">Events</h2>
    <ul class="events-list">
      
<li class="event" id="event-create">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-create">create</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectablecreate</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when selectable is created.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>create</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   create: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>create</code> event by type: <code>selectablecreate</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectablecreate&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-selected">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-selected">selected</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectableselected</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered at the end of the select operation, on each element added to the selection.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>selected</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   selected: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>selected</code> event by type: <code>selectableselected</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectableselected&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-selecting">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-selecting">selecting</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectableselecting</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered during the select operation, on each element added to the selection.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>selecting</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   selecting: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>selecting</code> event by type: <code>selectableselecting</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectableselecting&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-start">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-start">start</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectablestart</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered at the beginning of the select operation.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>start</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   start: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>start</code> event by type: <code>selectablestart</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectablestart&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-stop">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-stop">stop</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectablestop</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered at the end of the select operation.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>stop</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   stop: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>stop</code> event by type: <code>selectablestop</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectablestop&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-unselected">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-unselected">unselected</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectableunselected</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered at the end of the select operation, on each element removed from the selection.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>unselected</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   unselected: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>unselected</code> event by type: <code>selectableunselected</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectableunselected&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-unselecting">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-unselecting">unselecting</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">selectableunselecting</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered during the select operation, on each element removed from the selection.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>unselecting</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).selectable({
   unselecting: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>unselecting</code> event by type: <code>selectableunselecting</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;selectableunselecting&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="methods">
    <h2 class="top-header">Methods</h2>
    <ul class="methods-list">
      
<li class="method" id="method-destroy">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-destroy">destroy</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "destroy"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Remove the selectable functionality completely. This will return the element back to its pre-init state.</p>
  </div>
</li>


<li class="method" id="method-disable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-disable">disable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "disable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Disable the selectable.</p>
  </div>
</li>


<li class="method" id="method-enable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-enable">enable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "enable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Enable the selectable.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "option"

, optionName

, <span class="optional">[</span>value<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Get or set any selectable option. If no value is specified, will act as a getter.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "option"

, options





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Set multiple selectable options at once by providing an options object.</p>
  </div>
</li>


<li class="method" id="method-widget">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-widget">widget</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "widget"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Returns the .ui-selectable element.</p>
  </div>
</li>


<li class="method" id="method-refresh">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-refresh">refresh</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.selectable( "refresh"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Refresh the position and size of each selectee element. This method can be used to manually recalculate the position and size of each selectee element. Very useful if autoRefresh is set to false.</p>
  </div>
</li>

    </ul>
  </div>
  <div id="theming">
    <h2 class="top-header">Theming</h2>
    <p>The jQuery UI Selectable plugin uses the jQuery UI CSS Framework to style its look and feel, including colors and background textures. We recommend using the ThemeRoller tool to create and download custom themes that are easy to build and maintain.
</p>
  <p>If a deeper level of customization is needed, there are widget-specific classes referenced within the jquery.ui.selectable.css stylesheet that can be modified. These classes are highlighed in bold below.
</p>
    
  <h3>Sample markup with jQuery UI CSS Framework classes</h3>
  &lt;ul class=&quot;<strong>ui-selectable</strong>&quot;&gt;<br />
&#160;&#160;&#160;&lt;li class=&quot;<strong>ui-selectee</strong>&quot;&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&lt;li class=&quot;<strong>ui-selectee</strong>&quot;&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&lt;li class=&quot;<strong>ui-selectee</strong>&quot;&gt;&lt;/li&gt;<br />
&lt;/ul&gt;
  <p class="theme-note">
    <strong>
      Note: This is a sample of markup generated by the selectable plugin, not markup you should use to create a selectable. The only markup needed for that is <br />&lt;ul&gt;<br />
&#160;&#160;&#160;&lt;li&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&lt;li&gt;&lt;/li&gt;<br />
&#160;&#160;&#160;&lt;li&gt;&lt;/li&gt;<br />
&lt;/ul&gt;.
    </strong>
  </p>

  </div>
</div>

</p><!-- 
Pre-expand include size: 34703 bytes
Post-expand include size: 54220 bytes
Template argument size: 27222 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3771-1!1!0!!en!2 and timestamp 20120724122437 -->
