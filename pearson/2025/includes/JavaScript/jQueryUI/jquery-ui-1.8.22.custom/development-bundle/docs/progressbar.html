
<ul class="UIAPIPlugin-toc">
<li><a href="#overview">Overview</a></li>
<li><a href="#options">Options</a></li>
<li><a href="#events">Events</a></li>
<li><a href="#methods">Methods</a></li>
<li><a href="#theming">Theming</a></li>
</ul>
<div class="UIAPIPlugin">
  <h1>jQuery UI Progressbar</h1>
  <div id="overview">
    <h2 class="top-header">Overview</h2>
    <div id="overview-main">
        <p>
The progress bar is designed to simply display the current % complete for a process. The bar is coded to be flexibly sized through CSS and will scale to fit inside it's parent container by default.
</p>
<p>
This is a determinate progress bar, meaning that it should only be used in situations where the system can accurately update the current status complete. A determinate progress bar should never fill from left to right, then loop back to empty for a single process -- if the actual percent complete status cannot be calculated, an indeterminate progress bar (coming soon) or spinner animation is a better way to provide user feedback.
</p>
    </div>
    <div id="overview-dependencies">
        <h3>Dependencies</h3>
        <ul>
<li>UI Core</li>
<li>UI Widget</li>
</ul>
    </div>
    <div id="overview-example">
        <h3>Example</h3>
        <div id="overview-example" class="example">
<ul><li><a href="#demo"><span>Demo</span></a></li><li><a href="#source"><span>View Source</span></a></li></ul>
<p><div id="demo" class="tabs-container" rel="100">
A simple jQuery UI Progressbar.<br />
</p>
<pre>$(&quot;#progressbar&quot;).progressbar({ value: 37 });
</pre>
<p></div><div id="source" class="tabs-container">
</p>
<pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;link href=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css&quot; rel=&quot;stylesheet&quot; type=&quot;text/css&quot;/&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/jquery-ui.min.js&quot;&gt;&lt;/script&gt;
  
  &lt;script&gt;
  $(document).ready(function() {
    $(&quot;#progressbar&quot;).progressbar({ value: 37 });
  });
  &lt;/script&gt;
&lt;/head&gt;
&lt;body style="font-size:62.5%;"&gt;
  
&lt;div id=&quot;progressbar&quot;&gt;&lt;/div&gt;

&lt;/body&gt;
&lt;/html&gt;
</pre>
<p></div>
</p><p></div>
    </div>
  </div>
  <div id="options">
    <h2 class="top-header">Options</h2>
    <ul class="options-list">
      
<li class="option" id="option-disabled">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-disabled">disabled</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Boolean</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">false</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>Disables (true) or enables (false) the progressbar. Can be set when initialising (first creating) the progressbar.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a progressbar with the <code>disabled</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).progressbar({ disabled: true });</code></pre>
</dd>

    
<dt>
  Get or set the <code>disabled</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var disabled = $( ".selector" ).progressbar( "option", "disabled" );
//setter
$( ".selector" ).progressbar( "option", "disabled", true );</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="option" id="option-value">
  <div class="option-header">
    <h3 class="option-name"><a href="#option-value">value</a></h3>
    <dl>
      <dt class="option-type-label">Type:</dt>
        <dd class="option-type">Number</dd>
      
      <dt class="option-default-label">Default:</dt>
        <dd class="option-default">0</dd>
      
    </dl>
  </div>
  <div class="option-description">
    <p>The value of the progressbar.</p>
  </div>
  <div class="option-examples">
    <h4>Code examples</h4>
    <dl class="option-examples-list">
    
<dt>
  Initialize a progressbar with the <code>value</code> option specified.
</dt>
<dd>
<pre><code>$( ".selector" ).progressbar({ value: 37 });</code></pre>
</dd>

    
<dt>
  Get or set the <code>value</code> option, after init.
</dt>
<dd>
<pre><code>//getter
var value = $( ".selector" ).progressbar( "option", "value" );
//setter
$( ".selector" ).progressbar( "option", "value", 37 );</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="events">
    <h2 class="top-header">Events</h2>
    <ul class="events-list">
      
<li class="event" id="event-create">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-create">create</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">progressbarcreate</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when progressbar is created.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>create</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).progressbar({
   create: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>create</code> event by type: <code>progressbarcreate</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;progressbarcreate&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-change">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-change">change</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">progressbarchange</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when the value of the progressbar changes.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>change</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).progressbar({
   change: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>change</code> event by type: <code>progressbarchange</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;progressbarchange&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>


<li class="event" id="event-complete">
  <div class="event-header">
    <h3 class="event-name"><a href="#event-complete">complete</a></h3>
    <dl>
      <dt class="event-type-label">Type:</dt>
        <dd class="event-type">progressbarcomplete</dd>
    </dl>
  </div>
  <div class="event-description">
    <p>This event is triggered when the value of the progressbar reaches the maximum value of 100.</p>
  </div>
  <div class="event-examples">
    <h4>Code examples</h4>
    <dl class="event-examples-list">
    
<dt>
  Supply a callback function to handle the <code>complete</code> event as an init option.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).progressbar({
   complete: function(event, ui) { ... }
});</code></pre>
</dd>

    
<dt>
  Bind to the <code>complete</code> event by type: <code>progressbarcomplete</code>.
</dt>
<dd>
<pre><code>$( &quot;.selector&quot; ).bind( &quot;progressbarcomplete&quot;, function(event, ui) {
  ...
});</code></pre>
</dd>

    </dl>
  </div>
</li>

    </ul>
  </div>
  <div id="methods">
    <h2 class="top-header">Methods</h2>
    <ul class="methods-list">
      
<li class="method" id="method-destroy">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-destroy">destroy</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "destroy"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Remove the progressbar functionality completely. This will return the element back to its pre-init state.</p>
  </div>
</li>


<li class="method" id="method-disable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-disable">disable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "disable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Disable the progressbar.</p>
  </div>
</li>


<li class="method" id="method-enable">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-enable">enable</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "enable"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Enable the progressbar.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "option"

, optionName

, <span class="optional">[</span>value<span class="optional">] </span>



)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Get or set any progressbar option. If no value is specified, will act as a getter.</p>
  </div>
</li>


<li class="method" id="method-option">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-option">option</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "option"

, options





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Set multiple progressbar options at once by providing an options object.</p>
  </div>
</li>


<li class="method" id="method-widget">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-widget">widget</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "widget"







)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>Returns the .ui-progressbar element.</p>
  </div>
</li>


<li class="method" id="method-value">
  <div class="method-header">
    <h3 class="method-name"><a href="#method-value">value</a></h3>
    <dl>
      <dt class="method-signature-label">Signature:</dt>
        <dd class="method-signature">.progressbar( "value"

, <span class="optional">[</span>value<span class="optional">] </span>





)</dd>
    </dl>
  </div>
  <div class="method-description">
    <p>This method gets or sets the current value of the progressbar.</p>
  </div>
</li>

    </ul>
  </div>
  <div id="theming">
    <h2 class="top-header">Theming</h2>
    <p>The jQuery UI Progressbar plugin uses the jQuery UI CSS Framework to style its look and feel, including colors and background textures. We recommend using the ThemeRoller tool to create and download custom themes that are easy to build and maintain.
</p>
  <p>If a deeper level of customization is needed, there are widget-specific classes referenced within the jquery.ui.progressbar.css stylesheet that can be modified. These classes are highlighed in bold below.
</p>
    
  <h3>Sample markup with jQuery UI CSS Framework classes</h3>
  &lt;div class=&quot;<strong>ui-progressbar </strong>ui-widget ui-widget-content ui-corner-all&quot;&gt;<br />
	&nbsp;&nbsp;&nbsp;&lt;div style=&quot;width: 37%;&quot; class=&quot;<strong>ui-progressbar-value</strong> ui-widget-header ui-corner-left&quot;&gt;&lt;/div&gt;<br />
	&lt;/div&gt;
  <p class="theme-note">
    <strong>
      Note: This is a sample of markup generated by the progressbar plugin, not markup you should use to create a progressbar. The only markup needed for that is &lt;div&gt;&lt;/div&gt;.
    </strong>
  </p>

  </div>
</div>

</p><!-- 
Pre-expand include size: 21686 bytes
Post-expand include size: 27328 bytes
Template argument size: 10881 bytes
Maximum: 2097152 bytes
-->

<!-- Saved in parser cache with key jqdocs_docs:pcache:idhash:3780-1!1!0!!en!2 and timestamp 20120724075855 -->
