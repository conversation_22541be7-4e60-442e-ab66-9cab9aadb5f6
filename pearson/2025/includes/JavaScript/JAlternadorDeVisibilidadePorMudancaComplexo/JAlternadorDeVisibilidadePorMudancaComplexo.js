/**
** Classe JAlternadorDeVisibilidadePorMudancaComplexo
**/
var JAlternadorDeVisibilidadePorMudancaComplexo = new Class({
	alternarVisibilidade: function(alternador, camadas_por_alternador, camadas) {
		var teve_alteracao = false;
		var valor = alternador.value;
		
		if ( typeof(alternador.checked) != 'undefined' )
			valor = alternador.checked ? '1' : '0';

		camadas_por_alternador.each(function(camada) {
			if (valor == camada[0]) {
				teve_alteracao = true;
				camadas.each(function(sub_camada) {
					if ($type($(sub_camada)) == 'element') {
						if (camada[1].contains(sub_camada))
							$(sub_camada).show();
						else
							$(sub_camada).hide();
					}
				});
			}
		});
		
		if (!teve_alteracao) {
			camadas.each(function(sub_camada) {
				if ($type($(sub_camada)) == 'element')
					$(sub_camada).hide();
			});
		}
	}
});

var meu_AlternadorDeVisibilidadePorMudancaComplexo = new JAlternadorDeVisibilidadePorMudancaComplexo();