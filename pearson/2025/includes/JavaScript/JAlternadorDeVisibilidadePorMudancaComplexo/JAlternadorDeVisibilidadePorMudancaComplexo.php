<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JAlternadorDeVisibilidadePorMudancaComplexo {
	private $_valor_selecionado;
	private $_divs_por_alternador;
	private $_divs;
	private $_acao;

	public function __construct ($valor_selecionado, $divs_por_alternador = array(), $divs = array(), $acao = 'onchange="%s"')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JAlternadorDeVisibilidadePorMudancaComplexo/JAlternadorDeVisibilidadePorMudancaComplexo.js');

		$temp = array();
		foreach($divs_por_alternador as $k => $v)
			$temp[] = "['$k', ". implode_to_javascript($v) ."]";

		$this->_divs_por_alternador = implode_to_javascript($temp);
		$this->_divs = implode_to_javascript($divs);
		$this->_valor_selecionado = $valor_selecionado;
		$this->_acao = $acao;
	}

	public function obterHTML()	{
		return sprintf($this->_acao, sprintf("meu_AlternadorDeVisibilidadePorMudancaComplexo.alternarVisibilidade(this, %s, %s);", $this->_divs_por_alternador, $this->_divs));
	}
}

?>