<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JMascaradorDeCampos {
	protected $_classe = 'iMask';
	protected $_parametros = array();
	
	public function __construct ($parametros)
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/iMask/iMask.js');
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JMascaradorDeCampos/JMascaradorDeCampos.js');
		
		$this->_parametros = $parametros;
	}
	
	public function obterHTML()	{
		$alt = array();

		foreach ($this->_parametros as $k => $v) {
			if (is_bool($v))
				$v = $v ? 'true': 'false';
			else if (is_int($v))
				$v = $v;
			else
				$v = "'$v'";
			
			$alt[] = $k . ': ' . $v;
		}

		return ' class="'. $this->_classe .'" alt="{'. implode(',', $alt) .'}" ';
	}
}

?>