<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JAlteradorDeFormulario {
	private $_nome;
	private $_valor;
	private $_nomeFormulario;
	private $_acao;

	public function __construct ($nome, $valor, $nomeFormulario, $enviar = true, $acao = '%s')
	{
		Core::modulo('js')->incluirArquivo('https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/JAlteradorDeFormulario/JAlteradorDeFormulario.js');
		
		$this->_nome = $nome;
		$this->_valor = $valor;
		$this->_nomeFormulario = $nomeFormulario;
		$this->_enviar = ($enviar == true ? 'true' : 'false');
		$this->_acao = $acao;
	}
	
	public function obterHTML ($ignorarAcao = false)
	{
		if ($ignorarAcao)
			$this->_acao = '%s';
			
		return sprintf($this->_acao, sprintf("meu_AlteradorDeFormulario.alterarValor('%s', '%s', '%s', %s);", $this->_nome, $this->_valor, $this->_nomeFormulario, $this->_enviar));
	}
	
}

?>