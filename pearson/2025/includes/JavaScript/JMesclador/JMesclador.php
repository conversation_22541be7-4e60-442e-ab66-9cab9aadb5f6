<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JMesclador {
	protected $_componentes = array();
	protected $_acao;
	
	public function __construct ($acao = null) {		
		$this->_acao = $acao;
	}

	public function adicionarComponente (&$componente) {
		$this->_componentes[] = $componente;
	}
	
	public function removerComponentes () {
		$this->_componentes = array();
	}
	
	public function obterHTML () {
		$retorno = '';
		
		foreach ( $this->_componentes as $componente ) {
			$retorno .= @$componente->obterHTML( ($this->_acao != null) );
		}
		
		if ( $this->_acao != null ) {
			$retorno = sprintf($this->_acao, $retorno);
		}
		
		return $retorno;
	}
	
}

?>