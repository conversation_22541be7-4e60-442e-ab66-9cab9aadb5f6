<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JDesabilitadorDeCampos {
	private $_campos;
	private $_acao;

	public function __construct ($campos, $acao = '%s')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JDesabilitadorDeCampos/JDesabilitadorDeCampos.js');
		
		$this->_campos = implode_to_javascript($campos);
		$this->_acao = $acao;
	}
	
	public function obterHTML()	{
		return sprintf($this->_acao, "meu_DesabilitadorDeCampos.alterarEstadoDosCampos(this, ". $this->_campos .");");
	}
}

?>