<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JTrocadorDeMultiplasVisibilidades {
	private $_nome;
	private $_divs;

	public function __construct ($nome, $divs)
	{
		Core::modulo('js')->incluirArquivo('https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/JTrocadorDeMultiplasVisibilidades/JTrocadorDeMultiplasVisibilidades.js');
		
		$this->_nome = $nome;
		$this->_divs = implode_to_javascript($divs);
	}
	
	public function obterHTML()	{
		return sprintf("meu_TrocadorDeMultiplasVisibilidades.trocarVisibilidades('%s', %s);", $this->_nome, $this->_divs);
	}
	
}

?>