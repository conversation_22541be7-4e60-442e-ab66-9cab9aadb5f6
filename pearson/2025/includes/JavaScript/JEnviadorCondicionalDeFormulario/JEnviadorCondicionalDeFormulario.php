<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JEnviadorCondicionalDeFormulario {
	private $_nome;
	private $_valor;
	private $_nomeFormulario;
	private $_acao;

	public function __construct ($nome, $valor, $nomeFormulario, $acao = '%s')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JEnviadorCondicionalDeFormulario/JEnviadorCondicionalDeFormulario.js');
		
		$this->_nome = $nome;
		$this->_valor = $valor;
		$this->_nomeFormulario = $nomeFormulario;
		$this->_acao = $acao;
	}
	
	public function obterHTML ($ignorarAcao = false)
	{
		if ($ignorarAcao)
			$this->_acao = '%s';
			
		return sprintf($this->_acao, sprintf("meu_EnviadorCondicionalDeFormulario.enviar('%s', '%s', '%s');",
			$this->_nome,
			$this->_valor,
			$this->_nomeFormulario ));
	}
	
}

?>