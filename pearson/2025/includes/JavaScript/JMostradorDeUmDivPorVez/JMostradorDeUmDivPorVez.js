/**
** Classe JMostradorDeUmDivPorVez
**/
var JMostradorDeUmDivPorVez = new Class({
	initialize: function(divs, arrastavel, abrir_para_direita){
		this.nome_div_anterior = null;
		
		arrastavel = $pick(arrastavel, false);
		
		this.abrir_para_direita = $pick(abrir_para_direita, true);
		
		divs.each(function (d) {
			$(d).setOpacity(0);

			if (arrastavel)
				$(d).makeDraggable();
		});
	},
	
	toggleDiv: function(nome_div, objeto_posicionador) {
		var div = $(nome_div);
		var div_anterior = $(this.nome_div_anterior);
	
		if (this.nome_div_anterior != null) {
			div_anterior.setOpacity(0);
			var temp = this.nome_div_anterior;
			this.nome_div_anterior = null;
			
			if (temp == nome_div)
				return;
		}
		
		var coord = $(objeto_posicionador).getCoordinates();
		div.setStyle('top', coord.top);
		if (this.abrir_para_direita)
			div.setStyle('left', coord.left + coord.width + 4);
		else
			div.setStyle('left', coord.left - div.getCoordinates().width - 4 );
		
		div.setOpacity(1);
		
		this.nome_div_anterior = nome_div;
	},

	esconderDiv: function() {
		var div_anterior = $(this.nome_div_anterior);
		
		if ($type(div_anterior) == 'element')
			div_anterior.setOpacity(0);
		
		this.nome_div_anterior = null;
	}
});