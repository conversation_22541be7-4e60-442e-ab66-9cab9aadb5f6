<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JAlternadorDeVisibilidadePorMudanca {
	private $_valor_selecionado;
	private $_divs;
	private $_acao;

	public function __construct ($valor_selecionado, $divs = array(), $acao = 'onchange="%s"')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JAlternadorDeVisibilidadePorMudanca/JAlternadorDeVisibilidadePorMudanca.js');

		$temp = array();
		foreach($divs as $k => $v)
			$temp[] = "['$k', '$v']";

		$this->_divs = implode_to_javascript($temp);
		$this->_valor_selecionado = $valor_selecionado;
		$this->_acao = $acao;
	}

	public function obterHTML()	{
		return sprintf($this->_acao, sprintf("meu_AlternadorDeVisibilidadePorMudanca.alternarVisibilidade(this, %s);", $this->_divs));
	}
}

?>