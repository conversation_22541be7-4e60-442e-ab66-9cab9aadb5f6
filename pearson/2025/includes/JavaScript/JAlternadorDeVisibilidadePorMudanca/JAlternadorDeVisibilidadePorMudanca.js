/**
** Classe JAlternadorDeVisibilidadePorMudanca
**/
var JAlternadorDeVisibilidadePorMudanca = new Class({
	alternarVisibilidade: function(alternador, camadas) {
		var mostrar = null;
	
		camadas.each(function(camada) {
			if ( $type($(camada[1])) ) {
				if (alternador.value == camada[0]) {
					mostrar = $(camada[1]);
				} else {
					$(camada[1]).hide();
				}
			}
		});

		if (mostrar != null)
			mostrar.show();
	}
});

var meu_AlternadorDeVisibilidadePorMudanca = new JAlternadorDeVisibilidadePorMudanca();