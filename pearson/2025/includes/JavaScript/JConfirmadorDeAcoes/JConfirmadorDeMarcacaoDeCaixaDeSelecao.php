<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JConfirmadorDeMarcacaoDeCaixaDeSelecao {
	private $_mensagem;
	private $_acao;

	public function __construct ($mensagem, $acao = 'onclick="%s"')
	{
		Core::modulo('js')->incluirArquivo('https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/JConfirmadorDeAcoes/JConfirmadorDeAcoes.js');
		
		$this->_mensagem = $mensagem;
		$this->_acao = $acao;
	}
	
	public function obterHTML()	{
		return sprintf($this->_acao, sprintf("meu_ConfirmadorDeAcoes.confirmarMarcacaoDeCaixaDeSelecao(this, '%s');", $this->_mensagem));
	}
}

?>