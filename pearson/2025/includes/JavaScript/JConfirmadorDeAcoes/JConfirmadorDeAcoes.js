/**
** Classe JConfirmadorDeAcoesSobreCaixasSelecao
**/
var JConfirmadorDeAcoes = new Class({
	confirmarAcaoSobreCaixaDeSelecao: function(solicitador, mensagem, caixa_selecao) {
		var tem_selecao = false;
		caixa_selecao = $$('input[name^='+ caixa_selecao.substr(0, caixa_selecao.length-2) +']');
		
		caixa_selecao.each(function(sub_campo) {
			if ($type(sub_campo) == 'element' && sub_campo.checked)
				tem_selecao = true;
		});
		
		if (!tem_selecao) {
			alert("Nenhum item selecionado!");
		} else {
			if ( $type(mensagem) == 'string' && mensagem.length > 0 ) {
				if (confirm(mensagem))
					solicitador.form.submit();
			} else {
				solicitador.form.submit();
			}
		}
	},
	
	confirmarMarcacaoDeCaixaDeSelecao: function(solicitador, mensagem) {
		if ( solicitador.checked ) {
			if (confirm(mensagem))
				solicitador.checked = true;
			else
				solicitador.checked = false;
		} else {
			solicitador.checked = false;
		}
	}
});

var meu_ConfirmadorDeAcoes = new JConfirmadorDeAcoes();