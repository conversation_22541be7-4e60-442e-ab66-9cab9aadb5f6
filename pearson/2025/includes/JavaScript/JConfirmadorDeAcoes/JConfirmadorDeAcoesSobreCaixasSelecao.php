<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JConfirmadorDeAcoesSobreCaixasSelecao {
	private $_mensagem;
	private $_acao;
	private $_caixa_selecao;

	public function __construct ($mensagem, $acao = '%s', $caixa_selecao)
	{
		Core::modulo('js')->incluirArquivo('https://dyubuzjbgoyjh.cloudfront.net/includes/JavaScript/JConfirmadorDeAcoes/JConfirmadorDeAcoes.js');
		
		$this->_mensagem = $mensagem;
		$this->_caixa_selecao = $caixa_selecao;
		$this->_acao = $acao;
	}
	
	public function obterHTML()	{
		return sprintf($this->_acao, sprintf("meu_ConfirmadorDeAcoes.confirmarAcaoSobreCaixaDeSelecao(this, '%s', '%s');", $this->_mensagem, $this->_caixa_selecao));
	}
}

?>