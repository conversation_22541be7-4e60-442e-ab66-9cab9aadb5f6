/**
** Classe JAlteradorDeFormularioAjax
**/
var JAlteradorDeFormularioAjax = new Class({
	alterarValor: function(chamador, campo, url, dados, enviar, mostrador){
		campo = $(campo);
		chamador = $(chamador);	

		if ( $type(chamador) == 'element' && $type(campo) == 'element' && $type(dados) == 'object' ) {
			dados.id = chamador.value;
			dados.chamador = chamador.name;

			mostrador.addClass('ajax_carregando');
			
			var ajax = new Ajax(url, {
				encoding: 'iso-8859-1',
				method: 'post',
				data: dados,
				//update: $('AJAX_DEBUG'),
				onComplete: function(retorno) {				
					mostrador.removeClass('ajax_carregando');
				
					if (retorno.length < 2) return;
					
					retorno = Json.evaluate(retorno);
					
					if ($type(retorno.erro) == 'string') {	alert(retorno.erro); return; }
			    	
			    	if ($type(retorno.sucesso) == 'boolean' && retorno.sucesso) {
			    		mostrador.addClass('ajax_sucesso').removeClass.delay(500, mostrador, 'ajax_sucesso');
			    		
			    		campo.empty();

			    		if ($type(retorno.dados) == 'object' && $type(retorno.dados.chaves) == 'array' && $type(retorno.dados.valores) == 'array') {
			    			retorno.dados = retorno.dados.valores.associate(retorno.dados.chaves);

			    			$each(retorno.dados, function(v, k) {
			    				campo.adopt(new Element('option', { 'value': k }).setHTML(v));
			    			});
			    		} else if ($type(retorno.dados) == 'string')
			    			campo.value = retorno.dados;			    			
			    	} else
			    		mostrador.addClass('ajax_falha').removeClass.delay(500, mostrador, 'ajax_falha');
			    		
			    	campo.focus();
				}.bind(this)
			}).request();
		}

		//if (enviar)
			//formulario.submit();
	}
});

var meu_AlteradorDeFormularioAjax = new JAlteradorDeFormularioAjax();