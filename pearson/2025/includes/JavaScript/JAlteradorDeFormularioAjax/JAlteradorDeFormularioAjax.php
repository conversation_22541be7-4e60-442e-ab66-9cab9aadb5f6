<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class JAlteradorDeFormularioAjax {
	private $_nome;
	private $_url;
	private $_dados;
	private $_enviar;
	private $_acao;

	public function __construct ($nome, $url, $dados = array(), $enviar = false, $acao = ' onchange="%s" ')
	{
		Core::modulo('js')->incluirArquivo('includes/JavaScript/JAlteradorDeFormularioAjax/JAlteradorDeFormularioAjax.js');
		
		if (!is_array($dados))
			$dados = array();
			
		if (!isset($dados['campo']))
			$dados['campo'] = $nome;

		if (!isset($dados['id']))
			$dados['id'] = '';
			
		if (!isset($dados['chamador']))
			$dados['chamador'] = '';
		
		$this->_nome = $nome;
		$this->_url = $url;
		
		$this->_dados = str_replace('"', "'", json_encode($dados));
		
		$this->_enviar = ($enviar == true ? 'true' : 'false');
		$this->_acao = $acao;
	}
	
	public function obterHTML ($ignorarAcao = false)
	{
		if ($ignorarAcao)
			$this->_acao = '%s';

		return sprintf($this->_acao, sprintf("
			meu_AlteradorDeFormularioAjax.alterarValor(this, '%s', '%s', %s, %s, $('_perfil_falso_mostrador'));",
			$this->_nome,
			$this->_url,
			$this->_dados,
			$this->_enviar));
	}
	
}

?>