<?php

// Capturar erros fatais e converter para JSON
function handleFatalError() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Fatal Error: ' . $error['message'],
            'file' => $error['file'],
            'line' => $error['line']
        ]);
        exit;
    }
}
register_shutdown_function('handleFatalError');

// Capturar output indesejado
ob_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Aumentar limites
ini_set('upload_max_filesize', '50M');
ini_set('post_max_size', '60M');
ini_set('max_execution_time', '300');
ini_set('max_input_time', '300');
ini_set('memory_limit', '256M');

// Habilitar logs mas não mostrar na tela
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

function logDebug($message) {
    error_log("[TinyMCE Upload] " . $message);
}

logDebug("Iniciando processamento de upload");

$uploadErrors = array(
    UPLOAD_ERR_OK => 'No errors.',
    UPLOAD_ERR_INI_SIZE => 'Larger than upload_max_filesize.',
    UPLOAD_ERR_FORM_SIZE => 'Larger than form MAX_FILE_SIZE.',
    UPLOAD_ERR_PARTIAL => 'Partial upload.',
    UPLOAD_ERR_NO_FILE => 'No file uploaded.',
    UPLOAD_ERR_NO_TMP_DIR => 'No temporary directory.',
    UPLOAD_ERR_CANT_WRITE => 'Cannot write to disk.',
    UPLOAD_ERR_EXTENSION => 'File upload stopped by extension.'
);

// Tamanhos máximos aumentados para áudio
$maxFileSizes = [
    'image' => 10 * 1024 * 1024,  // 10MB para imagens
    'media' => 100 * 1024 * 1024, // 100MB para mídia (áudio pode ser grande)
    'audio' => 50 * 1024 * 1024   // 50MB específico para áudio
];

try {
    $documentRoot = $_SERVER['DOCUMENT_ROOT'];
    logDebug("Document root: " . $documentRoot);

    // Detectar automaticamente o caminho do projeto usando __DIR__
    $currentPath = __DIR__;
    logDebug("Current path: " . $currentPath);
    
    // Navegar até a raiz do projeto (4 níveis acima)
    $projectPath = dirname(dirname(dirname(dirname($currentPath))));
    logDebug("Project path (absolute): " . $projectPath);
    
    // Converter para caminho relativo ao document root
    $projectRoot = str_replace($documentRoot, '', $projectPath);
    logDebug("Project root (before normalization): " . $projectRoot);
    
    // Normalizar o caminho
    $projectRoot = '/' . trim($projectRoot, '/');
    logDebug("Project root (normalized): " . $projectRoot);
    
    // Caminhos físicos das pastas de upload
    $uploadBasePath = $documentRoot . $projectRoot . '/upload';
    logDebug("Upload base path: " . $uploadBasePath);

    // Verificar se o diretório base existe
    if (!is_dir($uploadBasePath)) {
        logDebug("ERRO: Upload base path não existe: " . $uploadBasePath);

        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $pathname = parse_url($requestUri, PHP_URL_PATH);
        $pathParts = array_filter(explode('/', $pathname));
        // Tentar algumas alternativas
        $alternatives = [
            $documentRoot . $pathParts[0] . '/' . $pathParts[1]  . '/' .$pathParts[2].  '/' .$pathParts[3]. '/upload',
            $documentRoot . '/upload',
            dirname($currentPath, 4) . '/upload'
        ];
  
        // Tentar algumas alternativas
        foreach ($alternatives as $alt) {
            logDebug("Testando alternativa: " . $alt);
            if (is_dir($alt)) {
                $uploadBasePath = $alt;
                $projectRoot = str_replace($documentRoot, '', dirname($alt));
                $projectRoot = '/' . trim($projectRoot, '/');
                logDebug("Usando alternativa: " . $uploadBasePath);
                break;
            }
        }
        
        if (!is_dir($uploadBasePath)) {
            throw new Exception('Não foi possível localizar o diretório de upload. Testados: ' . implode(', ', array_merge([$documentRoot . $projectRoot . '/upload'], $alternatives)));
        }
    }

    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido. Use POST.');
    }

    // Verificar se há arquivo enviado
    if (!isset($_FILES['file'])) {
        throw new Exception('Nenhum arquivo foi enviado.');
    }

    $file = $_FILES['file'];
    logDebug("Arquivo recebido: " . $file['name'] . " (size: " . $file['size'] . ", type: " . $file['type'] . ")");

    // Verificar erros de upload
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMsg = isset($uploadErrors[$file['error']]) ? $uploadErrors[$file['error']] : 'Unknown upload error.';
        throw new Exception('Erro no upload: ' . $errorMsg);
    }

    // Verificar se o arquivo foi realmente enviado via HTTP POST
    if (!is_uploaded_file($file['tmp_name'])) {
        throw new Exception('Arquivo não foi enviado via POST.');
    }

    // Detectar tipo de arquivo
    $fileType = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $mimeType = $file['type'];
    
    logDebug("Extensão: " . $fileType . ", MIME: " . $mimeType);

    // Categorizar arquivo
    $category = 'other';
    if (in_array($fileType, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
        $category = 'image';
        $uploadDir = $uploadBasePath . '/images';
        $webPath = $projectRoot . '/upload/images';
    } elseif (in_array($fileType, ['mp3', 'wav', 'ogg', 'mp4', 'webm'])) {
        $category = 'media';
        $uploadDir = $uploadBasePath . '/media';
        $webPath = $projectRoot . '/upload/media';
    } else {
        throw new Exception('Tipo de arquivo não permitido: ' . $fileType);
    }

    logDebug("Categoria: " . $category . ", Diretório: " . $uploadDir);

    // Verificar tamanho do arquivo
    $maxSize = $maxFileSizes[$category] ?? $maxFileSizes['image'];
    if ($file['size'] > $maxSize) {
        throw new Exception('Arquivo muito grande. Máximo permitido: ' . round($maxSize / (1024 * 1024), 2) . 'MB');
    }

    // Criar diretório se não existir
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception('Não foi possível criar o diretório: ' . $uploadDir);
        }
        logDebug("Diretório criado: " . $uploadDir);
    }

    // Gerar nome único para o arquivo
    $fileName = uniqid() . '_' . time() . '.' . $fileType;
    $filePath = $uploadDir . '/' . $fileName;
    $fileUrl = $webPath . '/' . $fileName;

    logDebug("Salvando em: " . $filePath);
    logDebug("URL final: " . $fileUrl);

    // Mover arquivo para destino final
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('Erro ao mover arquivo para: ' . $filePath);
    }

    logDebug("Upload concluído com sucesso");

    // Limpar output buffer
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Retornar resposta de sucesso
    $response = [
        'success' => true,
        'location' => $fileUrl,
        'file' => [
            'name' => $file['name'],
            'size' => $file['size'],
            'type' => $file['type'],
            'saved_as' => $fileName,
            'category' => $category,
            'url' => $fileUrl
        ]
    ];

    echo json_encode($response);
    exit;

} catch (Exception $e) {
    // Limpar qualquer output anterior
    if (ob_get_level()) {
        ob_clean();
    }
    
    $errorMsg = 'Upload error: ' . $e->getMessage();
    logDebug($errorMsg);
    
    http_response_code(500);
    
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => [
            'current_path' => __DIR__,
            'document_root' => $_SERVER['DOCUMENT_ROOT'],
            'project_root' => $projectRoot ?? 'N/A',
            'upload_base_path' => $uploadBasePath ?? 'N/A',
            'php_version' => PHP_VERSION,
            'post_max_size' => ini_get('post_max_size'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'memory_limit' => ini_get('memory_limit'),
            'error_code' => $_FILES['file']['error'] ?? 'No file',
            'file_type' => $_FILES['file']['type'] ?? 'Unknown'
        ]
    ];
    
    echo json_encode($response);
    exit;
}
?>