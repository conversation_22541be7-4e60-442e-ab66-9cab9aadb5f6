<?php
// Configuração TinyMCE com correção total para conflitos MooTools
$currentPath = dirname($_SERVER['PHP_SELF']);
$uploadHandlerPath = $currentPath . '/upload-handler.php';
?>

// CORREÇÃO TOTAL DO CONFLITO MOOTOOLS vs TINYMCE
(function() {
    ////console.log('🔧 Iniciando correção MooTools vs TinyMCE...');
    
// PATCH CRÍTICO: Corrigir Element.remove do MooTools
if (typeof Element !== 'undefined' && Element.prototype.remove) {
    var originalMooToolsRemove = Element.prototype.remove;
    
    // Criar versão segura do remove que funciona com TinyMCE
    Element.prototype.remove = function() {
        try {
            // Verificar se o elemento ainda está no DOM
            if (!this.parentNode) {
                ////console.log('🔧 Elemento sem parentNode, ignorando remove');
                return this;
            }
            
            // Se é um elemento com parentNode válido, usar removeChild nativo
            if (this.parentNode && this.parentNode.removeChild) {
                return this.parentNode.removeChild(this);
            }
            
            // Fallback: usar método original do MooTools se parentNode existe
            if (this.parentNode) {
                return originalMooToolsRemove.call(this);
            }
            
            // Se chegou até aqui, elemento já foi removido
           // //console.log('🔧 Elemento já removido do DOM');
            return this;
            
        } catch (e) {
            console.warn('⚠️ Erro no Element.remove, usando fallback silencioso:', e.message);
            
            // Fallback silencioso: tentar remover via DOM API moderna
            try {
                if (this.remove && this.remove !== Element.prototype.remove) {
                    this.remove();
                }
            } catch (e2) {
                // Ignorar silenciosamente se já foi removido
               // //console.log('🔧 Elemento não pôde ser removido (possivelmente já removido)');
            }
            
            return this;
        }
    };
    
    ////console.log('✅ Patch MooTools Element.remove aprimorado aplicado');
}
    
    var safeToInit = true;
    var initAttempts = 0;
    var maxAttempts = 3;
    var autoSaveIntervals = {}; // Para controlar intervals por editor
    
    function checkSafety() {
        // Verificar se estamos em uma transição de página
        if (document.readyState === 'unloading' || document.hidden) {
            ////console.log('🔄 Página sendo descarregada, pulando inicialização');
            return 'unloading';
        }
        
        var existingSpinners = document.querySelectorAll('.tox-throbber__busy-spinner');
        if (existingSpinners.length > 0) {
            ////console.log('⚠️ Detectados spinners existentes, aguardando limpeza...');
            return false;
        }
        
        var textareas = document.querySelectorAll('#enunciado, #resolucao');
        if (textareas.length === 0) {
            // Verificar se realmente estamos na página certa
            var isQuestionForm = document.querySelector('form') && 
                            (window.location.href.includes('questoes') || 
                                document.querySelector('input[name="enviar"]'));
            
            if (isQuestionForm) {
                ////console.log('⚠️ Textareas não encontrados na página de questões');
                return false;
            } else {
                // Não é a página de questões, não tentar inicializar
                ////console.log('ℹ️ Não é página de questões, pulando inicialização TinyMCE');
                return 'skip';
            }
        }
        
        return true;
    }
    
    function initTinyMCESafe() {
        initAttempts++;
        
        var safety = checkSafety();
        
        // Se retornou 'skip' ou 'unloading', não tentar mais
        if (safety === 'skip' || safety === 'unloading') {
            return;
        }
        
        if (safety === false) {
            if (initAttempts < maxAttempts) {
                ////console.log('🔄 Tentativa ' + initAttempts + ' falhou, tentando novamente em 2s...');
                setTimeout(initTinyMCESafe, 2000);
            } else {
               // //console.log('❌ Máximo de tentativas atingido, verificando se ainda é necessário...');
                
                // Verificação final - se não há textareas, não mostrar modo texto
                var textareas = document.querySelectorAll('#enunciado, #resolucao');
                if (textareas.length > 0) {
                    showTextareaMode();
                } else {
                    ////console.log('ℹ️ Não há textareas para inicializar');
                }
            }
            return;
        }
        
var config = {
    selector: '#enunciado, #resolucao, #texto_inicio, #texto_fim, .tinymce',
    
    // Licença
    license_key: 'gpl',
    promotion: false,
    branding: false,
    
    // Plugins CORRETOS (removendo fontsize e fontfamily que não existem)
    plugins: ['lists', 'link', 'image', 'code'],

    // Toolbar SEM o botão media duplicado
    toolbar: 'undo redo | fontfamily fontsize | bold italic underline strikethrough | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | indent outdent | link image audio | code',

    // Configurações básicas
    height: 250,
    resize: true,
    menubar: false,
    
    // Configurações de fonte NATIVAS do TinyMCE 6
    font_family_formats: 'Arial=arial,helvetica,sans-serif; Times New Roman=times new roman,times,serif; Courier New=courier new,courier,monospace; Verdana=verdana,geneva,sans-serif; Georgia=georgia,times,serif; Comic Sans MS=comic sans ms,cursive; Impact=impact,sans-serif; Tahoma=tahoma,geneva,sans-serif',
    
    font_size_formats: '8px 9px 10px 11px 12px 14px 16px 18px 20px 22px 24px 26px 28px 36px 48px 72px',
    
    // CONFIGURAÇÕES ANTI-CONFLITO MOOTOOLS
    automatic_uploads: true,
    images_upload_timeout: 30000,
    
    // CRÍTICO: Configurações para evitar conflitos
    cleanup: false,
    verify_html: false,
    convert_urls: false,
    relative_urls: false,
    remove_script_host: false,
    document_base_url: window.location.origin + '<?php echo dirname($_SERVER['REQUEST_URI']); ?>/',
    
    // Configurações de conteúdo permissivas
    valid_elements: '*[*]',
    extended_valid_elements: '*[*]',
    invalid_elements: '',
    
    // Configurações de formatação que não conflitam
    forced_root_block: 'p',
    force_br_newlines: false,
    remove_trailing_brs: false,
    
    // Upload config
    images_upload_url: '<?php echo $uploadHandlerPath; ?>',
    images_reuse_filename: true,
    
    // Configurações para preservar conteúdo
    entity_encoding: 'raw',
    remove_linebreaks: false,
    preformatted: false,
    
    // CALLBACK DE INICIALIZAÇÃO SEGURO
    init_instance_callback: function(editor) {
        ////console.log('✅ Editor inicializado:', editor.id);
        
        // Cancelar timeout de falha
        if (window.tinyMCEFailTimeout) {
            clearTimeout(window.tinyMCEFailTimeout);
        }
        
        // Marcar como seguro
        editor.getElement().setAttribute('data-tinymce-safe', 'true');
        
        // CARREGAR CONTEÚDO EXISTENTE
        var originalContent = editor.getElement().value;
        if (originalContent && originalContent.trim() !== '') {
            ////console.log('📄 Carregando conteúdo existente para:', editor.id);
            editor.setContent(originalContent, {format: 'raw'});
        }
        
        // AUTO-SAVE CONTROLADO (não no setInterval que causa loops)
        var lastContent = editor.getContent();
        var autoSaveTimeout;
        
        function scheduleSave() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(function() {
                if (editor && !editor.destroyed) {
                    var currentContent = editor.getContent();
                    if (currentContent !== lastContent) {
                        editor.save();
                        lastContent = currentContent;
                        //////console.log('💾 Auto-save realizado para:', editor.id);
                    }
                }
            }, 2000); // Save 2 segundos após parar de digitar
        }
        
        // Listeners para auto-save controlado
        editor.on('input', scheduleSave);
        editor.on('change', scheduleSave);
        editor.on('blur', function() {
            clearTimeout(autoSaveTimeout);
            editor.save();
            ////console.log('💾 Save no blur para:', editor.id);
        });
        
        // Limpar indicadores de recuperação
        var indicators = editor.getElement().parentNode.querySelectorAll('.recovery-indicator');
        for (var i = 0; i < indicators.length; i++) {
            indicators[i].remove();
        }
    },
    
    // FILE PICKER SEGURO COM SUPORTE A ÁUDIO
    file_picker_callback: function(callback, value, meta) {
        var input = document.createElement('input');
        input.setAttribute('type', 'file');
        
        if (meta.filetype === 'image') {
            input.setAttribute('accept', 'image/*');
        } else if (meta.filetype === 'media') {
            input.setAttribute('accept', 'audio/*,video/*');
        } else {
            // Para botão genérico, aceitar tudo
            input.setAttribute('accept', 'image/*,audio/*,video/*');
        }
        
        input.onchange = function() {
            var file = this.files[0];
            if (!file) return;
            
            // Detectar tipo baseado no MIME type
            var uploadType = 'image';
            if (file.type.startsWith('audio/')) {
                uploadType = 'audio';
            } else if (file.type.startsWith('video/')) {
                uploadType = 'media';
            }
            
            var formData = new FormData();
            formData.append('file', file);
            formData.append('type', uploadType);
            
            console.log('📤 Iniciando upload:', file.name, 'Tipo:', uploadType);
            
            // Mostrar indicador de progresso
            var progressDiv = document.createElement('div');
            progressDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #007bff; color: white; padding: 10px; border-radius: 5px; z-index: 9999;';
            progressDiv.innerHTML = '📤 Fazendo upload de ' + file.name + '...';
            document.body.appendChild(progressDiv);
            
            fetch('<?php echo $uploadHandlerPath; ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('📡 Response status:', response.status);
                return response.json();
            })
            .then(result => {
                // Remover indicador de progresso
                document.body.removeChild(progressDiv);
                
                console.log('📄 Upload result:', result);
                
                if (result.success && result.location) {
                    var fileUrl = result.location;
                    
                    // Garantir URL absoluta
                    if (!fileUrl.startsWith('http') && !fileUrl.startsWith('/')) {
                        fileUrl = '<?php echo dirname($_SERVER['REQUEST_URI']); ?>/' + fileUrl;
                    }
                    
                    if (uploadType === 'image') {
                        callback(fileUrl, { 
                            alt: file.name, 
                            title: file.name,
                            class: 'uploaded-image'
                        });
                    } else if (uploadType === 'audio') {
                        // Para áudio, inserir HTML simples diretamente
                        var audioHtml = '<p><audio controls style="width: 100%; max-width: 400px;"><source src="' + fileUrl + '" type="' + file.type + '">Seu navegador não suporta o elemento audio.</audio></p>';
                        
                        var editor = tinymce.activeEditor;
                        if (editor) {
                            editor.insertContent(audioHtml);
                        }
                        
                        // Não chamar callback para evitar conflito com media_url_resolver
                        console.log('✅ Áudio inserido diretamente');
                        return; // Sair aqui para não chamar callback
                    } else {
                        // Vídeo
                        callback(fileUrl, { 
                            source1: fileUrl,
                            type: 'video',
                            embed: '<video width="320" height="240" controls><source src="' + fileUrl + '" type="' + file.type + '"></video>'
                        });
                    }

                    console.log('✅ Arquivo inserido:', fileUrl);
                } else {
                    throw new Error(result.error || 'Upload falhou');
                }
            })
            .catch(error => {
                // Remover indicador de progresso
                if (progressDiv.parentNode) {
                    document.body.removeChild(progressDiv);
                }
                
                console.error('❌ Erro no upload:', error);
                alert('Erro no upload: ' + error.message);
            });
        };
        
        input.click();
    },
    
    // SETUP ULTRA SEGURO ANTI-MOOTOOLS
    setup: function(editor) {
        ////console.log('🔧 Setup do editor:', editor.id);

        // Adicionar botão personalizado para áudio
        editor.ui.registry.addButton('audio', {
            text: '🎵',
            tooltip: 'Inserir Áudio',
            onAction: function () {
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'audio/*');
                
                input.onchange = function() {
                    var file = this.files[0];
                    if (!file) return;
                    
                    if (!file.type.startsWith('audio/')) {
                        alert('Por favor, selecione um arquivo de áudio válido.');
                        return;
                    }
                    
                    var formData = new FormData();
                    formData.append('file', file);
                    formData.append('type', 'audio');
                    
                    console.log('🎵 Iniciando upload de áudio:', file.name);
                    
                    // Mostrar indicador de progresso
                    var progressDiv = document.createElement('div');
                    progressDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #28a745; color: white; padding: 10px; border-radius: 5px; z-index: 9999;';
                    progressDiv.innerHTML = '🎵 Fazendo upload de áudio: ' + file.name + '...';
                    document.body.appendChild(progressDiv);
                    
                    fetch('<?php echo $uploadHandlerPath; ?>', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(result => {
                        document.body.removeChild(progressDiv);
                        
                        if (result.success && result.location) {
                            var audioUrl = result.location;
                            
                            // Garantir URL absoluta
                            if (!audioUrl.startsWith('http') && !audioUrl.startsWith('/')) {
                                audioUrl = '<?php echo dirname($_SERVER['REQUEST_URI']); ?>/' + audioUrl;
                            }
                            
                            // Inserir player de áudio SIMPLES
                            var audioHtml = '<p><audio controls style="width: 100%; max-width: 400px;">' +
                                '<source src="' + audioUrl + '" type="' + file.type + '">' +
                                'Seu navegador não suporta o elemento audio.' +
                                '</audio></p>';
                            
                            editor.insertContent(audioHtml);
                            
                            console.log('✅ Áudio inserido:', audioUrl);
                        } else {
                            throw new Error(result.error || 'Upload de áudio falhou');
                        }
                    })
                    .catch(error => {
                        if (progressDiv.parentNode) {
                            document.body.removeChild(progressDiv);
                        }
                        console.error('❌ Erro no upload de áudio:', error);
                        alert('Erro no upload de áudio: ' + error.message);
                    });
                };
                
                input.click();
            }
        });

        // Controles de fonte SIMPLIFICADOS que funcionam
        editor.ui.registry.addSplitButton('fontfamily', {
            text: 'Fonte',
            tooltip: 'Família da Fonte',
            onAction: function () {
                // Ação padrão (opcional)
            },
            onItemAction: function (api, value) {
                editor.execCommand('FontName', false, value);
            },
            fetch: function (callback) {
                var items = [
                    { type: 'choiceitem', value: 'Arial', text: 'Arial' },
                    { type: 'choiceitem', value: 'Times New Roman', text: 'Times New Roman' },
                    { type: 'choiceitem', value: 'Courier New', text: 'Courier New' },
                    { type: 'choiceitem', value: 'Verdana', text: 'Verdana' },
                    { type: 'choiceitem', value: 'Georgia', text: 'Georgia' },
                    { type: 'choiceitem', value: 'Comic Sans MS', text: 'Comic Sans MS' },
                    { type: 'choiceitem', value: 'Impact', text: 'Impact' },
                    { type: 'choiceitem', value: 'Tahoma', text: 'Tahoma' }
                ];
                callback(items);
            }
        });

        editor.ui.registry.addSplitButton('fontsize', {
            text: 'Tamanho',
            tooltip: 'Tamanho da Fonte',
            onAction: function () {
                // Ação padrão (opcional)
            },
            onItemAction: function (api, value) {
                editor.execCommand('FontSize', false, value);
            },
            fetch: function (callback) {
                var items = [
                    { type: 'choiceitem', value: '1', text: '8px' },
                    { type: 'choiceitem', value: '2', text: '10px' },
                    { type: 'choiceitem', value: '3', text: '12px' },
                    { type: 'choiceitem', value: '4', text: '14px' },
                    { type: 'choiceitem', value: '5', text: '18px' },
                    { type: 'choiceitem', value: '6', text: '24px' },
                    { type: 'choiceitem', value: '7', text: '36px' }
                ];
                callback(items);
            }
        });

                editor.ui.registry.addSplitButton('fontsize', {
                    text: 'Tamanho',
                    tooltip: 'Tamanho da Fonte',
                    onAction: function () {
                        // Ação padrão (opcional)
                    },
                    onItemAction: function (api, value) {
                        editor.execCommand('FontSize', false, value);
                    },
                    fetch: function (callback) {
                        var items = [
                            { type: 'choiceitem', value: '1', text: '8px' },
                            { type: 'choiceitem', value: '2', text: '10px' },
                            { type: 'choiceitem', value: '3', text: '12px' },
                            { type: 'choiceitem', value: '4', text: '14px' },
                            { type: 'choiceitem', value: '5', text: '18px' },
                            { type: 'choiceitem', value: '6', text: '24px' },
                            { type: 'choiceitem', value: '7', text: '36px' }
                        ];
                        callback(items);
                    }
                });
                    
                    // Timeout de segurança
                    var editorTimeout = setTimeout(function() {
                        if (!editor.initialized) {
                            console.log('⚠️ Editor ' + editor.id + ' não inicializou, forçando fallback');
                            showTextareaModeForEditor(editor.id);
                        }
                    }, 15000);
                
                editor.on('init', function() {
                    clearTimeout(editorTimeout);
                    
                    ////console.log('🎯 Editor ' + editor.id + ' inicializado com sucesso');
                    
                    // CARREGAR CONTEÚDO EXISTENTE FORÇADAMENTE
                    var textarea = editor.getElement();
                    var existingContent = textarea.value;
                    
                    if (existingContent && existingContent.trim() !== '') {
                        ////console.log('📄 Carregando conteúdo para:', editor.id);
                        editor.setContent(existingContent, {format: 'raw'});
                    }
                    
                    // PATCH ADICIONAL: Interceptar operações do DOM que causam problemas
                    if (editor.dom) {
                        // Backup dos métodos originais
                        var originalDomRemove = editor.dom.remove;
                        var originalDomReplace = editor.dom.replace;
                        
                        // Override seguro do remove
                        editor.dom.remove = function(element, keepChildren) {
                            try {
                                if (!element) {
                                    return;
                                }
                                
                                // Se o elemento não tem parentNode, já foi removido
                                if (!element.parentNode) {
                                    //console.log('🔧 DOM: Elemento sem parentNode ignorado');
                                    return;
                                }
                                
                                return originalDomRemove.call(this, element, keepChildren);
                            } catch (e) {
                                //console.log('🔧 DOM: Erro no remove interceptado:', e.message);
                                // Tentar remoção manual segura
                                try {
                                    if (element && element.parentNode && element.parentNode.removeChild) {
                                        element.parentNode.removeChild(element);
                                    }
                                } catch (e2) {
                                    // Ignorar silenciosamente
                                }
                            }
                        };
                        
                        // Override seguro do replace
                        if (originalDomReplace) {
                            editor.dom.replace = function(newElm, oldElm, keepChildren) {
                                try {
                                    if (!oldElm || !oldElm.parentNode) {
                                        //console.log('🔧 DOM: Replace - elemento antigo sem parentNode');
                                        return newElm;
                                    }
                                    
                                    return originalDomReplace.call(this, newElm, oldElm, keepChildren);
                                } catch (e) {
                                    //console.log('🔧 DOM: Erro no replace interceptado:', e.message);
                                    return newElm;
                                }
                            };
                        }
                    }
                    
                    // PATCH ADICIONAL: Interceptar sanitização que causa problemas
                    if (editor.serializer && editor.serializer.schema) {
                        var originalSanitize = editor.serializer.schema.sanitize;
                        if (originalSanitize) {
                            editor.serializer.schema.sanitize = function(rootElm) {
                                try {
                                    return originalSanitize.call(this, rootElm);
                                } catch (e) {
                                    //console.log('🔧 Sanitize: Erro interceptado:', e.message);
                                    // Retornar elemento sem sanitização se houver erro
                                    return rootElm;
                                }
                            };
                        }
                    }
                });
                
                // Interceptar erros de salvamento
                editor.on('SaveContent', function(e) {
                    //console.log('💾 SaveContent trigger para:', editor.id);
                });
                
                editor.on('LoadContent', function() {
                    clearTimeout(editorTimeout);
                });
                
                // NOVO: Interceptar erros de getContent que causam os warnings
                editor.on('GetContent', function(e) {
                    // Este evento é disparado antes do getContent
                    // Podemos usar para preparar o DOM
                    try {
                        var body = editor.getBody();
                        if (body) {
                            // Verificar elementos órfãos antes do getContent
                            var orphanElements = body.querySelectorAll('*');
                            for (var i = 0; i < orphanElements.length; i++) {
                                var elem = orphanElements[i];
                                if (!elem.parentNode) {
                                    //console.log('🔧 Removendo elemento órfão antes do getContent');
                                    // Remover referência do array interno se possível
                                    if (elem.remove) {
                                        try {
                                            elem.remove();
                                        } catch (removeError) {
                                            // Ignorar erro
                                        }
                                    }
                                }
                            }
                        }
                    } catch (cleanupError) {
                        //console.log('🔧 Erro na limpeza pré-getContent (ignorando):', cleanupError.message);
                    }
                });
            }
        };
        
        // Timeout global de falha
        window.tinyMCEFailTimeout = setTimeout(function() {
            //console.log('❌ TinyMCE falhou em inicializar, usando modo texto');
            showTextareaMode();
        }, 20000);
        
        // INTERCEPTAR ENVIO DO FORMULÁRIO PRINCIPAL
        var forms = document.querySelectorAll('form');
        for (var f = 0; f < forms.length; f++) {
            forms[f].addEventListener('submit', function(e) {
                //console.log('📤 Formulário sendo enviado, sincronizando editores...');
                
                // Marcar que estamos enviando para evitar reinicializações
                window.formSubmitting = true;
                
                // Limpar todos os auto-saves
                if (typeof autoSaveIntervals === 'object' && autoSaveIntervals) {
                    var keys = Object.keys(autoSaveIntervals);
                    for (var k = 0; k < keys.length; k++) {
                        var key = keys[k];
                        if (autoSaveIntervals[key]) {
                            clearInterval(autoSaveIntervals[key]);
                        }
                    }
                }
                
                // Cancelar timeout de falha para evitar interferência
                if (window.tinyMCEFailTimeout) {
                    clearTimeout(window.tinyMCEFailTimeout);
                }
                
                // Sincronizar todos os editores antes do envio
                if (typeof tinymce !== 'undefined' && tinymce.editors) {
                    for (var i = 0; i < tinymce.editors.length; i++) {
                        var editor = tinymce.editors[i];
                        if (editor && editor.initialized && !editor.removed) {
                            try {
                                var content = editor.getContent() || '';
                                var textarea = document.getElementById(editor.id);
                                if (textarea) {
                                    textarea.value = content;
                                }
                            } catch (saveError) {
                                //console.log('⚠️ Erro ao sincronizar editor ' + editor.id + ':', saveError.message);
                            }
                        }
                    }
                }
            });
        }
        
        // Inicializar TinyMCE
        try {
            //console.log('🚀 Inicializando TinyMCE...');
            tinymce.init(config);
        } catch (e) {
            //console.log('❌ Erro na inicialização do TinyMCE:', e);
            showTextareaMode();
        }
    }
    
    function showTextareaMode() {
        var textareas = document.querySelectorAll('#enunciado, #resolucao');
        for (var i = 0; i < textareas.length; i++) {
            var textarea = textareas[i];
            textarea.style.display = 'block';
            textarea.style.height = '250px';
            textarea.style.border = '2px solid #007bff';
            textarea.style.backgroundColor = '#f8f9fa';
            
            if (!textarea.parentNode.querySelector('.safe-mode-indicator')) {
                var indicator = document.createElement('div');
                indicator.className = 'safe-mode-indicator';
                indicator.style.cssText = 'background: #007bff; color: white; padding: 5px; font-size: 12px; margin-bottom: 5px; border-radius: 3px;';
                indicator.innerHTML = '📝 Modo Texto Seguro (Editor: ' + textarea.id + ') - <a href="javascript:location.reload()" style="color: yellow;">Tentar TinyMCE novamente</a>';
                textarea.parentNode.insertBefore(indicator, textarea);
            }
        }
    }
    
    function showTextareaModeForEditor(editorId) {
        var textarea = document.getElementById(editorId);
        if (textarea) {
            textarea.style.display = 'block';
            textarea.style.height = '250px';
            textarea.style.border = '2px solid #ffc107';
            textarea.style.backgroundColor = '#fff3cd';
            
            var indicator = document.createElement('div');
            indicator.style.cssText = 'background: #ffc107; color: #856404; padding: 5px; font-size: 12px; margin-bottom: 5px; border-radius: 3px;';
            indicator.innerHTML = '⚠️ Editor ' + editorId + ' em modo texto (TinyMCE falhou)';
            textarea.parentNode.insertBefore(indicator, textarea);
        }
    }
    
    // Aguardar carregamento completo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar se não estamos enviando formulário
            if (!window.formSubmitting) {
                setTimeout(initTinyMCESafe, 1000);
            }
        });
    } else {
        // Verificar se não estamos enviando formulário
        if (!window.formSubmitting) {
            setTimeout(initTinyMCESafe, 1000);
        }
    }

    // Detectar mudanças de página para parar tentativas
    window.addEventListener('beforeunload', function() {
        window.formSubmitting = true;
        
        // Cancelar timeout de falha
        if (window.tinyMCEFailTimeout) {
            clearTimeout(window.tinyMCEFailTimeout);
        }
    });

    // Detectar quando página fica oculta (abas, minimizar, etc)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            window.formSubmitting = true;
        }
    });
    
})();