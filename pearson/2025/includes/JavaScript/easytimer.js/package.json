{"name": "easytimer.js", "version": "4.1.1", "description": "Timer/Chronometer/Countdown compatible with AMD and NodeJS", "main": "dist/easytimer.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/albert-gonzalez/easytimer.js"}, "directories": {"test": "test", "src": "src"}, "scripts": {"prestart": "npm install", "start": "http-server -a 0.0.0.0 -p 8001", "test": "mocha", "build": "rollup -c && rollup -c --environment optimize", "build-examples": "rollup -c rollup.config.examples.js"}, "keywords": ["javascript", "nodejs", "amd", "requirejs", "timer", "chronometer", "countdown", "mocha"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.7.7", "@babel/preset-env": "^7.7.7", "bootstrap": "^4.4.1", "chai": "^4.2.0", "eslint": "^6.8.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "http-server": "~0.12.0", "jquery": "^3.4.1", "mocha": "^6.2.2", "popper.js": "^1.16.0", "rollup": "^1.27.13", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-css-only": "^1.0.0", "rollup-plugin-license": "^0.13.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-uglify": "^6.0.4", "sinon": "^7.5.0"}, "babel": {"presets": [["@babel/env", {"modules": false}]]}}