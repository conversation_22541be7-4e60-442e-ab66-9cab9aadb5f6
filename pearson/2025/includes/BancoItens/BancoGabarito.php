<?php
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('BancoItens', null, true);

class BancoGabarito
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'numero' => null,
								'tipo' => 1,
								'valor' => null,
								'questao' => null );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoGabarito (BancoItens &$questao)
	{
		Core::registro('db')->query( sprintf('INSERT INTO banco_gabaritos (bg_banco_item) VALUES (%s)',
			Core::registro('db')->formatarValor($questao->obterID()) ) );

		$obj = new BancoGabarito ( Core::registro('db')->insert_id );
		
		$obj->fixarQuestao( $questao );
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE banco_gabaritos SET bg_numero = %s, bg_valor = %s, bg_tipo = %s, bg_banco_item = %s WHERE bg_id = %s',
				Core::registro('db')->formatarValor($this->_dados['numero']),
				Core::registro('db')->formatarValor($this->_dados['valor']),
				Core::registro('db')->formatarValor($this->_dados['tipo']),
				Core::registro('db')->formatarValor( $this->_dados['questao']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
						
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM banco_gabaritos WHERE bg_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}

	public function removerPeloItem ($id_item)
	{
		if ( $id_item != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM banco_gabaritos WHERE bg_banco_item = %s',
				Core::registro('db')->formatarValor($id_item) ) );
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar (BancoItens &$questao = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM banco_gabaritos WHERE bg_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNumero($row['bg_numero']);
				$this->fixarValor($row['bg_valor']);
				$this->fixarTipo($row['bg_tipo']);
				
				if ($questao == null) {
					$questao = new BancoItens($row['bg_banco_item']);
					$questao->carregar();
				}
				
				$this->fixarQuestao($questao);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNumero ($numero)
	{
		$this->_dados['numero'] = $numero;
	}
	
	public function obterNumero ()
	{
		return $this->_dados['numero'];
	}
	
	public function fixarValor ($valor)
	{
		$this->_dados['valor'] = $valor;
	}
	
	public function obterValor ()
	{
		return $this->_dados['valor'];
	}
	
	public function fixarTipo ($tipo)
	{
		$this->_dados['tipo'] = $tipo;
	}
	
	public function obterTipo ()
	{
		return $this->_dados['tipo'];
	}
	
	public function fixarQuestao (BancoItens &$questao)
	{
		$this->_dados['questao'] = $questao;
	}
	
	public function &obterQuestao ()
	{
		return $this->_dados['questao'];
	}
	
	public function podeRemover ()
	{
		return true;
	}
	
	static public function obterNumerosPelaQuestao (BancoItens &$questao) {
		$numeros = array();

		if ( $questao != null && $questao->obterID() != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT bg_numero FROM banco_gabaritos WHERE bg_banco_item = %s GROUP BY bg_tipo',
				Core::registro('db')->formatarValor( $questao->obterID() ) ) );
	
			if ($rs->num_rows) {
				while( $row = $rs->fetch_assoc() ) {
					if ( !in_array($row['bg_numero'], $numeros) )
					$numeros[] = $row['bg_numero'];
				}
			}
			$rs->free();
		}
		
		return $numeros;
	}
}

?>