<?php
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('BancoItens', null, true);

class BancoProposicao
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'numero' => null,
								'texto' => null,
								'questao' => null );
		
		$this->fixarID($id);
	}

	static public function &obterNovaProposicao (BancoItens &$questao)
	{
		Core::registro('db')->query( sprintf('INSERT INTO banco_proposicoes (bp_banco_item) VALUES (%s)',
			Core::registro('db')->formatarValor($questao->obterID()) ) );

		$obj = new BancoProposicao ( Core::registro('db')->insert_id );
		
		$obj->fixarQuestao( $questao );
		
		return $obj;
	}

	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE banco_proposicoes SET bp_numero = %s, bp_texto = %s, bp_banco_item = %s WHERE bp_id = %s',
				Core::registro('db')->formatarValor($this->_dados['numero']),
				Core::registro('db')->formatarValor($this->_dados['texto']),
				Core::registro('db')->formatarValor( $this->_dados['questao']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
						
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}

	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM banco_proposicoes WHERE bp_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}

	public function removerPeloItem ($id_item)
	{
		if ( $id_item != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM banco_proposicoes WHERE bp_banco_item = %s',
				Core::registro('db')->formatarValor($id_item) ) );
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}

	public function carregar (BancoItens &$questao = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM banco_proposicoes WHERE bp_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarPreposicao($row['bp_numero']);
				$this->fixarTexto($row['bp_texto']);
				
				if ($questao == null) {
					$questao = new BancoItens($row['bp_banco_item']);
					$questao->carregar();
				}				
				$this->fixarQuestao($questao);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNumero ($numero)
	{
		$this->_dados['numero'] = $numero;
	}
	
	public function obterNumero ()
	{
		return $this->_dados['numero'];
	}
	
	public function fixarTexto ($texto)
	{
		$this->_dados['texto'] = $texto;
	}
	
	public function obterTexto ()
	{
		return $this->_dados['texto'];
	}
	
	public function fixarQuestao (BancoItens &$questao)
	{
		$this->_dados['questao'] = $questao;
	}
	
	public function &obterQuestao ()
	{
		return $this->_dados['questao'];
	}
	
	public function podeRemover ()
	{
		return true;
	}
}

?>