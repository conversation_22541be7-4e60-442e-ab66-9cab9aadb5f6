<?php
if (!defined('CORE_INCLUIDO')) die('Ponto de entrada inválido');

Core::incluir('Disciplina', null, true);
Core::incluir('BancoGabarito', 'BancoItens/', true);
Core::incluir('EquivalenciaOpcao', null, true);
Core::incluir('MultiplaEscolha', 'TipoQuestao/', true);
Core::incluir('Somatoria', 'TipoQuestao/', true);
Core::incluir('Conteudo', null, true);
Core::incluir('BancoProposicao', 'BancoItens/', true);
Core::incluir('Habilidade', null, true);
Core::incluir('Instituicao', null, true);
Core::incluir('Universidade', null, true);

class BancoItens
{
	const FACIL = 'FACIL';
	const MEDIO = 'MEDIO';
	const DIFICIL = 'DIFICIL';

	const DISCURSIVA = 'DISCURSIVA';
	const SOMATORIO = 'SOMATORIO';
	const MULTIPLAESCOLHA = 'MULTIPLAESCOLHA';
	const ABERTA = 'ABERTA';

	private $_where_in = null;
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'identificador' => null,
								'enunciado' => null,
								'resolucao' => null,
								'pontos' => null,
								'tipo' => self::SOMATORIO,
								'disciplina' => null,
								'conteudo' => null,
								'divisao' => null,
  								'topico' => null,
  								'item' => null,
  								'subitem' => null,
								'nivel' => null,
								'fase_duracao' => null,
								'gabaritos' => array(),
								'proposicoes' => array(),
								'numero_proposicoes' => 1,
								'habilidades' => array(),
								'fonte' => null,
								'ano' => null,
								'ensino' => null,
								'regiao' => null,
								'uf' => null,
								'instituicoes' => array()
						     );

		$this->fixarID($id);
	}

	static public function &obterNovoItem ($identificador)
	{
		Core::registro('db')->query( sprintf('INSERT INTO banco_itens (bi_identificador) VALUES (%s)',
			Core::registro('db')->formatarValor($identificador) ) );

		$obj = new BancoItens ( Core::registro('db')->insert_id );

		$obj->fixarIdentificador( $identificador );

		return $obj;
	}

	public function salvar () {
		if ( $this->_dados['id'] != null ) {

			$habilidadesSQL = array();
			foreach ($this->_dados['habilidades'] as $habilidade) {
				if (Filtrador::natural($habilidade->obterID()))
					$habilidadesSQL[] = $habilidade->obterID();
			}
			$habilidadesSQL = Core::registro('db')->formatarValor( implode(',', $habilidadesSQL) );

			$instituicoesSQL = array();
			foreach ($this->_dados['instituicoes'] as $instituicoes) {
				if (Filtrador::natural($instituicoes->obterID()))
					$instituicoesSQL[] = $instituicoes->obterID();
			}
			$instituicoesSQL = Core::registro('db')->formatarValor( implode(',', $instituicoesSQL) );

			Core::registro('db')->query( sprintf('
				UPDATE banco_itens SET 
					bi_identificador = %s, 
					bi_enunciado = %s, 
					bi_resolucao = %s, 
					bi_pontos = %s, 
					bi_tipo = %s, 
					bi_disciplina = %s,	
					bi_conteudo = %s,
					bi_divisao = %s,
					bi_topico = %s,
					bi_item = %s,
					bi_subitem = %s,
					bi_nivel = %s, 
					bi_fase_duracao = %s, 
					bi_proposicoes = %s,
					bi_habilidades = %s,
					bi_fonte = %s,
					bi_ano = %s,
					bi_ensino = %s,
					bi_regiao = %s,
					bi_uf = %s,
					bi_instituicoes = %s
				WHERE 
					bi_id = %s',
				Core::registro('db')->formatarValor($this->_dados['identificador']),
				Core::registro('db')->formatarValor($this->_dados['enunciado']),
				Core::registro('db')->formatarValor($this->_dados['resolucao']),
				Core::registro('db')->formatarValor($this->_dados['pontos']),
				Core::registro('db')->formatarValor($this->_dados['tipo']),
				Core::registro('db')->formatarValor( @$this->_dados['disciplina']->obterID() ),
				Core::registro('db')->formatarValor( @$this->_dados['conteudo']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['divisao']),
				Core::registro('db')->formatarValor($this->_dados['topico']),
				Core::registro('db')->formatarValor($this->_dados['item']),
				Core::registro('db')->formatarValor($this->_dados['subitem']),
				Core::registro('db')->formatarValor($this->_dados['nivel']),
				Core::registro('db')->formatarValor($this->_dados['fase_duracao']),
				Core::registro('db')->formatarValor($this->_dados['numero_proposicoes']),
				$habilidadesSQL,
				Core::registro('db')->formatarValor( @$this->_dados['fonte']->id ),
				Core::registro('db')->formatarValor($this->_dados['ano']),
				Core::registro('db')->formatarValor($this->_dados['ensino']),
				Core::registro('db')->formatarValor($this->_dados['regiao']),
				Core::registro('db')->formatarValor($this->_dados['uf']),
				$instituicoesSQL,
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$retorno = (Core::registro('db')->errno == 0);

			foreach ($this->_dados['proposicoes'] as &$p) {
				if ( $p->obterID() != null && !$p->salvar() ) {
					$retorno = false;
				}
			}

			foreach ($this->_dados['gabaritos'] as &$g) {
				if ( !$g->salvar() ) {
					$retorno = false;
				}
			}

			return $retorno;
		}

		return false;
	}

	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM banco_itens WHERE bi_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$itemID = $this->_dados['id'];
			$rg = BancoGabarito::removerPeloItem($itemID);
			$rp = BancoProposicao::removerPeloItem($itemID);

			$this->fixarID(null);

			return (Core::registro('db')->errno == 0);
		}

		return false;
	}

	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$wi = '';
			$currentInst = Core::registro('instituicao')->obterID();
			if($currentInst != null){
				$wi = ' AND (bi_instituicoes IN ('.$currentInst.') OR bi_instituicoes IS NULL)';
			}

			$rs = Core::registro('db')->query( sprintf('SELECT * FROM banco_itens WHERE bi_id = %s %s',
				Core::registro('db')->formatarValor($this->_dados['id']),
				$wi ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$this->fixarIdentificador($row['bi_identificador']);
				$this->fixarEnunciado($row['bi_enunciado']);
				$this->fixarResolucao($row['bi_resolucao']);
				$this->fixarPontosQuestao($row['bi_pontos']);
				$this->fixarTipo($row['bi_tipo']);
				$this->fixarNivelDificuldade($row['bi_nivel']);
				$this->fixarFaseDuracao($row['bi_fase_duracao']);
				$this->fixarNumeroProposicoes($row['bi_proposicoes']);

				$this->fixarDisciplina( new Disciplina( $row['bi_disciplina'] ) );
				$this->obterDisciplina()->carregar();

				$this->fixarDivisao($row['bi_divisao']);
				$this->fixarTopico($row['bi_topico']);
				$this->fixarItem($row['bi_item']);
				$this->fixarSubItem($row['bi_subitem']);

				$this->fixarConteudo( new Conteudo( $row['bi_conteudo'] ) );
				$this->obterConteudo()->carregar();

				$habilidades = explode(',', $row['bi_habilidades']);
				foreach ($habilidades as $hID) {
					if (Filtrador::natural($hID))
						$this->adicionarHabilidade(new Habilidade($hID));
				}

				$this->fixarFonte( new Universidade( $row['bi_fonte'] ) );
				$this->obterFonte()->carregar();

				$this->fixarAno($row['bi_ano']);
				$this->fixarEnsino($row['bi_ensino']);
				$this->fixarRegiao($row['bi_regiao']);
				$this->fixarUF($row['bi_uf']);

				$this->carregarGabaritos();
				$this->carregarProposicoes();

				$instituicoes = explode(',', $row['bi_instituicoes']);
				foreach ($instituicoes as $iID) {
					if (Filtrador::natural($iID)){
						$inst = new Instituicao($iID);
						$inst->carregar();

						$this->adicionarInstituicoes($inst);
					}
				}

				return true;
			}
			$rs->free();
		}

		return false;
	}

	public function carregarTodas ()
	{
		$bitens = array();
		$wi = '';
		$currentInst = Core::registro('instituicao')->obterID();
		if($currentInst != null){
			$wi = ' AND (bi_instituicoes IN ('.$currentInst.') OR bi_instituicoes IS NULL)';
		}

		$rs = Core::registro('db')->query('SELECT * FROM banco_itens WHERE 1'.$wi);
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{
				$bitens[$row['bi_id']] = new BancoItens($row['bi_id']);
				$bitens[$row['bi_id']]->carregar();
			}
		}
		$rs->free();

		return $bitens;
	}

	public function fixarDivisao ($divisao)
	{
		$this->_dados['divisao'] = $divisao;
	}

	public function obterDivisao ()
	{
		return $this->_dados['divisao'];
	}

	public function fixarTopico ($topico)
	{
		$this->_dados['topico'] = $topico;
	}

	public function obterTopico ()
	{
		return $this->_dados['topico'];
	}

	public function fixarItem ($item)
	{
		$this->_dados['item'] = $item;
	}

	public function obterItem ()
	{
		return $this->_dados['item'];
	}

	public function fixarSubItem ($subitem)
	{
		$this->_dados['subitem'] = $subitem;
	}

	public function obterSubItem ()
	{
		return $this->_dados['subitem'];
	}

	public function fixarFonte (Universidade &$fonte)
	{
		$this->_dados['fonte'] = $fonte;
	}

	public function &obterFonte ()
	{
		return $this->_dados['fonte'];
	}

	public function fixarAno ($ano)
	{
		$this->_dados['ano'] = $ano;
	}

	public function obterAno ()
	{
		return $this->_dados['ano'];
	}

	public function fixarEnsino ($ensino)
	{
		$this->_dados['ensino'] = $ensino;
	}

	public function obterEnsino ()
	{
		return $this->_dados['ensino'];
	}

	public function fixarRegiao ($regiao)
	{
		$this->_dados['regiao'] = $regiao;
	}

	public function obterRegiao ()
	{
		return $this->_dados['regiao'];
	}

	public function fixarUF ($uf)
	{
		$this->_dados['uf'] = $uf;
	}

	public function obterUF ()
	{
		return $this->_dados['uf'];
	}

	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}

	public function obterID ()
	{
		return $this->_dados['id'];
	}

	public function fixarEnunciado ($enunciado)
	{
		$this->_dados['enunciado'] = $enunciado;
	}

	public function obterEnunciado ()
	{
		return $this->_dados['enunciado'];
	}

	public function fixarResolucao ($resolucao)
	{
		$this->_dados['resolucao'] = $resolucao;
	}

	public function obterResolucao ()
	{
		return $this->_dados['resolucao'];
	}

	public function fixarIdentificador ($identificador)
	{
		$this->_dados['identificador'] = $identificador;
	}

	public function obterIdentificador ()
	{
		return $this->_dados['identificador'];
	}

	public function fixarPontosQuestao ($pontuacao)
	{
		$this->_dados['pontos'] = $pontuacao;
	}

	public function obterPontosQuestao ()
	{
		return $this->_dados['pontos'];
	}

	public function fixarNumeroProposicoes ($numero)
	{
		$this->_dados['numero_proposicoes'] = (int) $numero;
	}

	public function obterNumeroProposicoes ()
	{
		return $this->_dados['numero_proposicoes'];
	}

	public function fixarTipo ($tipo)
	{
		$this->_dados['tipo'] = $tipo;
	}

	public function obterTipo ($nomeAmigavel = false)
	{
		if ($nomeAmigavel) {
			return self::nomeAmigavelTipo($this->_dados['tipo']);
		}

		return $this->_dados['tipo'];
	}

	public function fixarNivelDificuldade ($nivel)
	{
		$this->_dados['nivel'] = $nivel;
	}

	public function obterNivelDificuldade ($nomeAmigavel = false)
	{
		if ($nomeAmigavel) {
			return self::nomeAmigavelNivel($this->_dados['nivel']);
		}

		return $this->_dados['nivel'];
	}

	public function fixarFaseDuracao ($fase)
	{
		$this->_dados['fase_duracao'] = $fase;
	}

	public function obterFaseDuracao ($formatado = false)
	{
		if (!$formatado)
			return $this->_dados['fase_duracao'];
		else
			return $this->_dados['fase_duracao'] . 'º dia';
	}

	public function fixarDisciplina (Disciplina &$disciplina)
	{
		$this->_dados['disciplina'] = $disciplina;
	}

	public function &obterDisciplina ()
	{
		return $this->_dados['disciplina'];
	}

	public function fixarConteudo (Conteudo &$conteudo)
	{
		$this->_dados['conteudo'] = $conteudo;
	}

	public function &obterConteudo ()
	{
		return $this->_dados['conteudo'];
	}

	public function adicionarGabarito (BancoGabarito &$gabarito)
	{
		$this->_dados['gabaritos'][] = $gabarito;
	}

	public function &obterGabaritos ()
	{
		return $this->_dados['gabaritos'];
	}

	public function adicionarProposicao (BancoProposicao &$proposicao)
	{
		$this->_dados['proposicoes'][] = $proposicao;
	}

	public function &obterProposicoes ()
	{
		return $this->_dados['proposicoes'];
	}

	public function fixarHabilidades ($habilidades = array())
	{
		$this->_dados['habilidades'] = $habilidades;
	}

	public function adicionarHabilidade (Habilidade &$habilidade)
	{
		$this->_dados['habilidades'][] = $habilidade;
	}

	public function obterHabilidades ()
	{
		return $this->_dados['habilidades'];
	}

	public function fixarInstituicoes ($instituicoes = array())
	{
		$this->_dados['instituicoes'] = $instituicoes;
	}

	public function adicionarInstituicoes (Instituicao &$instituicoes)
	{
		$this->_dados['instituicoes'][] = $instituicoes;
	}

	public function obterInstituicoes ()
	{
		return $this->_dados['instituicoes'];
	}

	public function podeRemover ()
	{
		return true;
	}

	public function carregarHabilidades () {
		if ( $this->_dados['id'] != null && count($this->_dados['habilidades']) ) {
			$bufferHabilidades = array();
			foreach ($this->_dados['habilidades'] as $k => $habilidade)
				 $bufferHabilidades[$habilidade->obterID()] = $k;

			$rs = Core::registro('db')->query( sprintf('SELECT * FROM habilidades WHERE h_id IN (%s)',
				implode(',', array_keys($bufferHabilidades)) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$this->_dados['habilidades'][$bufferHabilidades[$row['h_id']]]->fixarNome($row['h_nome']);
					$this->_dados['habilidades'][$bufferHabilidades[$row['h_id']]]->fixarDescricao($row['h_descricao']);
					$this->_dados['habilidades'][$bufferHabilidades[$row['h_id']]]->fixarDisciplina($this->obterDisciplina());
				}
			}
			$rs->free();
		}
	}

	public function carregarInstituicoes () {
		if ( $this->_dados['id'] != null && count($this->_dados['instituicoes']) ) {
			$bufferInstituicoes = array();
			foreach ($this->_dados['instituicoes'] as $k => $instituicoes)
				 $bufferInstituicoes[$instituicoes->obterID()] = $k;

			$rs = Core::registro('db')->query( sprintf('SELECT * FROM instituicoes WHERE i_id IN (%s)',
				implode(',', array_keys($bufferInstituicoes)) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$this->_dados['instituicoes'][$bufferInstituicoes[$row['i_id']]] = new Instituicao($row['i_id']);
					$this->_dados['instituicoes'][$bufferInstituicoes[$row['i_id']]]->carregar();
				}
			}
			$rs->free();
		}
	}

	public function carregarGabaritos ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM banco_gabaritos WHERE bg_banco_item = %s ORDER BY bg_tipo',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
				{
					$obj = new BancoGabarito($row['bg_id']);
					$obj->fixarNumero($row['bg_numero']);
					$obj->fixarValor($row['bg_valor']);
					$obj->fixarTipo($row['bg_tipo']);
					$obj->fixarQuestao($this);

					$this->adicionarGabarito($obj);
				}
			}
			$rs->free();
		}
	}

	public function carregarProposicoes ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM banco_proposicoes WHERE bp_banco_item = %s ORDER BY bp_numero ASC',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc())
				{
					$obj = new BancoProposicao($row['bp_id']);
					$obj->fixarNumero($row['bp_numero']);
					$obj->fixarTexto($row['bp_texto']);
					$obj->fixarQuestao($this);

					$this->adicionarProposicao($obj);
				}
			}
			$rs->free();
		}
	}

	public function removerProposicoes ()
	{
		$retorno = true;

		foreach ($this->_dados['proposicoes'] as &$p) {
			if ( !$p->remover() ) {
				$retorno = false;
			}
		}

		return $retorno;
	}

	static public function obterArrayNiveisDificuldade ($plural = false)
	{
		return array( self::FACIL => self::nomeAmigavelNivel(self::FACIL, $plural),
					  self::MEDIO => self::nomeAmigavelNivel(self::MEDIO, $plural),
					  self::DIFICIL => self::nomeAmigavelNivel(self::DIFICIL, $plural) );
	}

	static public function nomeAmigavelNivel ($nivel, $plural = false)
	{
		switch ($nivel) {
			case self::FACIL:
				return !$plural ? 'Fácil' : 'Fáceis';
			case self::MEDIO:
				return !$plural ? 'Médio' : 'Médias';
			case self::DIFICIL:
				return !$plural ? 'Difícil' : 'Difíceis';
			default:
				return 'Desconhecido';
		}
	}

	static public function obterArrayTiposQuestao ()
	{
		return array( self::SOMATORIO => self::nomeAmigavelTipo(self::SOMATORIO),
					  self::MULTIPLAESCOLHA => self::nomeAmigavelTipo(self::MULTIPLAESCOLHA),
					  self::ABERTA => self::nomeAmigavelTipo(self::ABERTA),
					  self::DISCURSIVA => self::nomeAmigavelTipo(self::DISCURSIVA) );
	}

	static public function nomeAmigavelTipo ($tipo)
	{
		switch ($tipo) {
			case self::DISCURSIVA:
				return 'Discursiva';
			case self::SOMATORIO:
				return 'Somatório';
			case self::MULTIPLAESCOLHA:
				return 'Multipla-escolha';
			case self::ABERTA:
				return 'Aberta';
			default:
				return 'Desconhecido';
		}
	}

	public function questaoDeOpcao ()
	{
		return false;
		
		/*switch ( $this->_dados['tipo'] ) {
			case self::OPCAO_LINGUA_ESTRANGEIRA:
			case self::OPCAO_TIPO_PROVA:
			case self::OPCAO_CURSO_VESTIBULAR:
				return true;
			default:
				return false;
		}*/
	}
}

?>