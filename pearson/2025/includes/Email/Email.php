<?
if (!defined('CORE_INCLUIDO')) { exit(); }

class Email
{
	protected $_dados = array();
	protected $_diferencaTempoEntreEnvios = 0;
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'endereco' => null,
								'confirmado' => false,
								'chave_confirmacao' => null,
								'data_ultimo_envio' => null );
								
		$horas = (int) Core::diretiva('EMAIL:CONFIRMACAO:tempo_espera');
		if ( $horas <= 0 )
			$horas = 4;
		
		$this->_diferencaTempoEntreEnvios = 60 * 60 * $horas;
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoEmail ($endereco) {
		$obj = null;

		if ( self::obterIDPeloEndereco($endereco) !== false ) {
			$obj = new Email(null);
			return $obj;
		}

		Core::registro('db')->query( sprintf('INSERT INTO emails (e_endereco) VALUES (%s)',
			Core::registro('db')->formatarValor($endereco) ) );

		$obj = new Email(Core::registro('db')->insert_id);
		
		$obj->fixarEndereco($endereco);	
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			$confirmado = (int) $this->_dados['confirmado'];

			Core::registro('db')->query( sprintf('UPDATE emails SET e_endereco = %s, e_confirmado = %s, e_chave_confirmacao = %s, e_data_ultimo_envio = %s WHERE e_id = %s',
				Core::registro('db')->formatarValor($this->_dados['endereco']),
				Core::registro('db')->formatarValor($confirmado),
				Core::registro('db')->formatarValor($this->_dados['chave_confirmacao']),
				Core::registro('db')->formatarValor($this->_dados['data_ultimo_envio']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM emails WHERE e_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM emails WHERE e_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarEndereco($row['e_endereco']);
				$this->fixarEstadoDaConfirmacao( (bool) $row['e_confirmado'] );
				$this->fixarChaveConfirmacao( ( !empty($row['e_chave_confirmacao']) ? $row['e_chave_confirmacao'] : null ) );
				$this->fixarDataUltimoEnvio( $row['e_data_ultimo_envio'] );
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarEndereco ($endereco)
	{
		$this->_dados['endereco'] = $endereco;
	}
	
	public function obterEndereco ()
	{
		return $this->_dados['endereco'];
	}
	
	public function fixarEstadoDaConfirmacao ($confirmado)
	{
		$this->_dados['confirmado'] = $confirmado;
	}
	
	public function foiConfirmado ()
	{
		return $this->_dados['confirmado'];
	}
	
	public function fixarChaveConfirmacao ($chave)
	{
		$this->_dados['chave_confirmacao'] = $chave;
	}
	
	public function obterChaveConfirmacao ()
	{
		return $this->_dados['chave_confirmacao'];
	}

	public function fixarDataUltimoEnvio ($data_ultimo_envio) {
		$this->_dados['data_ultimo_envio'] = $data_ultimo_envio;
	}

	public function obterDataUltimoEnvio () {
		return (int) $this->_dados['data_ultimo_envio'];
	}
	
	public function podeSolicitarEmailVerificacaoEmail () {
		if ( $this->_dados['id'] != null && !$this->foiConfirmado() ) {
			if ( !Filtrador::md5Valido($this->obterChaveConfirmacao()) )
				return true;
				
			if ( (time() - $this->obterDataUltimoEnvio()) > $this->_diferencaTempoEntreEnvios )
				return true;
		}
		
		return false;
	}
	
	public function tentarEfetuarConfirmacao ($chave_confirmacao) {
		if ( $this->_dados['id'] != null && !$this->foiConfirmado() ) {
			if ( Filtrador::md5Valido($chave_confirmacao) && Filtrador::md5Valido($this->obterChaveConfirmacao()) ) {
				if ( strtolower($chave_confirmacao) == strtolower($this->obterChaveConfirmacao()) ) {
					$this->fixarEstadoDaConfirmacao( true );
					$this->fixarDataUltimoEnvio( null );
					$this->fixarChaveConfirmacao( null );
					return $this->salvar();
				}
			}			
		}
		
		return false;
	}

	static public function obterIDPeloEndereco ($endereco, $diferenciarLetras = false)
	{
		$emailSQL = $diferenciarLetras ? 'e_endereco' : 'LCASE(e_endereco)';
	
		$rs = Core::registro('db')->query( sprintf('SELECT e_id FROM emails WHERE %s = %s',
			$emailSQL,
			Core::registro('db')->formatarValor($endereco) ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			
			return $row['e_id'];
		}
		$rs->free();

		return false;
	}
	
}

?>