<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Questao', null, true);

class Gabarito
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'numero' => null,
								'tipo' => 1,
								'valor' => null,
								'questao' => null );
		
		$this->fixarID($id);
	}
	
	static public function &obterNovoGabarito (Questao &$questao)
	{
		Core::registro('db')->query( sprintf('INSERT INTO gabaritos (g_questao) VALUES (%s)',
			Core::registro('db')->formatarValor($questao->obterID()) ) );

		$obj = new Gabarito ( Core::registro('db')->insert_id );
		
		$obj->fixarQuestao( $questao );
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE gabaritos SET g_numero = %s, g_valor = %s, g_tipo = %s, g_questao = %s WHERE g_id = %s',
				Core::registro('db')->formatarValor($this->_dados['numero']),
				Core::registro('db')->formatarValor($this->_dados['valor']),
				Core::registro('db')->formatarValor($this->_dados['tipo']),
				Core::registro('db')->formatarValor( $this->_dados['questao']->obterID() ),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
						
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM gabaritos WHERE g_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar (Questao &$questao = null)
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT g_numero,g_valor,g_tipo,g_questao FROM gabaritos WHERE g_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNumero($row['g_numero']);
				$this->fixarValor($row['g_valor']);
				$this->fixarTipo($row['g_tipo']);
				
				if ($questao == null) {
					$questao = new Questao($row['g_questao']);
					$questao->carregar();
				}
				
				$this->fixarQuestao($questao);
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNumero ($numero)
	{
		$this->_dados['numero'] = $numero;
	}
	
	public function obterNumero ()
	{
		return $this->_dados['numero'];
	}
	
	public function fixarValor ($valor)
	{
		$this->_dados['valor'] = $valor;
	}
	
	public function obterValor ()
	{
		return $this->_dados['valor'];
	}
	
	public function fixarTipo ($tipo)
	{
		$this->_dados['tipo'] = $tipo;
	}
	
	public function obterTipo ()
	{
		return $this->_dados['tipo'];
	}
	
	public function fixarQuestao (Questao &$questao)
	{
		$this->_dados['questao'] = $questao;
	}
	
	public function &obterQuestao ()
	{
		return $this->_dados['questao'];
	}
	
	public function podeRemover ()
	{
		return true;
	}
	
	static public function obterNumerosPelaQuestao (Questao &$questao) {
		$numeros = array();

		if ( $questao != null && $questao->obterID() != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT g_numero FROM gabaritos WHERE g_questao = %s GROUP BY g_tipo',
				Core::registro('db')->formatarValor( $questao->obterID() ) ) );
	
			if ($rs->num_rows) {
				while( $row = $rs->fetch_assoc() ) {
					if ( !in_array($row['g_numero'], $numeros) )
					$numeros[] = $row['g_numero'];
				}
			}
			$rs->free();
		}
		
		return $numeros;
	}
	
	static public function obterQuestaoDeNumeracaoConflitante (Questao &$questao, $numero, $tipoProva)
	{
		$questaoConflitante = null;
				
		$simu = $questao->obterSimulado();		
		
		$questoesSQL = array();
		if ( $questao->obterID() != null ) {
			$questoesSQL[] = 'gabaritos.g_questao != ' . Core::registro('db')->formatarValor( $questao->obterID() );
		}
		
		// obtém questões de língua estrangeira
		if ( $questao->obterDisciplina()->obterLingua() && $simu->linguasCompartilham() ) {
			$rs = Core::registro('db')->query( sprintf(
					'SELECT questoes.q_id FROM questoes 
					INNER JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina AND disciplinas.d_lingua = \'1\' AND disciplinas.d_id != %s 
					WHERE q_simulado = %s AND q_fase_duracao = %s',
					Core::registro('db')->formatarValor( $questao->obterDisciplina()->obterID() ),
					Core::registro('db')->formatarValor( $questao->obterSimulado()->obterID() ),
					Core::registro('db')->formatarValor( $questao->obterFaseDuracao() ) ) );
	
			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$questoesSQL[] = 'gabaritos.g_questao != ' . Core::registro('db')->formatarValor( $row['q_id'] );
				}
			}
		}
		
		if ( count($questoesSQL) ) {
			$questoesSQL = ' AND ' . implode(' AND ', $questoesSQL);
		} else {
			$questoesSQL = '';
		} 
		
		// checa questões conflitantes
		$rs = Core::registro('db')->query( sprintf(
				'SELECT gabaritos.g_id, questoes.q_id, questoes.q_identificador FROM gabaritos 
				LEFT JOIN questoes ON questoes.q_id = gabaritos.g_questao 
				WHERE questoes.q_simulado = %s AND gabaritos.g_numero = %s AND questoes.q_fase_duracao = %s AND gabaritos.g_tipo = %s %s  
				GROUP BY q_id LIMIT 0,1',
				Core::registro('db')->formatarValor( $questao->obterSimulado()->obterID() ),
				Core::registro('db')->formatarValor( $numero ),
				Core::registro('db')->formatarValor( $questao->obterFaseDuracao() ),
				Core::registro('db')->formatarValor( $tipoProva ),
				$questoesSQL ) );

		if ($rs->num_rows) {
			$row = $rs->fetch_assoc();
			
			$questaoConflitante = new Questao( $row['q_id'] );
			$questaoConflitante->fixarIdentificador( $row['q_identificador'] );
		}

		return $questaoConflitante;
	}
}

?>