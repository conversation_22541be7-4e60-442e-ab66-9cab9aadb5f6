<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Instituicao', null, true);
Core::incluir('Aula', null, true);

class Disciplina
{
	private $_dados = array();

	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'nome_pequeno' => null,
								'lingua' => false,
								'instituicao' => 0,
								'ordem' => null);

		$this->fixarID($id);
	}

	static public function &obterNovaDisciplina ($nome, Instituicao &$inst)
	{
		Core::registro('db')->query( sprintf('INSERT INTO disciplinas (d_nome, d_instituicao) VALUES (%s, 0)',
			Core::registro('db')->formatarValor($nome)//,
			//Core::registro('db')->formatarValor($inst->obterID()) 
		) );

		$obj = new Disciplina ( Core::registro('db')->insert_id );

		$obj->fixarNome($nome);
		//$obj->fixarInstituicao($inst);

		return $obj;
	}

	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE disciplinas SET d_nome = %s, d_nome_pequeno = %s, d_lingua = %s, d_instituicao = 0, d_ordem = %s WHERE d_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['nome_pequeno']),
				Core::registro('db')->formatarValor( (int) $this->_dados['lingua'] ),
				//Core::registro('db')->formatarValor($this->_dados['instituicao']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['ordem']),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			return ( Core::registro('db')->errno == 0 );
		}

		return false;
	}

	public function remover ()//$bynome = false)
	{
		if ($this->_dados['id'] != null){// && $bynome === false) {
			Core::registro('db')->query( sprintf('DELETE FROM disciplinas WHERE d_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			$this->fixarID(null);

			return (Core::registro('db')->errno == 0);
		}/*
		else{
			Core::registro('db')->query( sprintf('DELETE FROM disciplinas WHERE d_nome = %s',
				Core::registro('db')->formatarValor($bynome) ) );

			$this->fixarID(null);

			return (Core::registro('db')->errno == 0);
		}*/

		return false;
	}

	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM disciplinas WHERE d_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$this->fixarNome($row['d_nome']);
				$this->fixarNomePequeno($row['d_nome_pequeno']);
				$this->fixarLingua($row['d_lingua']);
				$this->fixarOrdem($row['d_ordem']);

				//if ((int) $row['d_instituicao'] == (int) Core::registro('instituicao')->obterID()) {
				//	$this->fixarInstituicao( Core::registro('instituicao') );
				//} else {
				//	$inst = new Instituicao($row['d_instituicao']);
				//
				//	if ($inst->carregar())
				//		$this->fixarInstituicao( $inst );
				//}

				return true;
			}
			$rs->free();
		}

		return false;
	}

	public function fixarOrdem ($ordem)
	{
		$this->_dados['ordem'] = $ordem;
	}

	public function obterOrdem ()
	{
		return $this->_dados['ordem'];
	}

	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}

	public function obterID ()
	{
		return $this->_dados['id'];
	}

	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}

	public function fixarNomePequeno ($nome_pequeno) {
		$this->_dados['nome_pequeno'] = $nome_pequeno;
	}

	public function obterNomePequeno () {
		return $this->_dados['nome_pequeno'];
	}

	public function obterNome ()
	{
		return $this->_dados['nome'];
	}

	public function fixarLingua ($lingua)
	{
		$this->_dados['lingua'] = (bool) $lingua;
	}

	public function obterLingua ()
	{
		return $this->_dados['lingua'];
	}

	public function linguaEstrangeira ()
	{
		return (bool) $this->_dados['lingua'];
	}

	public function fixarInstituicao (Instituicao &$inst)
	{
		$this->_dados['instituicao'] = 0;
	}

	public function &obterInstituicao ()
	{
		return $this->_dados['instituicao'];
	}

	// se tem questoes ou aulas cadastradas não pode remover
	public function podeRemover ()
	{
		$total = 0;

		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM questoes WHERE q_disciplina = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();


			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM simulados_conteudos WHERE sc_disciplina = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();


			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM aulas WHERE a_disciplina = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();


			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM habilidades WHERE h_disciplina = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();

				$total += $row['total'];
			}
			$rs->free();
		}

		return ( $total == 0 );
	}

	public function validarInstituicao ()
	{
		return true;

		if ( $this->_dados['id'] != null && $this->_dados['instituicao'] != null ) {
			if ( Core::modulo('_seletor_instituicoes')->podeSelecionarInstituicao() || Core::registro('instituicao')->obterID() == $this->_dados['instituicao']->obterID() ) {
				return true;
			}
		}

		return false;
	}

	public function obterAulas ()
	{
		$aulas = array();

		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf(
				'SELECT aulas.a_id, turmas.t_nome FROM aulas
				LEFT JOIN turmas ON turmas.t_id = a_turma
				WHERE a_disciplina = %s ORDER BY t_nome ASC',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );

			if ($rs->num_rows) {
				while ($row = $rs->fetch_assoc()) {
					$aula = new Aula($row['a_id']);
					$aula->carregar();

					$aulas[] = $aula;
				}
			}
			$rs->free();
		}

		return $aulas;
	}

	static public function obterArrayDisciplinasParaFormulario ($somenteLinguas = false, $mostrarTextoLingua = true)
	{
		$disciplinas = array();

		$rs = Core::registro('db')->query('SELECT * FROM disciplinas WHERE d_instituicao = 0 ORDER BY d_ordem ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ( $somenteLinguas && $row['d_lingua'] != 1 )
					continue;

				if($row['d_nome'] != 'Produção de Texto'){
					$disciplinas[$row['d_id']] = $row['d_nome'] . ($mostrarTextoLingua && $row['d_lingua'] == 1 ? ' (Língua estrangeira)' : '');
				}

			}
		}
		$rs->free();

		return $disciplinas;
	}

	static public function obterArrayDisciplinasPequenasParaFormulario ($somenteLinguas = false, $mostrarTextoLingua = true)
	{
		$disciplinas = array();

		$rs = Core::registro('db')->query('SELECT * FROM disciplinas WHERE d_instituicao = 0 ORDER BY d_ordem ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ( $somenteLinguas && $row['d_lingua'] != 1 )
					continue;

				if($row['d_nome_pequeno'] != 'Produção de Texto'){
					$disciplinas[$row['d_id']] = $row['d_nome_pequeno'] . ($mostrarTextoLingua && $row['d_lingua'] == 1 ? ' (Língua estrangeira)' : '');
				}

			}
		}
		$rs->free();

		return $disciplinas;
	}

	static public function obterArrayDisciplinasParaFormularioHabilidade ($somenteLinguas = false, $mostrarTextoLingua = true)
	{
		$disciplinas = array();

		$rs = Core::registro('db')->query( sprintf('SELECT * FROM disciplinas WHERE d_instituicao = 0 ORDER BY d_ordem ASC, d_lingua ASC, d_nome ASC',
			Core::registro('db')->formatarValor( Core::modulo('_perfil_falso')->obterInstituicaoDoPerfil()->obterID() ) 
		) );
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				if ( $somenteLinguas && $row['d_lingua'] != 1 )
					continue;

				$disciplinas[$row['d_id']] = $row['d_nome'] . ($mostrarTextoLingua && $row['d_lingua'] == 1 ? ' (Língua estrangeira)' : '');
			}
		}
		$rs->free();

		return $disciplinas;
	}

	static public function obterArrayDisciplinasPorSimulado ($idSimulado)
	{
		$disciplinas = array();

		$rs = Core::registro('db')->query( sprintf('SELECT distinct d.d_id, d.d_nome FROM questoes q, disciplinas d
                      WHERE q.q_disciplina = d.d_id and q.q_simulado = %s ORDER BY d.d_ordem ASC, d_lingua ASC, d_nome ASC',
			Core::registro('db')->formatarValor($idSimulado) ) );
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$disciplinas[$row['d_id']] = $row['d_nome'];
			}
		}
		$rs->free();

		return $disciplinas;
	}

	static public function obterArrayDisciplinasPequenasPorSimulado ($idSimulado)
	{
		$disciplinas = array();

		$rs = Core::registro('db')->query( sprintf('SELECT distinct d.d_id, d.d_nome, d.d_nome_pequeno FROM questoes q, disciplinas d
                      WHERE q.q_disciplina = d.d_id and q.q_simulado = %s ORDER BY d.d_ordem ASC, d_lingua ASC, d_nome ASC',
			Core::registro('db')->formatarValor($idSimulado) ) );
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$disciplinas[$row['d_id']] = $row['d_nome_pequeno'];
			}
		}
		$rs->free();

		return $disciplinas;
	}

	static public function obterArrayDisciplinasPorEscola ($idInstituicao)
	{
		$disciplinas = array();
		$rs = Core::registro('db')->query(sprintf('SELECT d_id, d_nome FROM disciplinas WHERE d_instituicao = 0;',
			Core::registro('db')->formatarValor($idInstituicao)
		));

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$disciplinas[$row['d_id']] = $row['d_nome'];
			}
		}
		$rs->free();

		return $disciplinas;
	}

	static public function obterArrayTodasDisciplinas ()
	{
		$disciplinas = array();

		$rs = Core::registro('db')->query('SELECT * FROM disciplinas ORDER BY d_ordem ASC, d_lingua ASC, d_nome ASC');

		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc()) {
				$disciplinas[$row['d_id']] = $row['d_nome'].($row['d_lingua'] == 1 ? ' (Língua estrangeira)' : '');
			}
		}
		$rs->free();

		return $disciplinas;
	}
}

?>