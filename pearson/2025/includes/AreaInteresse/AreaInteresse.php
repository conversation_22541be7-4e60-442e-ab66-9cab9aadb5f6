<?
if (!defined('CORE_INCLUIDO')) { exit(); }

Core::incluir('Instituicao', null, true);

class AreaInteresse
{
	private $_dados = array();
	
	public function __construct ($id)
	{
		$this->_dados = array(	'id' => null,
								'nome' => null,
								'instituicao' => null);
		
		$this->fixarID($id);
	}
	
	static public function &obterNovaAreaInteresse ($nome, Instituicao &$inst)
	{
		Core::registro('db')->query( sprintf('INSERT INTO areas_interesse (a_nome, a_instituicao) VALUES (%s, %s)', 
			Core::registro('db')->formatarValor($nome),
			Core::registro('db')->formatarValor($inst->obterID()) ) );

		$obj = new AreaInteresse ( Core::registro('db')->insert_id );
		
		$obj->fixarNome($nome);
		$obj->fixarInstituicao($inst);
		
		return $obj;
	}
	
	public function salvar ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('UPDATE areas_interesse SET a_nome = %s, a_instituicao = %s WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['nome']),
				Core::registro('db')->formatarValor($this->_dados['instituicao']->obterID()),
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			return ( Core::registro('db')->errno == 0 );
		}
		
		return false;
	}
	
	public function remover ()
	{
		if ( $this->_dados['id'] != null ) {
			Core::registro('db')->query( sprintf('DELETE FROM areas_interesse WHERE a_id = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
			
			$this->fixarID(null);
			
			return (Core::registro('db')->errno == 0);
		}
		
		return false;
	}
	
	public function carregar ()
	{
		if ( $this->_dados['id'] != null ) {			
			$rs = Core::registro('db')->query( sprintf('SELECT * FROM areas_interesse WHERE a_id = %s', 
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$this->fixarNome($row['a_nome']);
				
				$inst = new Instituicao($row['a_instituicao']);
				
				if ($inst->carregar()) {
					$this->fixarInstituicao( $inst );
				}
				
				return true;
			}
			$rs->free();
		}
		
		return false;
	}
	
	public function fixarID ($id)
	{
		$this->_dados['id'] = $id;
	}
	
	public function obterID ()
	{
		return $this->_dados['id'];
	}
	
	public function fixarNome ($nome)
	{
		$this->_dados['nome'] = $nome;
	}
	
	public function obterNome ()
	{
		return $this->_dados['nome'];
	}
	
	public function fixarInstituicao (Instituicao &$inst)
	{
		$this->_dados['instituicao'] = $inst;
	}
	
	public function &obterInstituicao ()
	{
		return $this->_dados['instituicao'];
	}
	
	// não pode ter cursos de vestibular nessa área de interesse
	public function podeRemover ()
	{
		$total = 0;
		
		if ( $this->_dados['id'] != null ) {
			$rs = Core::registro('db')->query( sprintf('SELECT COUNT(0) AS total FROM cursos_vestibular WHERE c_area_interesse = %s',
				Core::registro('db')->formatarValor($this->_dados['id']) ) );
				
			if ($rs->num_rows) {
				$row = $rs->fetch_assoc();
				
				$total += $row['total'];
			}
			$rs->free();
		}
		
		return ( $total == 0 );
	}
	
	public function validarInstituicao ()
	{
		if ( $this->_dados['id'] != null && $this->_dados['instituicao'] != null ) {
			if ( Core::modulo('_seletor_instituicoes')->podeSelecionarInstituicao() || Core::registro('instituicao')->obterID() == $this->_dados['instituicao']->obterID() ) {
				return true;
			}
		}
		
		return false;
	}
	
	static public function obterArrayAreasInteresseParaFormulario ()
	{
		$areas = array();
		
		$rs = Core::registro('db')->query( sprintf('SELECT * FROM areas_interesse WHERE a_instituicao = %s ORDER BY a_nome ASC',
			Core::registro('db')->formatarValor( Core::registro('instituicao')->obterID() ) ) );
			
		if ($rs->num_rows) {
			while ($row = $rs->fetch_assoc())
			{			
				$areas[$row['a_id']] = $row['a_nome'];
			}
		}
		$rs->free();
		
		return $areas;
	}	
}

?>