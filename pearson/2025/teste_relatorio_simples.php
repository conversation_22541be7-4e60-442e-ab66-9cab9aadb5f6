<?php
/**
 * Teste Simples para verificar se o relatório abre
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('max_execution_time', 10); // 10 segundos máximo

echo "=== TESTE SIMPLES - RELATÓRIO TURMAS ===\n";
echo "Verificando se o relatório abre sem timeout...\n\n";

try {
    $inicio = microtime(true);
    
    // Incluir o core
    require_once 'includes/core.php';
    echo "✓ Core incluído (" . number_format(microtime(true) - $inicio, 3) . "s)\n";
    
    // Incluir dependências básicas
    Core::incluir('RelatorioListagem', null, true);
    echo "✓ RelatorioListagem incluído (" . number_format(microtime(true) - $inicio, 3) . "s)\n";
    
    // Incluir arquivo do relatório
    include_once 'modulos/relatorios/pf_rendimento_instituicao_series_turmas_por_disciplina_tab/pf_rendimento_instituicao_series_turmas_por_disciplina_tab.relatorio.php';
    echo "✓ Arquivo do relatório incluído (" . number_format(microtime(true) - $inicio, 3) . "s)\n";
    
    // Instanciar classe
    $relatorio = new RLPFRendimentoInstituicaoSeriesTurmasPorDisciplinaTab();
    echo "✓ Classe instanciada (" . number_format(microtime(true) - $inicio, 3) . "s)\n";
    
    $tempoTotal = microtime(true) - $inicio;
    echo "\n=== SUCESSO ===\n";
    echo "Relatório pode ser carregado em " . number_format($tempoTotal, 3) . "s\n";
    echo "Agora teste acessar o relatório via web interface.\n";
    
} catch (Exception $e) {
    echo "✗ ERRO: " . $e->getMessage() . "\n";
    echo "Linha: " . $e->getLine() . "\n";
    echo "Arquivo: " . $e->getFile() . "\n";
} catch (Error $e) {
    echo "✗ ERRO FATAL: " . $e->getMessage() . "\n";
    echo "Linha: " . $e->getLine() . "\n";
    echo "Arquivo: " . $e->getFile() . "\n";
}

?>
