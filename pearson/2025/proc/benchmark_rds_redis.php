<?php 
/**
 * Script de Benchmark - MySQL vs Redis
 * Mede tempos de execução para comparar performance
 * Compatível com estrutura de cache do mySQL_DB
 * 
 * Uso: php benchmark_query.php
 */

// Configurações
$config = [
    'mysql' => [
        'host' => 'av-rede.ctrnya9tildy.us-west-2.rds.amazonaws.com',
        'username' => 'avredeUserMDb',
        'passwd' => 'x4g5!0lb1neGDbPS',
        'dbname' => 'avaliare_db_pearson_dev_2025',
        'port' => '3306'
    ],
    'redis' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => 'avRede@red1sIn2025t2oUseRD$',
        'timeout' => 5
    ]
];

// Query de exemplo para teste
$test_query = array();
$test_query[] = "SELECT g_id FROM grupos";
$test_query[] = "SELECT p_nome, p_acesso_publico, p_grupos, p_grupos_negados, p_usuarios, p_usuarios_negados FROM permissoes";
$test_query[] = "SELECT d_nome, d_valor, d_usuario FROM diretivas WHERE d_usuario IS NULL";
$test_query[] = "SELECT i_id,i_nome FROM instituicoes ORDER BY i_nome ASC";
$test_query[] = "SELECT modulos.m_nome, modulos.m_classe, modulos.m_padrao, acoes.a_nome, acoes.a_funcao, acoes.a_padrao, permissoes.p_nome FROM modulos JOIN acoes ON acoes.a_modulo = modulos.m_id LEFT JOIN permissoes ON permissoes.p_id = acoes.a_permissao";
$test_query[] = "SELECT menu_principal.*, permissoes.p_nome FROM menu_principal LEFT JOIN permissoes ON permissoes.p_id = menu_principal.m_permissao ORDER BY m_menupai ASC, m_ordem ASC";
$test_query[] = "SELECT s_id, s_nome, s_data FROM simulados WHERE s_secao_rels='relatorios' ORDER BY s_ordem ASC";
$test_query[] = "SELECT i_id,i_nome,i_municipio,i_regiao FROM instituicoes ORDER BY i_nome ASC";
$test_query[] = "SELECT * FROM disciplinas WHERE d_instituicao = 0 ORDER BY d_ordem ASC";
$test_query[] = "SELECT * FROM relatorios INNER JOIN relatorios_grupos ON relatorios_grupos.rg_id = relatorios.r_grupo LEFT JOIN permissoes ON permissoes.p_id = r_permissao WHERE r_oculto != '1' AND r_secao = 'relatorios' ORDER BY rg_ordem ASC, r_ordem ASC, r_nome ASC";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '1'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '2'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '3'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '4'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '5'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '6'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '7'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '8'";
$test_query[] = "SELECT s_nome FROM series WHERE s_id = '9'";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '4';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '63';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '8';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '69';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '75';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '109';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1246';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1385';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1389';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '38';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1348';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1373';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '44';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '50';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1243';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '217';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1278';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '216';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1342';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1343';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '203';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1352';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '199';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '56';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1380';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1271';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '105';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '201';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '223';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1270';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '224';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '196';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1328';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '193';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1367';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1252';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1253';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1254';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1321';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1325';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '170';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '200';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1345';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1371';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1331';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '106';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1335';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1294';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1284';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1289';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1299';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1316';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1355';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1360';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1356';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1238';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '11';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '125';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '207';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '210';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1309';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1234';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1233';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1235';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1239';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1272';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1276';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1277';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1283';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1273';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1279';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1244';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1245';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '116';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '149';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '154';";
$test_query[] = "SELECT modulos.m_nome, acoes.a_nome FROM modulos, acoes WHERE acoes.a_modulo = modulos.m_id AND acoes.a_id = '1310';";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '22' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '11' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '23' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '12' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '3' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '13' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '4' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '14' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '5' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '7' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '15' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '6' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '16' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '8' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '17' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '9' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '18' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '10' LIMIT 1;";
$test_query[] = "SELECT s1.*, (UNIX_TIMESTAMP() > s1.s_data_inicio_inscricao AND UNIX_TIMESTAMP() < s1.s_data_fim_inscricao) AS inscricoes_abertas, COALESCE(si.numero_inscritos, 0) AS numero_inscritos FROM simulados s1 LEFT JOIN ( SELECT si_simulado, COUNT(*) AS numero_inscritos FROM simulados_inscricoes GROUP BY si_simulado ) si ON s1.s_id = si.si_simulado WHERE s1.s_id = '19' LIMIT 1;";

$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '11' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '11' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '12' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '12' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '13' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '13' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '14' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '14' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '15' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '15' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '16' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '16' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '17' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '17' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '18' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '18' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '19' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '19' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '25' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '25' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '26' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '26' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '27' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '27' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '28' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '28' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '29' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '29' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '30' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '30' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '31' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '31' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '32' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '32' ORDER BY g_tipo";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '33' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC";
$test_query[] = "SELECT * FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '33' ORDER BY g_tipo";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '11' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '11'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '12' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '12'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '13' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '13'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '14' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '14'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '15' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '15'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '16' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '16'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '17' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '17'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '18' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '18'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '19' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '19'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '25' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '25'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '26' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '26'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '27' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '27'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '28' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '28'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '29' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '29'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '30' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '30'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '31' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '31'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '32' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '32'";
$test_query[] = "SELECT COUNT(0) as numero_inscritos FROM simulados_inscricoes INNER JOIN alunos ON alunos.a_id = simulados_inscricoes.si_aluno INNER JOIN turmas ON turmas.t_id = alunos.a_turma WHERE si_simulado = '33' LIMIT 0,1";
$test_query[] = "SELECT q_id, q_fase_duracao, d_id, d_lingua FROM questoes LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina WHERE q_simulado = '33'";

$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '11' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '12' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '13' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '14' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '15' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '16' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '17' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '18' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '19' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '25' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '26' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '27' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '28' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '29' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '30' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '31' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '32' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";
$test_query[] = "SELECT gabaritos.g_questao, gabaritos.g_tipo, gabaritos.g_numero, questoes.q_id, questoes.q_tipo, questoes.q_resolucao, questoes.q_proposicoes, questoes.q_identificador, questoes.q_enunciado, questoes.q_pontos, questoes.q_nivel, questoes.q_fase_duracao, questoes.q_anulada, questoes.q_habilidades, disciplinas.d_id, disciplinas.d_nome, disciplinas.d_nome_pequeno, disciplinas.d_lingua, simulados_conteudos.sc_id, simulados_conteudos.sc_nome FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao LEFT JOIN disciplinas ON disciplinas.d_id = questoes.q_disciplina LEFT JOIN simulados_conteudos ON simulados_conteudos.sc_id = questoes.q_conteudo WHERE q_simulado = '33' AND g_numero IS NOT NULL ORDER BY q_fase_duracao ASC, g_numero ASC;";

$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '11' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '12' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '13' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '14' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '15' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '16' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '17' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '18' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '19' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '25' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '26' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '27' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '28' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '29' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '30' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '31' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '32' ORDER BY g_tipo;";
$test_query[] = "SELECT g_id, g_numero, g_valor, g_tipo, g_questao FROM gabaritos INNER JOIN questoes ON questoes.q_id = gabaritos.g_questao WHERE questoes.q_simulado = '33' ORDER BY g_tipo;";

class BenchmarkManager 
{
    private $mysql = null;
    private $redis = null;
    private $config = [];
    private $results = [];
    private $dbname = '';
    
    public function __construct($config) 
    {
        $this->config = $config;
        $this->dbname = $config['mysql']['dbname'];
        $this->connectMySQL();
        $this->connectRedis();
    }
    
    private function connectMySQL() 
    {
        try {
            $start = microtime(true);
            
            $this->mysql = new mysqli(
                $this->config['mysql']['host'],
                $this->config['mysql']['username'],
                $this->config['mysql']['passwd'],
                $this->config['mysql']['dbname'],
                $this->config['mysql']['port']
            );
            
            if ($this->mysql->connect_errno) {
                throw new Exception("Erro MySQL: " . $this->mysql->connect_error);
            }
            
            $this->mysql->set_charset("utf8");
            $connectionTime = (microtime(true) - $start) * 1000;
            
            $this->log("✓ MySQL conectado em " . number_format($connectionTime, 2) . "ms");
            
        } catch (Exception $e) {
            $this->log("✗ ERRO MySQL: " . $e->getMessage());
            exit(1);
        }
    }
    
    private function connectRedis() 
    {
        try {
            $start = microtime(true);
            
            $this->redis = new Redis();
            $connected = $this->redis->connect(
                $this->config['redis']['host'], 
                $this->config['redis']['port'], 
                $this->config['redis']['timeout']
            );
            
            if (!$connected) {
                throw new Exception("Falha na conexão");
            }
            
            if (!empty($this->config['redis']['password'])) {
                $this->redis->auth($this->config['redis']['password']);
            }
            
            $this->redis->ping();
            $connectionTime = (microtime(true) - $start) * 1000;
            
            $this->log("✓ Redis conectado em " . number_format($connectionTime, 2) . "ms");
            
        } catch (Exception $e) {
            $this->log("✗ ERRO Redis: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Limpa todos os caches Redis antes de iniciar o benchmark
     */
    private function flushRedisCache() 
    {
        try {
            $this->log("\n=== LIMPANDO CACHE REDIS ===");
            $start = microtime(true);
            
            $result = $this->redis->flushAll();
            
            if ($result) {
                $time = (microtime(true) - $start) * 1000;
                $this->log("✓ Cache Redis limpo com sucesso em " . number_format($time, 2) . "ms");
            } else {
                $this->log("✗ Falha ao limpar cache Redis");
            }
            
        } catch (Exception $e) {
            $this->log("✗ ERRO ao limpar cache: " . $e->getMessage());
        }
    }
    
    public function runMySQLBenchmark($query, $iterations = 1) 
    {
        // Limpa cache Redis antes de iniciar o benchmark
        //$this->flushRedisCache();
        
        $this->log("\n=== BENCHMARK MySQL ==="); 
        $times = [];
        $totalRows = 0;
        
        for ($i = 1; $i <= $iterations; $i++) {
            $this->log("Execução $i/$iterations...");
            
            $start = microtime(true);
            $result = $this->mysql->query($query);
            $queryTime = microtime(true) - $start;
            
            if (!$result) {
                $this->log("✗ Erro na query: " . $this->mysql->error);
                continue;
            }
            
            // Processa todos os resultados para simular uso real
            $rows = [];
            $fetchStart = microtime(true);
            while ($row = $result->fetch_assoc()) {
                $rows[] = $row;
            }
            $fetchTime = microtime(true) - $fetchStart;
            $totalTime = microtime(true) - $start;
            
            $rowCount = count($rows);
            $totalRows = $rowCount;
            
            $times[] = [
                'query_time' => $queryTime * 1000,
                'fetch_time' => $fetchTime * 1000,
                'total_time' => $totalTime * 1000,
                'rows' => $rowCount
            ];
            
            $this->log("  Query: " . number_format($queryTime * 1000, 2) . "ms");
            $this->log("  Fetch: " . number_format($fetchTime * 1000, 2) . "ms");
            $this->log("  Total: " . number_format($totalTime * 1000, 2) . "ms");
            $this->log("  Linhas: $rowCount");
            
            $result->free();
            
            if ($i < $iterations) {
                usleep(100000); // 100ms entre execuções
            }
        }
        
        $this->results['mysql'] = [
            'times' => $times,
            'total_rows' => $totalRows,
            'iterations' => $iterations
        ];
        
        $this->showMySQLStats();
        return $rows ?? []; // Retorna última execução para cache
    }
    
    /**
     * Cria cache usando a mesma estrutura e nomenclatura do mySQL_DB
     */
    public function cacheData($query, $data) 
    {
        $this->log("\n=== CRIANDO CACHE (Compatível com mySQL_DB) ===");
        
        $start = microtime(true);
        
        // Usa o mesmo padrão de chave do mySQL_DB: "query[database]:" + md5(query)
        $normalizedQuery = $this->normalizeQuery($query);
        $cacheKey = "query[" . $this->dbname . "]:" . md5($normalizedQuery);
        
        // Extrai tabelas da query usando o mesmo método do mySQL_DB
        $tables = $this->extractTablesFromQuery($query);
        
        // Usa a mesma estrutura de dados do mySQL_DB
        $now = time();
        $cacheData = [
            'timestamp' => $now,
            'tables' => $tables,
            'data' => $data
        ];
        
        $encoded = microtime(true);
        $jsonData = json_encode($cacheData);
        
        if ($jsonData === false) {
            $this->log("✗ Falha ao codificar dados para JSON: " . json_last_error_msg());
            return false;
        }
        
        $saveStart = microtime(true);
        $saved = $this->redis->set($cacheKey, $jsonData);
        
        if (!$saved) {
            $this->log("✗ Falha ao salvar no Redis");
            return false;
        }
        
        // Define expiração (mesmo tempo do mySQL_DB: 3600 segundos)
        $this->redis->expire($cacheKey, 3600);
        
        // Cria tags para tabelas (mesmo sistema do mySQL_DB)
        foreach ($tables as $table) {
            try {
                $tagKey = "tag:table:$table";
                $this->redis->sAdd($tagKey, $cacheKey);
                $this->redis->expire($tagKey, 3600);
                
                $this->log("  Tag criada: $tagKey -> $cacheKey");
            } catch (Exception $e) {
                $this->log("  ⚠️  Erro ao criar tag para tabela $table: " . $e->getMessage());
            }
        }
        
        // Armazena tags da chave (mesmo sistema do mySQL_DB)
        try {
            $tagsForKeyKey = "tags_for_key:$cacheKey";
            foreach ($tables as $table) {
                $this->redis->sAdd($tagsForKeyKey, "tag:table:$table");
            }
            $this->redis->expire($tagsForKeyKey, 3600);
        } catch (Exception $e) {
            $this->log("  ⚠️  Erro ao armazenar tags da chave: " . $e->getMessage());
        }
        
        // Armazena metadados separadamente (mesmo sistema do mySQL_DB)
        try {
            $metaKey = "meta:$cacheKey";
            $metaData = [
                'timestamp' => $now,
                'tables' => $tables,
                'query' => $query
            ];
            $this->redis->set($metaKey, json_encode($metaData));
            $this->redis->expire($metaKey, 3600);
        } catch (Exception $e) {
            $this->log("  ⚠️  Erro ao armazenar metadados: " . $e->getMessage());
        }
        
        $totalTime = (microtime(true) - $start) * 1000;
        $encodeTime = ($encoded - $start) * 1000;
        $saveTime = (microtime(true) - $saveStart) * 1000;
        
        $this->log("✓ Cache criado com sucesso (compatível com mySQL_DB)");
        $this->log("  Chave: $cacheKey");
        $this->log("  Tabelas: " . implode(', ', $tables));
        $this->log("  Timestamp: " . date('Y-m-d H:i:s', $now));
        $this->log("  Linhas: " . count($data));
        $this->log("  Codificação: " . number_format($encodeTime, 2) . "ms");
        $this->log("  Salvamento: " . number_format($saveTime, 2) . "ms");
        $this->log("  Total: " . number_format($totalTime, 2) . "ms");
        
        return $cacheKey;
    }
    
    /**
     * Extrai tabelas da query usando o mesmo método do mySQL_DB
     */
    private function extractTablesFromQuery($query) {
        $tables = [];
        
        // Remove comentários e strings
        $cleanQuery = preg_replace('/--.*$|\/\*.*?\*\//m', '', $query);
        $cleanQuery = preg_replace('/"[^"]*"|\'[^\']*\'/', '""', $cleanQuery);
        
        // Padrões mais robustos (mesmos do mySQL_DB)
        $patterns = [
            '/\bFROM\s+(?:`?(\w+)`?\.)?`?(\w+)`?(?:\s+(?:AS\s+)?`?\w+`?)?/i',
            '/\bJOIN\s+(?:`?(\w+)`?\.)?`?(\w+)`?(?:\s+(?:AS\s+)?`?\w+`?)?/i',
            '/\bINTO\s+(?:`?(\w+)`?\.)?`?(\w+)`?/i',
            '/\bUPDATE\s+(?:`?(\w+)`?\.)?`?(\w+)`?/i',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $cleanQuery, $matches)) {
                foreach ($matches[2] as $table) {
                    if (!empty($table)) {
                        $tables[] = $table;
                    }
                }
            }
        }
        
        return array_unique($tables);
    }
    
    public function runRedisBenchmark($query, $iterations = 1) 
    {
        $this->log("\n=== BENCHMARK Redis (Compatível com mySQL_DB) ===");
        
        // Usa o mesmo padrão de chave do mySQL_DB
        $normalizedQuery = $this->normalizeQuery($query);
        $cacheKey = "query[" . $this->dbname . "]:" . md5($normalizedQuery);
        $times = [];
        $totalRows = 0;
        
        // Verifica se existe cache
        if (!$this->redis->exists($cacheKey)) {
            $this->log("✗ Cache não existe! Execute o benchmark MySQL primeiro.");
            return false;
        }
        
        for ($i = 1; $i <= $iterations; $i++) {
            $this->log("Execução $i/$iterations...");
            
            $start = microtime(true);
            $cachedData = $this->redis->get($cacheKey);
            $getTime = microtime(true) - $start;
            
            if (!$cachedData) {
                $this->log("✗ Falha ao recuperar cache");
                continue;
            }
            
            $decodeStart = microtime(true);
            $data = json_decode($cachedData, true);
            $decodeTime = microtime(true) - $decodeStart;
            
            $totalTime = microtime(true) - $start;
            
            if (!$data || !isset($data['data'])) {
                $this->log("✗ Dados corrompidos no cache");
                continue;
            }
            
            // Verifica estrutura compatível com mySQL_DB
            $hasMetadata = isset($data['timestamp']) && isset($data['tables']) && isset($data['data']);
            
            if ($hasMetadata) {
                $rowCount = count($data['data']);
                $this->log("  Estrutura: Compatível com mySQL_DB");
                $this->log("  Timestamp: " . date('Y-m-d H:i:s', $data['timestamp']));
                $this->log("  Tabelas: " . implode(', ', $data['tables']));
            } else {
                $rowCount = count($data);
                $this->log("  Estrutura: Formato legado");
            }
            
            $totalRows = $rowCount;
            
            $times[] = [
                'get_time' => $getTime * 1000,
                'decode_time' => $decodeTime * 1000,
                'total_time' => $totalTime * 1000, 
                'rows' => $rowCount
            ];
            
            $this->log("  Get: " . number_format($getTime * 1000, 2) . "ms");
            $this->log("  Decode: " . number_format($decodeTime * 1000, 2) . "ms");
            $this->log("  Total: " . number_format($totalTime * 1000, 2) . "ms");
            $this->log("  Linhas: $rowCount");
            
            if ($i < $iterations) {
                usleep(50000); // 50ms entre execuções
            }
        }
        
        $this->results['redis'] = [
            'times' => $times,
            'total_rows' => $totalRows,
            'iterations' => $iterations
        ];
        
        $this->showRedisStats();
        return true;
    }
    
    private function showMySQLStats() 
    {
        if (!isset($this->results['mysql'])) return;
        
        $times = $this->results['mysql']['times'];
        $totalTimes = array_column($times, 'total_time');
        
        $this->log("\n--- Estatísticas MySQL ---");
        $this->log("Execuções: " . count($times));
        $this->log("Tempo médio: " . number_format(array_sum($totalTimes) / count($totalTimes), 2) . "ms");
        $this->log("Tempo mínimo: " . number_format(min($totalTimes), 2) . "ms");
        $this->log("Tempo máximo: " . number_format(max($totalTimes), 2) . "ms");
        $this->log("Linhas por execução: " . $this->results['mysql']['total_rows']);
    }
    
    private function showRedisStats() 
    {
        if (!isset($this->results['redis'])) return;
        
        $times = $this->results['redis']['times'];
        $totalTimes = array_column($times, 'total_time');
        
        $this->log("\n--- Estatísticas Redis ---");
        $this->log("Execuções: " . count($times));
        $this->log("Tempo médio: " . number_format(array_sum($totalTimes) / count($totalTimes), 2) . "ms");
        $this->log("Tempo mínimo: " . number_format(min($totalTimes), 2) . "ms");
        $this->log("Tempo máximo: " . number_format(max($totalTimes), 2) . "ms");
        $this->log("Linhas por execução: " . $this->results['redis']['total_rows']);
    }
    
    public function showComparison() 
    {
        if (!isset($this->results['mysql']) || !isset($this->results['redis'])) {
            $this->log("\n✗ Não há dados suficientes para comparação");
            return;
        }
        
        $mysqlAvg = array_sum(array_column($this->results['mysql']['times'], 'total_time')) / 
                   count($this->results['mysql']['times']);
                   
        $redisAvg = array_sum(array_column($this->results['redis']['times'], 'total_time')) / 
                   count($this->results['redis']['times']);
        
        $speedup = $mysqlAvg / $redisAvg;
        $improvement = (($mysqlAvg - $redisAvg) / $mysqlAvg) * 100;
        
        $this->log("\n" . str_repeat("=", 50));
        $this->log("           COMPARAÇÃO FINAL");
        $this->log(str_repeat("=", 50));
        $this->log(sprintf("MySQL (média):     %8.2fms", $mysqlAvg));
        $this->log(sprintf("Redis (média):     %8.2fms", $redisAvg));
        $this->log(sprintf("Speedup:           %8.2fx", $speedup));
        $this->log(sprintf("Melhoria:          %8.1f%%", $improvement));
        $this->log(str_repeat("=", 50));
        
        if ($speedup > 1) {
            $this->log("🚀 Redis é " . number_format($speedup, 1) . "x mais rápido!");
        } else {
            $this->log("⚠️  MySQL foi mais rápido neste teste");
        }
    }
    
    /**
     * Verifica se a chave de cache existe e mostra informações sobre ela
     */
    public function inspectCache($query) 
    {
        $normalizedQuery = $this->normalizeQuery($query);
        $cacheKey = "query[" . $this->dbname . "]:" . md5($normalizedQuery);
        
        $this->log("\n=== INSPEÇÃO DE CACHE ===");
        $this->log("Chave: $cacheKey");
        
        if (!$this->redis->exists($cacheKey)) {
            $this->log("❌ Cache não existe");
            return false;
        }
        
        $this->log("✅ Cache existe");
        
        // Verifica TTL
        $ttl = $this->redis->ttl($cacheKey);
        if ($ttl > 0) {
            $this->log("TTL: " . $ttl . " segundos (" . gmdate("H:i:s", $ttl) . ")");
        } else {
            $this->log("TTL: Sem expiração ou expirado");
        }
        
        // Pega informações do cache
        try {
            $cachedData = $this->redis->get($cacheKey);
            $data = json_decode($cachedData, true);
            
            if ($data && isset($data['timestamp'], $data['tables'], $data['data'])) {
                $this->log("Formato: Compatível com mySQL_DB");
                $this->log("Timestamp: " . date('Y-m-d H:i:s', $data['timestamp']));
                $this->log("Tabelas: " . implode(', ', $data['tables']));
                $this->log("Linhas: " . count($data['data']));
                $this->log("Idade: " . (time() - $data['timestamp']) . " segundos");
            } else {
                $this->log("Formato: Legado ou inválido");
                if (is_array($data)) {
                    $this->log("Linhas: " . count($data));
                }
            }
        } catch (Exception $e) {
            $this->log("❌ Erro ao ler cache: " . $e->getMessage());
        }
        
        // Verifica metadados
        $metaKey = "meta:$cacheKey";
        if ($this->redis->exists($metaKey)) {
            $this->log("✅ Metadados existem: $metaKey");
        } else {
            $this->log("❌ Metadados não existem");
        }
        
        // Verifica tags
        $tables = $this->extractTablesFromQuery($query);
        foreach ($tables as $table) {
            $tagKey = "tag:table:$table";
            if ($this->redis->sIsMember($tagKey, $cacheKey)) {
                $this->log("✅ Tag existe: $tagKey");
            } else {
                $this->log("❌ Tag não existe: $tagKey");
            }
        }
        
        return true;
    }

    private function normalizeQuery($query) {
        // Remove quebras de linha e tabs
        $query = str_replace(["\r\n", "\r", "\n", "\t"], ' ', $query);
        
        // Remove comentários SQL (-- comentário e /* comentário */)
        $query = preg_replace('/--.*$/m', '', $query);
        $query = preg_replace('/\/\*.*?\*\//s', '', $query);
        
        // Remove espaços múltiplos e substitui por um único espaço
        $query = preg_replace('/\s+/', ' ', $query);
        
        // Remove espaços no início e fim
        $query = trim($query);
        
        // Remove espaços antes e depois de caracteres especiais (mas preserva strings)
        $query = preg_replace('/\s*([(),=<>!])\s*/', '$1', $query);
        
        // Converte para minúsculas para comparação consistente
        $query = strtolower($query);
        
        return $query;
    }
    
    private function log($message) 
    {
        echo $message . PHP_EOL;
    }
    
    public function __destruct() 
    {
        if ($this->mysql) $this->mysql->close();
        if ($this->redis) $this->redis->close();
    }
}

// Função principal
function main() 
{
    global $config, $test_query;
    
    foreach ($test_query as $index => $query) {
        echo str_repeat("=", 60) . PHP_EOL;
        echo "       BENCHMARK MySQL vs Redis (Query " . ($index + 1) . "/" . count($test_query) . ")" . PHP_EOL;
        echo str_repeat("=", 60) . PHP_EOL;
        echo "Iniciado em: " . date('Y-m-d H:i:s') . PHP_EOL;
        
        $benchmark = new BenchmarkManager($config);
        
        // Número de execuções para média
        $iterations = 1;
        
        echo "\nQuery de teste:" . PHP_EOL;
        echo str_replace(["\n", "  "], [" ", " "], trim($query)) . PHP_EOL;
        echo "\nIterações por teste: $iterations" . PHP_EOL;
        
        // Primeiro verifica se já existe cache
        $benchmark->inspectCache($query);
        
        // 1. Executa benchmark MySQL
        $mysqlData = $benchmark->runMySQLBenchmark($query, $iterations);
        
        if (empty($mysqlData)) {
            echo "\n✗ Falha no benchmark MySQL - pulando para próxima query" . PHP_EOL;
            continue;
        }
        
        // 2. Cria cache com os dados (compatível com mySQL_DB)
        $cacheKey = $benchmark->cacheData($query, $mysqlData);
        
        if (!$cacheKey) {
            echo "\n✗ Falha ao criar cache - pulando benchmark Redis" . PHP_EOL;
            continue;
        }
        
        // 3. Executa benchmark Redis
        $benchmark->runRedisBenchmark($query, $iterations);
        
        // 4. Mostra comparação final
        $benchmark->showComparison();
        
        echo "\nFinalizado em: " . date('Y-m-d H:i:s') . PHP_EOL;
        echo "\n" . str_repeat("-", 60) . PHP_EOL;
        
        // Pausa entre queries para não sobrecarregar
        if ($index < count($test_query) - 1) {
            sleep(1);
        }
    }   
    
    echo "\n🎉 Benchmark completo! Todos os caches foram criados com compatibilidade mySQL_DB." . PHP_EOL;
}

// Executa apenas via CLI
if (php_sapi_name() === 'cli') {
    main();
} else {
    main();//echo "Execute via linha de comando: php benchmark_query.php" . PHP_EOL;
    exit(1);
}