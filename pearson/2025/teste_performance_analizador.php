<?php
/**
 * Script de teste para validar as melhorias de performance da classe AnalizadorSimuladoProvaFloripa
 * 
 * Este script compara a performance antes e depois das otimizações
 */

// Inclui os arquivos necessários
require_once 'includes/core.php';
require_once 'includes/ProvaFloripa/AnalizadorSimuladoProvaFloripa.php';

class TestePerformanceAnalizador {
    
    private $simuladoID;
    private $resultados = array();
    
    public function __construct($simuladoID = null) {
        $this->simuladoID = $simuladoID;
    }
    
    /**
     * Executa teste de performance básico
     */
    public function executarTestesBasicos() {
        echo "=== TESTE DE PERFORMANCE - AnalizadorSimuladoProvaFloripa ===\n\n";
        
        if (!$this->simuladoID) {
            echo "ERRO: ID do simulado não fornecido\n";
            return false;
        }
        
        // Teste 1: Carregamento e cálculos básicos
        $this->testarCarregamentoBasico();
        
        // Teste 2: C<PERSON>l<PERSON>los repetidos (teste de cache)
        $this->testarCacheCalculos();
        
        // Teste 3: Lazy loading
        $this->testarLazyLoading();
        
        // Teste 4: Método otimizado completo
        $this->testarCalculosCompletos();
        
        // Exibe resultados
        $this->exibirResultados();
        
        return true;
    }
    
    /**
     * Testa carregamento básico e cálculos individuais
     */
    private function testarCarregamentoBasico() {
        echo "Teste 1: Carregamento básico e cálculos individuais\n";
        
        $inicio = microtime(true);
        
        $analizador = new AnalizadorSimuladoProvaFloripa();
        $analizador->fixarSimulado(new Simulado($this->simuladoID));
        $analizador->carregarInscritos(true);
        $analizador->carregarRespostasDosInscritos();
        
        $tempoCarregamento = microtime(true) - $inicio;
        
        // Testa cálculos individuais
        $inicioCalculos = microtime(true);
        $analizador->calcularRendimentoPorQuestaoDosInscritos();
        $analizador->calcularRendimentoGlobalDosInscritos();
        $analizador->calcularRendimentoPorDisciplinaDosInscritos();
        $tempoCalculos = microtime(true) - $inicioCalculos;
        
        $this->resultados['carregamento_basico'] = array(
            'tempo_carregamento' => $tempoCarregamento,
            'tempo_calculos' => $tempoCalculos,
            'tempo_total' => $tempoCarregamento + $tempoCalculos,
            'estatisticas' => $analizador->obterEstatisticasCache()
        );
        
        echo sprintf("  - Carregamento: %.4f segundos\n", $tempoCarregamento);
        echo sprintf("  - Cálculos: %.4f segundos\n", $tempoCalculos);
        echo sprintf("  - Total: %.4f segundos\n\n", $tempoCarregamento + $tempoCalculos);
    }
    
    /**
     * Testa eficiência do cache executando cálculos repetidos
     */
    private function testarCacheCalculos() {
        echo "Teste 2: Eficiência do cache (cálculos repetidos)\n";
        
        $analizador = new AnalizadorSimuladoProvaFloripa();
        $analizador->fixarSimulado(new Simulado($this->simuladoID));
        $analizador->carregarInscritos(true);
        $analizador->carregarRespostasDosInscritos();
        
        // Primeira execução
        $inicio = microtime(true);
        $analizador->calcularRendimentoPorDisciplinaDosInscritos();
        $primeiraExecucao = microtime(true) - $inicio;
        
        // Segunda execução (deve usar cache)
        $inicio = microtime(true);
        $analizador->calcularRendimentoPorDisciplinaDosInscritos();
        $segundaExecucao = microtime(true) - $inicio;
        
        $this->resultados['cache_eficiencia'] = array(
            'primeira_execucao' => $primeiraExecucao,
            'segunda_execucao' => $segundaExecucao,
            'melhoria_percentual' => (($primeiraExecucao - $segundaExecucao) / $primeiraExecucao) * 100,
            'estatisticas' => $analizador->obterEstatisticasCache()
        );
        
        echo sprintf("  - Primeira execução: %.4f segundos\n", $primeiraExecucao);
        echo sprintf("  - Segunda execução: %.4f segundos\n", $segundaExecucao);
        echo sprintf("  - Melhoria: %.2f%%\n\n", (($primeiraExecucao - $segundaExecucao) / $primeiraExecucao) * 100);
    }
    
    /**
     * Testa lazy loading
     */
    private function testarLazyLoading() {
        echo "Teste 3: Lazy loading\n";
        
        $analizador = new AnalizadorSimuladoProvaFloripa();
        $analizador->fixarSimulado(new Simulado($this->simuladoID));
        $analizador->carregarInscritos(true);
        $analizador->carregarRespostasDosInscritos();
        $analizador->calcularRendimentoPorQuestaoDosInscritos();
        $analizador->calcularRendimentoGlobalDosInscritos();
        
        // Testa lazy loading
        $inicio = microtime(true);
        $rendimentoPorDisciplina = $analizador->obterRendimentoPorDisciplina();
        $tempoLazyLoading = microtime(true) - $inicio;
        
        $this->resultados['lazy_loading'] = array(
            'tempo_lazy_loading' => $tempoLazyLoading,
            'dados_carregados' => !empty($rendimentoPorDisciplina),
            'estatisticas' => $analizador->obterEstatisticasCache()
        );
        
        echo sprintf("  - Tempo lazy loading: %.4f segundos\n", $tempoLazyLoading);
        echo sprintf("  - Dados carregados: %s\n\n", !empty($rendimentoPorDisciplina) ? 'Sim' : 'Não');
    }
    
    /**
     * Testa método otimizado completo
     */
    private function testarCalculosCompletos() {
        echo "Teste 4: Método otimizado completo\n";
        
        $analizador = new AnalizadorSimuladoProvaFloripa();
        $analizador->fixarSimulado(new Simulado($this->simuladoID));
        $analizador->carregarInscritos(true);
        $analizador->carregarRespostasDosInscritos();
        
        // Executa método otimizado
        $resultado = $analizador->executarCalculosCompletos();
        
        $this->resultados['calculos_completos'] = $resultado;
        
        echo sprintf("  - Tempo total: %.4f segundos\n", $resultado['tempo_execucao']);
        echo sprintf("  - Memória usada: %.2f MB\n", $resultado['estatisticas_cache']['memoria_usada'] / 1024 / 1024);
        echo sprintf("  - Memória pico: %.2f MB\n\n", $resultado['estatisticas_cache']['memoria_pico'] / 1024 / 1024);
    }
    
    /**
     * Exibe resumo dos resultados
     */
    private function exibirResultados() {
        echo "=== RESUMO DOS RESULTADOS ===\n\n";
        
        foreach ($this->resultados as $teste => $dados) {
            echo "Teste: " . strtoupper(str_replace('_', ' ', $teste)) . "\n";
            
            if (isset($dados['tempo_total'])) {
                echo sprintf("  Tempo total: %.4f segundos\n", $dados['tempo_total']);
            }
            
            if (isset($dados['tempo_execucao'])) {
                echo sprintf("  Tempo execução: %.4f segundos\n", $dados['tempo_execucao']);
            }
            
            if (isset($dados['melhoria_percentual'])) {
                echo sprintf("  Melhoria cache: %.2f%%\n", $dados['melhoria_percentual']);
            }
            
            if (isset($dados['estatisticas']['memoria_usada'])) {
                echo sprintf("  Memória: %.2f MB\n", $dados['estatisticas']['memoria_usada'] / 1024 / 1024);
            }
            
            echo "\n";
        }
        
        echo "=== FIM DOS TESTES ===\n";
    }
}

// Execução do teste
if (isset($argv[1])) {
    $simuladoID = $argv[1];
    $teste = new TestePerformanceAnalizador($simuladoID);
    $teste->executarTestesBasicos();
} else {
    echo "Uso: php teste_performance_analizador.php <simulado_id>\n";
    echo "Exemplo: php teste_performance_analizador.php 123\n";
}

?>
