# Otimizações de Performance - AnalizadorSimuladoProvaFloripa

## Resumo das Melhorias Implementadas

Este documento descreve as otimizações de performance implementadas na classe `AnalizadorSimuladoProvaFloripa.php` para melhorar a velocidade dos relatórios.

## Problemas Identificados

### 1. Problema N+1 Query
- **Localização**: Método `calcularRendimentoPorDisciplinaDosInscritos()`
- **Problema**: Uma nova consulta SQL era executada para cada disciplina dentro de um loop
- **Impacto**: Múltiplas consultas desnecessárias ao banco de dados

### 2. Loops Aninhados Complexos
- **Localização**: Método `calcularRendimentoPorQuestaoDosInscritos()`
- **Problema**: Três níveis de loops aninhados processando questões
- **Impacto**: Complexidade O(n³) desnecessária

### 3. Re<PERSON><PERSON><PERSON><PERSON>los Desnecessários
- **Localização**: Vários méto<PERSON> de cálculo
- **Problema**: Métodos recalculavam dados já processados
- **Impacto**: Processamento redundante e perda de tempo

### 4. Falta de Cache
- **Localização**: Toda a classe
- **Problema**: Nenhum sistema de cache para consultas ou cálculos
- **Impacto**: Reprocessamento de dados idênticos

## Soluções Implementadas

### 1. Sistema de Cache Inteligente

#### Cache de Consultas SQL
```php
private $_cacheConsultas = array();

private function _executarConsultaComCache($sql, $chaveCache) {
    if (!isset($this->_cacheConsultas[$chaveCache])) {
        // Executa consulta e armazena resultado
    }
    return $this->_cacheConsultas[$chaveCache];
}
```

#### Cache de Quantidades por Disciplina
```php
private $_cacheQuantidadePorDisciplina = array();

private function _obterQuantidadePorDisciplina($simuladoID, $disciplinaID) {
    $chave = $simuladoID . '_' . $disciplinaID;
    if (!isset($this->_cacheQuantidadePorDisciplina[$chave])) {
        // Carrega dados uma única vez
    }
    return $this->_cacheQuantidadePorDisciplina[$chave];
}
```

### 2. Controle de Cálculos Realizados

#### Sistema de Flags
```php
private $_calculosRealizados = array();

private function _calculoJaRealizado($nomeCalculo) {
    return isset($this->_calculosRealizados[$nomeCalculo]) && 
           $this->_calculosRealizados[$nomeCalculo] === true;
}
```

#### Prevenção de Recálculos
Todos os métodos de cálculo agora verificam se já foram executados:
```php
public function calcularRendimentoPorDisciplinaDosInscritos() {
    if ($this->_calculoJaRealizado('rendimentoPorDisciplina')) {
        return true;
    }
    // ... lógica do cálculo ...
    $this->_marcarCalculoRealizado('rendimentoPorDisciplina');
}
```

### 3. Otimização de Loops

#### Pré-processamento de Questões
```php
// Antes: 3 loops aninhados para cada inscrito
foreach ($this->simuladoQuestoes as $fase => $tipos) {
    foreach ($tipos[$inscricao->obterTipo()] as $numero => $questoesNumeradas) {
        foreach ($questoesNumeradas as &$q) {
            // Processamento
        }
    }
}

// Depois: Pré-processa questões uma única vez
$questoesPorTipo = array();
foreach ($this->simuladoQuestoes as $fase => $tipos) {
    // Constrói estrutura otimizada
}
// Usa estrutura otimizada para cada inscrito
```

### 4. Lazy Loading

#### Métodos de Acesso Inteligente
```php
public function obterRendimentoPorDisciplina() {
    if (!$this->_calculoJaRealizado('rendimentoPorDisciplina')) {
        $this->calcularRendimentoPorDisciplinaDosInscritos();
    }
    return $this->rendimentoPorDisciplina;
}
```

### 5. Pré-carregamento Otimizado

#### Carregamento em Lote
```php
private function _preCarregarQuantidadesPorDisciplina($simuladoID) {
    $sql = 'SELECT q_disciplina, COUNT(*) as quantidade 
            FROM questoes 
            WHERE q_simulado = ? 
            GROUP BY q_disciplina';
    // Carrega todas as quantidades de uma vez
}
```

### 6. Método de Execução Otimizada

#### Execução Sequencial Inteligente
```php
public function executarCalculosCompletos() {
    // Pré-carrega dados compartilhados
    $this->_preCarregarQuantidadesPorDisciplina($this->simulado->obterID());
    
    // Executa cálculos na ordem de dependência
    $this->calcularRendimentoPorQuestaoDosInscritos();
    $this->calcularRendimentoGlobalDosInscritos();
    $this->calcularRendimentoPorDisciplinaDosInscritos();
    // ...
}
```

## Ferramentas de Monitoramento

### 1. Estatísticas de Cache
```php
public function obterEstatisticasCache() {
    return array(
        'cache_quantidade_disciplinas' => count($this->_cacheQuantidadePorDisciplina),
        'cache_consultas' => count($this->_cacheConsultas),
        'calculos_realizados' => count($this->_calculosRealizados),
        'memoria_usada' => memory_get_usage(true),
        'memoria_pico' => memory_get_peak_usage(true)
    );
}
```

### 2. Status dos Cálculos
```php
public function obterStatusCalculos() {
    return array(
        'rendimentoPorQuestao' => $this->_calculoJaRealizado('rendimentoPorQuestao'),
        'rendimentoGlobal' => $this->_calculoJaRealizado('rendimentoGlobal'),
        // ... outros cálculos
    );
}
```

## Compatibilidade

### Backward Compatibility
- **Todas as funções públicas mantêm a mesma assinatura**
- **Comportamento externo permanece idêntico**
- **Nenhuma mudança na API pública**

### Métodos Públicos Preservados
- `calcularRendimentoPorQuestaoDosInscritos()`
- `calcularRendimentoGlobalDosInscritos()`
- `calcularRendimentoPorDisciplinaDosInscritos()`
- `calcularRendimentoPorTurma()`
- `calcularRendimentoPorSerie()`
- `calcularRendimentoPorInstituicao()`

## Como Usar

### Uso Tradicional (Mantido)
```php
$analizador = new AnalizadorSimuladoProvaFloripa();
$analizador->fixarSimulado(new Simulado($sid));
$analizador->carregarInscritos(true);
$analizador->carregarRespostasDosInscritos();
$analizador->calcularRendimentoPorQuestaoDosInscritos();
$analizador->calcularRendimentoGlobalDosInscritos();
```

### Uso Otimizado (Novo)
```php
$analizador = new AnalizadorSimuladoProvaFloripa();
$analizador->fixarSimulado(new Simulado($sid));
$analizador->carregarInscritos(true);
$analizador->carregarRespostasDosInscritos();

// Executa todos os cálculos de forma otimizada
$resultado = $analizador->executarCalculosCompletos();
echo "Tempo de execução: " . $resultado['tempo_execucao'] . " segundos";
```

## Testes de Performance

Execute o script de teste para validar as melhorias:
```bash
php teste_performance_analizador.php <simulado_id>
```

## Benefícios Esperados

1. **Redução de Consultas SQL**: Até 90% menos queries ao banco
2. **Eliminação de Recálculos**: 100% de prevenção de processamento redundante
3. **Otimização de Loops**: Redução significativa na complexidade algorítmica
4. **Uso Eficiente de Memória**: Cache inteligente com limpeza automática
5. **Monitoramento**: Ferramentas para acompanhar performance em produção

## Próximos Passos

1. Executar testes em ambiente de produção
2. Monitorar métricas de performance
3. Ajustar tamanhos de cache se necessário
4. Considerar implementação de cache persistente (Redis/Memcached) para casos extremos
