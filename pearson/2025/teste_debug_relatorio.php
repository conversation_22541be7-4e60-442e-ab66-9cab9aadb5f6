<?php
/**
 * Teste de Debug para Relatório de Turmas
 * Verifica se há erros que impedem o relatório de abrir
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TESTE DE DEBUG - RELATÓRIO TURMAS ===\n";
echo "Verificando se o relatório pode ser carregado...\n\n";

try {
    // Incluir o core do sistema
    if (file_exists('includes/core.php')) {
        require_once 'includes/core.php';
        echo "✓ Core incluído com sucesso\n";
    } else {
        echo "✗ Arquivo core.php não encontrado\n";
        exit(1);
    }

    // Verificar se o arquivo do relatório existe
    $arquivoRelatorio = 'modulos/relatorios/pf_rendimento_instituicao_series_turmas_por_disciplina_tab/pf_rendimento_instituicao_series_turmas_por_disciplina_tab.relatorio.php';
    if (file_exists($arquivoRelatorio)) {
        echo "✓ Arquivo do relatório encontrado\n";
    } else {
        echo "✗ Arquivo do relatório não encontrado: $arquivoRelatorio\n";
        exit(1);
    }

    // Verificar sintaxe do arquivo
    $output = array();
    $return_var = 0;
    exec("php -l $arquivoRelatorio 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "✓ Sintaxe do arquivo está correta\n";
    } else {
        echo "✗ Erro de sintaxe no arquivo:\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
        exit(1);
    }

    // Tentar incluir dependências
    try {
        Core::incluir('RelatorioListagem', null, true);
        echo "✓ RelatorioListagem incluído\n";
    } catch (Exception $e) {
        echo "✗ Erro ao incluir RelatorioListagem: " . $e->getMessage() . "\n";
    }

    try {
        Core::incluir('Inscricao', null, true);
        echo "✓ Inscricao incluído\n";
    } catch (Exception $e) {
        echo "✗ Erro ao incluir Inscricao: " . $e->getMessage() . "\n";
    }

    try {
        Core::incluir('Professor', null, true);
        echo "✓ Professor incluído\n";
    } catch (Exception $e) {
        echo "✗ Erro ao incluir Professor: " . $e->getMessage() . "\n";
    }

    // Tentar incluir o arquivo do relatório
    try {
        include_once $arquivoRelatorio;
        echo "✓ Arquivo do relatório incluído com sucesso\n";
    } catch (Exception $e) {
        echo "✗ Erro ao incluir arquivo do relatório: " . $e->getMessage() . "\n";
        exit(1);
    }

    // Verificar se a classe existe
    if (class_exists('RLPFRendimentoInstituicaoSeriesTurmasPorDisciplinaTab')) {
        echo "✓ Classe do relatório encontrada\n";
    } else {
        echo "✗ Classe do relatório não encontrada\n";
        exit(1);
    }

    // Tentar instanciar a classe
    try {
        $relatorio = new RLPFRendimentoInstituicaoSeriesTurmasPorDisciplinaTab();
        echo "✓ Classe instanciada com sucesso\n";
    } catch (Exception $e) {
        echo "✗ Erro ao instanciar classe: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        exit(1);
    }

    // Verificar métodos críticos
    $metodos = array(
        '_obterDisciplinasPorSimulado',
        '_preCarregarDisciplinas',
        '_obterAnalizadorSimulado',
        '_deveProcessarSimulado',
        '_processarDadosLazy'
    );

    foreach ($metodos as $metodo) {
        if (method_exists($relatorio, $metodo)) {
            echo "✓ Método $metodo existe\n";
        } else {
            echo "✗ Método $metodo não encontrado\n";
        }
    }

    echo "\n=== TESTE CONCLUÍDO COM SUCESSO ===\n";
    echo "O relatório parece estar funcionando corretamente.\n";
    echo "Se ainda não está abrindo, o problema pode ser:\n";
    echo "1. Erro de permissões\n";
    echo "2. Problema de configuração do sistema\n";
    echo "3. Erro em tempo de execução específico\n";

} catch (Exception $e) {
    echo "✗ ERRO FATAL: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "✗ ERRO FATAL: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

?>
