/* PÁGINA */
html, body {
	margin:0;
    padding:0;
    height:100%;
}

body {
	color: #434343;	
	background-color: #ffffff;	
	margin: 0px;
}
body, td, th, p, li {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11px;
}

a {
	color: #0E487A;
	text-decoration: none;
}
a:hover {
	text-decoration: underline;
}

.normal {
	font-size: 11px;
}
.minusculo {
	font-size: 10px;
}
.menor {
	font-size: 12px;
}
.medio {
	font-size: 14px;
}
.maior {
	font-size: 18px;
}
.gigante {
	font-size: 22px;
}

div.medio table tbody tr td {
	font-size: 14px;
}

div.medio table tr td {
	font-size: 14px;
}

div.medio table tbody tr td.normal {
	font-size: 10px;
}

div.medio table tr td.normal {
	font-size: 10px;
}

.transparencia75 {
	filter:alpha(opacity=75); -moz-opacity: .75; opacity: .75;
}
.transparencia50 {
	filter:alpha(opacity=50); -moz-opacity: .50; opacity: .50;
}
.transparencia25 {
	filter:alpha(opacity=25); -moz-opacity: .25; opacity: .25;
}

#container{
	min-height: 100%;
	height: auto !important;
	*height:100%;
	_height:100%;
	height:100%;
    position: relative;
    /*background-color: #07003b;*/
	background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/fundo-grade.png");
    background-position: left bottom;
    background-repeat: no-repeat;
}

.bubbleInfo {
    float: left;
    height: 12px;
    margin-right: 5px;
    position: relative;
    width: 12px;
}

.popup {
    position: absolute;
    display: none; /* keeps the popup hidden if no JS available */
    background: none repeat scroll 0 0 #eee;
    border: 3px solid #0068CE;
    border-radius: 7px 7px 7px 7px;
    box-shadow: 2px 2px 4px #555555;
    color: #333333;
    display: block;
    font-size: 13px;
    opacity: 1;
    padding: 10px;
    width: 200px;
    z-index: 2147483647;
}

/* TOPO SITE */
#topo_site {	
	background-color: #ffffff;
	color: #434343;
}
#topo_site a {
	color: #434343;
	text-decoration: none;
}
#topo_site a:hover {
	text-decoration: underline;
}


/* MODULO: _cabecalho */
#cabecalho {
	 background: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/hdrNameBg.gif") repeat-x;
}
#cabecalho #logo {
	margin-left: 32px;
	padding-top: 0px;	
	height: 27px;
}
#cabecalho #logo a {
	color: #ffffff;
	text-decoration: none;
	font-size: 15px;
	font-weight: bold;
}
#cabecalho #logo a:hover {
	text-decoration: none;
}
#cabecalho #logo .esquerda {
    background: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/canto-titulo-esquerdo.png");
    background-repeat: no-repeat;

    padding: 0;
    width: 10px;
}
#cabecalho #logo .centro {
    background: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/canto-titulo-meio.png");
    background-repeat: repeat-x;

    padding-bottom: 4px;
    padding-left: 6px;
    padding-right: 6px;
}
#cabecalho #logo .direita {
    background: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/canto-titulo-direito.png");
    background-repeat: no-repeat;

    padding: 0;
    width: 10px;
}
#cabecalho .menu {
	padding-top: 8px;
}
#cabecalho .menu td {
	padding-left: 5px;
	padding-right: 5px;

	color: #434343;
	font-size:12px;
}

#btn_sair{
	font-weight: bold;
    margin-right: 10px;
}


/* MODULO: _menu_principal */
#menu_principal {
	padding-top: 8px;
}
#menu_pai {
	background:#ffffff url('https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/hdrTabBg.gif') bottom repeat-x ;
	background-color:#ffffff;
}
#menu_pai table {
	color: #000099;
    font-size: 11px;
    height: 32px;
    margin-left: 32px;
}
#menu_pai td {
	font-size: 12px;

	background-color: #DDDDDD;
    border-left: 0 solid #CCCCCC;

    border-radius: 10px 10px 0 0;
	-webkit-border-radius: 10px 10px 0 0;
  	-moz-border-radius: 10px 10px 0 0;
  	-khtml-border-radius: 10px 10px 0 0;

  	behavior: url(border-radius-ie.htc);

    border-right: 1px solid #CCCCCC;
    cursor: pointer;
    padding-left: 10px;
    padding-right: 10px;

    color: #434343;
}
#menu_pai td a {
	color: #07003B;
}
#menu_pai .selecionado {
	color: #ffffff;
	font-weight:bold;

	background-color:#07003B;
	
	padding-left:10px;
	padding-right:10px;
	
	border-bottom: 0px solid #ffffff;
	border-left: 0px solid #aae;
	border-right: 0px solid #aae;
}
#menu_pai .selecionado a {
	color: #ffffff;
}



#menu_filho {
	background-color: #07003B;

	background-image: -moz-linear-gradient(top, #07003B, #07003B);
	background-image: -ms-linear-gradient(top, #07003B, #07003B);
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #07003B), color-stop(100%, #07003B));
	background-image: -webkit-linear-gradient(top, #07003B, #07003B);
	background-image: -o-linear-gradient(top, #07003B, #07003B);
	background-image: linear-gradient(top, #07003B, #07003B);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#07003B', endColorstr='#07003B', GradientType=0);

    border-bottom: 1px solid #07003B;
    color: #FFFFFF;
    font-size: 11px;

    height: 20px;
}
#menu_filho table {
	height: 32px;
}
#menu_filho td {
	padding-left:20px;
	padding-right:20px;
	padding-top:5px;
	padding-bottom:5px;

	font-size: 13px;
}
#menu_filho td a {
	color: #E6E6E6;
}
#menu_filho .selecionado {
	font-weight:bold;
}

.menu_suspenso {
	color:#434343;
	position:absolute;
	left:0px;
	top:0px;
	z-index:1000000001;
	border-left:1px solid #CCCCCC;
	border-right:1px solid #CCCCCC;
	border-bottom:1px solid #CCCCCC;
	padding:0px;
	text-align:left;
	overflow-x:hidden;
	overflow-y:hidden;
	background-color:#eef;

	border-radius: 0 0 7px 7px;
	-webkit-border-radius: 0 0 7px 7px;
  	-moz-border-radius: 0 0 7px 7px;
  	-khtml-border-radius: 0 0 7px 7px;

  	behavior: url(border-radius-ie.htc);
}
.menu_suspenso table {
	border-radius: 0 0 7px 7px;
	-webkit-border-radius: 0 0 7px 7px;
  	-moz-border-radius: 0 0 7px 7px;
  	-khtml-border-radius: 0 0 7px 7px;

  	behavior: url(border-radius-ie.htc);
}
.menu_suspenso td a {
    width:250px;
    text-align:left;
    line-height:20px;
    font-weight:normal;
    color:#434343;
    background-color:#DDDDDD;
    padding:2px 5px 2px 5px;
    border-bottom: 1px solid #CCCCCC;
    border-top: 1px solid #CCCCCC;
    display:block;
}
.menu_suspenso td a:hover {
    padding:2px 5px 2px 5px;
    width:250px;
    text-align:left;
    color:#434343;
    font-weight:normal;
    text-decoration:none;
    background-color:#ffffff;
}
.menu_suspenso .selecionado a {
	font-weight: bold;
}
.menu_suspenso .selecionado a:hover {
	font-weight: bold;
}


/* MODULO: _navegador */
#navegador {
	padding: 8px;
	padding-bottom: 0px;
}
#navegador .separador {
	background-color: #CCCCCC;
}
#navegador .navegacao {
	padding-right: 40px;
}
#navegador .navegacao .maior {
	color: #000033;
	font-size: 18px;
	font-weight: bold;
}
#navegador .navegacao .maior a {
	color: #000033;
}
#navegador .navegacao .medio {
	color: #0000cc;
	font-size: 18px;
	font-weight: bold;
}
#navegador .navegacao .medio a {
	color: #0E487A;/*0000cc*/
}
#navegador .navegacao .menor {
	color: #0000cc;
	font-size: 18px;
}
#navegador .navegacao .menor a {
	color: #0000cc;
}
#navegador #atalhos td {
	padding-left: 8px;
}
#navegador #atalhos .espacador {
	padding-left: 16px;
}
#mini_navegador #atalhos td {
	padding-left: 4px;
}
#mini_navegador #atalhos .espacador {
	padding-left: 8px;
}

/* CONTEÚDO SITE */
#conteudo_site {
	min-height: 100%;
	height: auto !important;
	*height:100%;
	_height:100%;
	height:100%;

	padding: 8px;
	padding-bottom: 93px;
}

#painel {
	background:url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/showPanelTopBg.gif") repeat-x;
}
#painel .centro {
	padding: 16px;
	padding-bottom: 0px;
}



/* CONTEÚDO SITE */
/* listagens principais (lp) */
.lp {
	background-color:#ddf;
	border:1px solid #CCCCCC;

	border-radius: 5px 5px 5px 5px;
	-webkit-border-radius: 5px 5px 5px 5px;
  	-moz-border-radius: 5px 5px 5px 5px;
  	-khtml-border-radius: 5px 5px 5px 5px;

  	behavior: url(border-radius-ie.htc);
}
.lp_SuperHeader {
	background: none repeat scroll 0 0 #DDDDDD;
    color: #333333;
    font-size: 15px;
    font-weight: bold;
    padding: 8px;
}
.lp_ColHeader {
	background-color:#eef;
	font-weight:bold;
	font-size: 12px;
}
.lp_ColData {
	background-color:#ffffff;
}
.lp_ColDataHover {
	background-color:#eeefff;
}
.alfabeto td {
	text-align: center;
	padding-left: 8px;
	padding-right: 8px;
	font-weight:bold;
	font-size: 12px;
	cursor: pointer;
	color: #333333;
}
.alfabeto .selecionado {
	color: #434343;
	background-color: #ddf;
}


/* visao detalhada (vd) */
.vd {

}
.vd_Titulo {
	padding-bottom: 16px;
}
#vd_TabHeader {
	width: 100%;
	height: 24px;
}
#vd_TabHeader th {
	border:2px solid #AAA;
	padding-left:20px;
	padding-right:20px;
	background-color:#ffffee;
	
	border-radius: 5px 5px 0 0;
	-webkit-border-radius: 5px 5px 0 0;
  	-moz-border-radius: 5px 5px 0 0;
  	-khtml-border-radius: 5px 5px 0 0;

  	behavior: url(border-radius-ie.htc);

	white-space: nowrap;
	width: 1%;
}
#vd_TabHeader .selecionada {
	border-bottom: 2px solid #FFFFFF;
	background-color: #FFFFFF;

	border-radius: 5px 5px 0 0;
	-webkit-border-radius: 5px 5px 0 0;
  	-moz-border-radius: 5px 5px 0 0;
  	-khtml-border-radius: 5px 5px 0 0;

  	behavior: url(border-radius-ie.htc);
}
#vd_TabHeader .selecionada a {
	color: #434343;
}
#vd_TabHeader td {
	border-bottom:2px solid #AAA;
	white-space: nowrap;
	width: 8px;

	border-radius: 5px 5px 0 0;
	-webkit-border-radius: 5px 5px 0 0;
  	-moz-border-radius: 5px 5px 0 0;
  	-khtml-border-radius: 5px 5px 0 0;

  	behavior: url(border-radius-ie.htc);
}
#vd_TabHeader .ultimo {
	width: auto;
}
.vd_TabConteudo {
	border-left:2px solid #AAA;
	border-right:2px solid #AAA;
	border-bottom:2px solid #AAA;
	padding: 16px;

	background: #fff;
}
.vd_BlocoEspacador {
	margin-top: 16px;
}
.vd_BlocoEspacadorErro {
	margin-top: 4px;
	margin-bottom: 16px;
}
.vd_BlocoBotoes {
	margin-top: 4px;
	margin-bottom: 4px;
}
#vd_Bloco {
	
}
#vd_Bloco th {
	color:#545454;
	font-weight: normal;
	background-color:#f5f5ff;
	text-align: right;
	border-bottom:1px solid #dadaee ;
	border-top:1px solid #ffffff ;
	padding: 4px;
	padding-right: 8px;
	width: 25%;
}
#vd_Bloco .titulo {
	color: #434343;
	font-weight: bold;
	background-color:#DCDCDC;
	text-align: left;
	padding: 4px;
	border-bottom:2px solid #999;
	width: 100%;

	border-radius: 5px 5px 0 0;
	-webkit-border-radius: 5px 5px 0 0;
  	-moz-border-radius: 5px 5px 0 0;
  	-khtml-border-radius: 5px 5px 0 0;

  	behavior: url(border-radius-ie.htc);
}
#vd_Bloco td {
	padding: 4px;
	padding-left: 8px;
	
	border-bottom:1px solid #efefef;
}
#vd_Bloco .descricao {
	background-color: #efefef;
}
#vd_Bloco td .sem_estilo {
	padding: 0px;
	padding-left: 0px;
	
	border: 0px solid #efefef;
}


/* caixa de dialogo (cd) */
.cd {
	border:2px solid #CCCCCC;
	background-color:#FFFFFF;

	-webkit-border-radius: 5px 5px 5px 5px;
  	-moz-border-radius: 5px 5px 5px 5px;
	border-radius: 5px 5px 5px 5px;
  	-khtml-border-radius: 5px 5px 5px 5px;

  	behavior: url(border-radius-ie.htc);

	padding: 2px;
}
.cd_Titulo {
	background-color:#dedede;
	font-weight:bold;

	border-radius: 7px 7px 7px 7px;
	-webkit-border-radius: 7px 7px 7px 7px;
  	-moz-border-radius: 7px 7px 7px 7px;
  	-khtml-border-radius: 7px 7px 7px 7px;

  	behavior: url(border-radius-ie.htc);
}
.cd_Baixo {
	background-color:#eee;

	border-radius: 7px 7px 7px 7px;
	-webkit-border-radius: 7px 7px 7px 7px;
  	-moz-border-radius: 7px 7px 7px 7px;
  	-khtml-border-radius: 7px 7px 7px 7px;

  	behavior: url(border-radius-ie.htc);
}


/* MODULO: _reproducao_questao */
.rq_enunciado {
	font-weight: bold;
}
.rq_resolucao {
	
}
.rq_proposicao_correta {
	color: #3344bb;
	font-weight: bold;
}
.rq_proposicao_normal {
	
}
.rq_resposta_correta {
	color: #009900;
	font-weight: bold;
}
.rq_resposta_errada {
	color: #FF0000;
}

/* MODULO: _relatorios */
/* caixa de dialogo */
.rlt_cd {
	border: 2px solid #AAAAAA;
	border-radius: 10px 10px 10px 10px;
	-webkit-border-radius: 10px 10px 10px 10px;
  	-moz-border-radius: 10px 10px 10px 10px;
  	-khtml-border-radius: 10px 10px 10px 10px;

  	behavior: url(border-radius-ie.htc);
}
.rlt_cd_Titulo { }
.rlt_cd_TituloDireita {
	border-left: 2px dotted #aaa;
}

/* sub caixa de dialogo */
.rlt_scd {
	border: 2px dotted #aaa;
	border-top-width: 0px;
	background-color: #fff;
}
.rlt_scd_Espacador {
	margin-left: 24px;
	margin-right: 24px;
}
.rlt_scd_EspacadorVertical {
	border-right: 2px dotted #aaa;
}

/* painel de relatorio */
.rlt_painel {
	border: 1px solid #AAAAAA;
	background: #fff;

	border-radius: 10px 10px 10px 10px;
	-webkit-border-radius: 10px 10px 10px 10px;
  	-moz-border-radius: 10px 10px 10px 10px;
  	-khtml-border-radius: 10px 10px 10px 10px;

  	behavior: url(border-radius-ie.htc);
}
.rlt_painel_Botoes {
	background: none repeat scroll 0 0 #EEEEEE;
	padding: 8px;

	border-radius: 8px 8px 8px 8px;
	-webkit-border-radius: 8px 8px 8px 8px;
  	-moz-border-radius: 8px 8px 8px 8px;
  	-khtml-border-radius: 8px 8px 8px 8px;

  	behavior: url(border-radius-ie.htc);
}

#_bloco_notas_link{
	float: right; 
	margin-right: 20px;
}

.rlt_painel_Titulo {
	color: #888888;
	font-weight: bold;
}
.rlt_painel_Borda {
}
.rlt_painel_Relatorio {
	padding: 8px;
	padding-bottom: 16px;
}

/* listagem de relatorios */
.rltl {
	background-color: #DDDDDD;
	width: 80%;
	border-collapse: collapse;
}
.rltl th {
    background-color:#f6f6f6;
    border-bottom:1px solid #eaeaea;
    color:#737373;
    font-weight: bold;
    white-space:nowrap;
}
.rltl th a {
    color:#383838;
}
.rltl td {
	border-left: 1px solid #DDDDDD;
    border-right: 1px solid #DDDDDD;
    border-bottom: 1px solid #DDDDDD;
}
.rltl .colData {
	background-color: #ffffff;
}
.rltl .colDataHover {
	background-color: #eeefff;
}

/* listagem de relatorios para impressão */
.rltli {
    border: 1px solid #000000;
	background: #DDDDDD;
	
	border-collapse: collapse;
}
.rltli th {
	color: #434343;
    background-color: #f6f6f6;
    border: 1px solid #000000;
    text-align: left;
}
.rltli th a {
    color: #434343;
}
.rltli td {
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
	background-color: #ffffff;
}


/* MODULO: _alerta */
.alerta {
	border: 3px #999999 solid;
	background-color: #FFFFFF;
}
.alerta td {
	padding-right: 8px;
}

.alerta .titulo {
	padding: 0px;
	padding-bottom: 4px;
	border-bottom: 1px #CCCCCC solid;
	margin-bottom: 8px;
	margin-top: 8px;
}


/* BAIXO SITE */
#baixo_site {
	bottom: 0;
    position: absolute;
    width: 100%;
    height:93px;
    clear: both;
}

/* MODULO: _rodape */
.rodape {
	background: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/settingsSelUIBg.gif") repeat-x #fff;
	
	padding: 16px;
	margin-top: 48px;
}



/* FORMULÁRIOS */
.direita {
	padding-left: 6px;
}
form {
	display: inline;
}
textarea, .bginput
{
	font-size: 11px;
}
input
{
	 font-size: 11px;
}
input[disabled] {
	 background-color: #EEEEEE;
	 color: gray;
}
select
{
	font-size: 11px;
}
option, optgroup
{
	font-size: 11px;
}
fieldset {
	padding: 10px;
	margin-top: 0px;
	padding-top: 0px;
	
	border-color: #CCCCCC;
	border-style: solid;
	border-width: 1px;
}
fieldset legend {
	margin-bottom: 4px;
	font-style: italic;
}
.lb_requerido {
	font-weight:bold;
}
.lb_requerido_erros {
	font-weight:bold;
	color: #FF0000;
}
.lb_opcional {
	
}
.lb_opcional_com_erro {
	color: #FF0000;
}


/* FORMULÁRIOS: botoes */
.botao {
	padding: 5px;

	border-left:1px solid #555555;
	border-top:1px solid #555555;
	border-right:1px solid #555555;
	border-bottom:1px solid #555555;
	border-style: ridge;
    border-width: 1px;

	background-color:#D6D6D6;

	border-radius: 3px 3px 3px 3px;
	-webkit-border-radius: 3px 3px 3px 3px;
  	-moz-border-radius: 3px 3px 3px 3px;
  	-khtml-border-radius: 3px 3px 3px 3px;

  	behavior: url(border-radius-ie.htc);
}
.botao:hover {
	cursor: pointer;
	transition:all 0.3s ease;
}

.novo {
	background-color:#07003B;
	color:#fff;
	border: 0;
}

.novo:hover {
	background-color:#008935;
	color:#fff;
	border: 0;
	cursor: pointer;
	transition:all 0.3s ease;
}
.remover {
	background-color:#C43C35;
	color:#fff;
	font-weight:bold;
}
.editar {
	background-color:#0064CD;
	color:#fff;
}
.salvar {
	background-color:#57A957;
	color:#fff;
	font-weight:bold;
}
.cancelar {
	background-color:#C43C35;
	color:#fff;
}


/* ERROS */
.erros_caixa {
	border: #000066 dotted 2px;
	background-color: #CCCCCC;
	padding: 10px;
	margin-bottom: 15px;
}
.erros_caixa .nome {
	font-weight: bold;
}
.erros_caixa .descricao {
	padding-left: 3px;
}


/* MODULO: _ajuda */
.ajuda_indice div {
	background-repeat: no-repeat;
	background-position: 6px 2px;
	padding-left: 24px;
	white-space: nowrap;
}

.aluno_deficiente_texto {
	white-space: nowrap;
}

.aluno_deficiente_imagem {
	padding: 5px 20px 20px 10px;
	/*background-image: url(media/deficiente.jpg);*/
	background-repeat: no-repeat;
	background-size: 30px 25px;
	display: inline-block;
}

.combo_lancamento {
	border: 0;
	color: #000;
	background: transparent;
	font-size: 16px;
	font-weight: bold;
	text-align: center;
	align-items: center;
	width: 378px;
	-webkit-appearance: none;
	width: 50px;
	height: 25px;
	-moz-border-radius: 9px 9px 9px 9px;
	border-radius: 9px 9px 9px 9px;
	background-color: #ddd;
	font-family: Roboto;
}

.combo_lancamento_option {
	font-size: 20px;
	font-weight: normal;
	display: block;
	white-space: pre;
	min-height: 1.2em;
	padding: 0px 2px 1px;
	text-indent: 14px;
	font-family: Arial;
}


.checkbox_lancamento {
	border: 0;
	color: #ccc;
	background: transparent;
	font-size: 20px;
	font-weight: bold;
	text-align: center;
	/* padding: 2px 10px; */
	width: 20px;
	/* -webkit-appearance: inherit; */
	/* width: 60px; */
	height: 20px;
	-moz-border-radius: 9px 9px 9px 9px;
	border-radius: 9px 9px 9px 9px;
	background-color: #58B14C;


}

.input_lancamento {	
	border: 0;
	color: #000;
	background: transparent;
	font-size: 16px;
	font-weight: bold;
	text-align: center;
	align-items: center;
	width: 378px;
	-webkit-appearance: none;
	width: 50px;
	height: 25px;
	-moz-border-radius: 9px 9px 9px 9px;
	border-radius: 9px 9px 9px 9px;
	background-color: #ddd;
	font-family: Roboto;
}

.input_lancamento:focus, .combo_lancamento:focus {	
	border: 3px #ec5900 solid;
}

.grid-container-aluno {
	display: grid;
	grid-template-columns: 200px 7px 200px 7px 200px 7px 200px;
	padding-bottom:10px;
  }
  .inscrever-aluno {
	border-radius: 5px 5px 5px 5px;
	border: 1px solid rgba(103, 103, 103, 0.8);
	padding: 18px;
	font-size: 12px;
	text-align: center;
	display: inline-block;
  }

.novo_aluno {
	cursor:pointer;
	color:#004c25;
	background-color: #038c2e;
	color:#ffffff;rgb(18, 199, 2)rgb(15, 153, 2);
	box-shadow: 0 8px 12px 0 rgba(0,0,0,0.24), 0 4px 7px 0 rgba(0,0,0,0.19);

}

.novo_aluno:hover {
	background-color: #026421 !important;
	cursor:pointer;
	-webkit-transition: background-color 0.7s ease;
	-moz-transition: background-color 0.7s ease;
	transition: background-color 0.7s ease;
}

.play_tutorial {
	cursor:pointer;
	color:#004c25;
	background-color: #03458c;
	color:#ffffff;rgb(18, 199, 2)rgb(15, 153, 2);
	box-shadow: 0 8px 12px 0 rgba(0,0,0,0.24), 0 4px 7px 0 rgba(0,0,0,0.19);

}

.play_tutorial:hover {
	background-color: #03728c !important;
	cursor:pointer;
	-webkit-transition: background-color 0.7s ease;
	-moz-transition: background-color 0.7s ease;
	transition: background-color 0.7s ease;
}

.teste_gravacao {
	cursor:pointer;
	background-color: #ccb606;
	color:#ffffff;
	box-shadow: 0 8px 12px 0 rgba(0,0,0,0.24), 0 4px 7px 0 rgba(0,0,0,0.19);
}

.teste_gravacao:hover {
	background-color: #fadd00 !important;
	cursor:pointer;
	-webkit-transition: background-color 0.7s ease;
	-moz-transition: background-color 0.7s ease;
	transition: background-color 0.7s ease;
}


.delete_aluno {
	cursor:pointer;
	color:#ffffff;
	background-color: #9f0000;
	box-shadow: 0 8px 12px 0 rgba(0,0,0,0.24), 0 4px 7px 0 rgba(0,0,0,0.19);
}

.delete_aluno:hover {
	background-color: #700101 !important;
	-webkit-transition: background-color 0.7s ease;
	-moz-transition: background-color 0.7s ease;
	transition: background-color 0.7s ease;
}

.ajuda_indice .ponto {
	background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/ajuda_ponto.gif");
}
.ajuda_indice .mais {
	background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/ajuda_mais.gif");
}
.ajuda_indice .menos {
	background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/ajuda_menos.gif");
}

.ajuda_indice .selecionado {
	color: #CCCCCC;
}

.relatorio_rendimento_geral_baixo {
	background-color: #FFCC99;
}
.relatorio_rendimento_geral_baixo a {
	color: #FF0000;
}
.relatorio_rendimento_turma_baixo a {
	color: #FF0000;
}

.relatorio_conteudo_domina  {
	background-color: #999999 !important;
}
.relatorio_conteudo_domina_parcialmente {
	background-color: #cccccc !important;
}
.relatorio_conteudo_nao_domina {
	background-color: #eeeeee !important;
}


.ajax_carregando { background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/ajax_carregando.gif"); background-repeat: no-repeat; background-position: center; }
.ajax_sucesso { background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/ajax_sucesso.gif"); background-repeat: no-repeat; background-position: center; }
.ajax_falha { background-image: url("https://dyubuzjbgoyjh.cloudfront.net/estilos/azul/media/ajax_falha.gif"); background-repeat: no-repeat; background-position: center; }

.background_direita { background-position: right !important; }
.background_esquerda { background-position: left !important; }

#login-logo{
	bottom: 20%;
    position: fixed;
    width: 95%;
}

#ll-esquerda{
	float: left;
    width: 45%;
}

#ll-direita{
	float: right;
    width: 45%;
}

#ll-direita img{
	float: right;
}

/* Realização do Simulado On-line */

.sr_general{
	display: block;
    height: auto;
    width: 100%;
}

.sr_right{
	background: none repeat scroll 0 0 #FFFFFF;
	border: 1px solid #DEDEDE;
    border-radius: 5px 5px 5px 5px;
    display: block;
    height: auto;
    overflow: hidden;
    padding: 10px;
    width: 155px;
}

.sr_left{
	float: left;
    height: auto;
    margin-right: 20px;
    width: 1000px;
}

.sr_list{
	line-height: 20px;
    padding: 0 0 0 10px;
    margin:0;
}

.sr_list li{
	list-style: none outside none;
}

.sr_title{
	background-color: #F7F6F6;
    border-radius: 10px 10px 10px 10px;
    font-size: 20px;
    margin-bottom: 20px;
    padding: 10px;
}

.sr_crono{
	border: 1px solid #DEDEDE;
    border-radius: 5px 5px 5px 5px;
    color: #555555;
    font-size: 13px;
    margin: 0 auto;
    padding: 5px 5px 2px;
    width: 150px;
    overflow: hidden;
}

#crono_geral{
	display: block;
    float: right;
    margin-left: 5px;
}

.sr_questionario_general{
	border: 1px solid #DEDEDE;
    border-radius: 5px 5px 5px 5px;
    margin-top: 20px;
    background: #fff;
}

.sr_questionario_general_start{
	border: 1px solid #DEDEDE;
    border-radius: 5px 5px 5px 5px;
    margin-top: 20px;
    background: #fff;
}

.sr_questionario_general_end{
	border: 1px solid #DEDEDE;
    border-radius: 5px 5px 5px 5px;
    margin-top: 20px;
    background: #fff;
}

.sr_num_questao{
	border-bottom: 1px solid #DEDEDE;
    border-radius: 5px 0 5px 0;
    border-right: 1px solid #DEDEDE;
    color: #333333;
    font-size: 18px;
    height: 20px;
    padding: 5px 10px;
    float:left;
}

.sr_crono_questao{
	border-bottom: 1px solid #DEDEDE;
    border-left: 1px solid #DEDEDE;
    border-radius: 0 5px 0 5px;
    float: right;
    font-size: 14px;
    padding: 5px;
    color:#333333;
}

.sr_aluno_questao{
	border-bottom: 1px solid #DEDEDE;
    border-left: 1px solid #DEDEDE;
    border-radius: 0 5px 0 5px;
    float: right;
    color: #333333;
    font-size: 18px;
    height: 20px;
    padding: 5px 10px;
}

.sr_texto_questao{
	clear: both;
    padding: 15px;
    border-bottom: 1px solid #DEDEDE;
}

.sr_questao_titulo{
	font-size: 22px;
}

label.sr_questao_pergunta{
	font-size: 30px!important;
}

.sr_questao_texto{
	font-size: 12px;
    margin-top: 10px;
}

.sr_resposta_questao{
	border-bottom: 1px solid #DEDEDE;
    padding: 15px;
}

.sr_botoes{
	padding: 10px;
	border-top: 1px solid #DEDEDE;
}

li.realizada button{
	color:#FFFFFF!important;
	background-color: #70AF71!important;
	border: 1px solid #70AF71!important;
}

li.pendente button{
	color:#FFFFFF!important;
	background-color: #8B008B!important;
	border: 1px solid #8B008B!important;
	opacity: 80%!important;
}

.sr_list li.chute a{
	color:#EFAB73;
}

.sr_list li.emprogresso a{
	color:#8074AA;
}

.sr_list li.semresposta a{
	color:#CC5F5F;
}

.sr_list li.finish a{
	color:#CC5F5F;
}

.sr_list li.atual a{
	font-weight:bold;
}

.sr_pesquisa {
	background: #07003B !important; /* Old browsers */
	background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
	background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
	background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
	background: #ddd -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
	background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
	background: #ddd linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
    border-color: #CCCCCC #AAAAAA #AAAAAA #CCCCCC;
    border-radius: 3px 3px 3px 3px;
    border-right: 1px solid #AAAAAA;
    border-style: solid;
    border-width: 1px;
    color: #FFFFFF;
    cursor: pointer;
    display: inline-block;
    font-family: "HelveticaNeue","Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 11px;
    font-weight: normal;
    line-height: 30px;
    margin: 5px;
    padding: 4px 12px;
    text-decoration: none;
    text-shadow: 0 1px rgba(255, 255, 255, 0.75);
	border-radius: 15px 15px 15px 15px;

 }

 .sr_pesquisa:hover {
	color: #FFFFFF;
	font-weight: normal;
	background-color: #800080 !important;
	text-decoration:none;
  	-webkit-transition: background-color 0.7s ease;
  	-moz-transition: background-color 0.7s ease;
  	transition: background-color 0.7s ease;
}

.sr_pesquisa_retornar {
	background: #800080 !important; /* Old browsers */
	background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
	background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
	background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
	background: #ddd -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
	background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
	background: #ddd linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
    border-color: #CCCCCC #AAAAAA #AAAAAA #CCCCCC;
    border-radius: 3px 3px 3px 3px;
    border-right: 1px solid #AAAAAA;
    border-style: solid;
    border-width: 1px;
    color: #FFFFFF;
    font-family: "HelveticaNeue","Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    font-weight: normal;
    text-decoration: none;
    text-shadow: 0 1px rgba(255, 255, 255, 0.75);
	border-radius: 15px 15px 15px 15px;

 }

 .sr_pesquisa_retornar:hover {
	color: #FFFFFF;
	background-color: #9400D3 !important;
	text-decoration:none;
  	-webkit-transition: background-color 0.7s ease;
  	-moz-transition: background-color 0.7s ease;
  	transition: background-color 0.7s ease;
	cursor: pointer;
}


.sr_button {
   background: #ddd; /* Old browsers */
	background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
	background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
	background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
	background: #ddd -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
	background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
	background: #ddd linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
    border-color: #CCCCCC #AAAAAA #AAAAAA #CCCCCC;
    border-radius: 3px 3px 3px 3px;
    border-right: 1px solid #AAAAAA;
    border-style: solid;
    border-width: 1px;
    color: #444444;
    cursor: pointer;
    display: inline-block;
    font-family: "HelveticaNeue","Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 11px;
    font-weight: normal;
    line-height: 30px;
    margin: 5px;
    padding: 4px 12px;
    text-decoration: none;
    text-shadow: 0 1px rgba(255, 255, 255, 0.75);
	border-radius: 15px 15px 15px 15px;
}

.sr_button:hover {
	color: #FFFFFF;
	font-weight: normal;
	background-color: #07003B;
  	-webkit-transition: background-color 0.7s ease;
  	-moz-transition: background-color 0.7s ease;
  	transition: background-color 0.7s ease;
}

.sr_button:active {
	border: 1px solid #666;
	background: #ccc; /* Old browsers */
	background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
	background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
	background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
	background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
	background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
	background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ 
}

.sr_button span{
	float: left;
    font-size: 18px;
    overflow: hidden;
}

.sr_button img{
	float: left;
	margin-right:10px;
	margin-top: 3px;
}

.sr_click{
	cursor: pointer;
}

.sr_votacom{
	padding: 20px;
	overflow: hidden;
}

.sr_votacom div ul{
	overflow: hidden;
	padding: 0px;
	width:300px;
}

.sr_votacom div ul li{
	list-style: none outside none;
	float:left;
	margin-right:10px;
	margin-left:10px;
}

.sr_votacom_td{
	vertical-align: top;
}

.multiplaescolha{
	width:40px;
	height:20px;
	font-size:18px;
	text-align:center;
}

.discursiva, .aberta{
	font-size:14px;
	height:140px;
	width:100%;
}

.somatorio{
	width:80px;
	height:20px;
	font-size:18px;
	text-align:center;
}

.disabled{
	background-color:#eee;
	border:1px solid #ddd;
	border-radius:3px;
	color:#444;
}

.sr_cmmt{
	text-align:right;
}

.sr_cmmt textarea{
	margin: 10px 0 0 0;
	height:80px;
	width:300px;
}

.sr_vota{
	text-align: left;
}

.alert_box{
	border: 1px dashed #888888;
    border-radius: 3px 3px 3px 3px;
    color: #333333;
    font-size: 12px;
    font-weight: bold;
    margin: 25px;
    padding: 15px;
    text-align: center;
}

.alert_box_erro{
	background: none repeat scroll 0 0 #FCECEC;
    border: 1px dashed #EE1111;
}

.alert_box_sucesso{
	background: none repeat scroll 0 0 #F3FCEC;
    border: 1px dashed #558E2F;
}

/* CHAT */
#chat-label {
	background: #0064CD;
    border-radius: 10px 10px 0 0;
    bottom: 0;
    height: 12px;
    padding: 10px;
    position: fixed;
    right: 35px;
    width: 250px;
    color:#fff;
    font-weight: bold;
    box-shadow: 1px 1px 4px #333333;
}

#chat-list {
	display: none;
	background: #fff;
    border-radius: 10px 10px 0 0;
    bottom: 0;
    padding: 0 10px;
    position: fixed;
    right: 35px;
    width: 250px;
    color:#888;
    font-weight: bold;
    box-shadow: 1px 1px 4px #333333;
    border: 5px solid #0394D9;
}

.chat-list {
	display: none;
	background: #fff;
    border-radius: 10px 10px 0 0;
    bottom: 0;
    padding: 0 10px;
    position: fixed;
    right: 35px;
    width: 250px;
    color:#888;
    font-weight: bold;
    box-shadow: 1px 1px 4px #333333;
    border: 5px solid #0394D9;
}


/*a.button img{
	float: left;
	height: 100px;
}

a.button span{
	float: left;
	font-size: 60px;
	height: 98px;
	line-height: 100px;
	margin-left: 20px;
	overflow: hidden;
}*/

a.button {
	background: #eee; /* Old browsers */
	background: #eee -moz-linear-gradient(top, rgba(255,255,255,.2) 0%, rgba(0,0,0,.2) 100%); /* FF3.6+ */
	background: #eee -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.2)), color-stop(100%,rgba(0,0,0,.2))); /* Chrome,Safari4+ */
	background: #eee -webkit-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Chrome10+,Safari5.1+ */
	background: #eee -o-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* Opera11.10+ */
	background: #eee -ms-linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* IE10+ */
	background: #eee linear-gradient(top, rgba(255,255,255,.2) 0%,rgba(0,0,0,.2) 100%); /* W3C */
	border: 1px solid #aaa;
	border-top: 1px solid #ccc;
	border-left: 1px solid #ccc;
	padding: 4px 12px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	color: #444;
	display: inline-block;
	font-size: 11px;
	font-weight: bold;
	text-decoration: none;
	text-shadow: 0 1px rgba(255, 255, 255, .75);
	cursor: pointer;
	margin: 20px;
	line-height: 21px;
	font-family: "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif; }

a.button:hover {
	color: #222;
	background: #ddd; /* Old browsers */
	background: #ddd -moz-linear-gradient(top, rgba(255,255,255,.3) 0%, rgba(0,0,0,.3) 100%); /* FF3.6+ */
	background: #ddd -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.3)), color-stop(100%,rgba(0,0,0,.3))); /* Chrome,Safari4+ */
	background: #ddd -webkit-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Chrome10+,Safari5.1+ */
	background: #ddd -o-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* Opera11.10+ */
	background: #ddd -ms-linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* IE10+ */
	background: #ddd linear-gradient(top, rgba(255,255,255,.3) 0%,rgba(0,0,0,.3) 100%); /* W3C */
	border: 1px solid #888;
	border-top: 1px solid #aaa;
	border-left: 1px solid #aaa; }

a.button:active {
	border: 1px solid #666;
	background: #ccc; /* Old browsers */
	background: #ccc -moz-linear-gradient(top, rgba(255,255,255,.35) 0%, rgba(10,10,10,.4) 100%); /* FF3.6+ */
	background: #ccc -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,.35)), color-stop(100%,rgba(10,10,10,.4))); /* Chrome,Safari4+ */
	background: #ccc -webkit-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Chrome10+,Safari5.1+ */
	background: #ccc -o-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* Opera11.10+ */
	background: #ccc -ms-linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* IE10+ */
	background: #ccc linear-gradient(top, rgba(255,255,255,.35) 0%,rgba(10,10,10,.4) 100%); /* W3C */ }

	.links_login {
		width: 350px;
		height: 50px;
		border: 1px solid rgb(204, 204, 204);
		background-color: #07003B;
		border-radius: 5px 5px 5px 5px;
		vertical-align: middle;
		display: table-cell;
	}
	
	.links_login:hover {
		background-color: #008935;
		cursor: pointer;
		-webkit-transition: background-color 0.7s ease;
		-moz-transition: background-color 0.7s ease;
		transition: background-color 0.7s ease;

		width: 350px;
		height: 50px;
		border: 1px solid rgb(204, 204, 204);
		background-color: #07003B;
		border-radius: 5px 5px 5px 5px;
		vertical-align: middle;
		display: table-cell;
	}

.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  	background-color: #000000d4;
	color: #fff;
	text-align: center;
	border-radius: 6px;
	padding: 10px;
	position: absolute;
	z-index: 1;
	bottom: 30px;
	left: 25%;
	margin-left: -60px;
	border: 2px solid #000;
	font-weight: bold;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
}

.tooltip .tooltipImg{
  visibility: hidden;
	position: absolute;
	top: -12px;
	left: 30px;
}

.tooltip:hover .tooltipImg{
  visibility: visible;
}