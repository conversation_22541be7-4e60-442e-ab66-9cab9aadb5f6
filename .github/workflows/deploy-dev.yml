name: Deploy to EC2 DEV

on:
  push:
    branches: [ develop ]

jobs:
  deploy_prod1_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 1
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_1 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '
  deploy_prod2_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 2
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_2 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '
  deploy_prod3_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 3
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_3 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '
  deploy_prod4_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 4
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_4 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod5_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 5
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_5 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"
        
        '
  deploy_prod6_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 6
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_6 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod7_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 7
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_7 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod8_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 8
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_8 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod9_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 9
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_9 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod10_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 10
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_10 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '
  deploy_prod11_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 11
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_11 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod12_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 12
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_12 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod13_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 13
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_13 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf $HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "$HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "$HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "$HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="$HOME/githubactions-dev/pearson/2025/dev"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
          git checkout develop
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "$REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' $REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '
  deploy_prod14_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 14
      env:
        PRIVATE_KEY: ${{secrets.EC2_PRIVATE_KEY }}
        HOST: ${{secrets.EC2_HOST_PROD_14 }}
        USER: ${{secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod15_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 15
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_15 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod16_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 16
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_16 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod17_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 17
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_17 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod18_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 18
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_18 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod19_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 19
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_19 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"

        '

  deploy_prod20_dev:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 prod 20
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_20 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025 em DEV"

        rm -rf HOME/githubactions-dev/pearson/2025/dev

        if [ ! -d "HOME/githubactions-dev/pearson/2025/dev" ]; then
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev"
        else
          rm -rf "HOME/githubactions-dev/pearson/2025/dev"
          mkdir -p "HOME/githubactions-dev/pearson/2025/dev" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso HOME/githubactions-dev/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="develop"
        REPO_DIR="HOME/githubactions-dev/pearson/2025/dev"

        cd "REPO_DIR"

        if [ -d "REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "REPO_DIR"
          git pull origin "BRANCH"
        else
          ls REPO_DIR 
          echo "Clonando pull do repo REPO_URL no dir REPO_DIR "
          git clone "REPO_URL" "REPO_DIR"
          git checkout develop
        fi

        ls "REPO_DIR"

        echo "Repositorio clonado"

        echo "Criando diretorio no www"

        mkdir -p /var/www/html/avaliarede/pearson/dev

        cd "REPO_DIR"

        echo "Configurando banco de dev"

        ls

        sed -i -e 's/avaliare_db_pearson/avaliare_db_pearson_dev/g' REPO_DIR/pearson/2025/config.online.php

       

        echo "Copiando instalacao para /var/www/html/avaliarede/pearson/dev"
         
        cp -r "REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson/dev

        echo "Criando links com EFS comum das EC2"

        rm /var/www/html/avaliarede/pearson/dev/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/dev/2025/upload  -rf

        ln -s /mnt/efs/pearson/dev/2025/includes /var/www/html/avaliarede/pearson/dev/2025/includes

        ln -s /mnt/efs/pearson/dev/2025/upload /var/www/html/avaliarede/pearson/dev/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/dev/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/dev/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/*

        ls /var/www/html/avaliarede/pearson/dev

        cd /var/www/html/avaliarede/pearson/dev

        grep -rl 'avaliare_db_pearson_2025' . | xargs sed -i 's/avaliare_db_pearson_2025/avaliare_db_pearson_dev_2025/g'
        
        echo "Deploy Dev instalado com sucesso"
        '
