name: Deploy to EC2

on:
  push:
    branches: [ main ]

jobs:
  deploy_prod1:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 1
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"   

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod2:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 2
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_2 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod3:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 3
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_3 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod4:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 4
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_4 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod5:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 5
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_5 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod6:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 6
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_6 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod7:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 7
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_7 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod8:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 8
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_8 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod9:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 9
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_9 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod10:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 10
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_10 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod11:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 11
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_11 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod12:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 12
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_12 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '

  deploy_prod13:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 13
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_13 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod14:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 14
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_14 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod15:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 15
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_15 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod16:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 16
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_16 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod17:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 17
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_17 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod18:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 18
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_18 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod19:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 19
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_19 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '
  deploy_prod20:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to EC2 20
      env:
        PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
        HOST: ${{ secrets.EC2_HOST_PROD_20 }}
        USER: ${{ secrets.EC2_USER }}
        PRIVATE_KEY_GITHUB: ${{ secrets.PRIVATE_KEY_GITHUB }}
      run: |        
        echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
        ssh -o StrictHostKeyChecking=no -i private_key ${USER}@${HOST} '

        echo "Instalando Pearson 2025"

        rm -rf $HOME/githubactions/pearson/2025

        if [ ! -d "$HOME/githubactions/pearson/2025" ]; then
          mkdir -p "$HOME/githubactions/pearson/2025"
        else
          rm -rf "$HOME/githubactions/pearson/2025"
          mkdir -p "$HOME/githubactions/pearson/2025" 
        fi
 
        echo "Diretorio de clonagem criado com sucesso $HOME/githubactions/pearson"
        
        REPO_URL="https://${{ secrets.PRIVATE_KEY_GITHUB }}@github.com/ti-avaliativa/pearson-2025.git" 
        BRANCH="main"
        REPO_DIR="$HOME/githubactions/pearson/2025"

        cd "$REPO_DIR"

        if [ -d "$REPO_DIR/.git" ]; then
          echo "Fazendo pull do repo"
          cd "$REPO_DIR"
          git pull origin "$BRANCH"
        else
          ls  $REPO_DIR 
          echo "Clonando pull do repo $REPO_URL no dir : $REPO_DIR "
          git clone "$REPO_URL" "$REPO_DIR"
        fi

        ls "$REPO_DIR"

        echo "Repositorio clonado"

        echo "Fazendo backup do projeto"

        rm -rf $HOME/githubactions/pearson_2025_bkp

        if [ ! -d "$HOME/githubactions/pearson_2025_bkp" ]; then
          mkdir -p "$HOME/githubactions/pearson_2025_bkp"
        fi

        cp -r /var/www/html/avaliarede/pearson "$HOME/githubactions/pearson_2025_bkp"

        echo "Criando diretorio no www"

        rm -rf /var/www/html/avaliarede/pearson

        mkdir -p /var/www/html/avaliarede/pearson

        cd "$REPO_DIR/pearson/2025"

        ls
         
        cp -r "$REPO_DIR/pearson/2025" /var/www/html/avaliarede/pearson

        cp -r "$HOME/githubactions/pearson/2025/index.php" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/robots.txt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/mt" /var/www/html/avaliarede/pearson/

        cp -r "$HOME/githubactions/pearson/2025/_selecione_ano" /var/www/html/avaliarede/pearson/
           
        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/includes /var/www/html/avaliarede/pearson/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/2025/upload /var/www/html/avaliarede/pearson/2025

        mkdir -p /var/www/html/avaliarede/pearson/dev/2025

        cp -r $HOME/githubactions/pearson_2025_bkp/pearson/dev/2025/** /var/www/html/avaliarede/pearson/dev/2025

        rm /var/www/html/avaliarede/pearson/2025/includes -rf
        rm /var/www/html/avaliarede/pearson/2025/upload  -rf

        ln -s /mnt/efs/pearson/2025/includes /var/www/html/avaliarede/pearson/2025/includes

        ln -s /mnt/efs/pearson/2025/upload /var/www/html/avaliarede/pearson/2025/upload

        echo "Copiando pasta includes para mount"

        find /mnt/efs/pearson/2025/includes -name "*.php" -exec /bin/rm {} \;

        cp -r "$REPO_DIR/pearson/2025/includes" /mnt/efs/pearson/2025

        sudo chmod 777 -R /var/www/html/avaliarede/pearson

        sudo chmod 777 -R /var/www/html/avaliarede/pearson/dev/2025
        
        echo "Deploy instalado com sucesso"

        '